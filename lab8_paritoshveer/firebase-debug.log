[debug] [2025-07-25T23:21:13.361Z] ----------------------------------------------------------------------
[debug] [2025-07-25T23:21:13.367Z] Command:       /usr/local/bin/node /opt/homebrew/bin/firebase init
[debug] [2025-07-25T23:21:13.367Z] CLI Version:   14.11.1
[debug] [2025-07-25T23:21:13.367Z] Platform:      darwin
[debug] [2025-07-25T23:21:13.367Z] Node Version:  v22.13.0
[debug] [2025-07-25T23:21:13.367Z] Time:          Fri Jul 25 2025 19:21:13 GMT-0400 (Eastern Daylight Time)
[debug] [2025-07-25T23:21:13.367Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-25T23:21:13.371Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-25T23:21:13.371Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  /Users/<USER>/Desktop/Humber College/cpan213/labs/lab8_paritoshveer

[info] 
=== Project Setup
[info] 
[info] First, let's associate this project directory with a Firebase project.
[info] You can create multiple project aliases by running firebase use --add, 
[info] but for now we'll just set up a default project.
[info] 
[debug] [2025-07-25T23:23:35.465Z] Checked if tokens are valid: true, expires at: 1753489259730
[debug] [2025-07-25T23:23:35.469Z] Checked if tokens are valid: true, expires at: 1753489259730
[debug] [2025-07-25T23:23:35.470Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=100
[debug] [2025-07-25T23:23:36.148Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-25T23:23:36.148Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-07-25T23:23:39.874Z] ExitPromptError: User force closed the prompt with SIGINT
    at Interface.sigint (/opt/homebrew/lib/node_modules/firebase-tools/node_modules/@inquirer/core/dist/commonjs/lib/create-prompt.js:101:37)
    at Interface.emit (node:events:524:28)
    at Interface.emit (node:domain:489:12)
    at [_ttyWrite] [as _ttyWrite] (node:internal/readline/interface:1124:18)
    at ReadStream.onkeypress (node:internal/readline/interface:263:20)
    at ReadStream.emit (node:events:536:35)
    at ReadStream.emit (node:domain:489:12)
    at emitKeys (node:internal/readline/utils:370:14)
    at emitKeys.next (<anonymous>)
    at ReadStream.onData (node:internal/readline/emitKeypressEvents:64:36)
[error] 
[error] Error: An unexpected error has occurred.
