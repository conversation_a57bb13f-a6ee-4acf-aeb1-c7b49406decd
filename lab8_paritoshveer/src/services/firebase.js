import { initializeApp } from 'firebase/app';
import { getAuth, initializeAuth, getReactNativePersistence } from 'firebase/auth';
import { getFirestore, enableNetwork, disableNetwork } from 'firebase/firestore';
import { getFunctions } from 'firebase/functions';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Firebase configuration
// TODO: Replace with your actual Firebase config
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth with AsyncStorage persistence
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage)
});

// Initialize Firestore with offline persistence
const db = getFirestore(app);

// Initialize Cloud Functions
const functions = getFunctions(app);

// Network state management for offline support
let isOnline = true;

export const setNetworkState = async (online) => {
  isOnline = online;
  try {
    if (online) {
      await enableNetwork(db);
      console.log('Firestore: Network enabled');
    } else {
      await disableNetwork(db);
      console.log('Firestore: Network disabled');
    }
  } catch (error) {
    console.error('Error managing network state:', error);
  }
};

export const getNetworkState = () => isOnline;

// Export Firebase services
export { auth, db, functions };
export default app;
