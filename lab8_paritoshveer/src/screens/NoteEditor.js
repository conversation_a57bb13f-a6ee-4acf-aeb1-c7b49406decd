import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator
} from 'react-native';
import { useNotes } from '../hooks/useNotes';

const NoteEditor = ({ navigation, route }) => {
  const { noteId } = route.params || {};
  const { notes, createNote, updateNote, loading } = useNotes();
  
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [category, setCategory] = useState('general');
  const [tags, setTags] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const isEditing = !!noteId;
  const currentNote = isEditing ? notes.find(note => note.id === noteId) : null;

  // Load note data if editing
  useEffect(() => {
    if (currentNote) {
      setTitle(currentNote.title || '');
      setContent(currentNote.content || '');
      setCategory(currentNote.category || 'general');
      setTags(currentNote.tags ? currentNote.tags.join(', ') : '');
    }
  }, [currentNote]);

  // Track changes
  useEffect(() => {
    if (isEditing && currentNote) {
      const hasChanged = 
        title !== (currentNote.title || '') ||
        content !== (currentNote.content || '') ||
        category !== (currentNote.category || 'general') ||
        tags !== (currentNote.tags ? currentNote.tags.join(', ') : '');
      setHasChanges(hasChanged);
    } else {
      setHasChanges(title.trim() || content.trim() || tags.trim());
    }
  }, [title, content, category, tags, currentNote, isEditing]);

  const handleSave = async () => {
    if (!title.trim() && !content.trim()) {
      Alert.alert('Error', 'Please add a title or content to save the note.');
      return;
    }

    setIsSaving(true);

    try {
      const noteData = {
        title: title.trim(),
        content: content.trim(),
        category: category.trim() || 'general',
        tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      };

      if (isEditing) {
        await updateNote(noteId, noteData);
        Alert.alert('Success', 'Note updated successfully!');
      } else {
        await createNote(noteData);
        Alert.alert('Success', 'Note created successfully!');
      }

      setHasChanges(false);
      navigation.goBack();
    } catch (error) {
      Alert.alert('Error', 'Failed to save note. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    if (hasChanges) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Do you want to save before leaving?',
        [
          { text: 'Discard', style: 'destructive', onPress: () => navigation.goBack() },
          { text: 'Cancel', style: 'cancel' },
          { text: 'Save', onPress: handleSave }
        ]
      );
    } else {
      navigation.goBack();
    }
  };

  const categories = ['general', 'work', 'personal', 'ideas', 'todo', 'important'];

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Text style={styles.backText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {isEditing ? 'Edit Note' : 'New Note'}
        </Text>
        <TouchableOpacity
          style={[styles.saveButton, (!hasChanges || isSaving) && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={!hasChanges || isSaving}
        >
          {isSaving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.saveText}>Save</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Title Input */}
        <TextInput
          style={styles.titleInput}
          placeholder="Note title..."
          value={title}
          onChangeText={setTitle}
          multiline
          autoFocus={!isEditing}
        />

        {/* Category Selection */}
        <View style={styles.categorySection}>
          <Text style={styles.sectionLabel}>Category</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.categoryContainer}>
              {categories.map((cat) => (
                <TouchableOpacity
                  key={cat}
                  style={[
                    styles.categoryButton,
                    category === cat && styles.selectedCategoryButton
                  ]}
                  onPress={() => setCategory(cat)}
                >
                  <Text
                    style={[
                      styles.categoryButtonText,
                      category === cat && styles.selectedCategoryButtonText
                    ]}
                  >
                    {cat}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Tags Input */}
        <View style={styles.tagsSection}>
          <Text style={styles.sectionLabel}>Tags (comma separated)</Text>
          <TextInput
            style={styles.tagsInput}
            placeholder="work, important, meeting..."
            value={tags}
            onChangeText={setTags}
            multiline
          />
        </View>

        {/* Content Input */}
        <View style={styles.contentSection}>
          <Text style={styles.sectionLabel}>Content</Text>
          <TextInput
            style={styles.contentInput}
            placeholder="Start writing your note..."
            value={content}
            onChangeText={setContent}
            multiline
            textAlignVertical="top"
          />
        </View>

        {/* Note Info */}
        {isEditing && currentNote && (
          <View style={styles.noteInfo}>
            <Text style={styles.infoText}>
              Created: {currentNote.createdAt?.toLocaleDateString()} {currentNote.createdAt?.toLocaleTimeString()}
            </Text>
            <Text style={styles.infoText}>
              Last updated: {currentNote.updatedAt?.toLocaleDateString()} {currentNote.updatedAt?.toLocaleTimeString()}
            </Text>
          </View>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  backButton: {
    padding: 5,
  },
  backText: {
    fontSize: 16,
    color: '#007AFF',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  saveButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
    minWidth: 60,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 15,
  },
  titleInput: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    minHeight: 40,
    textAlignVertical: 'top',
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  categorySection: {
    marginBottom: 20,
  },
  categoryContainer: {
    flexDirection: 'row',
  },
  categoryButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  selectedCategoryButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  categoryButtonText: {
    fontSize: 14,
    color: '#666',
    textTransform: 'capitalize',
  },
  selectedCategoryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  tagsSection: {
    marginBottom: 20,
  },
  tagsInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    minHeight: 40,
  },
  contentSection: {
    flex: 1,
    marginBottom: 20,
  },
  contentInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    minHeight: 200,
    textAlignVertical: 'top',
  },
  noteInfo: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  infoText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
});

export default NoteEditor;
