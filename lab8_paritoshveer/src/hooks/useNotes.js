import { useState, useEffect, createContext, useContext } from 'react';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  serverTimestamp,
  enableNetwork,
  disableNetwork
} from 'firebase/firestore';
import { db } from '../services/firebase';
import { useAuth } from './useAuth';

// Create Notes Context
const NotesContext = createContext({});

// Notes Provider Component
export const NotesProvider = ({ children }) => {
  const { user } = useAuth();
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Real-time listener for user's notes
  useEffect(() => {
    if (!user) {
      setNotes([]);
      return;
    }

    setLoading(true);
    setError(null);

    const notesRef = collection(db, 'notes');
    const q = query(
      notesRef,
      where('userId', '==', user.uid),
      orderBy('updatedAt', 'desc')
    );

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const notesData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate(),
          updatedAt: doc.data().updatedAt?.toDate()
        }));
        setNotes(notesData);
        setLoading(false);
      },
      (error) => {
        console.error('Error fetching notes:', error);
        setError('Failed to load notes. Please try again.');
        setLoading(false);
      }
    );

    return unsubscribe;
  }, [user]);

  // Create a new note
  const createNote = async (noteData) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setError(null);
      const notesRef = collection(db, 'notes');
      
      const newNote = {
        ...noteData,
        userId: user.uid,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        category: noteData.category || 'general',
        tags: noteData.tags || [],
        isArchived: false
      };

      const docRef = await addDoc(notesRef, newNote);
      return docRef.id;
    } catch (error) {
      console.error('Error creating note:', error);
      setError('Failed to create note. Please try again.');
      throw error;
    }
  };

  // Update an existing note
  const updateNote = async (noteId, updates) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setError(null);
      const noteRef = doc(db, 'notes', noteId);
      
      const updateData = {
        ...updates,
        updatedAt: serverTimestamp()
      };

      await updateDoc(noteRef, updateData);
    } catch (error) {
      console.error('Error updating note:', error);
      setError('Failed to update note. Please try again.');
      throw error;
    }
  };

  // Delete a note
  const deleteNote = async (noteId) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setError(null);
      const noteRef = doc(db, 'notes', noteId);
      await deleteDoc(noteRef);
    } catch (error) {
      console.error('Error deleting note:', error);
      setError('Failed to delete note. Please try again.');
      throw error;
    }
  };

  // Archive/Unarchive a note
  const toggleArchiveNote = async (noteId, isArchived) => {
    await updateNote(noteId, { isArchived: !isArchived });
  };

  // Get filtered notes based on search and category
  const getFilteredNotes = () => {
    let filteredNotes = notes;

    // Filter by category
    if (selectedCategory !== 'all') {
      filteredNotes = filteredNotes.filter(note => 
        note.category === selectedCategory
      );
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filteredNotes = filteredNotes.filter(note =>
        note.title?.toLowerCase().includes(query) ||
        note.content?.toLowerCase().includes(query) ||
        note.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return filteredNotes;
  };

  // Get unique categories from notes
  const getCategories = () => {
    const categories = new Set(['all']);
    notes.forEach(note => {
      if (note.category) {
        categories.add(note.category);
      }
    });
    return Array.from(categories);
  };

  // Clear error
  const clearError = () => setError(null);

  const value = {
    notes,
    loading,
    error,
    searchQuery,
    selectedCategory,
    createNote,
    updateNote,
    deleteNote,
    toggleArchiveNote,
    getFilteredNotes,
    getCategories,
    setSearchQuery,
    setSelectedCategory,
    clearError
  };

  return (
    <NotesContext.Provider value={value}>
      {children}
    </NotesContext.Provider>
  );
};

// Custom hook to use notes context
export const useNotes = () => {
  const context = useContext(NotesContext);
  if (!context) {
    throw new Error('useNotes must be used within a NotesProvider');
  }
  return context;
};
