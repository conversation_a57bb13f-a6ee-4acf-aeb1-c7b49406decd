import { useState, useEffect, createContext, useContext } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { setNetworkState, getNetworkState } from '../services/firebase';

// Create Offline Sync Context
const OfflineSyncContext = createContext({});

// Offline Sync Provider Component
export const OfflineSyncProvider = ({ children }) => {
  const [isOnline, setIsOnline] = useState(true);
  const [pendingOperations, setPendingOperations] = useState([]);
  const [syncInProgress, setSyncInProgress] = useState(false);

  // Monitor network connectivity
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const connected = state.isConnected && state.isInternetReachable;
      setIsOnline(connected);
      setNetworkState(connected);
      
      // Trigger sync when coming back online
      if (connected && !getNetworkState()) {
        syncPendingOperations();
      }
    });

    // Load pending operations from storage
    loadPendingOperations();

    return unsubscribe;
  }, []);

  // Load pending operations from AsyncStorage
  const loadPendingOperations = async () => {
    try {
      const stored = await AsyncStorage.getItem('pendingOperations');
      if (stored) {
        const operations = JSON.parse(stored);
        setPendingOperations(operations);
        
        // If online, try to sync immediately
        if (isOnline) {
          syncPendingOperations(operations);
        }
      }
    } catch (error) {
      console.error('Error loading pending operations:', error);
    }
  };

  // Save pending operations to AsyncStorage
  const savePendingOperations = async (operations) => {
    try {
      await AsyncStorage.setItem('pendingOperations', JSON.stringify(operations));
    } catch (error) {
      console.error('Error saving pending operations:', error);
    }
  };

  // Add operation to pending queue
  const addPendingOperation = async (operation) => {
    const newOperations = [...pendingOperations, {
      ...operation,
      id: Date.now().toString(),
      timestamp: new Date().toISOString()
    }];
    
    setPendingOperations(newOperations);
    await savePendingOperations(newOperations);
    
    // Try to sync if online
    if (isOnline) {
      syncPendingOperations(newOperations);
    }
  };

  // Sync pending operations when online
  const syncPendingOperations = async (operations = pendingOperations) => {
    if (!isOnline || syncInProgress || operations.length === 0) {
      return;
    }

    setSyncInProgress(true);
    const successfulOperations = [];
    const failedOperations = [];

    for (const operation of operations) {
      try {
        await executeOperation(operation);
        successfulOperations.push(operation);
      } catch (error) {
        console.error('Failed to sync operation:', operation, error);
        failedOperations.push(operation);
      }
    }

    // Remove successful operations from pending queue
    const remainingOperations = operations.filter(op => 
      !successfulOperations.some(success => success.id === op.id)
    );

    setPendingOperations(remainingOperations);
    await savePendingOperations(remainingOperations);
    setSyncInProgress(false);

    console.log(`Synced ${successfulOperations.length} operations, ${failedOperations.length} failed`);
  };

  // Execute a single operation
  const executeOperation = async (operation) => {
    const { type, data, noteId } = operation;
    
    // Import Firebase functions dynamically to avoid circular dependencies
    const { createNote, updateNote, deleteNote } = await import('./useNotes');
    
    switch (type) {
      case 'CREATE_NOTE':
        await createNote(data);
        break;
      case 'UPDATE_NOTE':
        await updateNote(noteId, data);
        break;
      case 'DELETE_NOTE':
        await deleteNote(noteId);
        break;
      default:
        throw new Error(`Unknown operation type: ${type}`);
    }
  };

  // Cache note data for offline access
  const cacheNoteData = async (notes) => {
    try {
      await AsyncStorage.setItem('cachedNotes', JSON.stringify(notes));
    } catch (error) {
      console.error('Error caching notes:', error);
    }
  };

  // Load cached note data
  const loadCachedNotes = async () => {
    try {
      const cached = await AsyncStorage.getItem('cachedNotes');
      return cached ? JSON.parse(cached) : [];
    } catch (error) {
      console.error('Error loading cached notes:', error);
      return [];
    }
  };

  // Clear all cached data
  const clearCache = async () => {
    try {
      await AsyncStorage.multiRemove(['cachedNotes', 'pendingOperations']);
      setPendingOperations([]);
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  };

  // Get sync status
  const getSyncStatus = () => {
    return {
      isOnline,
      pendingCount: pendingOperations.length,
      syncInProgress,
      lastSyncAttempt: pendingOperations.length > 0 ? 
        Math.max(...pendingOperations.map(op => new Date(op.timestamp).getTime())) : null
    };
  };

  const value = {
    isOnline,
    pendingOperations,
    syncInProgress,
    addPendingOperation,
    syncPendingOperations,
    cacheNoteData,
    loadCachedNotes,
    clearCache,
    getSyncStatus
  };

  return (
    <OfflineSyncContext.Provider value={value}>
      {children}
    </OfflineSyncContext.Provider>
  );
};

// Custom hook to use offline sync context
export const useOfflineSync = () => {
  const context = useContext(OfflineSyncContext);
  if (!context) {
    throw new Error('useOfflineSync must be used within an OfflineSyncProvider');
  }
  return context;
};

// Helper function to create offline-aware operations
export const createOfflineOperation = (type, data, noteId = null) => {
  return {
    type,
    data,
    noteId,
    timestamp: new Date().toISOString()
  };
};
