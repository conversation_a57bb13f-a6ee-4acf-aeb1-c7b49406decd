import { Alert } from 'react-native';

/**
 * Comprehensive error handling utility for the Firebase Notes App
 */

// Error types
export const ERROR_TYPES = {
  NETWORK: 'NETWORK',
  AUTH: 'AUTH',
  FIRESTORE: 'FIRESTORE',
  VALIDATION: 'VALIDATION',
  PERMISSION: 'PERMISSION',
  UNKNOWN: 'UNKNOWN'
};

// Error severity levels
export const ERROR_SEVERITY = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL'
};

/**
 * Main error handler function
 */
export const handleError = (error, context = '', showAlert = true) => {
  const errorInfo = analyzeError(error);
  
  // Log error for debugging
  console.error(`[${context}] ${errorInfo.type}:`, {
    message: errorInfo.message,
    code: errorInfo.code,
    severity: errorInfo.severity,
    originalError: error
  });

  // Show user-friendly alert if requested
  if (showAlert) {
    showErrorAlert(errorInfo);
  }

  // Return error info for further handling
  return errorInfo;
};

/**
 * Analyze error and categorize it
 */
const analyzeError = (error) => {
  // Network errors
  if (error.code === 'unavailable' || 
      error.message?.includes('network') ||
      error.message?.includes('offline')) {
    return {
      type: ERROR_TYPES.NETWORK,
      severity: ERROR_SEVERITY.MEDIUM,
      code: error.code || 'network-error',
      message: 'Network connection issue. Please check your internet connection.',
      userMessage: 'You appear to be offline. Your changes will sync when you reconnect.',
      retryable: true
    };
  }

  // Authentication errors
  if (error.code?.startsWith('auth/')) {
    return {
      type: ERROR_TYPES.AUTH,
      severity: ERROR_SEVERITY.HIGH,
      code: error.code,
      message: getAuthErrorMessage(error.code),
      userMessage: getAuthErrorMessage(error.code),
      retryable: false
    };
  }

  // Firestore errors
  if (error.code?.startsWith('firestore/') || 
      error.code === 'permission-denied' ||
      error.code === 'not-found') {
    return {
      type: ERROR_TYPES.FIRESTORE,
      severity: ERROR_SEVERITY.MEDIUM,
      code: error.code,
      message: getFirestoreErrorMessage(error.code),
      userMessage: getFirestoreErrorMessage(error.code),
      retryable: true
    };
  }

  // Validation errors
  if (error.name === 'ValidationError' || error.type === 'validation') {
    return {
      type: ERROR_TYPES.VALIDATION,
      severity: ERROR_SEVERITY.LOW,
      code: 'validation-error',
      message: error.message || 'Invalid data provided',
      userMessage: error.message || 'Please check your input and try again.',
      retryable: false
    };
  }

  // Permission errors
  if (error.code === 'permission-denied' || 
      error.message?.includes('permission')) {
    return {
      type: ERROR_TYPES.PERMISSION,
      severity: ERROR_SEVERITY.HIGH,
      code: 'permission-denied',
      message: 'Permission denied',
      userMessage: 'You don\'t have permission to perform this action.',
      retryable: false
    };
  }

  // Unknown errors
  return {
    type: ERROR_TYPES.UNKNOWN,
    severity: ERROR_SEVERITY.MEDIUM,
    code: error.code || 'unknown-error',
    message: error.message || 'An unexpected error occurred',
    userMessage: 'Something went wrong. Please try again.',
    retryable: true
  };
};

/**
 * Show user-friendly error alert
 */
const showErrorAlert = (errorInfo) => {
  const { severity, userMessage, retryable } = errorInfo;
  
  let title = 'Error';
  if (severity === ERROR_SEVERITY.CRITICAL) {
    title = 'Critical Error';
  } else if (severity === ERROR_SEVERITY.HIGH) {
    title = 'Important';
  } else if (severity === ERROR_SEVERITY.LOW) {
    title = 'Notice';
  }

  const buttons = [{ text: 'OK' }];
  
  if (retryable) {
    buttons.unshift({ text: 'Retry', style: 'default' });
  }

  Alert.alert(title, userMessage, buttons);
};

/**
 * Get user-friendly authentication error messages
 */
const getAuthErrorMessage = (errorCode) => {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email address.';
    case 'auth/wrong-password':
      return 'Incorrect password. Please try again.';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists.';
    case 'auth/weak-password':
      return 'Password should be at least 6 characters long.';
    case 'auth/invalid-email':
      return 'Please enter a valid email address.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your connection.';
    case 'auth/user-disabled':
      return 'This account has been disabled.';
    case 'auth/operation-not-allowed':
      return 'This operation is not allowed.';
    case 'auth/invalid-credential':
      return 'Invalid credentials provided.';
    default:
      return 'Authentication failed. Please try again.';
  }
};

/**
 * Get user-friendly Firestore error messages
 */
const getFirestoreErrorMessage = (errorCode) => {
  switch (errorCode) {
    case 'permission-denied':
      return 'You don\'t have permission to access this data.';
    case 'not-found':
      return 'The requested data was not found.';
    case 'already-exists':
      return 'This data already exists.';
    case 'resource-exhausted':
      return 'Service is temporarily unavailable. Please try again later.';
    case 'failed-precondition':
      return 'Operation failed due to a conflict. Please refresh and try again.';
    case 'aborted':
      return 'Operation was aborted. Please try again.';
    case 'out-of-range':
      return 'Invalid data range provided.';
    case 'unimplemented':
      return 'This feature is not yet available.';
    case 'internal':
      return 'Internal server error. Please try again later.';
    case 'unavailable':
      return 'Service is temporarily unavailable. Please try again later.';
    case 'data-loss':
      return 'Data corruption detected. Please contact support.';
    default:
      return 'Database operation failed. Please try again.';
  }
};

/**
 * Validation utilities
 */
export const validateNoteData = (noteData) => {
  const errors = [];

  // Title validation
  if (noteData.title && noteData.title.length > 200) {
    errors.push('Title must be less than 200 characters');
  }

  // Content validation
  if (noteData.content && noteData.content.length > 50000) {
    errors.push('Content must be less than 50,000 characters');
  }

  // Category validation
  if (noteData.category && noteData.category.length > 50) {
    errors.push('Category must be less than 50 characters');
  }

  // Tags validation
  if (noteData.tags) {
    if (!Array.isArray(noteData.tags)) {
      errors.push('Tags must be an array');
    } else {
      if (noteData.tags.length > 20) {
        errors.push('Maximum 20 tags allowed');
      }
      
      noteData.tags.forEach((tag, index) => {
        if (typeof tag !== 'string') {
          errors.push(`Tag ${index + 1} must be a string`);
        } else if (tag.length > 30) {
          errors.push(`Tag "${tag}" must be less than 30 characters`);
        }
      });
    }
  }

  if (errors.length > 0) {
    throw {
      name: 'ValidationError',
      type: 'validation',
      message: errors.join('; ')
    };
  }

  return true;
};

/**
 * Email validation
 */
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw {
      name: 'ValidationError',
      type: 'validation',
      message: 'Please enter a valid email address'
    };
  }
  return true;
};

/**
 * Password validation
 */
export const validatePassword = (password) => {
  if (password.length < 6) {
    throw {
      name: 'ValidationError',
      type: 'validation',
      message: 'Password must be at least 6 characters long'
    };
  }
  return true;
};

/**
 * Retry mechanism for failed operations
 */
export const retryOperation = async (operation, maxRetries = 3, delay = 1000) => {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      const errorInfo = analyzeError(error);
      
      // Don't retry non-retryable errors
      if (!errorInfo.retryable) {
        throw error;
      }
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError;
};
