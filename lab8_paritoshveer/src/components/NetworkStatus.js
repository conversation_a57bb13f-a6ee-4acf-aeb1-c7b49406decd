import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useOfflineSync } from '../hooks/useOfflineSync';

const NetworkStatus = () => {
  const { isOnline, pendingOperations, syncInProgress } = useOfflineSync();

  if (isOnline && pendingOperations.length === 0) {
    return null; // Don't show anything when online and no pending operations
  }

  return (
    <View style={[styles.container, !isOnline && styles.offline]}>
      {!isOnline ? (
        <Text style={styles.text}>
          📱 Offline Mode - Changes will sync when you reconnect
        </Text>
      ) : syncInProgress ? (
        <Text style={styles.text}>
          🔄 Syncing {pendingOperations.length} changes...
        </Text>
      ) : pendingOperations.length > 0 ? (
        <Text style={styles.text}>
          ⏳ {pendingOperations.length} changes pending sync
        </Text>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#4CAF50',
    padding: 8,
    alignItems: 'center',
  },
  offline: {
    backgroundColor: '#FF9800',
  },
  text: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default NetworkStatus;
