const functions = require('firebase-functions');
const admin = require('firebase-admin');

// Initialize Firebase Admin
admin.initializeApp();
const db = admin.firestore();

/**
 * Cloud Function: Search Index Creation
 * Triggered when a note is created or updated
 * Creates search-friendly index for full-text search
 */
exports.createSearchIndex = functions.firestore
  .document('notes/{noteId}')
  .onWrite(async (change, context) => {
    try {
      const noteId = context.params.noteId;
      
      // If document was deleted, remove from search index
      if (!change.after.exists) {
        await db.collection('searchIndex').doc(noteId).delete();
        console.log(`Search index deleted for note: ${noteId}`);
        return null;
      }

      const noteData = change.after.data();
      
      // Create search index document
      const searchData = {
        noteId: noteId,
        userId: noteData.userId,
        title: noteData.title || '',
        content: noteData.content || '',
        category: noteData.category || 'general',
        tags: noteData.tags || [],
        searchTerms: createSearchTerms(noteData),
        updatedAt: noteData.updatedAt || admin.firestore.FieldValue.serverTimestamp(),
        isArchived: noteData.isArchived || false
      };

      await db.collection('searchIndex').doc(noteId).set(searchData);
      console.log(`Search index created/updated for note: ${noteId}`);
      
      return null;
    } catch (error) {
      console.error('Error creating search index:', error);
      throw new functions.https.HttpsError('internal', 'Failed to create search index');
    }
  });

/**
 * Cloud Function: Advanced Search
 * HTTP callable function for complex search queries
 */
exports.searchNotes = functions.https.onCall(async (data, context) => {
  try {
    // Verify user authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { query, category, tags, limit = 20 } = data;
    const userId = context.auth.uid;

    if (!query || query.trim().length === 0) {
      throw new functions.https.HttpsError('invalid-argument', 'Search query is required');
    }

    let searchQuery = db.collection('searchIndex')
      .where('userId', '==', userId)
      .where('isArchived', '==', false);

    // Add category filter if specified
    if (category && category !== 'all') {
      searchQuery = searchQuery.where('category', '==', category);
    }

    // Execute query
    const snapshot = await searchQuery.limit(limit).get();
    
    // Filter results by search terms
    const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);
    const results = [];

    snapshot.forEach(doc => {
      const data = doc.data();
      const score = calculateSearchScore(data, searchTerms, tags);
      
      if (score > 0) {
        results.push({
          noteId: data.noteId,
          title: data.title,
          content: data.content,
          category: data.category,
          tags: data.tags,
          updatedAt: data.updatedAt,
          score: score
        });
      }
    });

    // Sort by relevance score
    results.sort((a, b) => b.score - a.score);

    return { results: results.slice(0, limit) };
  } catch (error) {
    console.error('Error searching notes:', error);
    throw new functions.https.HttpsError('internal', 'Search failed');
  }
});

/**
 * Cloud Function: Cleanup Old Notes
 * Scheduled function to clean up old archived notes
 * Runs daily at midnight
 */
exports.cleanupOldNotes = functions.pubsub
  .schedule('0 0 * * *')
  .timeZone('America/Toronto')
  .onRun(async (context) => {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Find archived notes older than 30 days
      const oldNotesQuery = db.collection('notes')
        .where('isArchived', '==', true)
        .where('updatedAt', '<', thirtyDaysAgo);

      const snapshot = await oldNotesQuery.get();
      
      if (snapshot.empty) {
        console.log('No old archived notes to clean up');
        return null;
      }

      const batch = db.batch();
      let deleteCount = 0;

      snapshot.forEach(doc => {
        // Delete note
        batch.delete(doc.ref);
        
        // Delete search index
        batch.delete(db.collection('searchIndex').doc(doc.id));
        
        deleteCount++;
      });

      await batch.commit();
      console.log(`Cleaned up ${deleteCount} old archived notes`);
      
      return null;
    } catch (error) {
      console.error('Error cleaning up old notes:', error);
      throw error;
    }
  });

/**
 * Cloud Function: User Data Cleanup
 * Triggered when a user is deleted
 * Cleans up all user-related data
 */
exports.cleanupUserData = functions.auth.user().onDelete(async (user) => {
  try {
    const userId = user.uid;
    const batch = db.batch();

    // Delete user's notes
    const notesSnapshot = await db.collection('notes')
      .where('userId', '==', userId)
      .get();

    notesSnapshot.forEach(doc => {
      batch.delete(doc.ref);
    });

    // Delete user's search index entries
    const searchSnapshot = await db.collection('searchIndex')
      .where('userId', '==', userId)
      .get();

    searchSnapshot.forEach(doc => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Cleaned up data for deleted user: ${userId}`);
    
    return null;
  } catch (error) {
    console.error('Error cleaning up user data:', error);
    throw error;
  }
});

/**
 * Helper function to create search terms from note data
 */
function createSearchTerms(noteData) {
  const terms = [];
  
  // Add title words
  if (noteData.title) {
    terms.push(...noteData.title.toLowerCase().split(/\s+/));
  }
  
  // Add content words
  if (noteData.content) {
    terms.push(...noteData.content.toLowerCase().split(/\s+/));
  }
  
  // Add tags
  if (noteData.tags) {
    terms.push(...noteData.tags.map(tag => tag.toLowerCase()));
  }
  
  // Add category
  if (noteData.category) {
    terms.push(noteData.category.toLowerCase());
  }
  
  // Remove duplicates and empty strings
  return [...new Set(terms.filter(term => term.length > 0))];
}

/**
 * Helper function to calculate search relevance score
 */
function calculateSearchScore(data, searchTerms, filterTags = []) {
  let score = 0;
  
  // Check title matches (higher weight)
  searchTerms.forEach(term => {
    if (data.title && data.title.toLowerCase().includes(term)) {
      score += 3;
    }
  });
  
  // Check content matches
  searchTerms.forEach(term => {
    if (data.content && data.content.toLowerCase().includes(term)) {
      score += 1;
    }
  });
  
  // Check tag matches (medium weight)
  searchTerms.forEach(term => {
    if (data.tags && data.tags.some(tag => tag.toLowerCase().includes(term))) {
      score += 2;
    }
  });
  
  // Apply tag filters
  if (filterTags && filterTags.length > 0) {
    const hasMatchingTag = filterTags.some(filterTag => 
      data.tags && data.tags.some(tag => 
        tag.toLowerCase().includes(filterTag.toLowerCase())
      )
    );
    if (!hasMatchingTag) {
      score = 0; // Exclude if doesn't match tag filter
    }
  }
  
  return score;
}
