{"version": 3, "file": "AssetHooks.js", "sourceRoot": "", "sources": ["../src/AssetHooks.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAE5C,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,cAAc;AACd;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,UAAU,SAAS,CAAC,SAA4B;IACpD,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,EAAW,CAAC;IAChD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,EAAS,CAAC;IAE5C,SAAS,CAAC,GAAG,EAAE;QACb,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7D,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzB,CAAC", "sourcesContent": ["import { useEffect, useState } from 'react';\n\nimport { Asset } from './Asset';\n\n// @needsAudit\n/**\n * Downloads and stores one or more assets locally.\n * After the assets are loaded, this hook returns a list of asset instances.\n * If something went wrong when loading the assets, an error is returned.\n *\n * > Note, the assets are not \"reloaded\" when you dynamically change the asset list.\n *\n * @return Returns an array containing:\n * - on the first position, a list of all loaded assets. If they aren't loaded yet, this value is\n *   `undefined`.\n * - on the second position, an error which encountered when loading the assets. If there was no\n *   error, this value is `undefined`.\n *\n * @example\n * ```tsx\n * const [assets, error] = useAssets([require('path/to/asset.jpg'), require('path/to/other.png')]);\n *\n * return assets ? <Image source={assets[0]} /> : null;\n * ```\n */\nexport function useAssets(moduleIds: number | number[]): [Asset[] | undefined, Error | undefined] {\n  const [assets, setAssets] = useState<Asset[]>();\n  const [error, setError] = useState<Error>();\n\n  useEffect(() => {\n    Asset.loadAsync(moduleIds).then(setAssets).catch(setError);\n  }, []);\n\n  return [assets, error];\n}\n"]}