const e=(e,t)=>{if(!e)throw new Error(t)},t=!("undefined"==typeof process||!process.version),i="undefined"!=typeof process&&void 0!==process.env.PUPPETEER_DEFERRED_PROMISE_DEBUG_TIMEOUT?Number(process.env.PUPPETEER_DEFERRED_PROMISE_DEBUG_TIMEOUT):-1;let s=null;const r=e=>t?async(...t)=>{o&&n.push(e+t),(await async function(){return s||(s=(await import("debug")).default),s}())(e)(t)}:(...t)=>{const i=globalThis.__PUPPETEER_DEBUG;i&&("*"===i||(i.endsWith("*")?e.startsWith(i):e===i))&&console.log(`${e}:`,...t)};let n=[],o=!1;class a{constructor(){var e;this.eventsMap=new Map,this.emitter={all:e=(e=this.eventsMap)||new Map,on:function(t,i){var s=e.get(t);s?s.push(i):e.set(t,[i])},off:function(t,i){var s=e.get(t);s&&(i?s.splice(s.indexOf(i)>>>0,1):e.set(t,[]))},emit:function(t,i){var s=e.get(t);s&&s.slice().map((function(e){e(i)})),(s=e.get("*"))&&s.slice().map((function(e){e(t,i)}))}}}on(e,t){return this.emitter.on(e,t),this}off(e,t){return this.emitter.off(e,t),this}removeListener(e,t){return this.off(e,t),this}addListener(e,t){return this.on(e,t),this}emit(e,t){return this.emitter.emit(e,t),this.eventListenersCount(e)>0}once(e,t){const i=s=>{t(s),this.off(e,i)};return this.on(e,i)}listenerCount(e){return this.eventListenersCount(e)}removeAllListeners(e){return e?this.eventsMap.delete(e):this.eventsMap.clear(),this}eventListenersCount(e){var t;return(null===(t=this.eventsMap.get(e))||void 0===t?void 0:t.length)||0}}var c,d,l=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},h=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class f extends Error{constructor(e){super(e),this.name=this.constructor.name,Error.captureStackTrace(this,this.constructor)}}class u extends f{}class w extends f{constructor(){super(...arguments),c.set(this,void 0),d.set(this,"")}set code(e){l(this,c,e,"f")}get code(){return h(this,c,"f")}set originalMessage(e){l(this,d,e,"f")}get originalMessage(){return h(this,d,"f")}}c=new WeakMap,d=new WeakMap,Object.freeze({TimeoutError:u,ProtocolError:w});var m,p,y,g,v,k,b,E,C,M,P,_,T,I,x=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},N=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};const S=r("puppeteer:protocol:SEND ►"),F=r("puppeteer:protocol:RECV ◀"),W={Disconnected:Symbol("Connection.Disconnected")};class R extends a{constructor(e,t,i=0){super(),m.add(this),p.set(this,void 0),y.set(this,void 0),g.set(this,void 0),v.set(this,0),k.set(this,new Map),b.set(this,!1),E.set(this,new Map),C.set(this,new Set),x(this,p,e,"f"),x(this,g,i,"f"),x(this,y,t,"f"),N(this,y,"f").onmessage=this.onMessage.bind(this),N(this,y,"f").onclose=N(this,m,"m",M).bind(this)}static fromSession(e){return e.connection()}get _closed(){return N(this,b,"f")}get _sessions(){return N(this,k,"f")}session(e){return N(this,k,"f").get(e)||null}url(){return N(this,p,"f")}send(e,...t){const i=t.length?t[0]:void 0,s=this._rawSend({method:e,params:i});return new Promise(((t,i)=>{N(this,E,"f").set(s,{resolve:t,reject:i,error:new w,method:e})}))}_rawSend(e){var t;const i=x(this,v,(t=N(this,v,"f"),++t),"f"),s=JSON.stringify(Object.assign({},e,{id:i}));return S(s),N(this,y,"f").send(s),i}async onMessage(e){N(this,g,"f")&&await new Promise((e=>setTimeout(e,N(this,g,"f")))),F(e);const t=JSON.parse(e);if("Target.attachedToTarget"===t.method){const e=t.params.sessionId,i=new j(this,t.params.targetInfo.type,e);N(this,k,"f").set(e,i),this.emit("sessionattached",i);const s=N(this,k,"f").get(t.sessionId);s&&s.emit("sessionattached",i)}else if("Target.detachedFromTarget"===t.method){const e=N(this,k,"f").get(t.params.sessionId);if(e){e._onClosed(),N(this,k,"f").delete(t.params.sessionId),this.emit("sessiondetached",e);const i=N(this,k,"f").get(t.sessionId);i&&i.emit("sessiondetached",e)}}if(t.sessionId){const e=N(this,k,"f").get(t.sessionId);e&&e._onMessage(t)}else if(t.id){const e=N(this,E,"f").get(t.id);e&&(N(this,E,"f").delete(t.id),t.error?e.reject(A(e.error,e.method,t)):e.resolve(t.result))}else this.emit(t.method,t.params)}dispose(){N(this,m,"m",M).call(this),N(this,y,"f").close()}isAutoAttached(e){return!N(this,C,"f").has(e)}async _createSession(e,t=!0){t||N(this,C,"f").add(e.targetId);const{sessionId:i}=await this.send("Target.attachToTarget",{targetId:e.targetId,flatten:!0});N(this,C,"f").delete(e.targetId);const s=N(this,k,"f").get(i);if(!s)throw new Error("CDPSession creation failed.");return s}async createSession(e){return await this._createSession(e,!1)}}p=new WeakMap,y=new WeakMap,g=new WeakMap,v=new WeakMap,k=new WeakMap,b=new WeakMap,E=new WeakMap,C=new WeakMap,m=new WeakSet,M=function(){if(!N(this,b,"f")){x(this,b,!0,"f"),N(this,y,"f").onmessage=void 0,N(this,y,"f").onclose=void 0;for(const e of N(this,E,"f").values())e.reject(O(e.error,`Protocol error (${e.method}): Target closed.`));N(this,E,"f").clear();for(const e of N(this,k,"f").values())e._onClosed();N(this,k,"f").clear(),this.emit(W.Disconnected)}};const D={Disconnected:Symbol("CDPSession.Disconnected")};class q extends a{constructor(){super()}connection(){throw new Error("Not implemented")}send(){throw new Error("Not implemented")}async detach(){throw new Error("Not implemented")}id(){throw new Error("Not implemented")}}class j extends q{constructor(e,t,i){super(),P.set(this,void 0),_.set(this,void 0),T.set(this,new Map),I.set(this,void 0),x(this,I,e,"f"),x(this,_,t,"f"),x(this,P,i,"f")}connection(){return N(this,I,"f")}send(e,...t){if(!N(this,I,"f"))return Promise.reject(new Error(`Protocol error (${e}): Session closed. Most likely the ${N(this,_,"f")} has been closed.`));const i=t.length?t[0]:void 0,s=N(this,I,"f")._rawSend({sessionId:N(this,P,"f"),method:e,params:i});return new Promise(((t,i)=>{N(this,T,"f").set(s,{resolve:t,reject:i,error:new w,method:e})}))}_onMessage(t){const i=t.id?N(this,T,"f").get(t.id):void 0;t.id&&i?(N(this,T,"f").delete(t.id),t.error?i.reject(A(i.error,i.method,t)):i.resolve(t.result)):(e(!t.id),this.emit(t.method,t.params))}async detach(){if(!N(this,I,"f"))throw new Error(`Session already detached. Most likely the ${N(this,_,"f")} has been closed.`);await N(this,I,"f").send("Target.detachFromTarget",{sessionId:N(this,P,"f")})}_onClosed(){for(const e of N(this,T,"f").values())e.reject(O(e.error,`Protocol error (${e.method}): Target closed.`));N(this,T,"f").clear(),x(this,I,void 0,"f"),this.emit(D.Disconnected)}id(){return N(this,P,"f")}}function A(e,t,i){let s=`Protocol error (${t}): ${i.error.message}`;return"data"in i.error&&(s+=` ${i.error.data}`),O(e,s,i.error.message)}function O(e,t,i){return e.message=t,e.originalMessage=null!=i?i:e.originalMessage,e}function K(e){return e.message.includes("Target closed")||e.message.includes("Session closed")}function L(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}P=new WeakMap,_=new WeakMap,T=new WeakMap,I=new WeakMap;const $=Symbol("mainWorld"),B=Symbol("puppeteerWorld");async function H(e,t,i,s){const{nodes:r}=await e.send("Accessibility.queryAXTree",{objectId:t.remoteObject().objectId,accessibleName:i,role:s});return r.filter((e=>!e.role||"StaticText"!==e.role.value))}const U=e=>e.replace(/ +/g," ").trim(),V=new Set(["name","role"]),z=/\[\s*(?<attribute>\w+)\s*=\s*(?<quote>"|')(?<value>\\.|.*?(?=\k<quote>))\k<quote>\s*\]/g;function G(t){const i={},s=t.replace(z,((t,s,r,n)=>(s=s.trim(),e(function(e){return V.has(e)}(s),`Unknown aria attribute "${s}" in selector`),i[s]=U(n),"")));return s&&!i.name&&(i.name=U(s)),i}const Q=async(e,t)=>{const{name:i,role:s}=G(t),r=await H(e.client,e,i,s);return r[0]&&r[0].backendDOMNodeId?r[0].backendDOMNodeId:null},J={queryOne:async(e,t)=>{const i=await Q(e,t);return i?await e.frame.worlds[$].adoptBackendNode(i):null},waitFor:async(e,t,i)=>{let s,r;"isOOPFrame"in e?s=e:(s=e.frame,r=await s.worlds[B].adoptHandle(e));const n=await s.worlds[B]._waitForSelectorInPage(((e,t)=>globalThis.ariaQuerySelector(t)),r,t,i,new Map([["ariaQuerySelector",async e=>{const t=await Q(r||await s.worlds[B].document(),e);return t?await s.worlds[B].adoptBackendNode(t):null}]]));r&&await r.dispose();const o=null==n?void 0:n.asElement();return o?o.frame.worlds[$].transferHandle(o):(await(null==n?void 0:n.dispose()),null)},queryAll:async(e,t)=>{const i=e.executionContext(),{name:s,role:r}=G(t),n=await H(i._client,e,s,r),o=i._world;return Promise.all(n.map((e=>o.adoptBackendNode(e.backendDOMNodeId))))}};class X{constructor(){}get disposed(){throw new Error("Not implemented")}executionContext(){throw new Error("Not implemented")}get client(){throw new Error("Not implemented")}async evaluate(){throw new Error("Not implemented")}async evaluateHandle(){throw new Error("Not implemented")}async getProperty(){throw new Error("Not implemented")}async getProperties(){throw new Error("Not implemented")}async jsonValue(){throw new Error("Not implemented")}asElement(){throw new Error("Not implemented")}async dispose(){throw new Error("Not implemented")}toString(){throw new Error("Not implemented")}remoteObject(){throw new Error("Not implemented")}}class Y extends X{constructor(){super()}executionContext(){throw new Error("Not implemented")}get client(){throw new Error("Not implemented")}get frame(){throw new Error("Not implemented")}async $(){throw new Error("Not implemented")}async $$(){throw new Error("Not implemented")}async $eval(){throw new Error("Not implemented")}async $$eval(){throw new Error("Not implemented")}async $x(){throw new Error("Not implemented")}async waitForSelector(){throw new Error("Not implemented")}async waitForXPath(){throw new Error("Not implemented")}async toElement(){throw new Error("Not implemented")}asElement(){return this}async contentFrame(){throw new Error("Not implemented")}async clickablePoint(){throw new Error("Not implemented")}async hover(){throw new Error("Not implemented")}async click(){throw new Error("Not implemented")}async drag(){throw new Error("Not implemented")}async dragEnter(){throw new Error("Not implemented")}async dragOver(){throw new Error("Not implemented")}async drop(){throw new Error("Not implemented")}async dragAndDrop(){throw new Error("Not implemented")}async select(){throw new Error("Not implemented")}async uploadFile(){throw new Error("Not implemented")}async tap(){throw new Error("Not implemented")}async touchStart(){throw new Error("Not implemented")}async touchMove(){throw new Error("Not implemented")}async touchEnd(){throw new Error("Not implemented")}async focus(){throw new Error("Not implemented")}async type(){throw new Error("Not implemented")}async press(){throw new Error("Not implemented")}async boundingBox(){throw new Error("Not implemented")}async boxModel(){throw new Error("Not implemented")}async screenshot(){throw new Error("Not implemented")}async isIntersectingViewport(){throw new Error("Not implemented")}}function Z(e){let t,i,s=!1,r=!1;const n=new Promise(((e,s)=>{t=e,i=s})),o=e&&e.timeout>0?setTimeout((()=>{r=!0,i(new u(e.message))}),e.timeout):void 0;return Object.assign(n,{resolved:()=>s,finished:()=>s||r,resolve:e=>{o&&clearTimeout(o),s=!0,t(e)},reject:e=>{clearTimeout(o),r=!0,i(e)}})}var ee,te=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},ie=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class se{constructor(e){ee.set(this,void 0),te(this,ee,e,"f")}async get(e){return ie(this,ee,"f").call(this,e)}}ee=new WeakMap,se.create=e=>new se(e);var re,ne,oe,ae=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)},ce=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i};const de=/^[\040\t]*\/\/[@#] sourceURL=\s*(\S*?)\s*$/m;class le{constructor(e,t,i){re.add(this),ne.set(this,void 0),this._client=e,this._world=i,this._contextId=t.id,this._contextName=t.name}get puppeteerUtil(){return ae(this,ne,"f")||ce(this,ne,this.evaluateHandle('(() => {\n            const module = {};\n            "use strict";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === "object" || typeof from === "function") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);\n\n// src/injected/injected.ts\nvar injected_exports = {};\n__export(injected_exports, {\n  default: () => injected_default\n});\nmodule.exports = __toCommonJS(injected_exports);\n\n// src/common/Errors.ts\nvar CustomError = class extends Error {\n  constructor(message) {\n    super(message);\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n};\nvar TimeoutError = class extends CustomError {\n};\nvar ProtocolError = class extends CustomError {\n  #code;\n  #originalMessage = "";\n  set code(code) {\n    this.#code = code;\n  }\n  get code() {\n    return this.#code;\n  }\n  set originalMessage(originalMessage) {\n    this.#originalMessage = originalMessage;\n  }\n  get originalMessage() {\n    return this.#originalMessage;\n  }\n};\nvar errors = Object.freeze({\n  TimeoutError,\n  ProtocolError\n});\n\n// src/util/DeferredPromise.ts\nfunction createDeferredPromise(opts) {\n  let isResolved = false;\n  let isRejected = false;\n  let resolver;\n  let rejector;\n  const taskPromise = new Promise((resolve, reject) => {\n    resolver = resolve;\n    rejector = reject;\n  });\n  const timeoutId = opts && opts.timeout > 0 ? setTimeout(() => {\n    isRejected = true;\n    rejector(new TimeoutError(opts.message));\n  }, opts.timeout) : void 0;\n  return Object.assign(taskPromise, {\n    resolved: () => {\n      return isResolved;\n    },\n    finished: () => {\n      return isResolved || isRejected;\n    },\n    resolve: (value) => {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n      isResolved = true;\n      resolver(value);\n    },\n    reject: (err) => {\n      clearTimeout(timeoutId);\n      isRejected = true;\n      rejector(err);\n    }\n  });\n}\n\n// src/util/assert.ts\nvar assert = (value, message) => {\n  if (!value) {\n    throw new Error(message);\n  }\n};\n\n// src/injected/Poller.ts\nvar MutationPoller = class {\n  #fn;\n  #root;\n  #observer;\n  #promise;\n  constructor(fn, root) {\n    this.#fn = fn;\n    this.#root = root;\n  }\n  async start() {\n    const promise = this.#promise = createDeferredPromise();\n    const result = await this.#fn();\n    if (result) {\n      promise.resolve(result);\n      return;\n    }\n    this.#observer = new MutationObserver(async () => {\n      const result2 = await this.#fn();\n      if (!result2) {\n        return;\n      }\n      promise.resolve(result2);\n      await this.stop();\n    });\n    this.#observer.observe(this.#root, {\n      childList: true,\n      subtree: true,\n      attributes: true\n    });\n  }\n  async stop() {\n    assert(this.#promise, "Polling never started.");\n    if (!this.#promise.finished()) {\n      this.#promise.reject(new Error("Polling stopped"));\n    }\n    if (this.#observer) {\n      this.#observer.disconnect();\n      this.#observer = void 0;\n    }\n  }\n  result() {\n    assert(this.#promise, "Polling never started.");\n    return this.#promise;\n  }\n};\nvar RAFPoller = class {\n  #fn;\n  #promise;\n  constructor(fn) {\n    this.#fn = fn;\n  }\n  async start() {\n    const promise = this.#promise = createDeferredPromise();\n    const result = await this.#fn();\n    if (result) {\n      promise.resolve(result);\n      return;\n    }\n    const poll = async () => {\n      if (promise.finished()) {\n        return;\n      }\n      const result2 = await this.#fn();\n      if (!result2) {\n        window.requestAnimationFrame(poll);\n        return;\n      }\n      promise.resolve(result2);\n      await this.stop();\n    };\n    window.requestAnimationFrame(poll);\n  }\n  async stop() {\n    assert(this.#promise, "Polling never started.");\n    if (!this.#promise.finished()) {\n      this.#promise.reject(new Error("Polling stopped"));\n    }\n  }\n  result() {\n    assert(this.#promise, "Polling never started.");\n    return this.#promise;\n  }\n};\nvar IntervalPoller = class {\n  #fn;\n  #ms;\n  #interval;\n  #promise;\n  constructor(fn, ms) {\n    this.#fn = fn;\n    this.#ms = ms;\n  }\n  async start() {\n    const promise = this.#promise = createDeferredPromise();\n    const result = await this.#fn();\n    if (result) {\n      promise.resolve(result);\n      return;\n    }\n    this.#interval = setInterval(async () => {\n      const result2 = await this.#fn();\n      if (!result2) {\n        return;\n      }\n      promise.resolve(result2);\n      await this.stop();\n    }, this.#ms);\n  }\n  async stop() {\n    assert(this.#promise, "Polling never started.");\n    if (!this.#promise.finished()) {\n      this.#promise.reject(new Error("Polling stopped"));\n    }\n    if (this.#interval) {\n      clearInterval(this.#interval);\n      this.#interval = void 0;\n    }\n  }\n  result() {\n    assert(this.#promise, "Polling never started.");\n    return this.#promise;\n  }\n};\n\n// src/injected/TextContent.ts\nvar TRIVIAL_VALUE_INPUT_TYPES = /* @__PURE__ */ new Set(["checkbox", "image", "radio"]);\nvar isNonTrivialValueNode = (node) => {\n  if (node instanceof HTMLSelectElement) {\n    return true;\n  }\n  if (node instanceof HTMLTextAreaElement) {\n    return true;\n  }\n  if (node instanceof HTMLInputElement && !TRIVIAL_VALUE_INPUT_TYPES.has(node.type)) {\n    return true;\n  }\n  return false;\n};\nvar UNSUITABLE_NODE_NAMES = /* @__PURE__ */ new Set(["SCRIPT", "STYLE"]);\nvar isSuitableNodeForTextMatching = (node) => {\n  return !UNSUITABLE_NODE_NAMES.has(node.nodeName) && !document.head?.contains(node);\n};\nvar textContentCache = /* @__PURE__ */ new WeakMap();\nvar eraseFromCache = (node) => {\n  while (node) {\n    textContentCache.delete(node);\n    if (node instanceof ShadowRoot) {\n      node = node.host;\n    } else {\n      node = node.parentNode;\n    }\n  }\n};\nvar observedNodes = /* @__PURE__ */ new WeakSet();\nvar textChangeObserver = new MutationObserver((mutations) => {\n  for (const mutation of mutations) {\n    eraseFromCache(mutation.target);\n  }\n});\nvar createTextContent = (root) => {\n  let value = textContentCache.get(root);\n  if (value) {\n    return value;\n  }\n  value = { full: "", immediate: [] };\n  if (!isSuitableNodeForTextMatching(root)) {\n    return value;\n  }\n  let currentImmediate = "";\n  if (isNonTrivialValueNode(root)) {\n    value.full = root.value;\n    value.immediate.push(root.value);\n    root.addEventListener(\n      "input",\n      (event) => {\n        eraseFromCache(event.target);\n      },\n      { once: true, capture: true }\n    );\n  } else {\n    for (let child = root.firstChild; child; child = child.nextSibling) {\n      if (child.nodeType === Node.TEXT_NODE) {\n        value.full += child.nodeValue ?? "";\n        currentImmediate += child.nodeValue ?? "";\n        continue;\n      }\n      if (currentImmediate) {\n        value.immediate.push(currentImmediate);\n      }\n      currentImmediate = "";\n      if (child.nodeType === Node.ELEMENT_NODE) {\n        value.full += createTextContent(child).full;\n      }\n    }\n    if (currentImmediate) {\n      value.immediate.push(currentImmediate);\n    }\n    if (root instanceof Element && root.shadowRoot) {\n      value.full += createTextContent(root.shadowRoot).full;\n    }\n    if (!observedNodes.has(root)) {\n      textChangeObserver.observe(root, {\n        childList: true,\n        characterData: true\n      });\n      observedNodes.add(root);\n    }\n  }\n  textContentCache.set(root, value);\n  return value;\n};\n\n// src/injected/TextQuerySelector.ts\nvar TextQuerySelector_exports = {};\n__export(TextQuerySelector_exports, {\n  textQuerySelector: () => textQuerySelector,\n  textQuerySelectorAll: () => textQuerySelectorAll\n});\nvar textQuerySelector = (root, selector) => {\n  for (const node of root.childNodes) {\n    if (node instanceof Element && isSuitableNodeForTextMatching(node)) {\n      let matchedNode;\n      if (node.shadowRoot) {\n        matchedNode = textQuerySelector(node.shadowRoot, selector);\n      } else {\n        matchedNode = textQuerySelector(node, selector);\n      }\n      if (matchedNode) {\n        return matchedNode;\n      }\n    }\n  }\n  if (root instanceof Element) {\n    const textContent = createTextContent(root);\n    if (textContent.full.includes(selector)) {\n      return root;\n    }\n  }\n  return null;\n};\nvar textQuerySelectorAll = (root, selector) => {\n  let results = [];\n  for (const node of root.childNodes) {\n    if (node instanceof Element) {\n      let matchedNodes;\n      if (node.shadowRoot) {\n        matchedNodes = textQuerySelectorAll(node.shadowRoot, selector);\n      } else {\n        matchedNodes = textQuerySelectorAll(node, selector);\n      }\n      results = results.concat(matchedNodes);\n    }\n  }\n  if (results.length > 0) {\n    return results;\n  }\n  if (root instanceof Element) {\n    const textContent = createTextContent(root);\n    if (textContent.full.includes(selector)) {\n      return [root];\n    }\n  }\n  return [];\n};\n\n// src/injected/XPathQuerySelector.ts\nvar XPathQuerySelector_exports = {};\n__export(XPathQuerySelector_exports, {\n  xpathQuerySelector: () => xpathQuerySelector,\n  xpathQuerySelectorAll: () => xpathQuerySelectorAll\n});\nvar xpathQuerySelector = (root, selector) => {\n  const doc = root.ownerDocument || document;\n  const result = doc.evaluate(\n    selector,\n    root,\n    null,\n    XPathResult.FIRST_ORDERED_NODE_TYPE\n  );\n  return result.singleNodeValue;\n};\nvar xpathQuerySelectorAll = (root, selector) => {\n  const doc = root.ownerDocument || document;\n  const iterator = doc.evaluate(\n    selector,\n    root,\n    null,\n    XPathResult.ORDERED_NODE_ITERATOR_TYPE\n  );\n  const array = [];\n  let item;\n  while (item = iterator.iterateNext()) {\n    array.push(item);\n  }\n  return array;\n};\n\n// src/injected/PierceQuerySelector.ts\nvar PierceQuerySelector_exports = {};\n__export(PierceQuerySelector_exports, {\n  pierceQuerySelector: () => pierceQuerySelector,\n  pierceQuerySelectorAll: () => pierceQuerySelectorAll\n});\nvar pierceQuerySelector = (root, selector) => {\n  let found = null;\n  const search = (root2) => {\n    const iter = document.createTreeWalker(root2, NodeFilter.SHOW_ELEMENT);\n    do {\n      const currentNode = iter.currentNode;\n      if (currentNode.shadowRoot) {\n        search(currentNode.shadowRoot);\n      }\n      if (currentNode instanceof ShadowRoot) {\n        continue;\n      }\n      if (currentNode !== root2 && !found && currentNode.matches(selector)) {\n        found = currentNode;\n      }\n    } while (!found && iter.nextNode());\n  };\n  if (root instanceof Document) {\n    root = root.documentElement;\n  }\n  search(root);\n  return found;\n};\nvar pierceQuerySelectorAll = (element, selector) => {\n  const result = [];\n  const collect = (root) => {\n    const iter = document.createTreeWalker(root, NodeFilter.SHOW_ELEMENT);\n    do {\n      const currentNode = iter.currentNode;\n      if (currentNode.shadowRoot) {\n        collect(currentNode.shadowRoot);\n      }\n      if (currentNode instanceof ShadowRoot) {\n        continue;\n      }\n      if (currentNode !== root && currentNode.matches(selector)) {\n        result.push(currentNode);\n      }\n    } while (iter.nextNode());\n  };\n  if (element instanceof Document) {\n    element = element.documentElement;\n  }\n  collect(element);\n  return result;\n};\n\n// src/injected/util.ts\nvar util_exports = {};\n__export(util_exports, {\n  checkVisibility: () => checkVisibility,\n  createFunction: () => createFunction\n});\nvar createdFunctions = /* @__PURE__ */ new Map();\nvar createFunction = (functionValue) => {\n  let fn = createdFunctions.get(functionValue);\n  if (fn) {\n    return fn;\n  }\n  fn = new Function(`return ${functionValue}`)();\n  createdFunctions.set(functionValue, fn);\n  return fn;\n};\nvar HIDDEN_VISIBILITY_VALUES = ["hidden", "collapse"];\nvar checkVisibility = (node, visible) => {\n  if (!node) {\n    return visible === false;\n  }\n  if (visible === void 0) {\n    return node;\n  }\n  const element = node.nodeType === Node.TEXT_NODE ? node.parentElement : node;\n  const style = window.getComputedStyle(element);\n  const isVisible = style && !HIDDEN_VISIBILITY_VALUES.includes(style.visibility) && !isBoundingBoxEmpty(element);\n  return visible === isVisible ? node : false;\n};\nfunction isBoundingBoxEmpty(element) {\n  const rect = element.getBoundingClientRect();\n  return rect.width === 0 || rect.height === 0;\n}\n\n// src/injected/injected.ts\nvar PuppeteerUtil = Object.freeze({\n  ...util_exports,\n  ...TextQuerySelector_exports,\n  ...XPathQuerySelector_exports,\n  ...PierceQuerySelector_exports,\n  createDeferredPromise,\n  createTextContent,\n  IntervalPoller,\n  isSuitableNodeForTextMatching,\n  MutationPoller,\n  RAFPoller\n});\nvar injected_default = PuppeteerUtil;\n\n            return module.exports.default;\n          })()'),"f"),ae(this,ne,"f")}async evaluate(e,...t){return await ae(this,re,"m",oe).call(this,!0,e,...t)}async evaluateHandle(e,...t){return ae(this,re,"m",oe).call(this,!1,e,...t)}}ne=new WeakMap,re=new WeakSet,oe=async function(e,t,...i){const s="//# sourceURL=pptr://__puppeteer_evaluation_script__";if(Kr(t)){const i=this._contextId,r=t,n=de.test(r)?r:r+"\n"+s,{exceptionDetails:o,result:a}=await this._client.send("Runtime.evaluate",{expression:n,contextId:i,returnByValue:e,awaitPromise:!0,userGesture:!0}).catch(he);if(o)throw new Error("Evaluation failed: "+Dr(o));return e?qr(a):$r(this,a)}let r;try{r=this._client.send("Runtime.callFunctionOn",{functionDeclaration:Jr(t)+"\n"+s+"\n",executionContextId:this._contextId,arguments:await Promise.all(i.map(async function(e){if(e instanceof se&&(e=await e.get(this)),"bigint"==typeof e)return{unserializableValue:`${e.toString()}n`};if(Object.is(e,-0))return{unserializableValue:"-0"};if(Object.is(e,1/0))return{unserializableValue:"Infinity"};if(Object.is(e,-1/0))return{unserializableValue:"-Infinity"};if(Object.is(e,NaN))return{unserializableValue:"NaN"};const t=e&&e instanceof X?e:null;if(t){if(t.executionContext()!==this)throw new Error("JSHandles can be evaluated only in the context they were created!");if(t.disposed)throw new Error("JSHandle is disposed!");return t.remoteObject().unserializableValue?{unserializableValue:t.remoteObject().unserializableValue}:t.remoteObject().objectId?{objectId:t.remoteObject().objectId}:{value:t.remoteObject().value}}return{value:e}}.bind(this))),returnByValue:e,awaitPromise:!0,userGesture:!0})}catch(e){throw e instanceof TypeError&&e.message.startsWith("Converting circular structure to JSON")&&(e.message+=" Recursive objects are not allowed."),e}const{exceptionDetails:n,result:o}=await r.catch(he);if(n)throw new Error("Evaluation failed: "+Dr(n));return e?qr(o):$r(this,o)};const he=e=>{if(e.message.includes("Object reference chain is too long"))return{result:{type:"undefined"}};if(e.message.includes("Object couldn't be returned by value"))return{result:{type:"undefined"}};if(e.message.endsWith("Cannot find context with specified id")||e.message.endsWith("Inspected target navigated or closed"))throw new Error("Execution context was destroyed, most likely because of a navigation.");throw e};var fe,ue,we,me,pe,ye=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)},ge=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i};class ve{constructor(){fe.set(this,new Map),ue.set(this,new Map),we.set(this,new Map),me.set(this,void 0),pe.set(this,new Map)}getMainFrame(){return ye(this,me,"f")}getById(e){return ye(this,fe,"f").get(e)}waitForFrame(e){const t=this.getById(e);if(t)return Promise.resolve(t);const i=Z();return(ye(this,pe,"f").get(e)||new Set).add(i),i}frames(){return Array.from(ye(this,fe,"f").values())}addFrame(e){var t;ye(this,fe,"f").set(e._id,e),e._parentId?(ye(this,ue,"f").set(e._id,e._parentId),ye(this,we,"f").has(e._parentId)||ye(this,we,"f").set(e._parentId,new Set),ye(this,we,"f").get(e._parentId).add(e._id)):ge(this,me,e,"f"),null===(t=ye(this,pe,"f").get(e._id))||void 0===t||t.forEach((t=>t.resolve(e)))}removeFrame(e){var t;ye(this,fe,"f").delete(e._id),ye(this,ue,"f").delete(e._id),e._parentId?null===(t=ye(this,we,"f").get(e._parentId))||void 0===t||t.delete(e._id):ge(this,me,void 0,"f")}childFrames(e){const t=ye(this,we,"f").get(e);return t?Array.from(t).map((e=>this.getById(e))).filter((e=>void 0!==e)):[]}parentFrame(e){const t=ye(this,ue,"f").get(e);return t?this.getById(t):void 0}}fe=new WeakMap,ue=new WeakMap,we=new WeakMap,me=new WeakMap,pe=new WeakMap;var ke,be,Ee,Ce,Me,Pe,_e,Te,Ie,xe,Ne,Se,Fe,We,Re,De,qe,je,Ae,Oe,Ke,Le=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)},$e=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i};class Be{get client(){return Le(this,be,"f")}constructor(e,t,i,s,r,n){ke.add(this),this._failureText=null,this._response=null,this._fromMemoryCache=!1,be.set(this,void 0),Ee.set(this,void 0),Ce.set(this,void 0),Me.set(this,!1),Pe.set(this,void 0),_e.set(this,void 0),Te.set(this,void 0),Ie.set(this,void 0),xe.set(this,{}),Ne.set(this,void 0),Se.set(this,void 0),Fe.set(this,null),We.set(this,null),Re.set(this,{action:Ke.None}),De.set(this,void 0),qe.set(this,void 0),$e(this,be,e,"f"),this._requestId=r.requestId,$e(this,Ee,r.requestId===r.loaderId&&"Document"===r.type,"f"),this._interceptionId=i,$e(this,Ce,s,"f"),$e(this,Pe,r.request.url,"f"),$e(this,_e,(r.type||"other").toLowerCase(),"f"),$e(this,Te,r.request.method,"f"),$e(this,Ie,r.request.postData,"f"),$e(this,Ne,t,"f"),this._redirectChain=n,$e(this,Se,{},"f"),$e(this,De,[],"f"),$e(this,qe,r.initiator,"f");for(const[e,t]of Object.entries(r.request.headers))Le(this,xe,"f")[e.toLowerCase()]=t}url(){return Le(this,Pe,"f")}continueRequestOverrides(){return e(Le(this,Ce,"f"),"Request Interception is not enabled!"),Le(this,Se,"f")}responseForRequest(){return e(Le(this,Ce,"f"),"Request Interception is not enabled!"),Le(this,Fe,"f")}abortErrorReason(){return e(Le(this,Ce,"f"),"Request Interception is not enabled!"),Le(this,We,"f")}interceptResolutionState(){return Le(this,Ce,"f")?Le(this,Me,"f")?{action:Ke.AlreadyHandled}:{...Le(this,Re,"f")}:{action:Ke.Disabled}}isInterceptResolutionHandled(){return Le(this,Me,"f")}enqueueInterceptAction(e){Le(this,De,"f").push(e)}async finalizeInterceptions(){await Le(this,De,"f").reduce(((e,t)=>e.then(t)),Promise.resolve());const{action:e}=this.interceptResolutionState();switch(e){case"abort":return Le(this,ke,"m",Oe).call(this,Le(this,We,"f"));case"respond":if(null===Le(this,Fe,"f"))throw new Error("Response is missing for the interception");return Le(this,ke,"m",Ae).call(this,Le(this,Fe,"f"));case"continue":return Le(this,ke,"m",je).call(this,Le(this,Se,"f"))}}resourceType(){return Le(this,_e,"f")}method(){return Le(this,Te,"f")}postData(){return Le(this,Ie,"f")}headers(){return Le(this,xe,"f")}response(){return this._response}frame(){return Le(this,Ne,"f")}isNavigationRequest(){return Le(this,Ee,"f")}initiator(){return Le(this,qe,"f")}redirectChain(){return this._redirectChain.slice()}failure(){return this._failureText?{errorText:this._failureText}:null}async continue(t={},i){if(!Le(this,Pe,"f").startsWith("data:")){if(e(Le(this,Ce,"f"),"Request Interception is not enabled!"),e(!Le(this,Me,"f"),"Request is already handled!"),void 0===i)return Le(this,ke,"m",je).call(this,t);if($e(this,Se,t,"f"),void 0===Le(this,Re,"f").priority||i>Le(this,Re,"f").priority)$e(this,Re,{action:Ke.Continue,priority:i},"f");else if(i===Le(this,Re,"f").priority){if("abort"===Le(this,Re,"f").action||"respond"===Le(this,Re,"f").action)return;Le(this,Re,"f").action=Ke.Continue}}}async respond(t,i){if(!Le(this,Pe,"f").startsWith("data:")){if(e(Le(this,Ce,"f"),"Request Interception is not enabled!"),e(!Le(this,Me,"f"),"Request is already handled!"),void 0===i)return Le(this,ke,"m",Ae).call(this,t);if($e(this,Fe,t,"f"),void 0===Le(this,Re,"f").priority||i>Le(this,Re,"f").priority)$e(this,Re,{action:Ke.Respond,priority:i},"f");else if(i===Le(this,Re,"f").priority){if("abort"===Le(this,Re,"f").action)return;Le(this,Re,"f").action=Ke.Respond}}}async abort(t="failed",i){if(Le(this,Pe,"f").startsWith("data:"))return;const s=He[t];if(e(s,"Unknown error code: "+t),e(Le(this,Ce,"f"),"Request Interception is not enabled!"),e(!Le(this,Me,"f"),"Request is already handled!"),void 0===i)return Le(this,ke,"m",Oe).call(this,s);$e(this,We,s,"f"),(void 0===Le(this,Re,"f").priority||i>=Le(this,Re,"f").priority)&&$e(this,Re,{action:Ke.Abort,priority:i},"f")}}be=new WeakMap,Ee=new WeakMap,Ce=new WeakMap,Me=new WeakMap,Pe=new WeakMap,_e=new WeakMap,Te=new WeakMap,Ie=new WeakMap,xe=new WeakMap,Ne=new WeakMap,Se=new WeakMap,Fe=new WeakMap,We=new WeakMap,Re=new WeakMap,De=new WeakMap,qe=new WeakMap,ke=new WeakSet,je=async function(e={}){const{url:t,method:i,postData:s,headers:r}=e;$e(this,Me,!0,"f");const n=s?Buffer.from(s).toString("base64"):void 0;if(void 0===this._interceptionId)throw new Error("HTTPRequest is missing _interceptionId needed for Fetch.continueRequest");await Le(this,be,"f").send("Fetch.continueRequest",{requestId:this._interceptionId,url:t,method:i,postData:n,headers:r?Ue(r):void 0}).catch((e=>($e(this,Me,!1,"f"),Ve(e))))},Ae=async function(e){$e(this,Me,!0,"f");const t=e.body&&Kr(e.body)?Buffer.from(e.body):e.body||null,i={};if(e.headers)for(const t of Object.keys(e.headers)){const s=e.headers[t];i[t.toLowerCase()]=Array.isArray(s)?s.map((e=>String(e))):String(s)}e.contentType&&(i["content-type"]=e.contentType),t&&!("content-length"in i)&&(i["content-length"]=String(Buffer.byteLength(t)));const s=e.status||200;if(void 0===this._interceptionId)throw new Error("HTTPRequest is missing _interceptionId needed for Fetch.fulfillRequest");await Le(this,be,"f").send("Fetch.fulfillRequest",{requestId:this._interceptionId,responseCode:s,responsePhrase:ze[s],responseHeaders:Ue(i),body:t?t.toString("base64"):void 0}).catch((e=>($e(this,Me,!1,"f"),Ve(e))))},Oe=async function(e){if($e(this,Me,!0,"f"),void 0===this._interceptionId)throw new Error("HTTPRequest is missing _interceptionId needed for Fetch.failRequest");await Le(this,be,"f").send("Fetch.failRequest",{requestId:this._interceptionId,errorReason:e||"Failed"}).catch(Ve)},function(e){e.Abort="abort",e.Respond="respond",e.Continue="continue",e.Disabled="disabled",e.None="none",e.AlreadyHandled="already-handled"}(Ke||(Ke={}));const He={aborted:"Aborted",accessdenied:"AccessDenied",addressunreachable:"AddressUnreachable",blockedbyclient:"BlockedByClient",blockedbyresponse:"BlockedByResponse",connectionaborted:"ConnectionAborted",connectionclosed:"ConnectionClosed",connectionfailed:"ConnectionFailed",connectionrefused:"ConnectionRefused",connectionreset:"ConnectionReset",internetdisconnected:"InternetDisconnected",namenotresolved:"NameNotResolved",timedout:"TimedOut",failed:"Failed"};function Ue(e){const t=[];for(const i in e){const s=e[i];if(!Object.is(s,void 0)){const e=Array.isArray(s)?s:[s];t.push(...e.map((e=>({name:i,value:e+""}))))}}return t}async function Ve(e){if(["Invalid header"].includes(e.originalMessage))throw e;Rr(e)}const ze={100:"Continue",101:"Switching Protocols",102:"Processing",103:"Early Hints",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",306:"Switch Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Too Early",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",510:"Not Extended",511:"Network Authentication Required"};var Ge,Qe,Je,Xe,Ye,Ze,et=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},tt=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class it{constructor(e){Ge.set(this,void 0),Qe.set(this,void 0),Je.set(this,void 0),Xe.set(this,void 0),Ye.set(this,void 0),Ze.set(this,void 0),et(this,Ge,e.subjectName,"f"),et(this,Qe,e.issuer,"f"),et(this,Je,e.validFrom,"f"),et(this,Xe,e.validTo,"f"),et(this,Ye,e.protocol,"f"),et(this,Ze,e.sanList,"f")}issuer(){return tt(this,Qe,"f")}validFrom(){return tt(this,Je,"f")}validTo(){return tt(this,Xe,"f")}protocol(){return tt(this,Ye,"f")}subjectName(){return tt(this,Ge,"f")}subjectAlternativeNames(){return tt(this,Ze,"f")}}Ge=new WeakMap,Qe=new WeakMap,Je=new WeakMap,Xe=new WeakMap,Ye=new WeakMap,Ze=new WeakMap;var st,rt,nt,ot,at,ct,dt,lt,ht,ft,ut,wt,mt,pt,yt,gt,vt=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},kt=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class bt{constructor(e,t,i,s){st.add(this),rt.set(this,void 0),nt.set(this,void 0),ot.set(this,null),at.set(this,void 0),ct.set(this,(()=>{})),dt.set(this,void 0),lt.set(this,void 0),ht.set(this,void 0),ft.set(this,void 0),ut.set(this,void 0),wt.set(this,void 0),mt.set(this,{}),pt.set(this,void 0),yt.set(this,void 0),vt(this,rt,e,"f"),vt(this,nt,t,"f"),vt(this,at,new Promise((e=>{vt(this,ct,e,"f")})),"f"),vt(this,dt,{ip:i.remoteIPAddress,port:i.remotePort},"f"),vt(this,ht,kt(this,st,"m",gt).call(this,s)||i.statusText,"f"),vt(this,ft,t.url(),"f"),vt(this,ut,!!i.fromDiskCache,"f"),vt(this,wt,!!i.fromServiceWorker,"f"),vt(this,lt,s?s.statusCode:i.status,"f");const r=s?s.headers:i.headers;for(const[e,t]of Object.entries(r))kt(this,mt,"f")[e.toLowerCase()]=t;vt(this,pt,i.securityDetails?new it(i.securityDetails):null,"f"),vt(this,yt,i.timing||null,"f")}_resolveBody(e){return e?kt(this,ct,"f").call(this,e):kt(this,ct,"f").call(this)}remoteAddress(){return kt(this,dt,"f")}url(){return kt(this,ft,"f")}ok(){return 0===kt(this,lt,"f")||kt(this,lt,"f")>=200&&kt(this,lt,"f")<=299}status(){return kt(this,lt,"f")}statusText(){return kt(this,ht,"f")}headers(){return kt(this,mt,"f")}securityDetails(){return kt(this,pt,"f")}timing(){return kt(this,yt,"f")}buffer(){return kt(this,ot,"f")||vt(this,ot,kt(this,at,"f").then((async e=>{if(e)throw e;try{const e=await kt(this,rt,"f").send("Network.getResponseBody",{requestId:kt(this,nt,"f")._requestId});return Buffer.from(e.body,e.base64Encoded?"base64":"utf8")}catch(e){if(e instanceof w&&"No resource with given identifier found"===e.originalMessage)throw new w("Could not load body for this request. This might happen if the request is a preflight request.");throw e}})),"f"),kt(this,ot,"f")}async text(){return(await this.buffer()).toString("utf8")}async json(){const e=await this.text();return JSON.parse(e)}request(){return kt(this,nt,"f")}fromCache(){return kt(this,ut,"f")||kt(this,nt,"f")._fromMemoryCache}fromServiceWorker(){return kt(this,wt,"f")}frame(){return kt(this,nt,"f").frame()}}rt=new WeakMap,nt=new WeakMap,ot=new WeakMap,at=new WeakMap,ct=new WeakMap,dt=new WeakMap,lt=new WeakMap,ht=new WeakMap,ft=new WeakMap,ut=new WeakMap,wt=new WeakMap,mt=new WeakMap,pt=new WeakMap,yt=new WeakMap,st=new WeakSet,gt=function(e){if(!e||!e.headersText)return;const t=e.headersText.split("\r",1)[0];if(!t)return;const i=t.match(/[^ ]* [^ ]* (.*)/);if(!i)return;return i[1]||void 0};var Et,Ct,Mt,Pt,_t,Tt,It=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class xt{constructor(){Et.set(this,new Map),Ct.set(this,new Map),Mt.set(this,new Map),Pt.set(this,new Map),_t.set(this,new Map),Tt.set(this,new Map)}forget(e){It(this,Et,"f").delete(e),It(this,Ct,"f").delete(e),It(this,Tt,"f").delete(e),It(this,_t,"f").delete(e),It(this,Pt,"f").delete(e)}responseExtraInfo(e){return It(this,Pt,"f").has(e)||It(this,Pt,"f").set(e,[]),It(this,Pt,"f").get(e)}queuedRedirectInfo(e){return It(this,_t,"f").has(e)||It(this,_t,"f").set(e,[]),It(this,_t,"f").get(e)}queueRedirectInfo(e,t){this.queuedRedirectInfo(e).push(t)}takeQueuedRedirectInfo(e){return this.queuedRedirectInfo(e).shift()}numRequestsInProgress(){return[...It(this,Mt,"f")].filter((([,e])=>!e.response())).length}storeRequestWillBeSent(e,t){It(this,Et,"f").set(e,t)}getRequestWillBeSent(e){return It(this,Et,"f").get(e)}forgetRequestWillBeSent(e){It(this,Et,"f").delete(e)}getRequestPaused(e){return It(this,Ct,"f").get(e)}forgetRequestPaused(e){It(this,Ct,"f").delete(e)}storeRequestPaused(e,t){It(this,Ct,"f").set(e,t)}getRequest(e){return It(this,Mt,"f").get(e)}storeRequest(e,t){It(this,Mt,"f").set(e,t)}forgetRequest(e){It(this,Mt,"f").delete(e)}getQueuedEventGroup(e){return It(this,Tt,"f").get(e)}queueEventGroup(e,t){It(this,Tt,"f").set(e,t)}forgetQueuedEventGroup(e){It(this,Tt,"f").delete(e)}}Et=new WeakMap,Ct=new WeakMap,Mt=new WeakMap,Pt=new WeakMap,_t=new WeakMap,Tt=new WeakMap;var Nt,St,Ft,Wt,Rt,Dt,qt,jt,At,Ot,Kt,Lt,$t,Bt,Ht,Ut,Vt,zt,Gt,Qt,Jt,Xt,Yt,Zt,ei,ti,ii,si,ri,ni,oi,ai,ci=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},di=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};const li={Request:Symbol("NetworkManager.Request"),RequestServedFromCache:Symbol("NetworkManager.RequestServedFromCache"),Response:Symbol("NetworkManager.Response"),RequestFailed:Symbol("NetworkManager.RequestFailed"),RequestFinished:Symbol("NetworkManager.RequestFinished")};class hi extends a{constructor(e,t,i){super(),Nt.add(this),St.set(this,void 0),Ft.set(this,void 0),Wt.set(this,void 0),Rt.set(this,new xt),Dt.set(this,{}),qt.set(this,void 0),jt.set(this,new Set),At.set(this,!1),Ot.set(this,!1),Kt.set(this,!1),Lt.set(this,{offline:!1,upload:-1,download:-1,latency:0}),$t.set(this,void 0),ci(this,St,e,"f"),ci(this,Ft,t,"f"),ci(this,Wt,i,"f"),di(this,St,"f").on("Fetch.requestPaused",di(this,Nt,"m",Qt).bind(this)),di(this,St,"f").on("Fetch.authRequired",di(this,Nt,"m",Gt).bind(this)),di(this,St,"f").on("Network.requestWillBeSent",di(this,Nt,"m",zt).bind(this)),di(this,St,"f").on("Network.requestServedFromCache",di(this,Nt,"m",Yt).bind(this)),di(this,St,"f").on("Network.responseReceived",di(this,Nt,"m",ti).bind(this)),di(this,St,"f").on("Network.loadingFinished",di(this,Nt,"m",ri).bind(this)),di(this,St,"f").on("Network.loadingFailed",di(this,Nt,"m",oi).bind(this)),di(this,St,"f").on("Network.responseReceivedExtraInfo",di(this,Nt,"m",ii).bind(this))}initialize(){if(di(this,$t,"f"))return di(this,$t,"f");ci(this,$t,("NetworkManager initialization timed out",i>0?Z({message:"NetworkManager initialization timed out",timeout:i}):Z()),"f");const e=Promise.all([di(this,Ft,"f")?di(this,St,"f").send("Security.setIgnoreCertificateErrors",{ignore:!0}):null,di(this,St,"f").send("Network.enable")]),t=di(this,$t,"f");return e.then((()=>{t.resolve()})).catch((e=>{t.reject(e)})),di(this,$t,"f")}async authenticate(e){ci(this,qt,e,"f"),await di(this,Nt,"m",Ht).call(this)}async setExtraHTTPHeaders(t){ci(this,Dt,{},"f");for(const i of Object.keys(t)){const s=t[i];e(Kr(s),`Expected value of header "${i}" to be String, but "${typeof s}" is found.`),di(this,Dt,"f")[i.toLowerCase()]=s}await di(this,St,"f").send("Network.setExtraHTTPHeaders",{headers:di(this,Dt,"f")})}extraHTTPHeaders(){return Object.assign({},di(this,Dt,"f"))}numRequestsInProgress(){return di(this,Rt,"f").numRequestsInProgress()}async setOfflineMode(e){di(this,Lt,"f").offline=e,await di(this,Nt,"m",Bt).call(this)}async emulateNetworkConditions(e){di(this,Lt,"f").upload=e?e.upload:-1,di(this,Lt,"f").download=e?e.download:-1,di(this,Lt,"f").latency=e?e.latency:0,await di(this,Nt,"m",Bt).call(this)}async setUserAgent(e,t){await di(this,St,"f").send("Network.setUserAgentOverride",{userAgent:e,userAgentMetadata:t})}async setCacheEnabled(e){ci(this,Kt,!e,"f"),await di(this,Nt,"m",Vt).call(this)}async setRequestInterception(e){ci(this,At,e,"f"),await di(this,Nt,"m",Ht).call(this)}}St=new WeakMap,Ft=new WeakMap,Wt=new WeakMap,Rt=new WeakMap,Dt=new WeakMap,qt=new WeakMap,jt=new WeakMap,At=new WeakMap,Ot=new WeakMap,Kt=new WeakMap,Lt=new WeakMap,$t=new WeakMap,Nt=new WeakSet,Bt=async function(){await di(this,St,"f").send("Network.emulateNetworkConditions",{offline:di(this,Lt,"f").offline,latency:di(this,Lt,"f").latency,uploadThroughput:di(this,Lt,"f").upload,downloadThroughput:di(this,Lt,"f").download})},Ht=async function(){const e=di(this,At,"f")||!!di(this,qt,"f");e!==di(this,Ot,"f")&&(ci(this,Ot,e,"f"),e?await Promise.all([di(this,Nt,"m",Vt).call(this),di(this,St,"f").send("Fetch.enable",{handleAuthRequests:!0,patterns:[{urlPattern:"*"}]})]):await Promise.all([di(this,Nt,"m",Vt).call(this),di(this,St,"f").send("Fetch.disable")]))},Ut=function(){return di(this,Kt,"f")},Vt=async function(){await di(this,St,"f").send("Network.setCacheDisabled",{cacheDisabled:di(this,Nt,"m",Ut).call(this)})},zt=function(e){if(!di(this,At,"f")||e.request.url.startsWith("data:"))di(this,Nt,"m",Xt).call(this,e,void 0);else{const{requestId:t}=e;di(this,Rt,"f").storeRequestWillBeSent(t,e);const i=di(this,Rt,"f").getRequestPaused(t);if(i){const{requestId:s}=i;di(this,Nt,"m",Jt).call(this,e,i),di(this,Nt,"m",Xt).call(this,e,s),di(this,Rt,"f").forgetRequestPaused(t)}}},Gt=function(e){let t="Default";di(this,jt,"f").has(e.requestId)?t="CancelAuth":di(this,qt,"f")&&(t="ProvideCredentials",di(this,jt,"f").add(e.requestId));const{username:i,password:s}=di(this,qt,"f")||{username:void 0,password:void 0};di(this,St,"f").send("Fetch.continueWithAuth",{requestId:e.requestId,authChallengeResponse:{response:t,username:i,password:s}}).catch(Rr)},Qt=function(e){!di(this,At,"f")&&di(this,Ot,"f")&&di(this,St,"f").send("Fetch.continueRequest",{requestId:e.requestId}).catch(Rr);const{networkId:t,requestId:i}=e;if(!t)return;const s=(()=>{const i=di(this,Rt,"f").getRequestWillBeSent(t);if(!i||i.request.url===e.request.url&&i.request.method===e.request.method)return i;di(this,Rt,"f").forgetRequestWillBeSent(t)})();s?(di(this,Nt,"m",Jt).call(this,s,e),di(this,Nt,"m",Xt).call(this,s,i)):di(this,Rt,"f").storeRequestPaused(t,e)},Jt=function(e,t){e.request.headers={...e.request.headers,...t.request.headers}},Xt=function(e,t){let i=[];if(e.redirectResponse){let s=null;if(e.redirectHasExtraInfo&&(s=di(this,Rt,"f").responseExtraInfo(e.requestId).shift(),!s))return void di(this,Rt,"f").queueRedirectInfo(e.requestId,{event:e,fetchRequestId:t});const r=di(this,Rt,"f").getRequest(e.requestId);r&&(di(this,Nt,"m",Zt).call(this,r,e.redirectResponse,s),i=r._redirectChain)}const s=e.frameId?di(this,Wt,"f").frame(e.frameId):null,r=new Be(di(this,St,"f"),s,t,di(this,At,"f"),e,i);di(this,Rt,"f").storeRequest(e.requestId,r),this.emit(li.Request,r),r.finalizeInterceptions()},Yt=function(e){const t=di(this,Rt,"f").getRequest(e.requestId);t&&(t._fromMemoryCache=!0),this.emit(li.RequestServedFromCache,t)},Zt=function(e,t,i){const s=new bt(di(this,St,"f"),e,t,i);e._response=s,e._redirectChain.push(e),s._resolveBody(new Error("Response body is unavailable for redirect responses")),di(this,Nt,"m",si).call(this,e,!1),this.emit(li.Response,s),this.emit(li.RequestFinished,e)},ei=function(e,t){const i=di(this,Rt,"f").getRequest(e.requestId);if(!i)return;di(this,Rt,"f").responseExtraInfo(e.requestId).length&&Rr(new Error("Unexpected extraInfo events for request "+e.requestId));const s=new bt(di(this,St,"f"),i,e.response,t);i._response=s,this.emit(li.Response,s)},ti=function(e){const t=di(this,Rt,"f").getRequest(e.requestId);let i=null;!t||t._fromMemoryCache||!e.hasExtraInfo||(i=di(this,Rt,"f").responseExtraInfo(e.requestId).shift(),i)?di(this,Nt,"m",ei).call(this,e,i):di(this,Rt,"f").queueEventGroup(e.requestId,{responseReceivedEvent:e})},ii=function(e){const t=di(this,Rt,"f").takeQueuedRedirectInfo(e.requestId);if(t)return di(this,Rt,"f").responseExtraInfo(e.requestId).push(e),void di(this,Nt,"m",Xt).call(this,t.event,t.fetchRequestId);const i=di(this,Rt,"f").getQueuedEventGroup(e.requestId);if(i)return di(this,Rt,"f").forgetQueuedEventGroup(e.requestId),di(this,Nt,"m",ei).call(this,i.responseReceivedEvent,e),i.loadingFinishedEvent&&di(this,Nt,"m",ni).call(this,i.loadingFinishedEvent),void(i.loadingFailedEvent&&di(this,Nt,"m",ai).call(this,i.loadingFailedEvent));di(this,Rt,"f").responseExtraInfo(e.requestId).push(e)},si=function(e,t){const i=e._requestId,s=e._interceptionId;di(this,Rt,"f").forgetRequest(i),void 0!==s&&di(this,jt,"f").delete(s),t&&di(this,Rt,"f").forget(i)},ri=function(e){const t=di(this,Rt,"f").getQueuedEventGroup(e.requestId);t?t.loadingFinishedEvent=e:di(this,Nt,"m",ni).call(this,e)},ni=function(e){var t;const i=di(this,Rt,"f").getRequest(e.requestId);i&&(i.response()&&(null===(t=i.response())||void 0===t||t._resolveBody(null)),di(this,Nt,"m",si).call(this,i,!0),this.emit(li.RequestFinished,i))},oi=function(e){const t=di(this,Rt,"f").getQueuedEventGroup(e.requestId);t?t.loadingFailedEvent=e:di(this,Nt,"m",ai).call(this,e)},ai=function(e){const t=di(this,Rt,"f").getRequest(e.requestId);if(!t)return;t._failureText=e.errorText;const i=t.response();i&&i._resolveBody(null),di(this,Nt,"m",si).call(this,t,!0),this.emit(li.RequestFailed,t)};var fi,ui,wi,mi,pi,yi,gi,vi,ki,bi,Ei,Ci,Mi,Pi,_i,Ti,Ii,xi,Ni,Si,Fi,Wi=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)},Ri=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i};const Di={FrameAttached:Symbol("FrameManager.FrameAttached"),FrameNavigated:Symbol("FrameManager.FrameNavigated"),FrameDetached:Symbol("FrameManager.FrameDetached"),FrameSwapped:Symbol("FrameManager.FrameSwapped"),LifecycleEvent:Symbol("FrameManager.LifecycleEvent"),FrameNavigatedWithinDocument:Symbol("FrameManager.FrameNavigatedWithinDocument"),ExecutionContextCreated:Symbol("FrameManager.ExecutionContextCreated"),ExecutionContextDestroyed:Symbol("FrameManager.ExecutionContextDestroyed")};class qi extends a{get timeoutSettings(){return Wi(this,mi,"f")}get networkManager(){return Wi(this,wi,"f")}get client(){return Wi(this,gi,"f")}constructor(e,t,i,s){super(),fi.add(this),ui.set(this,void 0),wi.set(this,void 0),mi.set(this,void 0),pi.set(this,new Map),yi.set(this,new Set),gi.set(this,void 0),this._frameTree=new ve,vi.set(this,new Set),Ri(this,gi,e,"f"),Ri(this,ui,t,"f"),Ri(this,wi,new hi(e,i,this),"f"),Ri(this,mi,s,"f"),this.setupEventListeners(Wi(this,gi,"f"))}setupEventListeners(e){e.on("Page.frameAttached",(t=>{Wi(this,fi,"m",Mi).call(this,e,t.frameId,t.parentFrameId)})),e.on("Page.frameNavigated",(e=>{Wi(this,vi,"f").add(e.frame.id),Wi(this,fi,"m",Pi).call(this,e.frame)})),e.on("Page.navigatedWithinDocument",(e=>{Wi(this,fi,"m",Ti).call(this,e.frameId,e.url)})),e.on("Page.frameDetached",(e=>{Wi(this,fi,"m",Ii).call(this,e.frameId,e.reason)})),e.on("Page.frameStartedLoading",(e=>{Wi(this,fi,"m",bi).call(this,e.frameId)})),e.on("Page.frameStoppedLoading",(e=>{Wi(this,fi,"m",Ei).call(this,e.frameId)})),e.on("Runtime.executionContextCreated",(t=>{Wi(this,fi,"m",xi).call(this,t.context,e)})),e.on("Runtime.executionContextDestroyed",(t=>{Wi(this,fi,"m",Ni).call(this,t.executionContextId,e)})),e.on("Runtime.executionContextsCleared",(()=>{Wi(this,fi,"m",Si).call(this,e)})),e.on("Page.lifecycleEvent",(e=>{Wi(this,fi,"m",ki).call(this,e)}))}async initialize(e=Wi(this,gi,"f")){try{const t=await Promise.all([e.send("Page.enable"),e.send("Page.getFrameTree")]),{frameTree:i}=t[1];Wi(this,fi,"m",Ci).call(this,e,i),await Promise.all([e.send("Page.setLifecycleEventsEnabled",{enabled:!0}),e.send("Runtime.enable").then((()=>Wi(this,fi,"m",_i).call(this,e,"__puppeteer_utility_world__"))),e===Wi(this,gi,"f")?Wi(this,wi,"f").initialize():Promise.resolve()])}catch(e){if(L(e)&&K(e))return;throw e}}executionContextById(t,i=Wi(this,gi,"f")){const s=this.getExecutionContextById(t,i);return e(s,"INTERNAL ERROR: missing context with id = "+t),s}getExecutionContextById(e,t=Wi(this,gi,"f")){return Wi(this,pi,"f").get(`${t.id()}:${e}`)}page(){return Wi(this,ui,"f")}mainFrame(){const t=this._frameTree.getMainFrame();return e(t,"Requesting main frame too early!"),t}frames(){return Array.from(this._frameTree.frames())}frame(e){return this._frameTree.getById(e)||null}onAttachedToTarget(e){if("iframe"!==e._getTargetInfo().type)return;const t=this.frame(e._getTargetInfo().targetId);t&&t.updateClient(e._session()),this.setupEventListeners(e._session()),this.initialize(e._session())}}ui=new WeakMap,wi=new WeakMap,mi=new WeakMap,pi=new WeakMap,yi=new WeakMap,gi=new WeakMap,vi=new WeakMap,fi=new WeakSet,ki=function(e){const t=this.frame(e.frameId);t&&(t._onLifecycleEvent(e.loaderId,e.name),this.emit(Di.LifecycleEvent,t))},bi=function(e){const t=this.frame(e);t&&t._onLoadingStarted()},Ei=function(e){const t=this.frame(e);t&&(t._onLoadingStopped(),this.emit(Di.LifecycleEvent,t))},Ci=function e(t,i){if(i.frame.parentId&&Wi(this,fi,"m",Mi).call(this,t,i.frame.id,i.frame.parentId),Wi(this,vi,"f").has(i.frame.id)?Wi(this,vi,"f").delete(i.frame.id):Wi(this,fi,"m",Pi).call(this,i.frame),i.childFrames)for(const s of i.childFrames)Wi(this,fi,"m",e).call(this,t,s)},Mi=function(e,t,i){let s=this.frame(t);s?e&&s.isOOPFrame()&&s.updateClient(e):(s=new ir(this,t,i,e),this._frameTree.addFrame(s),this.emit(Di.FrameAttached,s))},Pi=async function(e){const t=e.id,i=!e.parentId;let s=this._frameTree.getById(t);if(s)for(const e of s.childFrames())Wi(this,fi,"m",Fi).call(this,e);i&&(s?(this._frameTree.removeFrame(s),s._id=t):s=new ir(this,t,void 0,Wi(this,gi,"f")),this._frameTree.addFrame(s)),s=await this._frameTree.waitForFrame(t),s._navigated(e),this.emit(Di.FrameNavigated,s)},_i=async function(e,t){const i=`${e.id()}:${t}`;Wi(this,yi,"f").has(i)||(await e.send("Page.addScriptToEvaluateOnNewDocument",{source:"//# sourceURL=pptr://__puppeteer_evaluation_script__",worldName:t}),await Promise.all(this.frames().filter((t=>t._client()===e)).map((i=>e.send("Page.createIsolatedWorld",{frameId:i._id,worldName:t,grantUniveralAccess:!0}).catch(Rr)))),Wi(this,yi,"f").add(i))},Ti=function(e,t){const i=this.frame(e);i&&(i._navigatedWithinDocument(t),this.emit(Di.FrameNavigatedWithinDocument,i),this.emit(Di.FrameNavigated,i))},Ii=function(e,t){const i=this.frame(e);"remove"===t?i&&Wi(this,fi,"m",Fi).call(this,i):"swap"===t&&this.emit(Di.FrameSwapped,i)},xi=function(e,t){const i=e.auxData,s=i&&i.frameId,r="string"==typeof s?this.frame(s):void 0;let n;if(r){if(r._client()!==t)return;e.auxData&&e.auxData.isDefault?n=r.worlds[$]:"__puppeteer_utility_world__"!==e.name||r.worlds[B].hasContext()||(n=r.worlds[B])}const o=new le((null==r?void 0:r._client())||Wi(this,gi,"f"),e,n);n&&n.setContext(o);const a=`${t.id()}:${e.id}`;Wi(this,pi,"f").set(a,o)},Ni=function(e,t){const i=`${t.id()}:${e}`,s=Wi(this,pi,"f").get(i);s&&(Wi(this,pi,"f").delete(i),s._world&&s._world.clearContext())},Si=function(e){for(const[t,i]of Wi(this,pi,"f").entries())i._client===e&&(i._world&&i._world.clearContext(),Wi(this,pi,"f").delete(t))},Fi=function e(t){for(const i of t.childFrames())Wi(this,fi,"m",e).call(this,i);t._detach(),this._frameTree.removeFrame(t),this.emit(Di.FrameDetached,t)};var ji,Ai,Oi,Ki,Li,$i,Bi,Hi,Ui,Vi,zi,Gi,Qi,Ji,Xi,Yi,Zi,es,ts,is,ss,rs,ns,os,as,cs,ds,ls,hs,fs,us,ws=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},ms=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};const ps=new Map([["load","load"],["domcontentloaded","DOMContentLoaded"],["networkidle0","networkIdle"],["networkidle2","networkAlmostIdle"]]),ys=()=>{};class gs{constructor(t,i,s,r){ji.add(this),Ai.set(this,void 0),Oi.set(this,void 0),Ki.set(this,void 0),Li.set(this,void 0),$i.set(this,null),Bi.set(this,void 0),Hi.set(this,void 0),Ui.set(this,ys),Vi.set(this,new Promise((e=>{ws(this,Ui,e,"f")}))),zi.set(this,ys),Gi.set(this,new Promise((e=>{ws(this,zi,e,"f")}))),Qi.set(this,ys),Ji.set(this,new Promise((e=>{ws(this,Qi,e,"f")}))),Xi.set(this,ys),Yi.set(this,new Promise((e=>{ws(this,Xi,e,"f")}))),Zi.set(this,void 0),es.set(this,void 0),ts.set(this,void 0),is.set(this,void 0),ss.set(this,void 0),Array.isArray(s)?s=s.slice():"string"==typeof s&&(s=[s]),ws(this,Hi,i._loaderId,"f"),ws(this,Ai,s.map((t=>{const i=ps.get(t);return e(i,"Unknown value for options.waitUntil: "+t),i})),"f"),ws(this,Oi,t,"f"),ws(this,Ki,i,"f"),ws(this,Li,r,"f"),ws(this,Bi,[Ar(t.client,D.Disconnected,ms(this,ji,"m",cs).bind(this,new Error("Navigation failed because browser has disconnected!"))),Ar(ms(this,Oi,"f"),Di.LifecycleEvent,ms(this,ji,"m",us).bind(this)),Ar(ms(this,Oi,"f"),Di.FrameNavigatedWithinDocument,ms(this,ji,"m",ls).bind(this)),Ar(ms(this,Oi,"f"),Di.FrameNavigated,ms(this,ji,"m",hs).bind(this)),Ar(ms(this,Oi,"f"),Di.FrameSwapped,ms(this,ji,"m",fs).bind(this)),Ar(ms(this,Oi,"f"),Di.FrameDetached,ms(this,ji,"m",as).bind(this)),Ar(ms(this,Oi,"f").networkManager,li.Request,ms(this,ji,"m",rs).bind(this)),Ar(ms(this,Oi,"f").networkManager,li.Response,ms(this,ji,"m",os).bind(this)),Ar(ms(this,Oi,"f").networkManager,li.RequestFailed,ms(this,ji,"m",ns).bind(this))],"f"),ws(this,Zi,ms(this,ji,"m",ds).call(this),"f"),ms(this,ji,"m",us).call(this)}async navigationResponse(){var e;return await(null===(e=ms(this,ss,"f"))||void 0===e?void 0:e.catch((()=>{}))),ms(this,$i,"f")?ms(this,$i,"f").response():null}sameDocumentNavigationPromise(){return ms(this,Vi,"f")}newDocumentNavigationPromise(){return ms(this,Ji,"f")}lifecyclePromise(){return ms(this,Gi,"f")}timeoutOrTerminationPromise(){return Promise.race([ms(this,Zi,"f"),ms(this,Yi,"f")])}dispose(){Or(ms(this,Bi,"f")),void 0!==ms(this,es,"f")&&clearTimeout(ms(this,es,"f"))}}Ai=new WeakMap,Oi=new WeakMap,Ki=new WeakMap,Li=new WeakMap,$i=new WeakMap,Bi=new WeakMap,Hi=new WeakMap,Ui=new WeakMap,Vi=new WeakMap,zi=new WeakMap,Gi=new WeakMap,Qi=new WeakMap,Ji=new WeakMap,Xi=new WeakMap,Yi=new WeakMap,Zi=new WeakMap,es=new WeakMap,ts=new WeakMap,is=new WeakMap,ss=new WeakMap,ji=new WeakSet,rs=function(e){var t,i;e.frame()===ms(this,Ki,"f")&&e.isNavigationRequest()&&(ws(this,$i,e,"f"),null===(t=ms(this,ss,"f"))||void 0===t||t.resolve(),ws(this,ss,Z(),"f"),null!==e.response()&&(null===(i=ms(this,ss,"f"))||void 0===i||i.resolve()))},ns=function(e){var t,i;(null===(t=ms(this,$i,"f"))||void 0===t?void 0:t._requestId)===e._requestId&&(null===(i=ms(this,ss,"f"))||void 0===i||i.resolve())},os=function(e){var t,i;(null===(t=ms(this,$i,"f"))||void 0===t?void 0:t._requestId)===e.request()._requestId&&(null===(i=ms(this,ss,"f"))||void 0===i||i.resolve())},as=function(e){ms(this,Ki,"f")!==e?ms(this,ji,"m",us).call(this):ms(this,Xi,"f").call(null,new Error("Navigating frame was detached"))},cs=function(e){ms(this,Xi,"f").call(null,e)},ds=async function(){if(!ms(this,Li,"f"))return new Promise(ys);const e="Navigation timeout of "+ms(this,Li,"f")+" ms exceeded";return await new Promise((e=>ws(this,es,setTimeout(e,ms(this,Li,"f")),"f"))),new u(e)},ls=function(e){e===ms(this,Ki,"f")&&(ws(this,ts,!0,"f"),ms(this,ji,"m",us).call(this))},hs=function(e){e===ms(this,Ki,"f")&&ms(this,ji,"m",us).call(this)},fs=function(e){e===ms(this,Ki,"f")&&(ws(this,is,!0,"f"),ms(this,ji,"m",us).call(this))},us=function(){(function e(t,i){for(const e of i)if(!t._lifecycleEvents.has(e))return!1;for(const s of t.childFrames())if(s._hasStartedLoading&&!e(s,i))return!1;return!0})(ms(this,Ki,"f"),ms(this,Ai,"f"))&&(ms(this,zi,"f").call(this),ms(this,ts,"f")&&ms(this,Ui,"f").call(this),(ms(this,is,"f")||ms(this,Ki,"f")._loaderId!==ms(this,Hi,"f"))&&ms(this,Qi,"f").call(this))};var vs,ks,bs,Es,Cs,Ms,Ps,_s,Ts,Is,xs=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},Ns=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class Ss{constructor(e,t,i,...s){var r;if(vs.set(this,void 0),ks.set(this,void 0),bs.set(this,void 0),Es.set(this,void 0),Cs.set(this,void 0),Ms.set(this,void 0),Ps.set(this,void 0),_s.set(this,Z()),Ts.set(this,void 0),xs(this,vs,e,"f"),xs(this,ks,null!==(r=t.bindings)&&void 0!==r?r:new Map,"f"),xs(this,bs,t.polling,"f"),xs(this,Es,t.root,"f"),xs(this,Cs,"string"==typeof i?`() => {return (${i});}`:i.toString(),"f"),xs(this,Ms,s,"f"),Ns(this,vs,"f").taskManager.add(this),t.timeout&&xs(this,Ps,setTimeout((()=>{this.terminate(new u(`Waiting failed: ${t.timeout}ms exceeded`))}),t.timeout),"f"),0!==Ns(this,ks,"f").size)for(const[e,t]of Ns(this,ks,"f"))Ns(this,vs,"f")._boundFunctions.set(e,t);this.rerun()}get result(){return Ns(this,_s,"f")}async rerun(){try{if(0!==Ns(this,ks,"f").size){const e=await Ns(this,vs,"f").executionContext();await Promise.all([...Ns(this,ks,"f")].map((async([t])=>await Ns(this,vs,"f")._addBindingToContext(e,t))))}switch(Ns(this,bs,"f")){case"raf":xs(this,Ts,await Ns(this,vs,"f").evaluateHandle((({RAFPoller:e,createFunction:t},i,...s)=>{const r=t(i);return new e((()=>r(...s)))}),se.create((e=>e.puppeteerUtil)),Ns(this,Cs,"f"),...Ns(this,Ms,"f")),"f");break;case"mutation":xs(this,Ts,await Ns(this,vs,"f").evaluateHandle((({MutationPoller:e,createFunction:t},i,s,...r)=>{const n=t(s);return new e((()=>n(...r)),i||document)}),se.create((e=>e.puppeteerUtil)),Ns(this,Es,"f"),Ns(this,Cs,"f"),...Ns(this,Ms,"f")),"f");break;default:xs(this,Ts,await Ns(this,vs,"f").evaluateHandle((({IntervalPoller:e,createFunction:t},i,s,...r)=>{const n=t(s);return new e((()=>n(...r)),i)}),se.create((e=>e.puppeteerUtil)),Ns(this,bs,"f"),Ns(this,Cs,"f"),...Ns(this,Ms,"f")),"f")}await Ns(this,Ts,"f").evaluate((e=>{e.start()}));const e=await Ns(this,Ts,"f").evaluateHandle((e=>e.result()));Ns(this,_s,"f").resolve(e),await this.terminate()}catch(e){const t=this.getBadError(e);t&&await this.terminate(t)}}async terminate(e){if(Ns(this,vs,"f").taskManager.delete(this),Ns(this,Ps,"f")&&clearTimeout(Ns(this,Ps,"f")),e&&!Ns(this,_s,"f").finished()&&Ns(this,_s,"f").reject(e),Ns(this,Ts,"f"))try{await Ns(this,Ts,"f").evaluateHandle((async e=>{await e.stop()})),Ns(this,Ts,"f")&&(await Ns(this,Ts,"f").dispose(),xs(this,Ts,void 0,"f"))}catch{}}getBadError(e){if(e instanceof Error){if(e.message.includes("Execution context is not available in detached frame"))return new Error("Waiting failed: Frame detached");if(e.message.includes("Execution context was destroyed"))return;if(e.message.includes("Cannot find context with specified id"))return}return e}}vs=new WeakMap,ks=new WeakMap,bs=new WeakMap,Es=new WeakMap,Cs=new WeakMap,Ms=new WeakMap,Ps=new WeakMap,_s=new WeakMap,Ts=new WeakMap;class Fs{constructor(){Is.set(this,new Set)}add(e){Ns(this,Is,"f").add(e)}delete(e){Ns(this,Is,"f").delete(e)}terminateAll(e){for(const t of Ns(this,Is,"f"))t.terminate(e);Ns(this,Is,"f").clear()}async rerunAll(){await Promise.all([...Ns(this,Is,"f")].map((e=>e.rerun())))}}Is=new WeakMap;var Ws,Rs,Ds,qs,js,As,Os,Ks,Ls,$s,Bs,Hs,Us,Vs,zs,Gs=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)},Qs=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i};class Js{get taskManager(){return Gs(this,Ls,"f")}get _boundFunctions(){return Gs(this,Ks,"f")}constructor(e){Ws.add(this),Ds.set(this,void 0),qs.set(this,void 0),js.set(this,Z()),As.set(this,!1),Os.set(this,new Set),Ks.set(this,new Map),Ls.set(this,new Fs),Vs.set(this,null),zs.set(this,(async e=>{let t;if(!this.hasContext())return;const i=await this.executionContext();try{t=JSON.parse(e.payload)}catch{return}const{type:s,name:r,seq:n,args:o}=t;if("internal"===s&&Gs(this,Os,"f").has(Gs(Js,Rs,"f",$s).call(Js,r,i._contextId))&&i._contextId===e.executionContextId)try{const e=this._boundFunctions.get(r);if(!e)throw new Error("Bound function $name is not found");const t=await e(...o);await i.evaluate(((e,t,i)=>{const s=self[e].callbacks;s.get(t).resolve(i),s.delete(t)}),r,n,t)}catch(e){if(e.message.includes("Protocol error"))return;Rr(e)}})),Qs(this,Ds,e,"f"),Gs(this,Ws,"a",Bs).on("Runtime.bindingCalled",Gs(this,zs,"f"))}frame(){return Gs(this,Ds,"f")}clearContext(){Qs(this,qs,void 0,"f"),Qs(this,js,Z(),"f")}setContext(e){Gs(this,Os,"f").clear(),Gs(this,js,"f").resolve(e),Gs(this,Ls,"f").rerunAll()}hasContext(){return Gs(this,js,"f").resolved()}_detach(){Qs(this,As,!0,"f"),Gs(this,Ws,"a",Bs).off("Runtime.bindingCalled",Gs(this,zs,"f")),Gs(this,Ls,"f").terminateAll(new Error("waitForFunction failed: frame got detached."))}executionContext(){if(Gs(this,As,"f"))throw new Error(`Execution context is not available in detached frame "${Gs(this,Ds,"f").url()}" (are you trying to evaluate?)`);if(null===Gs(this,js,"f"))throw new Error("Execution content promise is missing");return Gs(this,js,"f")}async evaluateHandle(e,...t){return(await this.executionContext()).evaluateHandle(e,...t)}async evaluate(e,...t){return(await this.executionContext()).evaluate(e,...t)}async $(e){return(await this.document()).$(e)}async $$(e){return(await this.document()).$$(e)}async document(){if(Gs(this,qs,"f"))return Gs(this,qs,"f");const e=await this.executionContext();return Qs(this,qs,await e.evaluateHandle((()=>document)),"f"),Gs(this,qs,"f")}async $x(e){return(await this.document()).$x(e)}async $eval(e,t,...i){return(await this.document()).$eval(e,t,...i)}async $$eval(e,t,...i){return(await this.document()).$$eval(e,t,...i)}async content(){return await this.evaluate((()=>{let e="";return document.doctype&&(e=(new XMLSerializer).serializeToString(document.doctype)),document.documentElement&&(e+=document.documentElement.outerHTML),e}))}async setContent(e,t={}){const{waitUntil:i=["load"],timeout:s=Gs(this,Ws,"a",Us).navigationTimeout()}=t;await this.evaluate((e=>{document.open(),document.write(e),document.close()}),e);const r=new gs(Gs(this,Ws,"a",Hs),Gs(this,Ds,"f"),i,s),n=await Promise.race([r.timeoutOrTerminationPromise(),r.lifecyclePromise()]);if(r.dispose(),n)throw n}async click(t,i){const s=await this.$(t);e(s,`No element found for selector: ${t}`),await s.click(i),await s.dispose()}async focus(t){const i=await this.$(t);e(i,`No element found for selector: ${t}`),await i.focus(),await i.dispose()}async hover(t){const i=await this.$(t);e(i,`No element found for selector: ${t}`),await i.hover(),await i.dispose()}async select(t,...i){const s=await this.$(t);e(s,`No element found for selector: ${t}`);const r=await s.select(...i);return await s.dispose(),r}async tap(t){const i=await this.$(t);e(i,`No element found for selector: ${t}`),await i.tap(),await i.dispose()}async type(t,i,s){const r=await this.$(t);e(r,`No element found for selector: ${t}`),await r.type(i,s),await r.dispose()}async _addBindingToContext(e,t){if(!Gs(this,Os,"f").has(Gs(Js,Rs,"f",$s).call(Js,t,e._contextId)))return Gs(this,Vs,"f")?(await Gs(this,Vs,"f"),this._addBindingToContext(e,t)):(Qs(this,Vs,(async t=>{const i=Hr("internal",t);try{await e._client.send("Runtime.addBinding",{name:t,executionContextName:e._contextName}),await e.evaluate(i)}catch(e){if(e instanceof Error){if(e.message.includes("Execution context was destroyed"))return;if(e.message.includes("Cannot find context with specified id"))return}return void Rr(e)}Gs(this,Os,"f").add(Gs(Js,Rs,"f",$s).call(Js,t,e._contextId))})(t),"f"),await Gs(this,Vs,"f"),void Qs(this,Vs,null,"f"))}async _waitForSelectorInPage(e,t,i,s,r=new Map){const{visible:n=!1,hidden:o=!1,timeout:a=Gs(this,Ws,"a",Us).timeout()}=s;try{const s=await this.waitForFunction((async(e,t,i,s,r)=>{if(!e)return;const n=await e.createFunction(t)(s||document,i,e);return e.checkVisibility(n,r)}),{bindings:r,polling:n||o?"raf":"mutation",root:t,timeout:a},se.create((e=>e.puppeteerUtil)),e.toString(),i,t,!!n||!o&&void 0);return s.asElement()||(await s.dispose(),null)}catch(e){if(!L(e))throw e;throw e.message=`Waiting for selector \`${i}\` failed: ${e.message}`,e}}waitForFunction(e,t={},...i){const{polling:s="raf",timeout:r=Gs(this,Ws,"a",Us).timeout(),bindings:n,root:o}=t;if("number"==typeof s&&s<0)throw new Error("Cannot poll with non-positive interval");return new Ss(this,{bindings:n,polling:s,root:o,timeout:r},e,...i).result}async title(){return this.evaluate((()=>document.title))}async adoptBackendNode(e){const t=await this.executionContext(),{object:i}=await Gs(this,Ws,"a",Bs).send("DOM.resolveNode",{backendNodeId:e,executionContextId:t._contextId});return $r(t,i)}async adoptHandle(t){const i=await this.executionContext();e(t.executionContext()!==i,"Cannot adopt handle that already belongs to this execution context");const s=await Gs(this,Ws,"a",Bs).send("DOM.describeNode",{objectId:t.remoteObject().objectId});return await this.adoptBackendNode(s.node.backendNodeId)}async transferHandle(e){const t=await this.adoptHandle(e);return await e.dispose(),t}}Rs=Js,Ds=new WeakMap,qs=new WeakMap,js=new WeakMap,As=new WeakMap,Os=new WeakMap,Ks=new WeakMap,Ls=new WeakMap,Vs=new WeakMap,zs=new WeakMap,Ws=new WeakSet,Bs=function(){return Gs(this,Ds,"f")._client()},Hs=function(){return Gs(this,Ds,"f")._frameManager},Us=function(){return Gs(this,Ws,"a",Hs).timeoutSettings},$s={value:(e,t)=>`${e}_${t}`};var Xs,Ys,Zs,er=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},tr=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class ir{constructor(e,t,i,s){Xs.set(this,""),Ys.set(this,!1),Zs.set(this,void 0),this._loaderId="",this._hasStartedLoading=!1,this._lifecycleEvents=new Set,this._frameManager=e,er(this,Xs,"","f"),this._id=t,this._parentId=i,er(this,Ys,!1,"f"),this._loaderId="",this.updateClient(s)}updateClient(e){er(this,Zs,e,"f"),this.worlds={[$]:new Js(this),[B]:new Js(this)}}page(){return this._frameManager.page()}isOOPFrame(){return tr(this,Zs,"f")!==this._frameManager.client}async goto(e,t={}){const{referer:i=this._frameManager.networkManager.extraHTTPHeaders().referer,referrerPolicy:s=this._frameManager.networkManager.extraHTTPHeaders()["referer-policy"],waitUntil:r=["load"],timeout:n=this._frameManager.timeoutSettings.navigationTimeout()}=t;let o=!1;const a=new gs(this._frameManager,this,r,n);let c=await Promise.race([async function(e,t,i,s,r){try{const n=await e.send("Page.navigate",{url:t,referrer:i,frameId:r,referrerPolicy:s});return o=!!n.loaderId,"net::ERR_HTTP_RESPONSE_CODE_FAILURE"===n.errorText?null:n.errorText?new Error(`${n.errorText} at ${t}`):null}catch(e){if(L(e))return e;throw e}}(tr(this,Zs,"f"),e,i,s,this._id),a.timeoutOrTerminationPromise()]);c||(c=await Promise.race([a.timeoutOrTerminationPromise(),o?a.newDocumentNavigationPromise():a.sameDocumentNavigationPromise()]));try{if(c)throw c;return await a.navigationResponse()}finally{a.dispose()}}async waitForNavigation(e={}){const{waitUntil:t=["load"],timeout:i=this._frameManager.timeoutSettings.navigationTimeout()}=e,s=new gs(this._frameManager,this,t,i),r=await Promise.race([s.timeoutOrTerminationPromise(),s.sameDocumentNavigationPromise(),s.newDocumentNavigationPromise()]);try{if(r)throw r;return await s.navigationResponse()}finally{s.dispose()}}_client(){return tr(this,Zs,"f")}executionContext(){return this.worlds[$].executionContext()}async evaluateHandle(e,...t){return this.worlds[$].evaluateHandle(e,...t)}async evaluate(e,...t){return this.worlds[$].evaluate(e,...t)}async $(e){return this.worlds[$].$(e)}async $$(e){return this.worlds[$].$$(e)}async $eval(e,t,...i){return this.worlds[$].$eval(e,t,...i)}async $$eval(e,t,...i){return this.worlds[$].$$eval(e,t,...i)}async $x(e){return this.worlds[$].$x(e)}async waitForSelector(t,i={}){const{updatedSelector:s,queryHandler:r}=hr(t);return e(r.waitFor,"Query handler does not support waiting"),await r.waitFor(this,s,i)}async waitForXPath(e,t={}){return e.startsWith("//")&&(e=`.${e}`),this.waitForSelector(`xpath/${e}`,t)}waitForFunction(e,t={},...i){return this.worlds[$].waitForFunction(e,t,...i)}async content(){return this.worlds[B].content()}async setContent(e,t={}){return this.worlds[B].setContent(e,t)}name(){return this._name||""}url(){return tr(this,Xs,"f")}parentFrame(){return this._frameManager._frameTree.parentFrame(this._id)||null}childFrames(){return this._frameManager._frameTree.childFrames(this._id)}isDetached(){return tr(this,Ys,"f")}async addScriptTag(e){let{content:t="",type:i}=e;const{path:s}=e;if(+!!e.url+ +!!s+ +!!t!=1)throw new Error("Exactly one of `url`, `path`, or `content` must be specified.");if(s){let i;try{i=(await import("fs")).promises}catch(e){if(e instanceof TypeError)throw new Error("Can only pass a file path in a Node-like environment.");throw e}t=await i.readFile(s,"utf8"),t+=`//# sourceURL=${s.replace(/\n/g,"")}`}return i=null!=i?i:"text/javascript",this.worlds[$].transferHandle(await this.worlds[B].evaluateHandle((async({createDeferredPromise:e},{url:t,id:i,type:s,content:r})=>{const n=e(),o=document.createElement("script");return o.type=s,o.text=r,t?(o.src=t,o.addEventListener("load",(()=>n.resolve()),{once:!0}),o.addEventListener("error",(e=>{var t;n.reject(new Error(null!==(t=e.message)&&void 0!==t?t:"Could not load script"))}),{once:!0})):n.resolve(),i&&(o.id=i),document.head.appendChild(o),await n,o}),se.create((e=>e.puppeteerUtil)),{...e,type:i,content:t}))}async addStyleTag(e){let{content:t=""}=e;const{path:i}=e;if(+!!e.url+ +!!i+ +!!t!=1)throw new Error("Exactly one of `url`, `path`, or `content` must be specified.");if(i){let s;try{s=(await zr()).promises}catch(e){if(e instanceof TypeError)throw new Error("Can only pass a file path in a Node-like environment.");throw e}t=await s.readFile(i,"utf8"),t+="/*# sourceURL="+i.replace(/\n/g,"")+"*/",e.content=t}return this.worlds[$].transferHandle(await this.worlds[B].evaluateHandle((async({createDeferredPromise:e},{url:t,content:i})=>{const s=e();let r;if(t){const e=document.createElement("link");e.rel="stylesheet",e.href=t,r=e}else r=document.createElement("style"),r.appendChild(document.createTextNode(i));return r.addEventListener("load",(()=>{s.resolve()}),{once:!0}),r.addEventListener("error",(e=>{var t;s.reject(new Error(null!==(t=e.message)&&void 0!==t?t:"Could not load style"))}),{once:!0}),document.head.appendChild(r),await s,r}),se.create((e=>e.puppeteerUtil)),e))}async click(e,t={}){return this.worlds[B].click(e,t)}async focus(e){return this.worlds[B].focus(e)}async hover(e){return this.worlds[B].hover(e)}select(e,...t){return this.worlds[B].select(e,...t)}async tap(e){return this.worlds[B].tap(e)}async type(e,t,i){return this.worlds[B].type(e,t,i)}waitForTimeout(e){return new Promise((t=>{setTimeout(t,e)}))}async title(){return this.worlds[B].title()}_navigated(e){this._name=e.name,er(this,Xs,`${e.url}${e.urlFragment||""}`,"f")}_navigatedWithinDocument(e){er(this,Xs,e,"f")}_onLifecycleEvent(e,t){"init"===t&&(this._loaderId=e,this._lifecycleEvents.clear()),this._lifecycleEvents.add(t)}_onLoadingStopped(){this._lifecycleEvents.add("DOMContentLoaded"),this._lifecycleEvents.add("load")}_onLoadingStarted(){this._hasStartedLoading=!0}_detach(){er(this,Ys,!0,"f"),this.worlds[$]._detach(),this.worlds[B]._detach()}}function sr(t){const i={};if(t.queryOne){const s=t.queryOne;i.queryOne=async(t,i)=>{const r=t.executionContext()._world;e(r);const n=await t.evaluateHandle(s,i,se.create((e=>e.puppeteerUtil)));return n.asElement()||(await n.dispose(),null)},i.waitFor=async(e,t,i)=>{let r,n;e instanceof ir?r=e:(r=e.frame,n=await r.worlds[B].adoptHandle(e));const o=await r.worlds[B]._waitForSelectorInPage(s,n,t,i);return n&&await n.dispose(),o?o instanceof Y?r.worlds[$].transferHandle(o):(await o.dispose(),null):null}}if(t.queryAll){const s=t.queryAll;i.queryAll=async(t,i)=>{const r=t.executionContext()._world;e(r);const n=await t.evaluateHandle(s,i,se.create((e=>e.puppeteerUtil))),o=await n.getProperties();await n.dispose();const a=[];for(const e of o.values()){const t=e.asElement();t&&a.push(t)}return a}}return i}Xs=new WeakMap,Ys=new WeakMap,Zs=new WeakMap;const rr=sr({queryOne:(e,t)=>{if(!("querySelector"in e))throw new Error(`Could not invoke \`querySelector\` on node of type ${e.nodeName}.`);return e.querySelector(t)},queryAll:(e,t)=>{if(!("querySelectorAll"in e))throw new Error(`Could not invoke \`querySelectorAll\` on node of type ${e.nodeName}.`);return[...e.querySelectorAll(t)]}}),nr=sr({queryOne:(e,t,{pierceQuerySelector:i})=>i(e,t),queryAll:(e,t,{pierceQuerySelectorAll:i})=>i(e,t)}),or=sr({queryOne:(e,t,{xpathQuerySelector:i})=>i(e,t),queryAll:(e,t,{xpathQuerySelectorAll:i})=>i(e,t)}),ar=sr({queryOne:(e,t,{textQuerySelector:i})=>i(e,t),queryAll:(e,t,{textQuerySelectorAll:i})=>i(e,t)}),cr=new Map([["aria",{handler:J}],["pierce",{handler:nr}],["xpath",{handler:or}],["text",{handler:ar}]]),dr=new Map,lr=["=","/"];function hr(e){for(const t of[dr,cr])for(const[i,{handler:s,transformSelector:r}]of t)for(const t of lr){const n=`${i}${t}`;if(e.startsWith(n))return e=e.slice(n.length),r&&(e=r(e)),{updatedSelector:e,queryHandler:s}}return{updatedSelector:e,queryHandler:rr}}var fr,ur,wr,mr,pr,yr,gr,vr,kr,br,Er,Cr,Mr=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},Pr=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};const _r=(e,t,i)=>e.map((e=>({x:e.x+t,y:e.y+i})));class Tr extends Y{constructor(e,t,i){super(),fr.add(this),ur.set(this,!1),wr.set(this,void 0),mr.set(this,void 0),pr.set(this,void 0),Mr(this,mr,e,"f"),Mr(this,pr,t,"f"),Mr(this,wr,i,"f")}executionContext(){return Pr(this,mr,"f")}get client(){return Pr(this,mr,"f")._client}remoteObject(){return Pr(this,pr,"f")}async evaluate(e,...t){return this.executionContext().evaluate(e,this,...t)}evaluateHandle(e,...t){return this.executionContext().evaluateHandle(e,this,...t)}get frame(){return Pr(this,wr,"f")}get disposed(){return Pr(this,ur,"f")}async getProperty(e){return this.evaluateHandle(((e,t)=>e[t]),e)}async jsonValue(){if(!Pr(this,pr,"f").objectId)return qr(Pr(this,pr,"f"));const e=await this.evaluate((e=>e));if(void 0===e)throw new Error("Could not serialize referenced object");return e}toString(){return Pr(this,pr,"f").objectId?"JSHandle@"+(Pr(this,pr,"f").subtype||Pr(this,pr,"f").type):"JSHandle:"+qr(Pr(this,pr,"f"))}async $(t){const{updatedSelector:i,queryHandler:s}=hr(t);return e(s.queryOne,"Cannot handle queries for a single element with the given selector"),await s.queryOne(this,i)}async $$(t){const{updatedSelector:i,queryHandler:s}=hr(t);return e(s.queryAll,"Cannot handle queries for a multiple element with the given selector"),await s.queryAll(this,i)}async $eval(e,t,...i){const s=await this.$(e);if(!s)throw new Error(`Error: failed to find element matching selector "${e}"`);const r=await s.evaluate(t,...i);return await s.dispose(),r}async $$eval(t,i,...s){const{updatedSelector:r,queryHandler:n}=hr(t);e(n.queryAll,"Cannot handle queries for a multiple element with the given selector");const o=await n.queryAll(this,r),a=await this.evaluateHandle(((e,...t)=>t),...o),[c]=await Promise.all([a.evaluate(i,...s),...o.map((e=>e.dispose()))]);return await a.dispose(),c}async $x(e){return e.startsWith("//")&&(e=`.${e}`),this.$$(`xpath/${e}`)}async waitForSelector(t,i={}){const{updatedSelector:s,queryHandler:r}=hr(t);return e(r.waitFor,"Query handler does not support waiting"),await r.waitFor(this,s,i)}async waitForXPath(e,t={}){return e.startsWith("//")&&(e=`.${e}`),this.waitForSelector(`xpath/${e}`,t)}async toElement(e){if(!await this.evaluate(((e,t)=>e.nodeName===t.toUpperCase()),e))throw new Error(`Element is not a(n) \`${e}\` element`);return this}asElement(){return this}async contentFrame(){const e=await this.client.send("DOM.describeNode",{objectId:this.remoteObject().objectId});return"string"!=typeof e.node.frameId?null:Pr(this,fr,"a",yr).frame(e.node.frameId)}async clickablePoint(e){const[t,i]=await Promise.all([this.client.send("DOM.getContentQuads",{objectId:this.remoteObject().objectId}).catch(Rr),Pr(this,fr,"a",gr)._client().send("Page.getLayoutMetrics")]);if(!t||!t.quads.length)throw new Error("Node is either not clickable or not an HTMLElement");const{clientWidth:s,clientHeight:r}=i.cssLayoutViewport||i.layoutViewport,{offsetX:n,offsetY:o}=await Pr(this,fr,"m",kr).call(this,Pr(this,wr,"f")),a=t.quads.map((e=>Pr(this,fr,"m",Er).call(this,e))).map((e=>_r(e,n,o))).map((e=>Pr(this,fr,"m",Cr).call(this,e,s,r))).filter((e=>function(e){let t=0;for(let i=0;i<e.length;++i){const s=e[i],r=e[(i+1)%e.length];t+=(s.x*r.y-r.x*s.y)/2}return Math.abs(t)}(e)>1));if(!a.length)throw new Error("Node is either not clickable or not an HTMLElement");const c=a[0];if(e){let t=Number.MAX_SAFE_INTEGER,i=Number.MAX_SAFE_INTEGER;for(const e of c)e.x<t&&(t=e.x),e.y<i&&(i=e.y);if(t!==Number.MAX_SAFE_INTEGER&&i!==Number.MAX_SAFE_INTEGER)return{x:t+e.x,y:i+e.y}}let d=0,l=0;for(const e of c)d+=e.x,l+=e.y;return{x:d/4,y:l/4}}async hover(){await Pr(this,fr,"m",vr).call(this);const{x:e,y:t}=await this.clickablePoint();await Pr(this,fr,"a",gr).mouse.move(e,t)}async click(e={}){await Pr(this,fr,"m",vr).call(this);const{x:t,y:i}=await this.clickablePoint(e.offset);await Pr(this,fr,"a",gr).mouse.click(t,i,e)}async drag(t){e(Pr(this,fr,"a",gr).isDragInterceptionEnabled(),"Drag Interception is not enabled!"),await Pr(this,fr,"m",vr).call(this);const i=await this.clickablePoint();return await Pr(this,fr,"a",gr).mouse.drag(i,t)}async dragEnter(e={items:[],dragOperationsMask:1}){await Pr(this,fr,"m",vr).call(this);const t=await this.clickablePoint();await Pr(this,fr,"a",gr).mouse.dragEnter(t,e)}async dragOver(e={items:[],dragOperationsMask:1}){await Pr(this,fr,"m",vr).call(this);const t=await this.clickablePoint();await Pr(this,fr,"a",gr).mouse.dragOver(t,e)}async drop(e={items:[],dragOperationsMask:1}){await Pr(this,fr,"m",vr).call(this);const t=await this.clickablePoint();await Pr(this,fr,"a",gr).mouse.drop(t,e)}async dragAndDrop(e,t){await Pr(this,fr,"m",vr).call(this);const i=await this.clickablePoint(),s=await e.clickablePoint();await Pr(this,fr,"a",gr).mouse.dragAndDrop(i,s,t)}async select(...t){for(const i of t)e(Kr(i),'Values must be strings. Found value "'+i+'" of type "'+typeof i+'"');return this.evaluate(((e,t)=>{const i=new Set(t);if(!(e instanceof HTMLSelectElement))throw new Error("Element is not a <select> element.");const s=new Set;if(e.multiple)for(const t of e.options)t.selected=i.has(t.value),t.selected&&s.add(t.value);else{for(const t of e.options)t.selected=!1;for(const t of e.options)if(i.has(t.value)){t.selected=!0,s.add(t.value);break}}return e.dispatchEvent(new Event("input",{bubbles:!0})),e.dispatchEvent(new Event("change",{bubbles:!0})),[...s.values()]}),t)}async uploadFile(...t){const i=await this.evaluate((e=>e.multiple));let s;e(t.length<=1||i,"Multiple file uploads only work with <input type=file multiple>");try{s=await import("path")}catch(e){if(e instanceof TypeError)throw new Error("JSHandle#uploadFile can only be used in Node-like environments.");throw e}const r=t.map((e=>s.win32.isAbsolute(e)||s.posix.isAbsolute(e)?e:s.resolve(e))),{objectId:n}=this.remoteObject(),{node:o}=await this.client.send("DOM.describeNode",{objectId:n}),{backendNodeId:a}=o;0===r.length?await this.evaluate((e=>{e.files=(new DataTransfer).files,e.dispatchEvent(new Event("input",{bubbles:!0})),e.dispatchEvent(new Event("change",{bubbles:!0}))})):await this.client.send("DOM.setFileInputFiles",{objectId:n,files:r,backendNodeId:a})}async tap(){await Pr(this,fr,"m",vr).call(this);const{x:e,y:t}=await this.clickablePoint();await Pr(this,fr,"a",gr).touchscreen.touchStart(e,t),await Pr(this,fr,"a",gr).touchscreen.touchEnd()}async touchStart(){await Pr(this,fr,"m",vr).call(this);const{x:e,y:t}=await this.clickablePoint();await Pr(this,fr,"a",gr).touchscreen.touchStart(e,t)}async touchMove(){await Pr(this,fr,"m",vr).call(this);const{x:e,y:t}=await this.clickablePoint();await Pr(this,fr,"a",gr).touchscreen.touchMove(e,t)}async touchEnd(){await Pr(this,fr,"m",vr).call(this),await Pr(this,fr,"a",gr).touchscreen.touchEnd()}async focus(){await this.evaluate((e=>{if(!(e instanceof HTMLElement))throw new Error("Cannot focus non-HTMLElement");return e.focus()}))}async type(e,t){await this.focus(),await Pr(this,fr,"a",gr).keyboard.type(e,t)}async press(e,t){await this.focus(),await Pr(this,fr,"a",gr).keyboard.press(e,t)}async boundingBox(){const e=await Pr(this,fr,"m",br).call(this);if(!e)return null;const{offsetX:t,offsetY:i}=await Pr(this,fr,"m",kr).call(this,Pr(this,wr,"f")),s=e.model.border,r=Math.min(s[0],s[2],s[4],s[6]),n=Math.min(s[1],s[3],s[5],s[7]);return{x:r+t,y:n+i,width:Math.max(s[0],s[2],s[4],s[6])-r,height:Math.max(s[1],s[3],s[5],s[7])-n}}async boxModel(){const e=await Pr(this,fr,"m",br).call(this);if(!e)return null;const{offsetX:t,offsetY:i}=await Pr(this,fr,"m",kr).call(this,Pr(this,wr,"f")),{content:s,padding:r,border:n,margin:o,width:a,height:c}=e.model;return{content:_r(Pr(this,fr,"m",Er).call(this,s),t,i),padding:_r(Pr(this,fr,"m",Er).call(this,r),t,i),border:_r(Pr(this,fr,"m",Er).call(this,n),t,i),margin:_r(Pr(this,fr,"m",Er).call(this,o),t,i),width:a,height:c}}async screenshot(t={}){let i=!1,s=await this.boundingBox();e(s,"Node is either not visible or not an HTMLElement");const r=Pr(this,fr,"a",gr).viewport();if(r&&(s.width>r.width||s.height>r.height)){const e={width:Math.max(r.width,Math.ceil(s.width)),height:Math.max(r.height,Math.ceil(s.height))};await Pr(this,fr,"a",gr).setViewport(Object.assign({},r,e)),i=!0}await Pr(this,fr,"m",vr).call(this),s=await this.boundingBox(),e(s,"Node is either not visible or not an HTMLElement"),e(0!==s.width,"Node has 0 width."),e(0!==s.height,"Node has 0 height.");const n=await this.client.send("Page.getLayoutMetrics"),{pageX:o,pageY:a}=n.cssVisualViewport||n.layoutViewport,c=Object.assign({},s);c.x+=o,c.y+=a;const d=await Pr(this,fr,"a",gr).screenshot(Object.assign({},{clip:c},t));return i&&r&&await Pr(this,fr,"a",gr).setViewport(r),d}async isIntersectingViewport(e){const{threshold:t=0}=null!=e?e:{};return await this.evaluate((async(e,t)=>{const i=await new Promise((t=>{const i=new IntersectionObserver((e=>{t(e[0].intersectionRatio),i.disconnect()}));i.observe(e)}));return 1===t?1===i:i>t}),t)}async dispose(){Pr(this,ur,"f")||(Mr(this,ur,!0,"f"),await jr(this.client,Pr(this,pr,"f")))}}ur=new WeakMap,wr=new WeakMap,mr=new WeakMap,pr=new WeakMap,fr=new WeakSet,yr=function(){return Pr(this,wr,"f")._frameManager},gr=function(){return Pr(this,wr,"f").page()},vr=async function(){const e=await this.evaluate((async e=>e.isConnected?e.nodeType!==Node.ELEMENT_NODE?"Node is not of type HTMLElement":void 0:"Node is detached from document"));if(e)throw new Error(e);try{await this.client.send("DOM.scrollIntoViewIfNeeded",{objectId:this.remoteObject().objectId})}catch(e){await this.evaluate((async(e,t)=>{t&&1===await(async()=>await new Promise((t=>{const i=new IntersectionObserver((e=>{t(e[0].intersectionRatio),i.disconnect()}));i.observe(e)})))()||e.scrollIntoView({block:"center",inline:"center",behavior:"instant"})}),Pr(this,fr,"a",gr).isJavaScriptEnabled())}},kr=async function(e){let t=0,i=0,s=e;for(;s&&s.parentFrame();){const e=s.parentFrame();if(!s.isOOPFrame()||!e){s=e;continue}const{backendNodeId:r}=await e._client().send("DOM.getFrameOwner",{frameId:s._id}),n=await e._client().send("DOM.getBoxModel",{backendNodeId:r});if(!n)break;const o=n.model.content,a=Pr(this,fr,"m",Er).call(this,o)[0];t+=a.x,i+=a.y,s=e}return{offsetX:t,offsetY:i}},br=function(){const e={objectId:this.remoteObject().objectId};return this.client.send("DOM.getBoxModel",e).catch((e=>Rr(e)))},Er=function(e){return[{x:e[0],y:e[1]},{x:e[2],y:e[3]},{x:e[4],y:e[5]},{x:e[6],y:e[7]}]},Cr=function(e,t,i){return e.map((e=>({x:Math.min(Math.max(e.x,0),t),y:Math.min(Math.max(e.y,0),i)})))};var Ir,xr,Nr,Sr=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)},Fr=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i};class Wr extends X{get disposed(){return Sr(this,Ir,"f")}constructor(e,t){super(),Ir.set(this,!1),xr.set(this,void 0),Nr.set(this,void 0),Fr(this,xr,e,"f"),Fr(this,Nr,t,"f")}executionContext(){return Sr(this,xr,"f")}get client(){return Sr(this,xr,"f")._client}async evaluate(e,...t){return await this.executionContext().evaluate(e,this,...t)}async evaluateHandle(e,...t){return await this.executionContext().evaluateHandle(e,this,...t)}async getProperty(e){return this.evaluateHandle(((e,t)=>e[t]),e)}async getProperties(){e(Sr(this,Nr,"f").objectId);const t=await this.client.send("Runtime.getProperties",{objectId:Sr(this,Nr,"f").objectId,ownProperties:!0}),i=new Map;for(const e of t.result)e.enumerable&&e.value&&i.set(e.name,$r(Sr(this,xr,"f"),e.value));return i}async jsonValue(){if(!Sr(this,Nr,"f").objectId)return qr(Sr(this,Nr,"f"));const e=await this.evaluate((e=>e));if(void 0===e)throw new Error("Could not serialize referenced object");return e}asElement(){return null}async dispose(){Sr(this,Ir,"f")||(Fr(this,Ir,!0,"f"),await jr(this.client,Sr(this,Nr,"f")))}toString(){return Sr(this,Nr,"f").objectId?"JSHandle@"+(Sr(this,Nr,"f").subtype||Sr(this,Nr,"f").type):"JSHandle:"+qr(Sr(this,Nr,"f"))}remoteObject(){return Sr(this,Nr,"f")}}Ir=new WeakMap,xr=new WeakMap,Nr=new WeakMap;const Rr=r("puppeteer:error");function Dr(e){if(e.exception)return e.exception.description||e.exception.value;let t=e.text;if(e.stackTrace)for(const i of e.stackTrace.callFrames){const e=i.url+":"+i.lineNumber+":"+i.columnNumber;t+=`\n    at ${i.functionName||"<anonymous>"} (${e})`}return t}function qr(t){if(e(!t.objectId,"Cannot extract value when objectId is given"),t.unserializableValue){if("bigint"===t.type&&"undefined"!=typeof BigInt)return BigInt(t.unserializableValue.replace("n",""));switch(t.unserializableValue){case"-0":return-0;case"NaN":return NaN;case"Infinity":return 1/0;case"-Infinity":return-1/0;default:throw new Error("Unsupported unserializable value: "+t.unserializableValue)}}return t.value}async function jr(e,t){t.objectId&&await e.send("Runtime.releaseObject",{objectId:t.objectId}).catch((e=>{Rr(e)}))}function Ar(e,t,i){return e.on(t,i),{emitter:e,eventName:t,handler:i}}function Or(e){for(const t of e)t.emitter.removeListener(t.eventName,t.handler);e.length=0}const Kr=e=>"string"==typeof e||e instanceof String;async function Lr(e,t,i,s,r){let n,o,a;const c=new Promise(((e,t)=>{o=e,a=t})),d=Ar(e,t,(async e=>{await i(e)&&o(e)}));function l(){Or([d]),clearTimeout(n)}s&&(n=setTimeout((()=>{a(new u("Timeout exceeded while waiting for event"))}),s));const h=await Promise.race([c,r]).then((e=>(l(),e)),(e=>{throw l(),e}));if(L(h))throw h;return h}function $r(e,t){return"node"===t.subtype&&e._world?new Tr(e,t,e._world.frame()):new Wr(e,t)}function Br(t,...i){return Kr(t)?(e(0===i.length,"Cannot evaluate a string with arguments"),t):`(${t})(${i.map((function(e){return Object.is(e,void 0)?"undefined":JSON.stringify(e)})).join(",")})`}function Hr(e,t){return Br((function(e,t){const i=self[t];Object.assign(self,{[t](...s){var r,n;const o=self[t];null!==(r=o.callbacks)&&void 0!==r||(o.callbacks=new Map);const a=(null!==(n=o.lastSeq)&&void 0!==n?n:0)+1;return o.lastSeq=a,i(JSON.stringify({type:e,name:t,seq:a,args:s})),new Promise(((e,t)=>{o.callbacks.set(a,{resolve:e,reject:t})}))}})}),e,t)}async function Ur(e,t,i){let s;const r=new u(`waiting for ${t} failed: timeout ${i}ms exceeded`),n=new Promise(((e,t)=>s=t));let o=null;i&&(o=setTimeout((()=>s(r)),i));try{return await Promise.race([e,n])}finally{o&&clearTimeout(o)}}let Vr=null;async function zr(){return Vr||(Vr=await import("fs")),Vr}async function Gr(e,t){const i=[];if(t){let s;try{s=(await zr()).promises}catch(e){if(e instanceof TypeError)throw new Error("Cannot write to a path outside of a Node-like environment.");throw e}const r=await s.open(t,"w+");for await(const t of e)i.push(t),await r.writeFile(t);await r.close()}else for await(const t of e)i.push(t);try{return Buffer.concat(i)}catch(e){return null}}async function Qr(e,i){if(!t)throw new Error("Cannot create a stream outside of Node.js environment.");const{Readable:s}=await import("stream");let r=!1;return new s({async read(t){if(r)return;const s=await e.send("IO.read",{handle:i,size:t});this.push(s.data,s.base64Encoded?"base64":void 0),s.eof&&(r=!0,await e.send("IO.close",{handle:i}),this.push(null))}})}function Jr(e){let t=e.toString();try{new Function("("+t+")")}catch(e){t=t.startsWith("async ")?"async function "+t.substring("async ".length):"function "+t;try{new Function("("+t+")")}catch(e){throw new Error("Passed function is not well-serializable!")}}return t}var Xr,Yr,Zr,en=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},tn=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class sn extends a{constructor(e,t,i,s){super(),Xr.set(this,Z()),Yr.set(this,void 0),Zr.set(this,void 0),en(this,Yr,e,"f"),en(this,Zr,t,"f"),tn(this,Yr,"f").once("Runtime.executionContextCreated",(async t=>{const i=new le(e,t.context);tn(this,Xr,"f").resolve(i)})),tn(this,Yr,"f").on("Runtime.consoleAPICalled",(async e=>{const t=await tn(this,Xr,"f");return i(e.type,e.args.map((e=>new Wr(t,e))),e.stackTrace)})),tn(this,Yr,"f").on("Runtime.exceptionThrown",(e=>s(e.exceptionDetails))),tn(this,Yr,"f").send("Runtime.enable").catch(Rr)}async executionContext(){return tn(this,Xr,"f")}url(){return tn(this,Zr,"f")}async evaluate(e,...t){return(await tn(this,Xr,"f")).evaluate(e,...t)}async evaluateHandle(e,...t){return(await tn(this,Xr,"f")).evaluateHandle(e,...t)}}Xr=new WeakMap,Yr=new WeakMap,Zr=new WeakMap;var rn,nn=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class on extends a{constructor(){super(),rn.set(this,new WeakMap)}isDragInterceptionEnabled(){throw new Error("Not implemented")}isJavaScriptEnabled(){throw new Error("Not implemented")}on(e,t){if("request"===e){const i=nn(this,rn,"f").get(t)||(e=>{e.enqueueInterceptAction((()=>t(e)))});return nn(this,rn,"f").set(t,i),super.on(e,i)}return super.on(e,t)}once(e,t){return super.once(e,t)}off(e,t){return"request"===e&&(t=nn(this,rn,"f").get(t)||t),super.off(e,t)}waitForFileChooser(){throw new Error("Not implemented")}async setGeolocation(){throw new Error("Not implemented")}target(){throw new Error("Not implemented")}browser(){throw new Error("Not implemented")}browserContext(){throw new Error("Not implemented")}mainFrame(){throw new Error("Not implemented")}get keyboard(){throw new Error("Not implemented")}get touchscreen(){throw new Error("Not implemented")}get coverage(){throw new Error("Not implemented")}get tracing(){throw new Error("Not implemented")}get accessibility(){throw new Error("Not implemented")}frames(){throw new Error("Not implemented")}workers(){throw new Error("Not implemented")}async setRequestInterception(){throw new Error("Not implemented")}async setDragInterception(){throw new Error("Not implemented")}setOfflineMode(){throw new Error("Not implemented")}emulateNetworkConditions(){throw new Error("Not implemented")}setDefaultNavigationTimeout(){throw new Error("Not implemented")}setDefaultTimeout(){throw new Error("Not implemented")}getDefaultTimeout(){throw new Error("Not implemented")}async $(){throw new Error("Not implemented")}async $$(){throw new Error("Not implemented")}async evaluateHandle(){throw new Error("Not implemented")}async queryObjects(){throw new Error("Not implemented")}async $eval(){throw new Error("Not implemented")}async $$eval(){throw new Error("Not implemented")}async $x(){throw new Error("Not implemented")}async cookies(){throw new Error("Not implemented")}async deleteCookie(){throw new Error("Not implemented")}async setCookie(){throw new Error("Not implemented")}async addScriptTag(){throw new Error("Not implemented")}async addStyleTag(){throw new Error("Not implemented")}async exposeFunction(){throw new Error("Not implemented")}async authenticate(){throw new Error("Not implemented")}async setExtraHTTPHeaders(){throw new Error("Not implemented")}async setUserAgent(){throw new Error("Not implemented")}async metrics(){throw new Error("Not implemented")}url(){throw new Error("Not implemented")}async content(){throw new Error("Not implemented")}async setContent(){throw new Error("Not implemented")}async goto(){throw new Error("Not implemented")}async reload(){throw new Error("Not implemented")}async waitForNavigation(){throw new Error("Not implemented")}async waitForRequest(){throw new Error("Not implemented")}async waitForResponse(){throw new Error("Not implemented")}async waitForNetworkIdle(){throw new Error("Not implemented")}async waitForFrame(){throw new Error("Not implemented")}async goBack(){throw new Error("Not implemented")}async goForward(){throw new Error("Not implemented")}async bringToFront(){throw new Error("Not implemented")}async emulate(e){await Promise.all([this.setUserAgent(e.userAgent),this.setViewport(e.viewport)])}async setJavaScriptEnabled(){throw new Error("Not implemented")}async setBypassCSP(){throw new Error("Not implemented")}async emulateMediaType(){throw new Error("Not implemented")}async emulateCPUThrottling(){throw new Error("Not implemented")}async emulateMediaFeatures(){throw new Error("Not implemented")}async emulateTimezone(){throw new Error("Not implemented")}async emulateIdleState(){throw new Error("Not implemented")}async emulateVisionDeficiency(){throw new Error("Not implemented")}async setViewport(){throw new Error("Not implemented")}viewport(){throw new Error("Not implemented")}async evaluate(){throw new Error("Not implemented")}async evaluateOnNewDocument(){throw new Error("Not implemented")}async setCacheEnabled(){throw new Error("Not implemented")}async screenshot(){throw new Error("Not implemented")}async createPDFStream(){throw new Error("Not implemented")}async pdf(){throw new Error("Not implemented")}async title(){throw new Error("Not implemented")}async close(){throw new Error("Not implemented")}isClosed(){throw new Error("Not implemented")}get mouse(){throw new Error("Not implemented")}click(){throw new Error("Not implemented")}focus(){throw new Error("Not implemented")}hover(){throw new Error("Not implemented")}select(){throw new Error("Not implemented")}tap(){throw new Error("Not implemented")}type(){throw new Error("Not implemented")}waitForTimeout(){throw new Error("Not implemented")}async waitForSelector(){throw new Error("Not implemented")}waitForXPath(){throw new Error("Not implemented")}waitForFunction(){throw new Error("Not implemented")}}rn=new WeakMap,new Set(["Timestamp","Documents","Frames","JSEventListeners","Nodes","LayoutCount","RecalcStyleCount","LayoutDuration","RecalcStyleDuration","ScriptDuration","TaskDuration","JSHeapUsedSize","JSHeapTotalSize"]);var an,cn,dn,ln,hn,fn,un,wn,mn,pn,yn,gn,vn,kn=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},bn=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class En{constructor(e){an.set(this,void 0),kn(this,an,e,"f")}async snapshot(e={}){var t,i;const{interestingOnly:s=!0,root:r=null}=e,{nodes:n}=await bn(this,an,"f").send("Accessibility.getFullAXTree");let o;if(r){const{node:e}=await bn(this,an,"f").send("DOM.describeNode",{objectId:r.remoteObject().objectId});o=e.backendNodeId}const a=Cn.createTree(n);let c=a;if(o&&(c=a.find((e=>e.payload.backendDOMNodeId===o)),!c))return null;if(!s)return null!==(t=this.serializeTree(c)[0])&&void 0!==t?t:null;const d=new Set;return this.collectInterestingNodes(d,a,!1),d.has(c)&&null!==(i=this.serializeTree(c,d)[0])&&void 0!==i?i:null}serializeTree(e,t){const i=[];for(const s of e.children)i.push(...this.serializeTree(s,t));if(t&&!t.has(e))return i;const s=e.serialize();return i.length&&(s.children=i),[s]}collectInterestingNodes(e,t,i){if(t.isInteresting(i)&&e.add(t),!t.isLeafNode()){i=i||t.isControl();for(const s of t.children)this.collectInterestingNodes(e,s,i)}}}an=new WeakMap;class Cn{constructor(e){cn.add(this),this.children=[],dn.set(this,!1),ln.set(this,!1),hn.set(this,!1),fn.set(this,!1),un.set(this,void 0),wn.set(this,void 0),mn.set(this,void 0),pn.set(this,void 0),this.payload=e,kn(this,un,this.payload.name?this.payload.name.value:"","f"),kn(this,wn,this.payload.role?this.payload.role.value:"Unknown","f"),kn(this,mn,this.payload.ignored,"f");for(const e of this.payload.properties||[])"editable"===e.name&&(kn(this,dn,"richtext"===e.value.value,"f"),kn(this,ln,!0,"f")),"focusable"===e.name&&kn(this,hn,e.value.value,"f"),"hidden"===e.name&&kn(this,fn,e.value.value,"f")}find(e){if(e(this))return this;for(const t of this.children){const i=t.find(e);if(i)return i}return null}isLeafNode(){if(!this.children.length)return!0;if(bn(this,cn,"m",yn).call(this)||bn(this,cn,"m",gn).call(this))return!0;switch(bn(this,wn,"f")){case"doc-cover":case"graphics-symbol":case"img":case"Meter":case"scrollbar":case"slider":case"separator":case"progressbar":return!0}return!(bn(this,cn,"m",vn).call(this)||(!bn(this,hn,"f")||!bn(this,un,"f"))&&("heading"!==bn(this,wn,"f")||!bn(this,un,"f")))}isControl(){switch(bn(this,wn,"f")){case"button":case"checkbox":case"ColorWell":case"combobox":case"DisclosureTriangle":case"listbox":case"menu":case"menubar":case"menuitem":case"menuitemcheckbox":case"menuitemradio":case"radio":case"scrollbar":case"searchbox":case"slider":case"spinbutton":case"switch":case"tab":case"textbox":case"tree":case"treeitem":return!0;default:return!1}}isInteresting(e){return"Ignored"!==bn(this,wn,"f")&&!bn(this,fn,"f")&&!bn(this,mn,"f")&&(!(!bn(this,hn,"f")&&!bn(this,dn,"f"))||!!this.isControl()||!e&&this.isLeafNode()&&!!bn(this,un,"f"))}serialize(){const e=new Map;for(const t of this.payload.properties||[])e.set(t.name.toLowerCase(),t.value.value);this.payload.name&&e.set("name",this.payload.name.value),this.payload.value&&e.set("value",this.payload.value.value),this.payload.description&&e.set("description",this.payload.description.value);const t={role:bn(this,wn,"f")},i=["name","value","description","keyshortcuts","roledescription","valuetext"];for(const r of i)e.has(r)&&(t[r]=(s=r,e.get(s)));var s;const r=["disabled","expanded","focused","modal","multiline","multiselectable","readonly","required","selected"],n=t=>e.get(t);for(const e of r)"focused"===e&&"RootWebArea"===bn(this,wn,"f")||n(e)&&(t[e]=n(e));const o=["checked","pressed"];for(const i of o){if(!e.has(i))continue;const s=e.get(i);t[i]="mixed"===s?"mixed":"true"===s}const a=["level","valuemax","valuemin"],c=t=>e.get(t);for(const i of a)e.has(i)&&(t[i]=c(i));const d=["autocomplete","haspopup","invalid","orientation"],l=t=>e.get(t);for(const e of d){const i=l(e);i&&"false"!==i&&(t[e]=l(e))}return t}static createTree(e){const t=new Map;for(const i of e)t.set(i.nodeId,new Cn(i));for(const e of t.values())for(const i of e.payload.childIds||[]){const s=t.get(i);s&&e.children.push(s)}return t.values().next().value}}dn=new WeakMap,ln=new WeakMap,hn=new WeakMap,fn=new WeakMap,un=new WeakMap,wn=new WeakMap,mn=new WeakMap,pn=new WeakMap,cn=new WeakSet,yn=function(){return!(bn(this,dn,"f")||!bn(this,ln,"f")&&"textbox"!==bn(this,wn,"f")&&"searchbox"!==bn(this,wn,"f"))},gn=function(){const e=bn(this,wn,"f");return"LineBreak"===e||"text"===e||"InlineTextBox"===e},vn=function e(){if(void 0===bn(this,pn,"f")){kn(this,pn,!1,"f");for(const t of this.children)if(bn(t,hn,"f")||bn(t,cn,"m",e).call(t)){kn(this,pn,!0,"f");break}}return bn(this,pn,"f")};var Mn,Pn,_n,Tn,In=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},xn=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class Nn{constructor(e,t,i,s){Mn.set(this,void 0),Pn.set(this,void 0),_n.set(this,void 0),Tn.set(this,void 0),In(this,Mn,e,"f"),In(this,Pn,t,"f"),In(this,_n,i,"f"),In(this,Tn,s,"f")}type(){return xn(this,Mn,"f")}text(){return xn(this,Pn,"f")}args(){return xn(this,_n,"f")}location(){var e;return null!==(e=xn(this,Tn,"f")[0])&&void 0!==e?e:{}}stackTrace(){return xn(this,Tn,"f")}}Mn=new WeakMap,Pn=new WeakMap,_n=new WeakMap,Tn=new WeakMap;var Sn,Fn,Wn,Rn,Dn,qn,jn,An,On,Kn,Ln,$n,Bn,Hn,Un,Vn,zn,Gn,Qn,Jn,Xn,Yn,Zn=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},eo=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class to{constructor(e){Sn.set(this,void 0),Fn.set(this,void 0),Zn(this,Sn,new io(e),"f"),Zn(this,Fn,new so(e),"f")}async startJSCoverage(e={}){return await eo(this,Sn,"f").start(e)}async stopJSCoverage(){return await eo(this,Sn,"f").stop()}async startCSSCoverage(e={}){return await eo(this,Fn,"f").start(e)}async stopCSSCoverage(){return await eo(this,Fn,"f").stop()}}Sn=new WeakMap,Fn=new WeakMap;class io{constructor(e){Wn.add(this),Rn.set(this,void 0),Dn.set(this,!1),qn.set(this,new Map),jn.set(this,new Map),An.set(this,[]),On.set(this,!1),Kn.set(this,!1),Ln.set(this,!1),Zn(this,Rn,e,"f")}async start(t={}){e(!eo(this,Dn,"f"),"JSCoverage is already enabled");const{resetOnNavigation:i=!0,reportAnonymousScripts:s=!1,includeRawScriptCoverage:r=!1,useBlockCoverage:n=!0}=t;Zn(this,On,i,"f"),Zn(this,Kn,s,"f"),Zn(this,Ln,r,"f"),Zn(this,Dn,!0,"f"),eo(this,qn,"f").clear(),eo(this,jn,"f").clear(),Zn(this,An,[Ar(eo(this,Rn,"f"),"Debugger.scriptParsed",eo(this,Wn,"m",Bn).bind(this)),Ar(eo(this,Rn,"f"),"Runtime.executionContextsCleared",eo(this,Wn,"m",$n).bind(this))],"f"),await Promise.all([eo(this,Rn,"f").send("Profiler.enable"),eo(this,Rn,"f").send("Profiler.startPreciseCoverage",{callCount:eo(this,Ln,"f"),detailed:n}),eo(this,Rn,"f").send("Debugger.enable"),eo(this,Rn,"f").send("Debugger.setSkipAllPauses",{skip:!0})])}async stop(){e(eo(this,Dn,"f"),"JSCoverage is not enabled"),Zn(this,Dn,!1,"f");const t=await Promise.all([eo(this,Rn,"f").send("Profiler.takePreciseCoverage"),eo(this,Rn,"f").send("Profiler.stopPreciseCoverage"),eo(this,Rn,"f").send("Profiler.disable"),eo(this,Rn,"f").send("Debugger.disable")]);Or(eo(this,An,"f"));const i=[],s=t[0];for(const e of s.result){let t=eo(this,qn,"f").get(e.scriptId);!t&&eo(this,Kn,"f")&&(t="debugger://VM"+e.scriptId);const s=eo(this,jn,"f").get(e.scriptId);if(void 0===s||void 0===t)continue;const r=[];for(const t of e.functions)r.push(...t.ranges);const n=ro(r);eo(this,Ln,"f")?i.push({url:t,ranges:n,text:s,rawScriptCoverage:e}):i.push({url:t,ranges:n,text:s})}return i}}Rn=new WeakMap,Dn=new WeakMap,qn=new WeakMap,jn=new WeakMap,An=new WeakMap,On=new WeakMap,Kn=new WeakMap,Ln=new WeakMap,Wn=new WeakSet,$n=function(){eo(this,On,"f")&&(eo(this,qn,"f").clear(),eo(this,jn,"f").clear())},Bn=async function(e){if("pptr://__puppeteer_evaluation_script__"!==e.url&&(e.url||eo(this,Kn,"f")))try{const t=await eo(this,Rn,"f").send("Debugger.getScriptSource",{scriptId:e.scriptId});eo(this,qn,"f").set(e.scriptId,e.url),eo(this,jn,"f").set(e.scriptId,t.scriptSource)}catch(e){Rr(e)}};class so{constructor(e){Hn.add(this),Un.set(this,void 0),Vn.set(this,!1),zn.set(this,new Map),Gn.set(this,new Map),Qn.set(this,[]),Jn.set(this,!1),Zn(this,Un,e,"f")}async start(t={}){e(!eo(this,Vn,"f"),"CSSCoverage is already enabled");const{resetOnNavigation:i=!0}=t;Zn(this,Jn,i,"f"),Zn(this,Vn,!0,"f"),eo(this,zn,"f").clear(),eo(this,Gn,"f").clear(),Zn(this,Qn,[Ar(eo(this,Un,"f"),"CSS.styleSheetAdded",eo(this,Hn,"m",Yn).bind(this)),Ar(eo(this,Un,"f"),"Runtime.executionContextsCleared",eo(this,Hn,"m",Xn).bind(this))],"f"),await Promise.all([eo(this,Un,"f").send("DOM.enable"),eo(this,Un,"f").send("CSS.enable"),eo(this,Un,"f").send("CSS.startRuleUsageTracking")])}async stop(){e(eo(this,Vn,"f"),"CSSCoverage is not enabled"),Zn(this,Vn,!1,"f");const t=await eo(this,Un,"f").send("CSS.stopRuleUsageTracking");await Promise.all([eo(this,Un,"f").send("CSS.disable"),eo(this,Un,"f").send("DOM.disable")]),Or(eo(this,Qn,"f"));const i=new Map;for(const e of t.ruleUsage){let t=i.get(e.styleSheetId);t||(t=[],i.set(e.styleSheetId,t)),t.push({startOffset:e.startOffset,endOffset:e.endOffset,count:e.used?1:0})}const s=[];for(const t of eo(this,zn,"f").keys()){const r=eo(this,zn,"f").get(t);e(void 0!==r,`Stylesheet URL is undefined (styleSheetId=${t})`);const n=eo(this,Gn,"f").get(t);e(void 0!==n,`Stylesheet text is undefined (styleSheetId=${t})`);const o=ro(i.get(t)||[]);s.push({url:r,ranges:o,text:n})}return s}}function ro(e){const t=[];for(const i of e)t.push({offset:i.startOffset,type:0,range:i}),t.push({offset:i.endOffset,type:1,range:i});t.sort(((e,t)=>{if(e.offset!==t.offset)return e.offset-t.offset;if(e.type!==t.type)return t.type-e.type;const i=e.range.endOffset-e.range.startOffset,s=t.range.endOffset-t.range.startOffset;return 0===e.type?s-i:i-s}));const i=[],s=[];let r=0;for(const e of t){if(i.length&&r<e.offset&&i[i.length-1]>0){const t=s[s.length-1];t&&t.end===r?t.end=e.offset:s.push({start:r,end:e.offset})}r=e.offset,0===e.type?i.push(e.range.count):i.pop()}return s.filter((e=>e.end-e.start>0))}Un=new WeakMap,Vn=new WeakMap,zn=new WeakMap,Gn=new WeakMap,Qn=new WeakMap,Jn=new WeakMap,Hn=new WeakSet,Xn=function(){eo(this,Jn,"f")&&(eo(this,zn,"f").clear(),eo(this,Gn,"f").clear())},Yn=async function(e){const t=e.header;if(t.sourceURL)try{const e=await eo(this,Un,"f").send("CSS.getStyleSheetText",{styleSheetId:t.styleSheetId});eo(this,zn,"f").set(t.styleSheetId,t.sourceURL),eo(this,Gn,"f").set(t.styleSheetId,e.text)}catch(e){Rr(e)}};var no,oo,ao,co,lo,ho=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},fo=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class uo{constructor(e,t,i,s=""){no.set(this,void 0),oo.set(this,void 0),ao.set(this,void 0),co.set(this,void 0),lo.set(this,!1),ho(this,no,e,"f"),ho(this,oo,t,"f"),ho(this,ao,i,"f"),ho(this,co,s,"f")}type(){return fo(this,oo,"f")}message(){return fo(this,ao,"f")}defaultValue(){return fo(this,co,"f")}async accept(t){e(!fo(this,lo,"f"),"Cannot accept dialog which is already handled!"),ho(this,lo,!0,"f"),await fo(this,no,"f").send("Page.handleJavaScriptDialog",{accept:!0,promptText:t})}async dismiss(){e(!fo(this,lo,"f"),"Cannot dismiss dialog which is already handled!"),ho(this,lo,!0,"f"),await fo(this,no,"f").send("Page.handleJavaScriptDialog",{accept:!1})}}no=new WeakMap,oo=new WeakMap,ao=new WeakMap,co=new WeakMap,lo=new WeakMap;var wo,mo,po,yo=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},go=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class vo{constructor(e){wo.set(this,void 0),mo.set(this,!1),po.set(this,!1),yo(this,wo,e,"f")}async emulateViewport(e){const t=e.isMobile||!1,i=e.width,s=e.height,r=e.deviceScaleFactor||1,n=e.isLandscape?{angle:90,type:"landscapePrimary"}:{angle:0,type:"portraitPrimary"},o=e.hasTouch||!1;await Promise.all([go(this,wo,"f").send("Emulation.setDeviceMetricsOverride",{mobile:t,width:i,height:s,deviceScaleFactor:r,screenOrientation:n}),go(this,wo,"f").send("Emulation.setTouchEmulationEnabled",{enabled:o})]);const a=go(this,mo,"f")!==t||go(this,po,"f")!==o;return yo(this,mo,t,"f"),yo(this,po,o,"f"),a}}wo=new WeakMap,mo=new WeakMap,po=new WeakMap;var ko,bo,Eo,Co=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},Mo=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class Po{constructor(e,t){ko.set(this,void 0),bo.set(this,void 0),Eo.set(this,!1),Co(this,ko,e,"f"),Co(this,bo,"selectSingle"!==t.mode,"f")}isMultiple(){return Mo(this,bo,"f")}async accept(t){e(!Mo(this,Eo,"f"),"Cannot accept FileChooser which is already handled!"),Co(this,Eo,!0,"f"),await Mo(this,ko,"f").uploadFile(...t)}cancel(){e(!Mo(this,Eo,"f"),"Cannot cancel FileChooser which is already handled!"),Co(this,Eo,!0,"f")}}ko=new WeakMap,bo=new WeakMap,Eo=new WeakMap;const _o={0:{keyCode:48,key:"0",code:"Digit0"},1:{keyCode:49,key:"1",code:"Digit1"},2:{keyCode:50,key:"2",code:"Digit2"},3:{keyCode:51,key:"3",code:"Digit3"},4:{keyCode:52,key:"4",code:"Digit4"},5:{keyCode:53,key:"5",code:"Digit5"},6:{keyCode:54,key:"6",code:"Digit6"},7:{keyCode:55,key:"7",code:"Digit7"},8:{keyCode:56,key:"8",code:"Digit8"},9:{keyCode:57,key:"9",code:"Digit9"},Power:{key:"Power",code:"Power"},Eject:{key:"Eject",code:"Eject"},Abort:{keyCode:3,code:"Abort",key:"Cancel"},Help:{keyCode:6,code:"Help",key:"Help"},Backspace:{keyCode:8,code:"Backspace",key:"Backspace"},Tab:{keyCode:9,code:"Tab",key:"Tab"},Numpad5:{keyCode:12,shiftKeyCode:101,key:"Clear",code:"Numpad5",shiftKey:"5",location:3},NumpadEnter:{keyCode:13,code:"NumpadEnter",key:"Enter",text:"\r",location:3},Enter:{keyCode:13,code:"Enter",key:"Enter",text:"\r"},"\r":{keyCode:13,code:"Enter",key:"Enter",text:"\r"},"\n":{keyCode:13,code:"Enter",key:"Enter",text:"\r"},ShiftLeft:{keyCode:16,code:"ShiftLeft",key:"Shift",location:1},ShiftRight:{keyCode:16,code:"ShiftRight",key:"Shift",location:2},ControlLeft:{keyCode:17,code:"ControlLeft",key:"Control",location:1},ControlRight:{keyCode:17,code:"ControlRight",key:"Control",location:2},AltLeft:{keyCode:18,code:"AltLeft",key:"Alt",location:1},AltRight:{keyCode:18,code:"AltRight",key:"Alt",location:2},Pause:{keyCode:19,code:"Pause",key:"Pause"},CapsLock:{keyCode:20,code:"CapsLock",key:"CapsLock"},Escape:{keyCode:27,code:"Escape",key:"Escape"},Convert:{keyCode:28,code:"Convert",key:"Convert"},NonConvert:{keyCode:29,code:"NonConvert",key:"NonConvert"},Space:{keyCode:32,code:"Space",key:" "},Numpad9:{keyCode:33,shiftKeyCode:105,key:"PageUp",code:"Numpad9",shiftKey:"9",location:3},PageUp:{keyCode:33,code:"PageUp",key:"PageUp"},Numpad3:{keyCode:34,shiftKeyCode:99,key:"PageDown",code:"Numpad3",shiftKey:"3",location:3},PageDown:{keyCode:34,code:"PageDown",key:"PageDown"},End:{keyCode:35,code:"End",key:"End"},Numpad1:{keyCode:35,shiftKeyCode:97,key:"End",code:"Numpad1",shiftKey:"1",location:3},Home:{keyCode:36,code:"Home",key:"Home"},Numpad7:{keyCode:36,shiftKeyCode:103,key:"Home",code:"Numpad7",shiftKey:"7",location:3},ArrowLeft:{keyCode:37,code:"ArrowLeft",key:"ArrowLeft"},Numpad4:{keyCode:37,shiftKeyCode:100,key:"ArrowLeft",code:"Numpad4",shiftKey:"4",location:3},Numpad8:{keyCode:38,shiftKeyCode:104,key:"ArrowUp",code:"Numpad8",shiftKey:"8",location:3},ArrowUp:{keyCode:38,code:"ArrowUp",key:"ArrowUp"},ArrowRight:{keyCode:39,code:"ArrowRight",key:"ArrowRight"},Numpad6:{keyCode:39,shiftKeyCode:102,key:"ArrowRight",code:"Numpad6",shiftKey:"6",location:3},Numpad2:{keyCode:40,shiftKeyCode:98,key:"ArrowDown",code:"Numpad2",shiftKey:"2",location:3},ArrowDown:{keyCode:40,code:"ArrowDown",key:"ArrowDown"},Select:{keyCode:41,code:"Select",key:"Select"},Open:{keyCode:43,code:"Open",key:"Execute"},PrintScreen:{keyCode:44,code:"PrintScreen",key:"PrintScreen"},Insert:{keyCode:45,code:"Insert",key:"Insert"},Numpad0:{keyCode:45,shiftKeyCode:96,key:"Insert",code:"Numpad0",shiftKey:"0",location:3},Delete:{keyCode:46,code:"Delete",key:"Delete"},NumpadDecimal:{keyCode:46,shiftKeyCode:110,code:"NumpadDecimal",key:"\0",shiftKey:".",location:3},Digit0:{keyCode:48,code:"Digit0",shiftKey:")",key:"0"},Digit1:{keyCode:49,code:"Digit1",shiftKey:"!",key:"1"},Digit2:{keyCode:50,code:"Digit2",shiftKey:"@",key:"2"},Digit3:{keyCode:51,code:"Digit3",shiftKey:"#",key:"3"},Digit4:{keyCode:52,code:"Digit4",shiftKey:"$",key:"4"},Digit5:{keyCode:53,code:"Digit5",shiftKey:"%",key:"5"},Digit6:{keyCode:54,code:"Digit6",shiftKey:"^",key:"6"},Digit7:{keyCode:55,code:"Digit7",shiftKey:"&",key:"7"},Digit8:{keyCode:56,code:"Digit8",shiftKey:"*",key:"8"},Digit9:{keyCode:57,code:"Digit9",shiftKey:"(",key:"9"},KeyA:{keyCode:65,code:"KeyA",shiftKey:"A",key:"a"},KeyB:{keyCode:66,code:"KeyB",shiftKey:"B",key:"b"},KeyC:{keyCode:67,code:"KeyC",shiftKey:"C",key:"c"},KeyD:{keyCode:68,code:"KeyD",shiftKey:"D",key:"d"},KeyE:{keyCode:69,code:"KeyE",shiftKey:"E",key:"e"},KeyF:{keyCode:70,code:"KeyF",shiftKey:"F",key:"f"},KeyG:{keyCode:71,code:"KeyG",shiftKey:"G",key:"g"},KeyH:{keyCode:72,code:"KeyH",shiftKey:"H",key:"h"},KeyI:{keyCode:73,code:"KeyI",shiftKey:"I",key:"i"},KeyJ:{keyCode:74,code:"KeyJ",shiftKey:"J",key:"j"},KeyK:{keyCode:75,code:"KeyK",shiftKey:"K",key:"k"},KeyL:{keyCode:76,code:"KeyL",shiftKey:"L",key:"l"},KeyM:{keyCode:77,code:"KeyM",shiftKey:"M",key:"m"},KeyN:{keyCode:78,code:"KeyN",shiftKey:"N",key:"n"},KeyO:{keyCode:79,code:"KeyO",shiftKey:"O",key:"o"},KeyP:{keyCode:80,code:"KeyP",shiftKey:"P",key:"p"},KeyQ:{keyCode:81,code:"KeyQ",shiftKey:"Q",key:"q"},KeyR:{keyCode:82,code:"KeyR",shiftKey:"R",key:"r"},KeyS:{keyCode:83,code:"KeyS",shiftKey:"S",key:"s"},KeyT:{keyCode:84,code:"KeyT",shiftKey:"T",key:"t"},KeyU:{keyCode:85,code:"KeyU",shiftKey:"U",key:"u"},KeyV:{keyCode:86,code:"KeyV",shiftKey:"V",key:"v"},KeyW:{keyCode:87,code:"KeyW",shiftKey:"W",key:"w"},KeyX:{keyCode:88,code:"KeyX",shiftKey:"X",key:"x"},KeyY:{keyCode:89,code:"KeyY",shiftKey:"Y",key:"y"},KeyZ:{keyCode:90,code:"KeyZ",shiftKey:"Z",key:"z"},MetaLeft:{keyCode:91,code:"MetaLeft",key:"Meta",location:1},MetaRight:{keyCode:92,code:"MetaRight",key:"Meta",location:2},ContextMenu:{keyCode:93,code:"ContextMenu",key:"ContextMenu"},NumpadMultiply:{keyCode:106,code:"NumpadMultiply",key:"*",location:3},NumpadAdd:{keyCode:107,code:"NumpadAdd",key:"+",location:3},NumpadSubtract:{keyCode:109,code:"NumpadSubtract",key:"-",location:3},NumpadDivide:{keyCode:111,code:"NumpadDivide",key:"/",location:3},F1:{keyCode:112,code:"F1",key:"F1"},F2:{keyCode:113,code:"F2",key:"F2"},F3:{keyCode:114,code:"F3",key:"F3"},F4:{keyCode:115,code:"F4",key:"F4"},F5:{keyCode:116,code:"F5",key:"F5"},F6:{keyCode:117,code:"F6",key:"F6"},F7:{keyCode:118,code:"F7",key:"F7"},F8:{keyCode:119,code:"F8",key:"F8"},F9:{keyCode:120,code:"F9",key:"F9"},F10:{keyCode:121,code:"F10",key:"F10"},F11:{keyCode:122,code:"F11",key:"F11"},F12:{keyCode:123,code:"F12",key:"F12"},F13:{keyCode:124,code:"F13",key:"F13"},F14:{keyCode:125,code:"F14",key:"F14"},F15:{keyCode:126,code:"F15",key:"F15"},F16:{keyCode:127,code:"F16",key:"F16"},F17:{keyCode:128,code:"F17",key:"F17"},F18:{keyCode:129,code:"F18",key:"F18"},F19:{keyCode:130,code:"F19",key:"F19"},F20:{keyCode:131,code:"F20",key:"F20"},F21:{keyCode:132,code:"F21",key:"F21"},F22:{keyCode:133,code:"F22",key:"F22"},F23:{keyCode:134,code:"F23",key:"F23"},F24:{keyCode:135,code:"F24",key:"F24"},NumLock:{keyCode:144,code:"NumLock",key:"NumLock"},ScrollLock:{keyCode:145,code:"ScrollLock",key:"ScrollLock"},AudioVolumeMute:{keyCode:173,code:"AudioVolumeMute",key:"AudioVolumeMute"},AudioVolumeDown:{keyCode:174,code:"AudioVolumeDown",key:"AudioVolumeDown"},AudioVolumeUp:{keyCode:175,code:"AudioVolumeUp",key:"AudioVolumeUp"},MediaTrackNext:{keyCode:176,code:"MediaTrackNext",key:"MediaTrackNext"},MediaTrackPrevious:{keyCode:177,code:"MediaTrackPrevious",key:"MediaTrackPrevious"},MediaStop:{keyCode:178,code:"MediaStop",key:"MediaStop"},MediaPlayPause:{keyCode:179,code:"MediaPlayPause",key:"MediaPlayPause"},Semicolon:{keyCode:186,code:"Semicolon",shiftKey:":",key:";"},Equal:{keyCode:187,code:"Equal",shiftKey:"+",key:"="},NumpadEqual:{keyCode:187,code:"NumpadEqual",key:"=",location:3},Comma:{keyCode:188,code:"Comma",shiftKey:"<",key:","},Minus:{keyCode:189,code:"Minus",shiftKey:"_",key:"-"},Period:{keyCode:190,code:"Period",shiftKey:">",key:"."},Slash:{keyCode:191,code:"Slash",shiftKey:"?",key:"/"},Backquote:{keyCode:192,code:"Backquote",shiftKey:"~",key:"`"},BracketLeft:{keyCode:219,code:"BracketLeft",shiftKey:"{",key:"["},Backslash:{keyCode:220,code:"Backslash",shiftKey:"|",key:"\\"},BracketRight:{keyCode:221,code:"BracketRight",shiftKey:"}",key:"]"},Quote:{keyCode:222,code:"Quote",shiftKey:'"',key:"'"},AltGraph:{keyCode:225,code:"AltGraph",key:"AltGraph"},Props:{keyCode:247,code:"Props",key:"CrSel"},Cancel:{keyCode:3,key:"Cancel",code:"Abort"},Clear:{keyCode:12,key:"Clear",code:"Numpad5",location:3},Shift:{keyCode:16,key:"Shift",code:"ShiftLeft",location:1},Control:{keyCode:17,key:"Control",code:"ControlLeft",location:1},Alt:{keyCode:18,key:"Alt",code:"AltLeft",location:1},Accept:{keyCode:30,key:"Accept"},ModeChange:{keyCode:31,key:"ModeChange"}," ":{keyCode:32,key:" ",code:"Space"},Print:{keyCode:42,key:"Print"},Execute:{keyCode:43,key:"Execute",code:"Open"},"\0":{keyCode:46,key:"\0",code:"NumpadDecimal",location:3},a:{keyCode:65,key:"a",code:"KeyA"},b:{keyCode:66,key:"b",code:"KeyB"},c:{keyCode:67,key:"c",code:"KeyC"},d:{keyCode:68,key:"d",code:"KeyD"},e:{keyCode:69,key:"e",code:"KeyE"},f:{keyCode:70,key:"f",code:"KeyF"},g:{keyCode:71,key:"g",code:"KeyG"},h:{keyCode:72,key:"h",code:"KeyH"},i:{keyCode:73,key:"i",code:"KeyI"},j:{keyCode:74,key:"j",code:"KeyJ"},k:{keyCode:75,key:"k",code:"KeyK"},l:{keyCode:76,key:"l",code:"KeyL"},m:{keyCode:77,key:"m",code:"KeyM"},n:{keyCode:78,key:"n",code:"KeyN"},o:{keyCode:79,key:"o",code:"KeyO"},p:{keyCode:80,key:"p",code:"KeyP"},q:{keyCode:81,key:"q",code:"KeyQ"},r:{keyCode:82,key:"r",code:"KeyR"},s:{keyCode:83,key:"s",code:"KeyS"},t:{keyCode:84,key:"t",code:"KeyT"},u:{keyCode:85,key:"u",code:"KeyU"},v:{keyCode:86,key:"v",code:"KeyV"},w:{keyCode:87,key:"w",code:"KeyW"},x:{keyCode:88,key:"x",code:"KeyX"},y:{keyCode:89,key:"y",code:"KeyY"},z:{keyCode:90,key:"z",code:"KeyZ"},Meta:{keyCode:91,key:"Meta",code:"MetaLeft",location:1},"*":{keyCode:106,key:"*",code:"NumpadMultiply",location:3},"+":{keyCode:107,key:"+",code:"NumpadAdd",location:3},"-":{keyCode:109,key:"-",code:"NumpadSubtract",location:3},"/":{keyCode:111,key:"/",code:"NumpadDivide",location:3},";":{keyCode:186,key:";",code:"Semicolon"},"=":{keyCode:187,key:"=",code:"Equal"},",":{keyCode:188,key:",",code:"Comma"},".":{keyCode:190,key:".",code:"Period"},"`":{keyCode:192,key:"`",code:"Backquote"},"[":{keyCode:219,key:"[",code:"BracketLeft"},"\\":{keyCode:220,key:"\\",code:"Backslash"},"]":{keyCode:221,key:"]",code:"BracketRight"},"'":{keyCode:222,key:"'",code:"Quote"},Attn:{keyCode:246,key:"Attn"},CrSel:{keyCode:247,key:"CrSel",code:"Props"},ExSel:{keyCode:248,key:"ExSel"},EraseEof:{keyCode:249,key:"EraseEof"},Play:{keyCode:250,key:"Play"},ZoomOut:{keyCode:251,key:"ZoomOut"},")":{keyCode:48,key:")",code:"Digit0"},"!":{keyCode:49,key:"!",code:"Digit1"},"@":{keyCode:50,key:"@",code:"Digit2"},"#":{keyCode:51,key:"#",code:"Digit3"},$:{keyCode:52,key:"$",code:"Digit4"},"%":{keyCode:53,key:"%",code:"Digit5"},"^":{keyCode:54,key:"^",code:"Digit6"},"&":{keyCode:55,key:"&",code:"Digit7"},"(":{keyCode:57,key:"(",code:"Digit9"},A:{keyCode:65,key:"A",code:"KeyA"},B:{keyCode:66,key:"B",code:"KeyB"},C:{keyCode:67,key:"C",code:"KeyC"},D:{keyCode:68,key:"D",code:"KeyD"},E:{keyCode:69,key:"E",code:"KeyE"},F:{keyCode:70,key:"F",code:"KeyF"},G:{keyCode:71,key:"G",code:"KeyG"},H:{keyCode:72,key:"H",code:"KeyH"},I:{keyCode:73,key:"I",code:"KeyI"},J:{keyCode:74,key:"J",code:"KeyJ"},K:{keyCode:75,key:"K",code:"KeyK"},L:{keyCode:76,key:"L",code:"KeyL"},M:{keyCode:77,key:"M",code:"KeyM"},N:{keyCode:78,key:"N",code:"KeyN"},O:{keyCode:79,key:"O",code:"KeyO"},P:{keyCode:80,key:"P",code:"KeyP"},Q:{keyCode:81,key:"Q",code:"KeyQ"},R:{keyCode:82,key:"R",code:"KeyR"},S:{keyCode:83,key:"S",code:"KeyS"},T:{keyCode:84,key:"T",code:"KeyT"},U:{keyCode:85,key:"U",code:"KeyU"},V:{keyCode:86,key:"V",code:"KeyV"},W:{keyCode:87,key:"W",code:"KeyW"},X:{keyCode:88,key:"X",code:"KeyX"},Y:{keyCode:89,key:"Y",code:"KeyY"},Z:{keyCode:90,key:"Z",code:"KeyZ"},":":{keyCode:186,key:":",code:"Semicolon"},"<":{keyCode:188,key:"<",code:"Comma"},_:{keyCode:189,key:"_",code:"Minus"},">":{keyCode:190,key:">",code:"Period"},"?":{keyCode:191,key:"?",code:"Slash"},"~":{keyCode:192,key:"~",code:"Backquote"},"{":{keyCode:219,key:"{",code:"BracketLeft"},"|":{keyCode:220,key:"|",code:"Backslash"},"}":{keyCode:221,key:"}",code:"BracketRight"},'"':{keyCode:222,key:'"',code:"Quote"},SoftLeft:{key:"SoftLeft",code:"SoftLeft",location:4},SoftRight:{key:"SoftRight",code:"SoftRight",location:4},Camera:{keyCode:44,key:"Camera",code:"Camera",location:4},Call:{key:"Call",code:"Call",location:4},EndCall:{keyCode:95,key:"EndCall",code:"EndCall",location:4},VolumeDown:{keyCode:182,key:"VolumeDown",code:"VolumeDown",location:4},VolumeUp:{keyCode:183,key:"VolumeUp",code:"VolumeUp",location:4}};var To,Io,xo,No,So,Fo,Wo,Ro,Do,qo,jo,Ao,Oo=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},Ko=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class Lo{constructor(e){To.add(this),Io.set(this,void 0),xo.set(this,new Set),this._modifiers=0,Oo(this,Io,e,"f")}async down(e,t={text:void 0,commands:[]}){const i=Ko(this,To,"m",So).call(this,e),s=Ko(this,xo,"f").has(i.code);Ko(this,xo,"f").add(i.code),this._modifiers|=Ko(this,To,"m",No).call(this,i.key);const r=void 0===t.text?i.text:t.text;await Ko(this,Io,"f").send("Input.dispatchKeyEvent",{type:r?"keyDown":"rawKeyDown",modifiers:this._modifiers,windowsVirtualKeyCode:i.keyCode,code:i.code,key:i.key,text:r,unmodifiedText:r,autoRepeat:s,location:i.location,isKeypad:3===i.location,commands:t.commands})}async up(e){const t=Ko(this,To,"m",So).call(this,e);this._modifiers&=~Ko(this,To,"m",No).call(this,t.key),Ko(this,xo,"f").delete(t.code),await Ko(this,Io,"f").send("Input.dispatchKeyEvent",{type:"keyUp",modifiers:this._modifiers,key:t.key,windowsVirtualKeyCode:t.keyCode,code:t.code,location:t.location})}async sendCharacter(e){await Ko(this,Io,"f").send("Input.insertText",{text:e})}charIsKey(e){return!!_o[e]}async type(e,t={}){const i=t.delay||void 0;for(const t of e)this.charIsKey(t)?await this.press(t,{delay:i}):(i&&await new Promise((e=>setTimeout(e,i))),await this.sendCharacter(t))}async press(e,t={}){const{delay:i=null}=t;await this.down(e,t),i&&await new Promise((e=>setTimeout(e,t.delay))),await this.up(e)}}Io=new WeakMap,xo=new WeakMap,To=new WeakSet,No=function(e){return"Alt"===e?1:"Control"===e?2:"Meta"===e?4:"Shift"===e?8:0},So=function(t){const i=8&this._modifiers,s={key:"",keyCode:0,code:"",text:"",location:0},r=_o[t];return e(r,`Unknown key: "${t}"`),r.key&&(s.key=r.key),i&&r.shiftKey&&(s.key=r.shiftKey),r.keyCode&&(s.keyCode=r.keyCode),i&&r.shiftKeyCode&&(s.keyCode=r.shiftKeyCode),r.code&&(s.code=r.code),r.location&&(s.location=r.location),1===s.key.length&&(s.text=s.key),r.text&&(s.text=r.text),i&&r.shiftText&&(s.text=r.shiftText),-9&this._modifiers&&(s.text=""),s};class $o{constructor(e,t){Fo.set(this,void 0),Wo.set(this,void 0),Ro.set(this,0),Do.set(this,0),qo.set(this,"none"),Oo(this,Fo,e,"f"),Oo(this,Wo,t,"f")}async move(e,t,i={}){const{steps:s=1}=i,r=Ko(this,Ro,"f"),n=Ko(this,Do,"f");Oo(this,Ro,e,"f"),Oo(this,Do,t,"f");for(let e=1;e<=s;e++)await Ko(this,Fo,"f").send("Input.dispatchMouseEvent",{type:"mouseMoved",button:Ko(this,qo,"f"),x:r+(Ko(this,Ro,"f")-r)*(e/s),y:n+(Ko(this,Do,"f")-n)*(e/s),modifiers:Ko(this,Wo,"f")._modifiers})}async click(e,t,i={}){const{delay:s=null}=i;null!==s?(await this.move(e,t),await this.down(i),await new Promise((e=>setTimeout(e,s))),await this.up(i)):(await this.move(e,t),await this.down(i),await this.up(i))}async down(e={}){const{button:t="left",clickCount:i=1}=e;Oo(this,qo,t,"f"),await Ko(this,Fo,"f").send("Input.dispatchMouseEvent",{type:"mousePressed",button:t,x:Ko(this,Ro,"f"),y:Ko(this,Do,"f"),modifiers:Ko(this,Wo,"f")._modifiers,clickCount:i})}async up(e={}){const{button:t="left",clickCount:i=1}=e;Oo(this,qo,"none","f"),await Ko(this,Fo,"f").send("Input.dispatchMouseEvent",{type:"mouseReleased",button:t,x:Ko(this,Ro,"f"),y:Ko(this,Do,"f"),modifiers:Ko(this,Wo,"f")._modifiers,clickCount:i})}async wheel(e={}){const{deltaX:t=0,deltaY:i=0}=e;await Ko(this,Fo,"f").send("Input.dispatchMouseEvent",{type:"mouseWheel",x:Ko(this,Ro,"f"),y:Ko(this,Do,"f"),deltaX:t,deltaY:i,modifiers:Ko(this,Wo,"f")._modifiers,pointerType:"mouse"})}async drag(e,t){const i=new Promise((e=>{Ko(this,Fo,"f").once("Input.dragIntercepted",(t=>e(t.data)))}));return await this.move(e.x,e.y),await this.down(),await this.move(t.x,t.y),i}async dragEnter(e,t){await Ko(this,Fo,"f").send("Input.dispatchDragEvent",{type:"dragEnter",x:e.x,y:e.y,modifiers:Ko(this,Wo,"f")._modifiers,data:t})}async dragOver(e,t){await Ko(this,Fo,"f").send("Input.dispatchDragEvent",{type:"dragOver",x:e.x,y:e.y,modifiers:Ko(this,Wo,"f")._modifiers,data:t})}async drop(e,t){await Ko(this,Fo,"f").send("Input.dispatchDragEvent",{type:"drop",x:e.x,y:e.y,modifiers:Ko(this,Wo,"f")._modifiers,data:t})}async dragAndDrop(e,t,i={}){const{delay:s=null}=i,r=await this.drag(e,t);await this.dragEnter(t,r),await this.dragOver(t,r),s&&await new Promise((e=>setTimeout(e,s))),await this.drop(t,r),await this.up()}}Fo=new WeakMap,Wo=new WeakMap,Ro=new WeakMap,Do=new WeakMap,qo=new WeakMap;class Bo{constructor(e,t){jo.set(this,void 0),Ao.set(this,void 0),Oo(this,jo,e,"f"),Oo(this,Ao,t,"f")}async tap(e,t){await this.touchStart(e,t),await this.touchEnd()}async touchStart(e,t){const i=[{x:Math.round(e),y:Math.round(t)}];await Ko(this,jo,"f").send("Input.dispatchTouchEvent",{type:"touchStart",touchPoints:i,modifiers:Ko(this,Ao,"f")._modifiers})}async touchMove(e,t){const i=[{x:Math.round(e),y:Math.round(t)}];await Ko(this,jo,"f").send("Input.dispatchTouchEvent",{type:"touchMove",touchPoints:i,modifiers:Ko(this,Ao,"f")._modifiers})}async touchEnd(){await Ko(this,jo,"f").send("Input.dispatchTouchEvent",{type:"touchEnd",touchPoints:[],modifiers:Ko(this,Ao,"f")._modifiers})}}jo=new WeakMap,Ao=new WeakMap;const Ho={letter:{width:8.5,height:11},legal:{width:8.5,height:14},tabloid:{width:11,height:17},ledger:{width:17,height:11},a0:{width:33.1,height:46.8},a1:{width:23.4,height:33.1},a2:{width:16.54,height:23.4},a3:{width:11.7,height:16.54},a4:{width:8.27,height:11.7},a5:{width:5.83,height:8.27},a6:{width:4.13,height:5.83}};var Uo,Vo,zo=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},Go=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class Qo{constructor(){Uo.set(this,void 0),Vo.set(this,void 0),zo(this,Uo,null,"f"),zo(this,Vo,null,"f")}setDefaultTimeout(e){zo(this,Uo,e,"f")}setDefaultNavigationTimeout(e){zo(this,Vo,e,"f")}navigationTimeout(){return null!==Go(this,Vo,"f")?Go(this,Vo,"f"):null!==Go(this,Uo,"f")?Go(this,Uo,"f"):3e4}timeout(){return null!==Go(this,Uo,"f")?Go(this,Uo,"f"):3e4}}Uo=new WeakMap,Vo=new WeakMap;var Jo,Xo,Yo,Zo=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},ea=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class ta{constructor(e){Jo.set(this,void 0),Xo.set(this,!1),Yo.set(this,void 0),Zo(this,Jo,e,"f")}async start(t={}){e(!ea(this,Xo,"f"),"Cannot start recording trace while already recording trace.");const i=["-*","devtools.timeline","v8.execute","disabled-by-default-devtools.timeline","disabled-by-default-devtools.timeline.frame","toplevel","blink.console","blink.user_timing","latencyInfo","disabled-by-default-devtools.timeline.stack","disabled-by-default-v8.cpu_profiler"],{path:s,screenshots:r=!1,categories:n=i}=t;r&&n.push("disabled-by-default-devtools.screenshot");const o=n.filter((e=>e.startsWith("-"))).map((e=>e.slice(1))),a=n.filter((e=>!e.startsWith("-")));Zo(this,Yo,s,"f"),Zo(this,Xo,!0,"f"),await ea(this,Jo,"f").send("Tracing.start",{transferMode:"ReturnAsStream",traceConfig:{excludedCategories:o,includedCategories:a}})}async stop(){let e,t;const i=new Promise(((i,s)=>{e=i,t=s}));return ea(this,Jo,"f").once("Tracing.tracingComplete",(async i=>{try{const t=await Qr(ea(this,Jo,"f"),i.stream),s=await Gr(t,ea(this,Yo,"f"));e(null!=s?s:void 0)}catch(e){L(e)?t(e):t(new Error(`Unknown error: ${e}`))}})),await ea(this,Jo,"f").send("Tracing.end"),Zo(this,Xo,!1,"f"),i}}Jo=new WeakMap,Xo=new WeakMap,Yo=new WeakMap;var ia,sa,ra,na,oa,aa,ca,da,la,ha,fa,ua,wa,ma,pa,ya,ga,va,ka,ba,Ea,Ca,Ma,Pa,_a,Ta,Ia,xa,Na,Sa,Fa,Wa,Ra,Da,qa,ja,Aa,Oa,Ka,La=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)},$a=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i};class Ba extends on{static async _create(e,t,i,s,r){const n=new Ba(e,t,i,r);if(await La(n,ia,"m",Pa).call(n),s)try{await n.setViewport(s)}catch(e){if(!L(e)||!K(e))throw e;Rr(e)}return n}constructor(t,i,s,r){super(),ia.add(this),sa.set(this,!1),ra.set(this,void 0),na.set(this,void 0),oa.set(this,void 0),aa.set(this,void 0),ca.set(this,new Qo),da.set(this,void 0),la.set(this,void 0),ha.set(this,void 0),fa.set(this,void 0),ua.set(this,void 0),wa.set(this,new Map),ma.set(this,void 0),pa.set(this,!0),ya.set(this,void 0),ga.set(this,void 0),va.set(this,new Map),ka.set(this,new Set),ba.set(this,void 0),Ea.set(this,!1),Ca.set(this,(e=>{var t;const i=null===(t=e._session())||void 0===t?void 0:t.id(),s=La(this,va,"f").get(i);s&&(La(this,va,"f").delete(i),this.emit("workerdestroyed",s))})),Ma.set(this,(t=>{if(La(this,ha,"f").onAttachedToTarget(t),"worker"===t._getTargetInfo().type){const i=t._session();e(i);const s=new sn(i,t.url(),La(this,ia,"m",Ra).bind(this),La(this,ia,"m",Sa).bind(this));La(this,va,"f").set(i.id(),s),this.emit("workercreated",s)}t._session()&&La(this,na,"f")._targetManager().addTargetInterceptor(t._session(),La(this,Ma,"f"))})),$a(this,ra,t,"f"),$a(this,na,i,"f"),$a(this,oa,new Lo(t),"f"),$a(this,aa,new $o(t,La(this,oa,"f")),"f"),$a(this,da,new Bo(t,La(this,oa,"f")),"f"),$a(this,la,new En(t),"f"),$a(this,ha,new qi(t,this,s,La(this,ca,"f")),"f"),$a(this,fa,new vo(t),"f"),$a(this,ua,new ta(t),"f"),$a(this,ma,new to(t),"f"),$a(this,ga,r,"f"),$a(this,ya,null,"f"),La(this,na,"f")._targetManager().addTargetInterceptor(La(this,ra,"f"),La(this,Ma,"f")),La(this,na,"f")._targetManager().on("targetGone",La(this,Ca,"f")),La(this,ha,"f").on(Di.FrameAttached,(e=>this.emit("frameattached",e))),La(this,ha,"f").on(Di.FrameDetached,(e=>this.emit("framedetached",e))),La(this,ha,"f").on(Di.FrameNavigated,(e=>this.emit("framenavigated",e)));const n=La(this,ha,"f").networkManager;n.on(li.Request,(e=>this.emit("request",e))),n.on(li.RequestServedFromCache,(e=>this.emit("requestservedfromcache",e))),n.on(li.Response,(e=>this.emit("response",e))),n.on(li.RequestFailed,(e=>this.emit("requestfailed",e))),n.on(li.RequestFinished,(e=>this.emit("requestfinished",e))),t.on("Page.domContentEventFired",(()=>this.emit("domcontentloaded"))),t.on("Page.loadEventFired",(()=>this.emit("load"))),t.on("Runtime.consoleAPICalled",(e=>La(this,ia,"m",Fa).call(this,e))),t.on("Runtime.bindingCalled",(e=>La(this,ia,"m",Wa).call(this,e))),t.on("Page.javascriptDialogOpening",(e=>La(this,ia,"m",Da).call(this,e))),t.on("Runtime.exceptionThrown",(e=>La(this,ia,"m",Sa).call(this,e.exceptionDetails))),t.on("Inspector.targetCrashed",(()=>La(this,ia,"m",Ta).call(this))),t.on("Performance.metrics",(e=>La(this,ia,"m",xa).call(this,e))),t.on("Log.entryAdded",(e=>La(this,ia,"m",Ia).call(this,e))),t.on("Page.fileChooserOpened",(e=>La(this,ia,"m",_a).call(this,e))),La(this,na,"f")._isClosedPromise.then((()=>{La(this,na,"f")._targetManager().removeTargetInterceptor(La(this,ra,"f"),La(this,Ma,"f")),La(this,na,"f")._targetManager().off("targetGone",La(this,Ca,"f")),this.emit("close"),$a(this,sa,!0,"f")}))}_client(){return La(this,ra,"f")}isDragInterceptionEnabled(){return La(this,Ea,"f")}isJavaScriptEnabled(){return La(this,pa,"f")}waitForFileChooser(e={}){const t=0===La(this,ka,"f").size,{timeout:i=La(this,ca,"f").timeout()}=e,s=Z({message:`Waiting for \`FileChooser\` failed: ${i}ms exceeded`,timeout:i});let r;return La(this,ka,"f").add(s),t&&(r=La(this,ra,"f").send("Page.setInterceptFileChooserDialog",{enabled:!0})),Promise.all([s,r]).then((([e])=>e)).catch((e=>{throw La(this,ka,"f").delete(s),e}))}async setGeolocation(e){const{longitude:t,latitude:i,accuracy:s=0}=e;if(t<-180||t>180)throw new Error(`Invalid longitude "${t}": precondition -180 <= LONGITUDE <= 180 failed.`);if(i<-90||i>90)throw new Error(`Invalid latitude "${i}": precondition -90 <= LATITUDE <= 90 failed.`);if(s<0)throw new Error(`Invalid accuracy "${s}": precondition 0 <= ACCURACY failed.`);await La(this,ra,"f").send("Emulation.setGeolocationOverride",{longitude:t,latitude:i,accuracy:s})}target(){return La(this,na,"f")}browser(){return La(this,na,"f").browser()}browserContext(){return La(this,na,"f").browserContext()}mainFrame(){return La(this,ha,"f").mainFrame()}get keyboard(){return La(this,oa,"f")}get touchscreen(){return La(this,da,"f")}get coverage(){return La(this,ma,"f")}get tracing(){return La(this,ua,"f")}get accessibility(){return La(this,la,"f")}frames(){return La(this,ha,"f").frames()}workers(){return Array.from(La(this,va,"f").values())}async setRequestInterception(e){return La(this,ha,"f").networkManager.setRequestInterception(e)}async setDragInterception(e){return $a(this,Ea,e,"f"),La(this,ra,"f").send("Input.setInterceptDrags",{enabled:e})}setOfflineMode(e){return La(this,ha,"f").networkManager.setOfflineMode(e)}emulateNetworkConditions(e){return La(this,ha,"f").networkManager.emulateNetworkConditions(e)}setDefaultNavigationTimeout(e){La(this,ca,"f").setDefaultNavigationTimeout(e)}setDefaultTimeout(e){La(this,ca,"f").setDefaultTimeout(e)}getDefaultTimeout(){return La(this,ca,"f").timeout()}async $(e){return this.mainFrame().$(e)}async $$(e){return this.mainFrame().$$(e)}async evaluateHandle(e,...t){return(await this.mainFrame().executionContext()).evaluateHandle(e,...t)}async queryObjects(t){const i=await this.mainFrame().executionContext();e(!t.disposed,"Prototype JSHandle is disposed!");const s=t.remoteObject();e(s.objectId,"Prototype JSHandle must not be referencing primitive value");const r=await i._client.send("Runtime.queryObjects",{prototypeObjectId:s.objectId});return $r(i,r.objects)}async $eval(e,t,...i){return this.mainFrame().$eval(e,t,...i)}async $$eval(e,t,...i){return this.mainFrame().$$eval(e,t,...i)}async $x(e){return this.mainFrame().$x(e)}async cookies(...e){const t=(await La(this,ra,"f").send("Network.getCookies",{urls:e.length?e:[this.url()]})).cookies,i=["priority"];return t.map((e=>{for(const t of i)delete e[t];return e}))}async deleteCookie(...e){const t=this.url();for(const i of e){const e=Object.assign({},i);!i.url&&t.startsWith("http")&&(e.url=t),await La(this,ra,"f").send("Network.deleteCookies",e)}}async setCookie(...t){const i=this.url(),s=i.startsWith("http"),r=t.map((t=>{const r=Object.assign({},t);return!r.url&&s&&(r.url=i),e("about:blank"!==r.url,`Blank page can not have cookie "${r.name}"`),e(!String.prototype.startsWith.call(r.url||"","data:"),`Data URL page can not have cookie "${r.name}"`),r}));await this.deleteCookie(...r),r.length&&await La(this,ra,"f").send("Network.setCookies",{cookies:r})}async addScriptTag(e){return this.mainFrame().addScriptTag(e)}async addStyleTag(e){return this.mainFrame().addStyleTag(e)}async exposeFunction(e,t){if(La(this,wa,"f").has(e))throw new Error(`Failed to add page binding with name ${e}: window['${e}'] already exists!`);let i;i="function"==typeof t?t:t.default,La(this,wa,"f").set(e,i);const s=Hr("exposedFun",e);await La(this,ra,"f").send("Runtime.addBinding",{name:e}),await La(this,ra,"f").send("Page.addScriptToEvaluateOnNewDocument",{source:s}),await Promise.all(this.frames().map((e=>e.evaluate(s).catch(Rr))))}async authenticate(e){return La(this,ha,"f").networkManager.authenticate(e)}async setExtraHTTPHeaders(e){return La(this,ha,"f").networkManager.setExtraHTTPHeaders(e)}async setUserAgent(e,t){return La(this,ha,"f").networkManager.setUserAgent(e,t)}async metrics(){const e=await La(this,ra,"f").send("Performance.getMetrics");return La(this,ia,"m",Na).call(this,e.metrics)}url(){return this.mainFrame().url()}async content(){return await La(this,ha,"f").mainFrame().content()}async setContent(e,t={}){await La(this,ha,"f").mainFrame().setContent(e,t)}async goto(e,t={}){return await La(this,ha,"f").mainFrame().goto(e,t)}async reload(e){return(await Promise.all([this.waitForNavigation(e),La(this,ra,"f").send("Page.reload")]))[0]}async waitForNavigation(e={}){return await La(this,ha,"f").mainFrame().waitForNavigation(e)}async waitForRequest(e,t={}){const{timeout:i=La(this,ca,"f").timeout()}=t;return Lr(La(this,ha,"f").networkManager,li.Request,(async t=>Kr(e)?e===t.url():"function"==typeof e&&!!await e(t)),i,La(this,ia,"m",Aa).call(this))}async waitForResponse(e,t={}){const{timeout:i=La(this,ca,"f").timeout()}=t;return Lr(La(this,ha,"f").networkManager,li.Response,(async t=>Kr(e)?e===t.url():"function"==typeof e&&!!await e(t)),i,La(this,ia,"m",Aa).call(this))}async waitForNetworkIdle(e={}){const{idleTime:t=500,timeout:i=La(this,ca,"f").timeout()}=e,s=La(this,ha,"f").networkManager;let r;const n=new Promise((e=>{r=e}));let o;const a=new Promise(((e,t)=>{o=t}));let c;const d=()=>r(),l=()=>{c&&clearTimeout(c),o(new Error("abort"))},h=()=>{c&&clearTimeout(c),0===s.numRequestsInProgress()&&(c=setTimeout(d,t))};h();const f=()=>(h(),!1),u=e=>Lr(s,e,f,i,a),w=[u(li.Request),u(li.Response)];await Promise.race([n,...w,La(this,ia,"m",Aa).call(this)]).then((e=>(l(),e)),(e=>{throw l(),e}))}async waitForFrame(e,t={}){const{timeout:i=La(this,ca,"f").timeout()}=t;let s;s=Kr(e)?t=>Promise.resolve(e===t.url()):t=>{const i=e(t);return"boolean"==typeof i?Promise.resolve(i):i};const r=Promise.race([Lr(La(this,ha,"f"),Di.FrameAttached,s,i,La(this,ia,"m",Aa).call(this)),Lr(La(this,ha,"f"),Di.FrameNavigated,s,i,La(this,ia,"m",Aa).call(this)),...this.frames().map((async e=>await s(e)?e:await r))]);return r}async goBack(e={}){return La(this,ia,"m",Oa).call(this,-1,e)}async goForward(e={}){return La(this,ia,"m",Oa).call(this,1,e)}async bringToFront(){await La(this,ra,"f").send("Page.bringToFront")}async setJavaScriptEnabled(e){La(this,pa,"f")!==e&&($a(this,pa,e,"f"),await La(this,ra,"f").send("Emulation.setScriptExecutionDisabled",{value:!e}))}async setBypassCSP(e){await La(this,ra,"f").send("Page.setBypassCSP",{enabled:e})}async emulateMediaType(t){e("screen"===t||"print"===t||void 0===(null!=t?t:void 0),"Unsupported media type: "+t),await La(this,ra,"f").send("Emulation.setEmulatedMedia",{media:t||""})}async emulateCPUThrottling(t){e(null===t||t>=1,"Throttling rate should be greater or equal to 1"),await La(this,ra,"f").send("Emulation.setCPUThrottlingRate",{rate:null!==t?t:1})}async emulateMediaFeatures(t){if(t||await La(this,ra,"f").send("Emulation.setEmulatedMedia",{}),Array.isArray(t)){for(const i of t){const t=i.name;e(/^(?:prefers-(?:color-scheme|reduced-motion)|color-gamut)$/.test(t),"Unsupported media feature: "+t)}await La(this,ra,"f").send("Emulation.setEmulatedMedia",{features:t})}}async emulateTimezone(e){try{await La(this,ra,"f").send("Emulation.setTimezoneOverride",{timezoneId:e||""})}catch(t){if(L(t)&&t.message.includes("Invalid timezone"))throw new Error(`Invalid timezone ID: ${e}`);throw t}}async emulateIdleState(e){e?await La(this,ra,"f").send("Emulation.setIdleOverride",{isUserActive:e.isUserActive,isScreenUnlocked:e.isScreenUnlocked}):await La(this,ra,"f").send("Emulation.clearIdleOverride")}async emulateVisionDeficiency(t){const i=new Set(["none","achromatopsia","blurredVision","deuteranopia","protanopia","tritanopia"]);try{e(!t||i.has(t),`Unsupported vision deficiency: ${t}`),await La(this,ra,"f").send("Emulation.setEmulatedVisionDeficiency",{type:t||"none"})}catch(e){throw e}}async setViewport(e){const t=await La(this,fa,"f").emulateViewport(e);$a(this,ya,e,"f"),t&&await this.reload()}viewport(){return La(this,ya,"f")}async evaluate(e,...t){return La(this,ha,"f").mainFrame().evaluate(e,...t)}async evaluateOnNewDocument(e,...t){const i=Br(e,...t);await La(this,ra,"f").send("Page.addScriptToEvaluateOnNewDocument",{source:i})}async setCacheEnabled(e=!0){await La(this,ha,"f").networkManager.setCacheEnabled(e)}async screenshot(t={}){let i="png";if(t.type)i=t.type;else if(t.path){const e=t.path,s=e.slice(e.lastIndexOf(".")+1).toLowerCase();switch(s){case"png":i="png";break;case"jpeg":case"jpg":i="jpeg";break;case"webp":i="webp";break;default:throw new Error(`Unsupported screenshot type for extension \`.${s}\``)}}return t.quality&&(e("jpeg"===i||"webp"===i,"options.quality is unsupported for the "+i+" screenshots"),e("number"==typeof t.quality,"Expected options.quality to be a number but found "+typeof t.quality),e(Number.isInteger(t.quality),"Expected options.quality to be an integer"),e(t.quality>=0&&t.quality<=100,"Expected options.quality to be between 0 and 100 (inclusive), got "+t.quality)),e(!t.clip||!t.fullPage,"options.clip and options.fullPage are exclusive"),t.clip&&(e("number"==typeof t.clip.x,"Expected options.clip.x to be a number but found "+typeof t.clip.x),e("number"==typeof t.clip.y,"Expected options.clip.y to be a number but found "+typeof t.clip.y),e("number"==typeof t.clip.width,"Expected options.clip.width to be a number but found "+typeof t.clip.width),e("number"==typeof t.clip.height,"Expected options.clip.height to be a number but found "+typeof t.clip.height),e(0!==t.clip.width,"Expected options.clip.width not to be 0."),e(0!==t.clip.height,"Expected options.clip.height not to be 0.")),La(this,ga,"f").postTask((()=>La(this,ia,"m",Ka).call(this,i,t)))}async createPDFStream(t={}){const{scale:i=1,displayHeaderFooter:s=!1,headerTemplate:r="",footerTemplate:n="",printBackground:o=!1,landscape:a=!1,pageRanges:c="",preferCSSPageSize:d=!1,margin:l={},omitBackground:h=!1,timeout:f=3e4}=t;let u=8.5,w=11;if(t.format){const i=Ho[t.format.toLowerCase()];e(i,"Unknown paper format: "+t.format),u=i.width,w=i.height}else u=Va(t.width)||u,w=Va(t.height)||w;const m=Va(l.top)||0,p=Va(l.left)||0,y=Va(l.bottom)||0,g=Va(l.right)||0;h&&await La(this,ia,"m",ja).call(this);const v=La(this,ra,"f").send("Page.printToPDF",{transferMode:"ReturnAsStream",landscape:a,displayHeaderFooter:s,headerTemplate:r,footerTemplate:n,printBackground:o,scale:i,paperWidth:u,paperHeight:w,marginTop:m,marginBottom:y,marginLeft:p,marginRight:g,pageRanges:c,preferCSSPageSize:d}),k=await Ur(v,"Page.printToPDF",f);return h&&await La(this,ia,"m",qa).call(this),e(k.stream,"`stream` is missing from `Page.printToPDF"),Qr(La(this,ra,"f"),k.stream)}async pdf(t={}){const{path:i}=t,s=await this.createPDFStream(t),r=await Gr(s,i);return e(r,"Could not create buffer"),r}async title(){return this.mainFrame().title()}async close(t={runBeforeUnload:void 0}){const i=La(this,ra,"f").connection();e(i,"Protocol error: Connection closed. Most likely the page has been closed."),t.runBeforeUnload?await La(this,ra,"f").send("Page.close"):(await i.send("Target.closeTarget",{targetId:La(this,na,"f")._targetId}),await La(this,na,"f")._isClosedPromise)}isClosed(){return La(this,sa,"f")}get mouse(){return La(this,aa,"f")}click(e,t={}){return this.mainFrame().click(e,t)}focus(e){return this.mainFrame().focus(e)}hover(e){return this.mainFrame().hover(e)}select(e,...t){return this.mainFrame().select(e,...t)}tap(e){return this.mainFrame().tap(e)}type(e,t,i){return this.mainFrame().type(e,t,i)}waitForTimeout(e){return this.mainFrame().waitForTimeout(e)}async waitForSelector(e,t={}){return await this.mainFrame().waitForSelector(e,t)}waitForXPath(e,t={}){return this.mainFrame().waitForXPath(e,t)}waitForFunction(e,t={},...i){return this.mainFrame().waitForFunction(e,t,...i)}}sa=new WeakMap,ra=new WeakMap,na=new WeakMap,oa=new WeakMap,aa=new WeakMap,ca=new WeakMap,da=new WeakMap,la=new WeakMap,ha=new WeakMap,fa=new WeakMap,ua=new WeakMap,wa=new WeakMap,ma=new WeakMap,pa=new WeakMap,ya=new WeakMap,ga=new WeakMap,va=new WeakMap,ka=new WeakMap,ba=new WeakMap,Ea=new WeakMap,Ca=new WeakMap,Ma=new WeakMap,ia=new WeakSet,Pa=async function(){try{await Promise.all([La(this,ha,"f").initialize(),La(this,ra,"f").send("Performance.enable"),La(this,ra,"f").send("Log.enable")])}catch(e){if(!L(e)||!K(e))throw e;Rr(e)}},_a=async function(t){if(!La(this,ka,"f").size)return;const i=La(this,ha,"f").frame(t.frameId);e(i,"This should never happen.");const s=await i.worlds[$].adoptBackendNode(t.backendNodeId),r=new Po(s,t);for(const e of La(this,ka,"f"))e.resolve(r);La(this,ka,"f").clear()},Ta=function(){this.emit("error",new Error("Page crashed!"))},Ia=function(e){const{level:t,text:i,args:s,source:r,url:n,lineNumber:o}=e.entry;s&&s.map((e=>jr(La(this,ra,"f"),e))),"worker"!==r&&this.emit("console",new Nn(t,i,[],[{url:n,lineNumber:o}]))},xa=function(e){this.emit("metrics",{title:e.title,metrics:La(this,ia,"m",Na).call(this,e.metrics)})},Na=function(e){const t={};for(const i of e||[])Ha.has(i.name)&&(t[i.name]=i.value);return t},Sa=function(e){const t=Dr(e),i=new Error(t);i.stack="",this.emit("pageerror",i)},Fa=async function(e){if(0===e.executionContextId)return;const t=La(this,ha,"f").getExecutionContextById(e.executionContextId,La(this,ra,"f"));if(!t)return void Rr(new Error(`ExecutionContext not found for a console message: ${JSON.stringify(e)}`));const i=e.args.map((e=>$r(t,e)));La(this,ia,"m",Ra).call(this,e.type,i,e.stackTrace)},Wa=async function(t){let i;try{i=JSON.parse(t.payload)}catch{return}const{type:s,name:r,seq:n,args:o}=i;if("exposedFun"!==s||!La(this,wa,"f").has(r))return;let a=null;try{const t=La(this,wa,"f").get(r);e(t),a=function(e,t,i){return Br((function(e,t,i){window[e].callbacks.get(t).resolve(i),window[e].callbacks.delete(t)}),e,t,i)}(r,n,await t(...o))}catch(e){a=L(e)?function(e,t,i,s){return Br((function(e,t,i,s){const r=new Error(i);r.stack=s,window[e].callbacks.get(t).reject(r),window[e].callbacks.delete(t)}),e,t,i,s)}(r,n,e.message,e.stack):function(e,t,i){return Br((function(e,t,i){window[e].callbacks.get(t).reject(i),window[e].callbacks.delete(t)}),e,t,i)}(r,n,e)}La(this,ra,"f").send("Runtime.evaluate",{expression:a,contextId:t.executionContextId}).catch(Rr)},Ra=function(e,t,i){if(!this.listenerCount("console"))return void t.forEach((e=>e.dispose()));const s=[];for(const e of t){const t=e.remoteObject();t.objectId?s.push(e.toString()):s.push(qr(t))}const r=[];if(i)for(const e of i.callFrames)r.push({url:e.url,lineNumber:e.lineNumber,columnNumber:e.columnNumber});const n=new Nn(e,s.join(" "),t,r);this.emit("console",n)},Da=function(t){let i=null;new Set(["alert","confirm","prompt","beforeunload"]).has(t.type)&&(i=t.type),e(i,"Unknown javascript dialog type: "+t.type);const s=new uo(La(this,ra,"f"),i,t.message,t.defaultPrompt);this.emit("dialog",s)},qa=async function(){await La(this,ra,"f").send("Emulation.setDefaultBackgroundColorOverride")},ja=async function(){await La(this,ra,"f").send("Emulation.setDefaultBackgroundColorOverride",{color:{r:0,g:0,b:0,a:0}})},Aa=function(){return La(this,ba,"f")||$a(this,ba,new Promise((e=>La(this,ra,"f").once(D.Disconnected,(()=>e(new Error("Target closed")))))),"f"),La(this,ba,"f")},Oa=async function(e,t){const i=await La(this,ra,"f").send("Page.getNavigationHistory"),s=i.entries[i.currentIndex+e];return s?(await Promise.all([this.waitForNavigation(t),La(this,ra,"f").send("Page.navigateToHistoryEntry",{entryId:s.id})]))[0]:null},Ka=async function(e,t={}){var i,s;await La(this,ra,"f").send("Target.activateTarget",{targetId:La(this,na,"f")._targetId});let r=t.clip?function(e){const t=Math.round(e.x),i=Math.round(e.y);return{x:t,y:i,width:Math.round(e.width+e.x-t),height:Math.round(e.height+e.y-i),scale:e.scale}}(t.clip):void 0,n=null===(i=t.captureBeyondViewport)||void 0===i||i;const o=t.fromSurface;if(t.fullPage){if(r=void 0,!n){const e=await La(this,ra,"f").send("Page.getLayoutMetrics"),{width:t,height:i}=e.cssContentSize||e.contentSize,{isMobile:s=!1,deviceScaleFactor:r=1,isLandscape:n=!1}=La(this,ya,"f")||{},o=n?{angle:90,type:"landscapePrimary"}:{angle:0,type:"portraitPrimary"};await La(this,ra,"f").send("Emulation.setDeviceMetricsOverride",{mobile:s,width:t,height:i,deviceScaleFactor:r,screenOrientation:o})}}else r||(n=!1);const a=t.omitBackground&&("png"===e||"webp"===e);a&&await La(this,ia,"m",ja).call(this);const c=await La(this,ra,"f").send("Page.captureScreenshot",{format:e,quality:t.quality,clip:r&&{...r,scale:null!==(s=r.scale)&&void 0!==s?s:1},captureBeyondViewport:n,fromSurface:o});a&&await La(this,ia,"m",qa).call(this),t.fullPage&&La(this,ya,"f")&&await this.setViewport(La(this,ya,"f"));const d="base64"===t.encoding?c.data:Buffer.from(c.data,"base64");if(t.path)try{const e=(await zr()).promises;await e.writeFile(t.path,d)}catch(e){if(e instanceof TypeError)throw new Error("Screenshots can only be written to a file path in a Node-like environment.");throw e}return d};const Ha=new Set(["Timestamp","Documents","Frames","JSEventListeners","Nodes","LayoutCount","RecalcStyleCount","LayoutDuration","RecalcStyleDuration","ScriptDuration","TaskDuration","JSHeapUsedSize","JSHeapTotalSize"]),Ua={px:1,in:96,cm:37.8,mm:3.78};function Va(t){if(void 0===t)return;let i;if("number"==typeof(s=t)||s instanceof Number)i=t;else{if(!Kr(t))throw new Error("page.pdf() Cannot handle parameter type: "+typeof t);{const s=t;let r=s.substring(s.length-2).toLowerCase(),n="";r in Ua?n=s.substring(0,s.length-2):(r="px",n=s);const o=Number(n);e(!isNaN(o),"Failed to parse parameter value: "+s),i=o*Ua[r]}}var s;return i/96}var za,Ga,Qa,Ja,Xa,Ya,Za,ec,tc,ic,sc=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},rc=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class nc{constructor(e,t,i,s,r,n,o,a,c){za.set(this,void 0),Ga.set(this,void 0),Qa.set(this,void 0),Ja.set(this,void 0),Xa.set(this,void 0),Ya.set(this,void 0),Za.set(this,void 0),ec.set(this,void 0),tc.set(this,void 0),ic.set(this,void 0),sc(this,Ga,t,"f"),sc(this,ic,s,"f"),sc(this,Qa,e,"f"),sc(this,za,i,"f"),this._targetId=e.targetId,sc(this,Ja,r,"f"),sc(this,Xa,n,"f"),sc(this,Ya,null!=o?o:void 0,"f"),sc(this,tc,a,"f"),this._isPageTargetCallback=c,this._initializedPromise=new Promise((e=>this._initializedCallback=e)).then((async e=>{if(!e)return!1;const t=this.opener();if(!t||!rc(t,Za,"f")||"page"!==this.type())return!0;const i=await rc(t,Za,"f");if(!i.listenerCount("popup"))return!0;const s=await this.page();return i.emit("popup",s),!0})),this._isClosedPromise=new Promise((e=>this._closedCallback=e)),this._isInitialized=!this._isPageTargetCallback(rc(this,Qa,"f"))||""!==rc(this,Qa,"f").url,this._isInitialized&&this._initializedCallback(!0)}_session(){return rc(this,Ga,"f")}createCDPSession(){return rc(this,Ja,"f").call(this,!1)}_targetManager(){return rc(this,ic,"f")}_getTargetInfo(){return rc(this,Qa,"f")}async page(){var e;return this._isPageTargetCallback(rc(this,Qa,"f"))&&!rc(this,Za,"f")&&sc(this,Za,(rc(this,Ga,"f")?Promise.resolve(rc(this,Ga,"f")):rc(this,Ja,"f").call(this,!0)).then((e=>{var t;return Ba._create(e,this,rc(this,Xa,"f"),null!==(t=rc(this,Ya,"f"))&&void 0!==t?t:null,rc(this,tc,"f"))})),"f"),null!==(e=await rc(this,Za,"f"))&&void 0!==e?e:null}async worker(){return"service_worker"!==rc(this,Qa,"f").type&&"shared_worker"!==rc(this,Qa,"f").type?null:(rc(this,ec,"f")||sc(this,ec,(rc(this,Ga,"f")?Promise.resolve(rc(this,Ga,"f")):rc(this,Ja,"f").call(this,!1)).then((e=>new sn(e,rc(this,Qa,"f").url,(()=>{}),(()=>{})))),"f"),rc(this,ec,"f"))}url(){return rc(this,Qa,"f").url}type(){const e=rc(this,Qa,"f").type;return"page"===e||"background_page"===e||"service_worker"===e||"shared_worker"===e||"browser"===e||"webview"===e?e:"other"}browser(){return rc(this,za,"f").browser()}browserContext(){return rc(this,za,"f")}opener(){const{openerId:e}=rc(this,Qa,"f");if(e)return this.browser()._targets.get(e)}_targetInfoChanged(e){if(sc(this,Qa,e,"f"),!(this._isInitialized||this._isPageTargetCallback(rc(this,Qa,"f"))&&""===rc(this,Qa,"f").url))return this._isInitialized=!0,void this._initializedCallback(!0)}}za=new WeakMap,Ga=new WeakMap,Qa=new WeakMap,Ja=new WeakMap,Xa=new WeakMap,Ya=new WeakMap,Za=new WeakMap,ec=new WeakMap,tc=new WeakMap,ic=new WeakMap;var oc,ac=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},cc=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class dc{constructor(){oc.set(this,void 0),ac(this,oc,Promise.resolve(),"f")}postTask(e){const t=cc(this,oc,"f").then(e);return ac(this,oc,t.then((()=>{}),(()=>{})),"f"),t}}oc=new WeakMap;var lc,hc,fc,uc,wc,mc,pc,yc,gc,vc,kc,bc,Ec,Cc,Mc,Pc,_c,Tc,Ic,xc,Nc,Sc,Fc,Wc,Rc=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},Dc=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class qc extends a{constructor(t,i,s){super(),lc.add(this),hc.set(this,void 0),fc.set(this,new Map),uc.set(this,new Map),wc.set(this,new Map),mc.set(this,new Set),pc.set(this,void 0),yc.set(this,void 0),gc.set(this,new WeakMap),vc.set(this,new WeakMap),kc.set(this,new WeakMap),bc.set(this,(()=>{})),Ec.set(this,new Promise((e=>{Rc(this,bc,e,"f")}))),Cc.set(this,new Set),Mc.set(this,(()=>{for(const[e,t]of Dc(this,fc,"f").entries())Dc(this,pc,"f")&&!Dc(this,pc,"f").call(this,t)||"browser"===t.type||Dc(this,Cc,"f").add(e)})),Tc.set(this,(e=>{Dc(this,lc,"m",_c).call(this,e),Dc(this,gc,"f").delete(e)})),Ic.set(this,(async e=>{if(Dc(this,fc,"f").set(e.targetInfo.targetId,e.targetInfo),this.emit("targetDiscovered",e.targetInfo),"browser"===e.targetInfo.type&&e.targetInfo.attached){if(Dc(this,uc,"f").has(e.targetInfo.targetId))return;const t=Dc(this,yc,"f").call(this,e.targetInfo,void 0);Dc(this,uc,"f").set(e.targetInfo.targetId,t)}})),xc.set(this,(e=>{const t=Dc(this,fc,"f").get(e.targetId);if(Dc(this,fc,"f").delete(e.targetId),Dc(this,lc,"m",Fc).call(this,e.targetId),"service_worker"===(null==t?void 0:t.type)&&Dc(this,uc,"f").has(e.targetId)){const t=Dc(this,uc,"f").get(e.targetId);this.emit("targetGone",t),Dc(this,uc,"f").delete(e.targetId)}})),Nc.set(this,(e=>{if(Dc(this,fc,"f").set(e.targetInfo.targetId,e.targetInfo),Dc(this,mc,"f").has(e.targetInfo.targetId)||!Dc(this,uc,"f").has(e.targetInfo.targetId)||!e.targetInfo.attached)return;const t=Dc(this,uc,"f").get(e.targetInfo.targetId);this.emit("targetChanged",{target:t,targetInfo:e.targetInfo})})),Sc.set(this,(async(t,i)=>{const s=i.targetInfo,r=Dc(this,hc,"f").session(i.sessionId);if(!r)throw new Error(`Session ${i.sessionId} was not created.`);const n=async()=>{await r.send("Runtime.runIfWaitingForDebugger").catch(Rr),await t.send("Target.detachFromTarget",{sessionId:r.id()}).catch(Rr)};if(!Dc(this,hc,"f").isAutoAttached(s.targetId))return;if("service_worker"===s.type&&Dc(this,hc,"f").isAutoAttached(s.targetId)){if(Dc(this,lc,"m",Fc).call(this,s.targetId),await n(),Dc(this,uc,"f").has(s.targetId))return;const e=Dc(this,yc,"f").call(this,s);return Dc(this,uc,"f").set(s.targetId,e),void this.emit("targetAvailable",e)}if(Dc(this,pc,"f")&&!Dc(this,pc,"f").call(this,s))return Dc(this,mc,"f").add(s.targetId),Dc(this,lc,"m",Fc).call(this,s.targetId),void await n();const o=Dc(this,uc,"f").has(s.targetId),a=o?Dc(this,uc,"f").get(s.targetId):Dc(this,yc,"f").call(this,s,r);Dc(this,lc,"m",Pc).call(this,r),o?Dc(this,wc,"f").set(r.id(),Dc(this,uc,"f").get(s.targetId)):(Dc(this,uc,"f").set(s.targetId,a),Dc(this,wc,"f").set(r.id(),a));for(const i of Dc(this,gc,"f").get(t)||[])t instanceof R||e(Dc(this,wc,"f").has(t.id())),i(a,t instanceof R?null:Dc(this,wc,"f").get(t.id()));Dc(this,Cc,"f").delete(a._targetId),o||this.emit("targetAvailable",a),Dc(this,lc,"m",Fc).call(this),await Promise.all([r.send("Target.setAutoAttach",{waitForDebuggerOnStart:!0,flatten:!0,autoAttach:!0}),r.send("Runtime.runIfWaitingForDebugger")]).catch(Rr)})),Wc.set(this,((e,t)=>{const i=Dc(this,wc,"f").get(t.sessionId);Dc(this,wc,"f").delete(t.sessionId),i&&(Dc(this,uc,"f").delete(i._targetId),this.emit("targetGone",i))})),Rc(this,hc,t,"f"),Rc(this,pc,s,"f"),Rc(this,yc,i,"f"),Dc(this,hc,"f").on("Target.targetCreated",Dc(this,Ic,"f")),Dc(this,hc,"f").on("Target.targetDestroyed",Dc(this,xc,"f")),Dc(this,hc,"f").on("Target.targetInfoChanged",Dc(this,Nc,"f")),Dc(this,hc,"f").on("sessiondetached",Dc(this,Tc,"f")),Dc(this,lc,"m",Pc).call(this,Dc(this,hc,"f")),Dc(this,hc,"f").send("Target.setDiscoverTargets",{discover:!0,filter:[{type:"tab",exclude:!0},{}]}).then(Dc(this,Mc,"f")).catch(Rr)}async initialize(){await Dc(this,hc,"f").send("Target.setAutoAttach",{waitForDebuggerOnStart:!0,flatten:!0,autoAttach:!0}),Dc(this,lc,"m",Fc).call(this),await Dc(this,Ec,"f")}dispose(){Dc(this,hc,"f").off("Target.targetCreated",Dc(this,Ic,"f")),Dc(this,hc,"f").off("Target.targetDestroyed",Dc(this,xc,"f")),Dc(this,hc,"f").off("Target.targetInfoChanged",Dc(this,Nc,"f")),Dc(this,hc,"f").off("sessiondetached",Dc(this,Tc,"f")),Dc(this,lc,"m",_c).call(this,Dc(this,hc,"f"))}getAvailableTargets(){return Dc(this,uc,"f")}addTargetInterceptor(e,t){const i=Dc(this,gc,"f").get(e)||[];i.push(t),Dc(this,gc,"f").set(e,i)}removeTargetInterceptor(e,t){const i=Dc(this,gc,"f").get(e)||[];Dc(this,gc,"f").set(e,i.filter((e=>e!==t)))}}hc=new WeakMap,fc=new WeakMap,uc=new WeakMap,wc=new WeakMap,mc=new WeakMap,pc=new WeakMap,yc=new WeakMap,gc=new WeakMap,vc=new WeakMap,kc=new WeakMap,bc=new WeakMap,Ec=new WeakMap,Cc=new WeakMap,Mc=new WeakMap,Tc=new WeakMap,Ic=new WeakMap,xc=new WeakMap,Nc=new WeakMap,Sc=new WeakMap,Wc=new WeakMap,lc=new WeakSet,Pc=function(t){const i=e=>Dc(this,Sc,"f").call(this,t,e);e(!Dc(this,vc,"f").has(t)),Dc(this,vc,"f").set(t,i),t.on("Target.attachedToTarget",i);const s=e=>Dc(this,Wc,"f").call(this,t,e);e(!Dc(this,kc,"f").has(t)),Dc(this,kc,"f").set(t,s),t.on("Target.detachedFromTarget",s)},_c=function(e){Dc(this,vc,"f").has(e)&&(e.off("Target.attachedToTarget",Dc(this,vc,"f").get(e)),Dc(this,vc,"f").delete(e)),Dc(this,kc,"f").has(e)&&(e.off("Target.detachedFromTarget",Dc(this,kc,"f").get(e)),Dc(this,kc,"f").delete(e))},Fc=function(e){void 0!==e&&Dc(this,Cc,"f").delete(e),0===Dc(this,Cc,"f").size&&Dc(this,bc,"f").call(this)};var jc,Ac,Oc,Kc,Lc,$c,Bc,Hc,Uc,Vc,zc,Gc,Qc,Jc,Xc,Yc,Zc,ed,td=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i},id=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)};class sd extends a{constructor(t,i,s){super(),jc.add(this),Ac.set(this,void 0),Oc.set(this,new Map),Kc.set(this,new Map),Lc.set(this,new Map),$c.set(this,new Set),Bc.set(this,void 0),Hc.set(this,void 0),Uc.set(this,new WeakMap),Vc.set(this,new WeakMap),zc.set(this,(()=>{})),Gc.set(this,new Promise((e=>{td(this,zc,e,"f")}))),Qc.set(this,new Set),Jc.set(this,(e=>{this.removeSessionListeners(e),id(this,Uc,"f").delete(e),id(this,Lc,"f").delete(e.id())})),Xc.set(this,(async e=>{if(id(this,Oc,"f").has(e.targetInfo.targetId))return;if(id(this,Oc,"f").set(e.targetInfo.targetId,e.targetInfo),"browser"===e.targetInfo.type&&e.targetInfo.attached){const t=id(this,Hc,"f").call(this,e.targetInfo,void 0);return id(this,Kc,"f").set(e.targetInfo.targetId,t),void id(this,jc,"m",ed).call(this,t._targetId)}if(id(this,Bc,"f")&&!id(this,Bc,"f").call(this,e.targetInfo))return id(this,$c,"f").add(e.targetInfo.targetId),void id(this,jc,"m",ed).call(this,e.targetInfo.targetId);const t=id(this,Hc,"f").call(this,e.targetInfo,void 0);id(this,Kc,"f").set(e.targetInfo.targetId,t),this.emit("targetAvailable",t),id(this,jc,"m",ed).call(this,t._targetId)})),Yc.set(this,(e=>{id(this,Oc,"f").delete(e.targetId),id(this,jc,"m",ed).call(this,e.targetId);const t=id(this,Kc,"f").get(e.targetId);t&&(this.emit("targetGone",t),id(this,Kc,"f").delete(e.targetId))})),Zc.set(this,(async(t,i)=>{const s=i.targetInfo,r=id(this,Ac,"f").session(i.sessionId);if(!r)throw new Error(`Session ${i.sessionId} was not created.`);const n=id(this,Kc,"f").get(s.targetId);e(n,`Target ${s.targetId} is missing`),this.setupAttachmentListeners(r),id(this,Lc,"f").set(r.id(),id(this,Kc,"f").get(s.targetId));for(const i of id(this,Uc,"f").get(t)||[])t instanceof R||e(id(this,Lc,"f").has(t.id())),await i(n,t instanceof R?null:id(this,Lc,"f").get(t.id()))})),td(this,Ac,t,"f"),td(this,Bc,s,"f"),td(this,Hc,i,"f"),id(this,Ac,"f").on("Target.targetCreated",id(this,Xc,"f")),id(this,Ac,"f").on("Target.targetDestroyed",id(this,Yc,"f")),id(this,Ac,"f").on("sessiondetached",id(this,Jc,"f")),this.setupAttachmentListeners(id(this,Ac,"f"))}addTargetInterceptor(e,t){const i=id(this,Uc,"f").get(e)||[];i.push(t),id(this,Uc,"f").set(e,i)}removeTargetInterceptor(e,t){const i=id(this,Uc,"f").get(e)||[];id(this,Uc,"f").set(e,i.filter((e=>e!==t)))}setupAttachmentListeners(t){const i=e=>id(this,Zc,"f").call(this,t,e);e(!id(this,Vc,"f").has(t)),id(this,Vc,"f").set(t,i),t.on("Target.attachedToTarget",i)}removeSessionListeners(e){id(this,Vc,"f").has(e)&&(e.off("Target.attachedToTarget",id(this,Vc,"f").get(e)),id(this,Vc,"f").delete(e))}getAvailableTargets(){return id(this,Kc,"f")}dispose(){id(this,Ac,"f").off("Target.targetCreated",id(this,Xc,"f")),id(this,Ac,"f").off("Target.targetDestroyed",id(this,Yc,"f"))}async initialize(){await id(this,Ac,"f").send("Target.setDiscoverTargets",{discover:!0}),td(this,Qc,new Set(id(this,Oc,"f").keys()),"f"),await id(this,Gc,"f")}}Ac=new WeakMap,Oc=new WeakMap,Kc=new WeakMap,Lc=new WeakMap,$c=new WeakMap,Bc=new WeakMap,Hc=new WeakMap,Uc=new WeakMap,Vc=new WeakMap,zc=new WeakMap,Gc=new WeakMap,Qc=new WeakMap,Jc=new WeakMap,Xc=new WeakMap,Yc=new WeakMap,Zc=new WeakMap,jc=new WeakSet,ed=function(e){id(this,Qc,"f").delete(e),0===id(this,Qc,"f").size&&id(this,zc,"f").call(this)};const rd=new Map([["geolocation","geolocation"],["midi","midi"],["notifications","notifications"],["camera","videoCapture"],["microphone","audioCapture"],["background-sync","backgroundSync"],["ambient-light-sensor","sensors"],["accelerometer","sensors"],["gyroscope","sensors"],["magnetometer","sensors"],["accessibility-events","accessibilityEvents"],["clipboard-read","clipboardReadWrite"],["clipboard-write","clipboardReadWrite"],["payment-handler","paymentHandler"],["persistent-storage","durableStorage"],["idle-detection","idleDetection"],["midi-sysex","midiSysex"]]);class nd extends a{constructor(){super()}_attach(){throw new Error("Not implemented")}_detach(){throw new Error("Not implemented")}get _targets(){throw new Error("Not implemented")}process(){throw new Error("Not implemented")}_getIsPageTargetCallback(){throw new Error("Not implemented")}createIncognitoBrowserContext(){throw new Error("Not implemented")}browserContexts(){throw new Error("Not implemented")}defaultBrowserContext(){throw new Error("Not implemented")}_disposeContext(){throw new Error("Not implemented")}wsEndpoint(){throw new Error("Not implemented")}newPage(){throw new Error("Not implemented")}_createPageInContext(){throw new Error("Not implemented")}targets(){throw new Error("Not implemented")}target(){throw new Error("Not implemented")}waitForTarget(){throw new Error("Not implemented")}pages(){throw new Error("Not implemented")}version(){throw new Error("Not implemented")}userAgent(){throw new Error("Not implemented")}close(){throw new Error("Not implemented")}disconnect(){throw new Error("Not implemented")}isConnected(){throw new Error("Not implemented")}}class od extends a{constructor(){super()}targets(){throw new Error("Not implemented")}waitForTarget(){throw new Error("Not implemented")}pages(){throw new Error("Not implemented")}isIncognito(){throw new Error("Not implemented")}overridePermissions(){throw new Error("Not implemented")}clearPermissionOverrides(){throw new Error("Not implemented")}newPage(){throw new Error("Not implemented")}browser(){throw new Error("Not implemented")}close(){throw new Error("Not implemented")}get id(){}}var ad,cd,dd,ld,hd,fd,ud,wd,md,pd,yd,gd,vd,kd,bd,Ed,Cd,Md,Pd,_d,Td,Id,xd,Nd=self&&self.__classPrivateFieldGet||function(e,t,i,s){if("a"===i&&!s)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(e):s?s.value:t.get(e)},Sd=self&&self.__classPrivateFieldSet||function(e,t,i,s,r){if("m"===s)throw new TypeError("Private method is not writable");if("a"===s&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?r.call(e,i):r?r.value=i:t.set(e,i),i};class Fd extends nd{static async _create(e,t,i,s,r,n,o,a,c){const d=new Fd(e,t,i,s,r,n,o,a,c);return await d._attach(),d}get _targets(){return Nd(this,gd,"f").getAvailableTargets()}constructor(e,t,i,s,r,n,o,a,c){super(),ad.add(this),cd.set(this,void 0),dd.set(this,void 0),ld.set(this,void 0),hd.set(this,void 0),fd.set(this,void 0),ud.set(this,void 0),wd.set(this,void 0),md.set(this,void 0),pd.set(this,void 0),yd.set(this,void 0),gd.set(this,void 0),vd.set(this,(()=>{this.emit("disconnected")})),bd.set(this,((e,t)=>{var i;const{browserContextId:s}=e,r=s&&Nd(this,pd,"f").has(s)?Nd(this,pd,"f").get(s):Nd(this,md,"f");if(!r)throw new Error("Missing browser context");return new nc(e,t,r,Nd(this,gd,"f"),(t=>Nd(this,hd,"f")._createSession(e,t)),Nd(this,cd,"f"),null!==(i=Nd(this,dd,"f"))&&void 0!==i?i:null,Nd(this,yd,"f"),Nd(this,wd,"f"))})),Ed.set(this,(async e=>{await e._initializedPromise&&(this.emit("targetcreated",e),e.browserContext().emit("targetcreated",e))})),Cd.set(this,(async e=>{e._initializedCallback(!1),e._closedCallback(),await e._initializedPromise&&(this.emit("targetdestroyed",e),e.browserContext().emit("targetdestroyed",e))})),Md.set(this,(({target:e,targetInfo:t})=>{const i=e.url(),s=e._isInitialized;e._targetInfoChanged(t),s&&i!==e.url()&&(this.emit("targetchanged",e),e.browserContext().emit("targetchanged",e))})),Pd.set(this,(e=>{this.emit("targetdiscovered",e)})),e=e||"chrome",Sd(this,cd,s,"f"),Sd(this,dd,r,"f"),Sd(this,ld,n,"f"),Sd(this,yd,new dc,"f"),Sd(this,hd,t,"f"),Sd(this,fd,o||function(){},"f"),Sd(this,ud,a||(()=>!0),"f"),Nd(this,ad,"m",kd).call(this,c),Sd(this,gd,"firefox"===e?new sd(t,Nd(this,bd,"f"),Nd(this,ud,"f")):new qc(t,Nd(this,bd,"f"),Nd(this,ud,"f")),"f"),Sd(this,md,new Wd(Nd(this,hd,"f"),this),"f"),Sd(this,pd,new Map,"f");for(const e of i)Nd(this,pd,"f").set(e,new Wd(Nd(this,hd,"f"),this,e))}async _attach(){Nd(this,hd,"f").on(W.Disconnected,Nd(this,vd,"f")),Nd(this,gd,"f").on("targetAvailable",Nd(this,Ed,"f")),Nd(this,gd,"f").on("targetGone",Nd(this,Cd,"f")),Nd(this,gd,"f").on("targetChanged",Nd(this,Md,"f")),Nd(this,gd,"f").on("targetDiscovered",Nd(this,Pd,"f")),await Nd(this,gd,"f").initialize()}_detach(){Nd(this,hd,"f").off(W.Disconnected,Nd(this,vd,"f")),Nd(this,gd,"f").off("targetAvailable",Nd(this,Ed,"f")),Nd(this,gd,"f").off("targetGone",Nd(this,Cd,"f")),Nd(this,gd,"f").off("targetChanged",Nd(this,Md,"f")),Nd(this,gd,"f").off("targetDiscovered",Nd(this,Pd,"f"))}process(){var e;return null!==(e=Nd(this,ld,"f"))&&void 0!==e?e:null}_targetManager(){return Nd(this,gd,"f")}_getIsPageTargetCallback(){return Nd(this,wd,"f")}async createIncognitoBrowserContext(e={}){const{proxyServer:t,proxyBypassList:i}=e,{browserContextId:s}=await Nd(this,hd,"f").send("Target.createBrowserContext",{proxyServer:t,proxyBypassList:i&&i.join(",")}),r=new Wd(Nd(this,hd,"f"),this,s);return Nd(this,pd,"f").set(s,r),r}browserContexts(){return[Nd(this,md,"f"),...Array.from(Nd(this,pd,"f").values())]}defaultBrowserContext(){return Nd(this,md,"f")}async _disposeContext(e){e&&(await Nd(this,hd,"f").send("Target.disposeBrowserContext",{browserContextId:e}),Nd(this,pd,"f").delete(e))}wsEndpoint(){return Nd(this,hd,"f").url()}async newPage(){return Nd(this,md,"f").newPage()}async _createPageInContext(e){const{targetId:t}=await Nd(this,hd,"f").send("Target.createTarget",{url:"about:blank",browserContextId:e||void 0}),i=Nd(this,gd,"f").getAvailableTargets().get(t);if(!i)throw new Error(`Missing target for page (id = ${t})`);if(!await i._initializedPromise)throw new Error(`Failed to create target for page (id = ${t})`);const s=await i.page();if(!s)throw new Error(`Failed to create a page for context (id = ${e})`);return s}targets(){return Array.from(Nd(this,gd,"f").getAvailableTargets().values()).filter((e=>e._isInitialized))}target(){const e=this.targets().find((e=>"browser"===e.type()));if(!e)throw new Error("Browser target is not found");return e}async waitForTarget(e,t={}){const{timeout:i=3e4}=t;let s,r=!1;const n=new Promise((e=>s=e));this.on("targetcreated",o),this.on("targetchanged",o);try{return this.targets().forEach(o),i?await Ur(n,"target",i):await n}finally{this.off("targetcreated",o),this.off("targetchanged",o)}async function o(t){await e(t)&&!r&&(r=!0,s(t))}}async pages(){return(await Promise.all(this.browserContexts().map((e=>e.pages())))).reduce(((e,t)=>e.concat(t)),[])}async version(){return(await Nd(this,ad,"m",_d).call(this)).product}async userAgent(){return(await Nd(this,ad,"m",_d).call(this)).userAgent}async close(){await Nd(this,fd,"f").call(null),this.disconnect()}disconnect(){Nd(this,gd,"f").dispose(),Nd(this,hd,"f").dispose()}isConnected(){return!Nd(this,hd,"f")._closed}}cd=new WeakMap,dd=new WeakMap,ld=new WeakMap,hd=new WeakMap,fd=new WeakMap,ud=new WeakMap,wd=new WeakMap,md=new WeakMap,pd=new WeakMap,yd=new WeakMap,gd=new WeakMap,vd=new WeakMap,bd=new WeakMap,Ed=new WeakMap,Cd=new WeakMap,Md=new WeakMap,Pd=new WeakMap,ad=new WeakSet,kd=function(e){Sd(this,wd,e||(e=>"page"===e.type||"background_page"===e.type||"webview"===e.type),"f")},_d=function(){return Nd(this,hd,"f").send("Browser.getVersion")};class Wd extends od{constructor(e,t,i){super(),Td.set(this,void 0),Id.set(this,void 0),xd.set(this,void 0),Sd(this,Td,e,"f"),Sd(this,Id,t,"f"),Sd(this,xd,i,"f")}get id(){return Nd(this,xd,"f")}targets(){return Nd(this,Id,"f").targets().filter((e=>e.browserContext()===this))}waitForTarget(e,t={}){return Nd(this,Id,"f").waitForTarget((t=>t.browserContext()===this&&e(t)),t)}async pages(){const e=await Promise.all(this.targets().filter((e=>{var t;return"page"===e.type()||"other"===e.type()&&(null===(t=Nd(this,Id,"f")._getIsPageTargetCallback())||void 0===t?void 0:t(e._getTargetInfo()))})).map((e=>e.page())));return e.filter((e=>!!e))}isIncognito(){return!!Nd(this,xd,"f")}async overridePermissions(e,t){const i=t.map((e=>{const t=rd.get(e);if(!t)throw new Error("Unknown permission: "+e);return t}));await Nd(this,Td,"f").send("Browser.grantPermissions",{origin:e,browserContextId:Nd(this,xd,"f")||void 0,permissions:i})}async clearPermissionOverrides(){await Nd(this,Td,"f").send("Browser.resetPermissions",{browserContextId:Nd(this,xd,"f")||void 0})}newPage(){return Nd(this,Id,"f")._createPageInContext(Nd(this,xd,"f"))}browser(){return Nd(this,Id,"f")}async close(){e(Nd(this,xd,"f"),"Non-incognito profiles cannot be closed!"),await Nd(this,Id,"f")._disposeContext(Nd(this,xd,"f"))}}Td=new WeakMap,Id=new WeakMap,xd=new WeakMap;class Rd{#e;#t=new Set;constructor(e){this.#e=e}send(e){const t=JSON.parse(e);this.#t.add(t.id),this.#e.sendRawMessage(e)}close(){this.#e.disconnect()}set onmessage(e){this.#e.setOnMessage((t=>{const i=t;if((!i.id||this.#t.has(i.id))&&(this.#t.delete(i.id),i.sessionId))return e(JSON.stringify({...i,sessionId:i.sessionId===this.#e.getSessionId()?void 0:i.sessionId}))}))}set onclose(e){const t=this.#e.getOnDisconnect();this.#e.setOnDisconnect((i=>{t&&t(i),e&&e()}))}}class Dd extends R{async onMessage(e){const t=JSON.parse(e);t.sessionId&&!this._sessions.has(t.sessionId)||super.onMessage(e)}}var qd=Object.freeze({__proto__:null,PuppeteerConnectionHelper:class{static async connectPuppeteerToConnection(e){const{connection:t,mainFrameId:i,targetInfos:s,targetFilterCallback:r,isPageTargetCallback:n}=e,o=new Rd(t),a=new Dd("",o),c=s.filter(r).map((e=>e.targetId)),d=Fd._create("chrome",a,[],!1,void 0,void 0,void 0,r,n),[,l]=await Promise.all([Promise.all(c.map((e=>a._createSession({targetId:e},!0)))),d]),h=(await l.pages()).filter((e=>null!==e)).find((e=>e.mainFrame()._id===i))||null;return{page:h,browser:l,puppeteerConnection:a}}}});export{qd as PuppeteerConnection};
