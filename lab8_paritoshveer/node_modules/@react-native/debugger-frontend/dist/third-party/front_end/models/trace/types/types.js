var n=Object.freeze({__proto__:null,MicroSeconds:function(n){return n},MilliSeconds:function(n){return n},Seconds:function(n){return n}});function e(n){return"b"===n||"e"===n||"n"===n}function t(n){return"X"===n.ph}function r(n){return"I"===n.ph}function i(n){return"navigationStart"===n.name}function a(n){return"LayoutShift"===n.name}function c(n){return"EventTiming"===n.name}function u(n){return new Set(["b","n","e","T","S","F","p"]).has(n.ph)}var o=Object.freeze({__proto__:null,isNestableAsyncPhase:e,isAsyncPhase:function(n){return e(n)||"S"===n||"T"===n||"F"===n||"p"===n},isFlowPhase:function(n){return"s"===n||"t"===n||"f"===n},isSyntheticInteractionEvent:function(n){return Boolean("interactionId"in n&&n.args?.data&&"beginEvent"in n.args.data&&"endEvent"in n.args.data)},ProfileID:function(n){return n},CallFrameID:function(n){return n},ProcessID:function(n){return n},ThreadID:function(n){return n},isTraceEventComplete:t,isTraceEventDispatch:function(n){return"EventDispatch"===n.name},isTraceEventInstant:r,isTraceEventRendererEvent:function(n){return r(n)||t(n)},isThreadName:function(n){return"thread_name"===n.name},isProcessName:function(n){return"process_name"===n.name},isTraceEventTracingStartedInBrowser:function(n){return"TracingStartedInBrowser"===n.name},isTraceEventFrameCommittedInBrowser:function(n){return"FrameCommittedInBrowser"===n.name},isTraceEventCommitLoad:function(n){return"CommitLoad"===n.name},isTraceEventNavigationStart:i,isTraceEventAnimation:function(n){return"Animation"===n.name},isTraceEventLayoutShift:a,isTraceEventLayoutInvalidation:function(n){return"LayoutInvalidationTracking"===n.name||"ScheduleStyleInvalidationTracking"===n.name},isTraceEventStyleRecalcInvalidation:function(n){return"StyleRecalcInvalidationTracking"===n.name},isTraceEventFirstContentfulPaint:function(n){return"firstContentfulPaint"===n.name},isTraceEventLargestContentfulPaintCandidate:function(n){return"largestContentfulPaint::Candidate"===n.name},isTraceEventLargestImagePaintCandidate:function(n){return"LargestImagePaint::Candidate"===n.name},isTraceEventLargestTextPaintCandidate:function(n){return"LargestTextPaint::Candidate"===n.name},isTraceEventMarkLoad:function(n){return"MarkLoad"===n.name},isTraceEventFirstPaint:function(n){return"firstPaint"===n.name},isTraceEventMarkDOMContent:function(n){return"MarkDOMContent"===n.name},isTraceEventInteractiveTime:function(n){return"InteractiveTime"===n.name},isTraceEventEventTiming:c,isTraceEventEventTimingEnd:function(n){return c(n)&&"e"===n.ph},isTraceEventEventTimingStart:function(n){return c(n)&&"b"===n.ph},isTraceEventGPUTask:function(n){return"GPUTask"===n.name},isTraceEventProfile:function(n){return"Profile"===n.name},isTraceEventProfileChunk:function(n){return"ProfileChunk"===n.name},isTraceEventResourceSendRequest:function(n){return"ResourceSendRequest"===n.name},isTraceEventResourceReceiveResponse:function(n){return"ResourceReceiveResponse"===n.name},isTraceEventResourceFinish:function(n){return"ResourceFinish"===n.name},isTraceEventResourceWillSendRequest:function(n){return"ResourceWillSendRequest"===n.name},isTraceEventResourceReceivedData:function(n){return"ResourceReceivedData"===n.name},isSyntheticNetworkRequestDetailsEvent:function(n){return"SyntheticNetworkRequest"===n.name},isTraceEventPrePaint:function(n){return"PrePaint"===n.name},isTraceEventNavigationStartWithURL:function(n){return Boolean(i(n)&&n.args.data&&""!==n.args.data.documentLoaderURL)},isTraceEventMainFrameViewport:function(n){return"PaintTimingVisualizer::Viewport"===n.name},isSyntheticUserTimingTraceEvent:function(n){if("blink.user_timing"!==n.cat)return!1;const e=n.args?.data;return!!e&&("beginEvent"in e&&"endEvent"in e)},isSyntheticConsoleTimingTraceEvent:function(n){if("blink.console"!==n.cat)return!1;const e=n.args?.data;return!!e&&("beginEvent"in e&&"endEvent"in e)},isTraceEventPerformanceMeasure:function(n){return u(n)&&"blink.user_timing"===n.cat},isTraceEventPerformanceMark:function(n){return("R"===n.ph||"I"===n.ph)&&"blink.user_timing"===n.cat},isTraceEventConsoleTime:function(n){return u(n)&&"blink.console"===n.cat},isTraceEventTimeStamp:function(n){return"I"===n.ph&&"TimeStamp"===n.name},isTraceEventAsyncPhase:u,isSyntheticLayoutShift:function(n){return!(!a(n)||!n.args.data)&&"rawEvent"in n.args.data}});export{n as Timing,o as TraceEvents};
