import*as e from"./persistence.js";self.Persistence=self.Persistence||{},Persistence=Persistence||{},Persistence.Automapping=e.Automapping.Automapping,Persistence.AutomappingStatus=e.Automapping.AutomappingStatus,Persistence.FileSystemWorkspaceBinding=e.FileSystemWorkspaceBinding.FileSystemWorkspaceBinding,Persistence.FileSystemWorkspaceBinding.FileSystem=e.FileSystemWorkspaceBinding.FileSystem,Persistence.IsolatedFileSystem=e.IsolatedFileSystem.IsolatedFileSystem,Persistence.IsolatedFileSystemManager=e.IsolatedFileSystemManager.IsolatedFileSystemManager,Persistence.IsolatedFileSystemManager.Events=e.IsolatedFileSystemManager.Events,Persistence.NetworkPersistenceManager=e.NetworkPersistenceManager.NetworkPersistenceManager,Persistence.NetworkPersistenceManager.Events=e.NetworkPersistenceManager.Events,Persistence.PersistenceActions={},Persistence.PersistenceActions.ContextMenuProvider=e.PersistenceActions.ContextMenuProvider,Persistence.Persistence=e.Persistence.PersistenceImpl,Persistence.Persistence.Events=e.Persistence.Events,Persistence.Persistence._NodeShebang=e.Persistence.NodeShebang,Persistence.Persistence._NodePrefix=e.Persistence.NodePrefix,Persistence.Persistence._NodeSuffix=e.Persistence.NodeSuffix,Persistence.PersistenceBinding=e.Persistence.PersistenceBinding,Persistence.PersistenceUtils=e.PersistenceUtils.PersistenceUtils,Persistence.PlatformFileSystem=e.PlatformFileSystem.PlatformFileSystem,Persistence.WorkspaceSettingsTab=e.WorkspaceSettingsTab.WorkspaceSettingsTab;
