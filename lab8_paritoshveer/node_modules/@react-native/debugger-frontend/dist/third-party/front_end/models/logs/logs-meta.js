import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";const o={preserveLog:"Preserve log",preserve:"preserve",clear:"clear",reset:"reset",preserveLogOnPageReload:"Preserve log on page reload / navigation",doNotPreserveLogOnPageReload:"Do not preserve log on page reload / navigation",recordNetworkLog:"Record network log"},r=t.i18n.registerUIStrings("models/logs/logs-meta.ts",o),g=t.i18n.getLazilyComputedLocalizedString.bind(void 0,r);e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.NETWORK,title:g(o.preserveLog),settingName:"network_log.preserve-log",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!1,tags:[g(o.preserve),g(o.clear),g(o.reset)],options:[{value:!0,title:g(o.preserveLogOnPageReload)},{value:!1,title:g(o.doNotPreserveLogOnPageReload)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.NETWORK,title:g(o.recordNetworkLog),settingName:"network_log.record-log",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!0,storageType:e.Settings.SettingStorageType.Session});
