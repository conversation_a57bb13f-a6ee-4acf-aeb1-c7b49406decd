import*as e from"../../core/sdk/sdk.js";import*as t from"../bindings/bindings.js";import*as n from"../formatter/formatter.js";import*as r from"../text_utils/text_utils.js";import*as o from"../../core/platform/platform.js";const s=new WeakMap,i=new WeakMap,a=new WeakMap;async function c(e){const t=await e.requestContent();let n=a.get(t);if(void 0===n){const{content:e}=t;n=e?new r.Text.Text(e):null,a.set(t,n)}return n}class u{name;positions;constructor(e,t=[]){this.name=e,this.positions=t}addPosition(e,t){this.positions.push({lineNumber:e,columnNumber:t})}}const l=async function(e){const t=e.startLocation(),o=e.endLocation();if(!t||!o)return null;const s=t.script();if(!s||!s.sourceMapURL||s!==o.script())return null;const i=await c(s);if(!i)return null;const a=new r.TextRange.TextRange(t.lineNumber,t.columnNumber,o.lineNumber,o.columnNumber),u=i.extract(a),l=i.toSourceRange(a).offset,f=await async function(e){const t=[{prefix:"class DummyClass extends DummyBase { constructor",suffix:"}"},{prefix:"async function* __DEVTOOLS_DUMMY__",suffix:""},{prefix:"async ",suffix:""}];for(const{prefix:r,suffix:o}of t){const t=await n.FormatterWorkerPool.formatterWorkerPool().javaScriptScopeTree(r+e+o);if(t)return{prefixLength:r.length,scopeTree:t}}return null}(u);if(!f)return null;const{prefixLength:p,scopeTree:g}=f;return{scopeTree:g,text:i,slide:l-p}},f=async function(e,t){if(!e)return null;const n=t.startLocation(),o=t.endLocation();if(!n||!o)return null;const s=await l(e);if(!s)return null;const{scopeTree:i,text:a,slide:c}=s,f={start:a.offsetFromPosition(n.lineNumber,n.columnNumber)-c,end:a.offsetFromPosition(o.lineNumber,o.columnNumber)-c};if(!h(i,f))return null;let p=i;const g=[];for(;;){let e=!1;for(const t of p.children){if(h(t,f)){g.push(p),p=t,e=!0;break}if(!w(f,t)&&!h(f,t))return console.error("Wrong nesting of scopes"),null}if(!e)break}const m=[],b=new r.TextCursor.TextCursor(a.lineEndings());for(const e of p.variables){if(3===e.kind&&e.offsets.length<=1)continue;const t=new u(e.name);for(const n of e.offsets){const e=n+c;b.resetTo(e),t.addPosition(b.lineNumber(),b.columnNumber())}m.push(t)}const d=[];for(const e of g)for(const t of e.variables){let e=null;for(const n of t.offsets)if(n>=p.start&&n<p.end){e||(e=new u(t.name));const r=n+c;b.resetTo(r),e.addPosition(b.lineNumber(),b.columnNumber())}e&&d.push(e)}return{boundVariables:m,freeVariables:d};function h(e,t){return e.start<=t.start&&e.end>=t.end}function w(e,t){return e.end<=t.start||t.end<=e.start}},p=/^\s*([A-Za-z_$][A-Za-z_$0-9]*)\s*([.;,=]?)\s*$/,g=async e=>{let n=s.get(e);const r=e.callFrame().script,o=r.debuggerModel.sourceMapManager().sourceMapForClient(r);if(!n||n.sourceMap!==o){const t=(async()=>{const t=new Map;let n=null;if(!o)return{variableMapping:t,thisMapping:n};const s=[],a=(e,t)=>{for(const n of e.positions){const e=o.findEntry(n.lineNumber,n.columnNumber);if(e&&e.name)return void t(e.name)}s.push(async function(){if(o)for(const n of e.positions){const s=await i(r,o,e.name,n);if(s)return void t(s)}}())},c=function(){const t=e.callFrame().scopeChain();let n=0;for(;n<t.length&&t[n]!==e;n++);for(;n<t.length;n++){const e=t[n].type();if("local"===e||"closure"===e)break}return n===t.length?null:t[n]}(),u=await f(c,e);if(!u)return{variableMapping:t,thisMapping:n};for(const e of u.boundVariables)a(e,(n=>{"this"!==n&&t.set(e.name,n)}));for(const e of u.freeVariables)a(e,(t=>{"this"===t&&(n=e.name)}));return await Promise.all(s).then(w()),{variableMapping:t,thisMapping:n}})();n={sourceMap:o,mappingPromise:t},s.set(e,{sourceMap:o,mappingPromise:t})}return await n.mappingPromise;async function i(e,n,r,o){const s=n.findEntryRanges(o.lineNumber,o.columnNumber);if(!s)return null;const i=t.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().uiSourceCodeForSourceMapSourceURL(e.debuggerModel,s.sourceURL,e.isContentScript());if(!i)return null;const a=await c(e);if(!a)return null;const u=h(a.extract(s.range));if(!u)return null;const{name:l,punctuation:f}=u;if(l!==r)return null;const g=await c(i);if(!g)return null;const m=h(g.extract(s.sourceRange));if(!m)return null;const{name:b,punctuation:d}=m;return f===d||"comma"===f&&"semicolon"===d?b:null;function h(e){const t=e.match(p);if(!t)return null;const n=t[1];let r=null;switch(t[2]){case".":r="dot";break;case",":r="comma";break;case";":r="semicolon";break;case"=":r="equals";break;case"":r="none";break;default:return console.error(`Name token parsing error: unexpected token "${t[2]}"`),null}return{name:n,punctuation:r}}}},m=async e=>{const t=i.get(e);if(t)return t;const n=e.scopeChain(),r=await Promise.all(n.map(g)),o=new Map;for(const{variableMapping:e}of r)for(const[t,n]of e)n&&!o.has(n)&&o.set(n,t);return i.set(e,o),o};class b extends e.RemoteObject.RemoteObject{scope;object;constructor(e){super(),this.scope=e,this.object=e.object()}customPreview(){return this.object.customPreview()}get objectId(){return this.object.objectId}get type(){return this.object.type}get subtype(){return this.object.subtype}get value(){return this.object.value}get description(){return this.object.description}get hasChildren(){return this.object.hasChildren}get preview(){return this.object.preview}arrayLength(){return this.object.arrayLength()}getOwnProperties(e){return this.object.getOwnProperties(e)}async getAllProperties(t,n){const r=await this.object.getAllProperties(t,n),{variableMapping:o}=await g(this.scope),s=r.properties,i=r.internalProperties,a=[];if(s)for(let t=0;t<s.length;++t){const n=s[t],r=o.get(n.name)||s[t].name;n.value&&a.push(new e.RemoteObject.RemoteObjectProperty(r,n.value,n.enumerable,n.writable,n.isOwn,n.wasThrown,n.symbol,n.synthetic))}return{properties:a,internalProperties:i}}async setPropertyValue(e,t){const{variableMapping:n}=await g(this.scope);let r;r="string"==typeof e?e:e.value;let o=r;for(const e of n.keys())if(n.get(e)===r){o=e;break}return this.object.setPropertyValue(o,t)}async deleteProperty(e){return this.object.deleteProperty(e)}callFunction(e,t){return this.object.callFunction(e,t)}callFunctionJSON(e,t){return this.object.callFunctionJSON(e,t)}release(){this.object.release()}debuggerModel(){return this.object.debuggerModel()}runtimeModel(){return this.object.runtimeModel()}isNode(){return this.object.isNode()}}async function d(e,t,n){const o=e.debuggerModel.sourceMapManager().sourceMapForClient(e);if(!o)return null;const s=o.findEntry(t,n)?.name;if(!s)return null;const i=await c(e);if(!i)return null;const a=new r.TextRange.TextRange(t,n,t,n+1);return"("!==i.extract(a)?null:s}let h=function(){};const w=()=>h;var M=Object.freeze({__proto__:null,IdentifierPositions:u,scopeIdentifiers:f,resolveScopeChain:async function(e){if(!e)return null;const{pluginManager:n}=t.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance();if(n){const t=await n.resolveScopeChain(e);if(t)return t}return e.scopeChain()},allVariablesInCallFrame:m,resolveExpression:async(e,s,i,a,u,l)=>{if("application/wasm"===i.mimeType())return`memories["${s}"] ?? locals["${s}"] ?? tables["${s}"] ?? functions["${s}"] ?? globals["${s}"]`;if(!i.contentType().isFromSourceMap())return"";const f=await m(e);if(f.has(s))return f.get(s);const p=(await t.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().uiLocationToRawLocations(i,a,u)).find((t=>t.debuggerModel===e.debuggerModel));if(!p)return"";const g=p.script();if(!g)return"";const b=g.debuggerModel.sourceMapManager().sourceMapForClient(g);if(!b)return"";const d=await c(g);if(!d)return"";const h=b.reverseMapTextRanges(i.url(),new r.TextRange.TextRange(a,u,a,l));if(1!==h.length)return"";const[w]=h,M=d.extract(w);if(!M)return"";const y=await c(i);if(!y)return"";const j=b.findEntryRanges(w.startLine,w.startColumn),x=0===w.endColumn?w.endLine-1:w.endLine,v=0===w.endColumn?d.lineEndings()[x]:w.endColumn-1,R=b.findEntryRanges(x,v);if(!j||!R)return"";const k=y.extract(new r.TextRange.TextRange(j.sourceRange.startLine,j.sourceRange.startColumn,R.sourceRange.endLine,R.sourceRange.endColumn));return new RegExp(`^[\\s,;]*${o.StringUtilities.escapeForRegExp(s)}`,"g").test(k)?await n.FormatterWorkerPool.formatterWorkerPool().evaluatableJavaScriptSubstring(M):""},resolveThisObject:async e=>{if(!e)return null;const t=e.scopeChain();if(0===t.length)return e.thisObject();const{thisMapping:n}=await g(t[0]);if(!n)return e.thisObject();const r=await e.evaluate({expression:n,objectGroup:"backtrace",includeCommandLineAPI:!1,silent:!0,returnByValue:!1,generatePreview:!0});return"exceptionDetails"in r?!r.exceptionDetails&&r.object?r.object:e.thisObject():null},resolveScopeInObject:function(e){const t=e.startLocation(),n=e.endLocation(),r=t?t.script():null;return"global"!==e.type()&&r&&n&&r.sourceMapURL&&r===n.script()?new b(e):e.object()},RemoteObject:b,resolveDebuggerFrameFunctionName:async function(e){const t=e.localScope()?.startLocation();return t?await d(e.script,t.lineNumber,t.columnNumber):null},resolveProfileFrameFunctionName:async function({scriptId:n,lineNumber:r,columnNumber:o},s){if(!s||void 0===r||void 0===o||void 0===n)return null;const i=s.model(e.DebuggerModel.DebuggerModel),a=i?.scriptForId(String(n));if(!i||!a)return null;const c=t.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance(),u=new e.DebuggerModel.Location(i,n,r,o),l=await(c.pluginManager?.getFunctionInfo(a,u));if(l&&"frames"in l){const e=l.frames.at(-1);if(e?.name)return e.name}return await d(a,r,o)},getScopeResolvedForTest:w,setScopeResolvedForTest:e=>{h=e}});export{M as NamesResolver};
