import*as e from"./extras/extras.js";export{e as Extras};import*as t from"./handlers/handlers.js";export{t as Handlers};import*as s from"./helpers/helpers.js";export{s as Helpers};import*as r from"../../core/platform/platform.js";import*as a from"./types/types.js";export{a as Types};class n extends Event{data;static eventName="traceparseprogress";constructor(e,t={bubbles:!0}){super(n.eventName,t),this.data=e}}class o extends EventTarget{#e;#t;#s;#r="IDLE";static createWithAllHandlers(){return new o(t.ModelHandlers)}constructor(e,{pauseDuration:s=1,eventsPerChunk:r=15e3}={}){super(),this.#a(e),this.#e={Meta:t.ModelHandlers.Meta,...e},this.#t=s,this.#s=r}#a(e){if(Object.keys(e).length===Object.keys(t.ModelHandlers).length)return;const s=new Set;for(const[t,r]of Object.entries(e)){s.add(t);for(const e of r.deps?.()||[])s.add(e)}const r=new Set(Object.keys(e));s.delete("Meta");for(const e of s)if(!r.has(e))throw new Error(`Required handler ${e} not provided.`)}reset(){if("PARSING"===this.#r)throw new Error("Trace processor can't reset while parsing.");const e=Object.values(this.#e);for(const t of e)t.reset();this.#r="IDLE"}async parse(e,t=!1){if("IDLE"!==this.#r)throw new Error(`Trace processor can't start parsing when not idle. Current state: ${this.#r}`);try{this.#r="PARSING",await this.#n(e,t),this.#r="FINISHED_PARSING"}catch(e){throw this.#r="ERRORED_WHILE_PARSING",e}}async#n(e,t){const s=new c(e,this.#t,this.#s),r=[...i(this.#e).values()];for(const e of r)e.reset();for(const e of r)e.initialize?.(t);for await(const e of s)if(2!==e.kind)for(const t of r)t.handleEvent(e.data);else this.dispatchEvent(new n(e.data));for(const e of r)await(e.finalize?.())}get data(){if("FINISHED_PARSING"!==this.#r)return null;const e={};for(const[t,s]of Object.entries(this.#e))Object.assign(e,{[t]:s.data()});return e}}function i(e){const t=new Map,s=new Set,r=a=>{if(t.has(a))return;if(s.has(a)){let e="";for(const t of s)(e||t===a)&&(e+=`${t}->`);throw e+=a,new Error(`Found dependency cycle in trace event handlers: ${e}`)}s.add(a);const n=e[a];if(!n)return;const o=n.deps?.();o&&o.forEach(r),t.set(a,n)};for(const t of Object.keys(e))r(t);return t}class c{traceEvents;pauseDuration;eventsPerChunk;#o;constructor(e,t,s){this.traceEvents=e,this.pauseDuration=t,this.eventsPerChunk=s,this.#o=0}async*[Symbol.asyncIterator](){for(let e=0,t=this.traceEvents.length;e<t;e++)++this.#o%this.eventsPerChunk==0&&(yield{kind:2,data:{index:e,total:t}},await new Promise((e=>setTimeout(e,this.pauseDuration)))),yield{kind:1,data:this.traceEvents[e]}}}var d=Object.freeze({__proto__:null,TraceParseProgressEvent:n,TraceProcessor:o,sortHandlers:i});class l extends EventTarget{#i=[];#c=new Map;#d=[];#l=0;#h;static createWithAllHandlers(){return new l(t.ModelHandlers)}static createWithRequiredHandlersForMigration(){return new l(t.Migration.ENABLED_TRACE_HANDLERS)}constructor(e){super(),this.#h=new o(e)}async parse(e,t){const s=t?.metadata||{},r=t?.isFreshRecording||!1,a=e=>{const{data:t}=e;this.dispatchEvent(new h({type:"PROGRESS_UPDATE",data:t}))};this.#h.addEventListener(n.eventName,a);const o={traceEvents:e,metadata:s,traceParsedData:null};try{await this.#h.parse(e,r),this.#u(o,this.#h.data),this.#i.push(o)}catch(e){throw e}finally{this.#h.removeEventListener(n.eventName,a),this.dispatchEvent(new h({type:"COMPLETE",data:"done"}))}}#u(e,t){e.traceParsedData=t,this.#l++;let a=`Trace ${this.#l}`,n=null;if(e.traceParsedData&&(n=s.Trace.extractOriginFromTrace(e.traceParsedData.Meta.mainFrameURL),n)){const e=r.MapUtilities.getWithDefault(this.#c,n,(()=>1));a=`${n} (${e})`,this.#c.set(n,e+1)}this.#d.push(a)}traceParsedData(e=this.#i.length-1){return this.#i[e]?this.#i[e].traceParsedData:null}metadata(e){return this.#i[e]?this.#i[e].metadata:null}traceEvents(e){return this.#i[e]?this.#i[e].traceEvents:null}size(){return this.#i.length}deleteTraceByIndex(e){this.#i.splice(e,1),this.#d.splice(e,1)}getRecordingsAvailable(){return this.#d}reset(){this.#h.reset()}}class h extends Event{data;static eventName="modelupdate";constructor(e){super(h.eventName),this.data=e}}var u=Object.freeze({__proto__:null,Model:l,ModelUpdateEvent:h,isModelUpdateDataComplete:function(e){return"COMPLETE"===e.type},isModelUpdateDataProgress:function(e){return"PROGRESS_UPDATE"===e.type}});export{d as Processor,u as TraceModel};
