import*as t from"../../third_party/codemirror.next/codemirror.next.js";import*as e from"../../core/platform/platform.js";var n=Object.freeze({__proto__:null,createCssTokenizer:function(){return async function(e,n){const i=await t.cssStreamParser(),s=new t.StringStream(e,4,2),r=i.startState();let l=s.pos;for(;!s.eol();){s.start=l;let t=i.token(s,r);"error"===t&&"maybeprop"===r.state&&(t="property");n(s.current(),t),l=s.pos}}}});class i{lineNumber;lineContent;columnNumber;constructor(t,e,n){this.lineNumber=t,this.lineContent=e,this.columnNumber=n}}var s=Object.freeze({__proto__:null,ContentProvider:class{},SearchMatch:i,contentAsDataURL:function(t,e,n,i,s=!0){return null==t||s&&t.length>1048576?null:"data:"+e+(i?";charset="+i:"")+(n?";base64":"")+","+t}});class r{lineEndings;offsetInternal;lineNumberInternal;columnNumberInternal;constructor(t){this.lineEndings=t,this.offsetInternal=0,this.lineNumberInternal=0,this.columnNumberInternal=0}advance(t){for(this.offsetInternal=t;this.lineNumberInternal<this.lineEndings.length&&this.lineEndings[this.lineNumberInternal]<this.offsetInternal;)++this.lineNumberInternal;this.columnNumberInternal=this.lineNumberInternal?this.offsetInternal-this.lineEndings[this.lineNumberInternal-1]-1:this.offsetInternal}offset(){return this.offsetInternal}resetTo(t){this.offsetInternal=t,this.lineNumberInternal=e.ArrayUtilities.lowerBound(this.lineEndings,t,e.ArrayUtilities.DEFAULT_COMPARATOR),this.columnNumberInternal=this.lineNumberInternal?this.offsetInternal-this.lineEndings[this.lineNumberInternal-1]-1:this.offsetInternal}lineNumber(){return this.lineNumberInternal}columnNumber(){return this.columnNumberInternal}}var l=Object.freeze({__proto__:null,TextCursor:r});class o{startLine;startColumn;endLine;endColumn;constructor(t,e,n,i){this.startLine=t,this.startColumn=e,this.endLine=n,this.endColumn=i}static createFromLocation(t,e){return new o(t,e,t,e)}static createUnboundedFromLocation(t,e){return new o(t,e,2147483647,2147483647)}static fromObject(t){return new o(t.startLine,t.startColumn,t.endLine,t.endColumn)}static comparator(t,e){return t.compareTo(e)}static fromEdit(t,n){let i=t.startLine,s=t.startColumn+n.length;const r=e.StringUtilities.findLineEndingIndexes(n);if(r.length>1){i=t.startLine+r.length-1;const e=r.length;s=r[e-1]-r[e-2]-1}return new o(t.startLine,t.startColumn,i,s)}isEmpty(){return this.startLine===this.endLine&&this.startColumn===this.endColumn}immediatelyPrecedes(t){return!!t&&(this.endLine===t.startLine&&this.endColumn===t.startColumn)}immediatelyFollows(t){return!!t&&t.immediatelyPrecedes(this)}follows(t){return t.endLine===this.startLine&&t.endColumn<=this.startColumn||t.endLine<this.startLine}get linesCount(){return this.endLine-this.startLine}collapseToEnd(){return new o(this.endLine,this.endColumn,this.endLine,this.endColumn)}collapseToStart(){return new o(this.startLine,this.startColumn,this.startLine,this.startColumn)}normalize(){return this.startLine>this.endLine||this.startLine===this.endLine&&this.startColumn>this.endColumn?new o(this.endLine,this.endColumn,this.startLine,this.startColumn):this.clone()}clone(){return new o(this.startLine,this.startColumn,this.endLine,this.endColumn)}serializeToObject(){return{startLine:this.startLine,startColumn:this.startColumn,endLine:this.endLine,endColumn:this.endColumn}}compareTo(t){return this.startLine>t.startLine?1:this.startLine<t.startLine?-1:this.startColumn>t.startColumn?1:this.startColumn<t.startColumn?-1:0}compareToPosition(t,e){return t<this.startLine||t===this.startLine&&e<this.startColumn?-1:t>this.endLine||t===this.endLine&&e>this.endColumn?1:0}equal(t){return this.startLine===t.startLine&&this.endLine===t.endLine&&this.startColumn===t.startColumn&&this.endColumn===t.endColumn}relativeTo(t,e){const n=this.clone();return this.startLine===t&&(n.startColumn-=e),this.endLine===t&&(n.endColumn-=e),n.startLine-=t,n.endLine-=t,n}relativeFrom(t,e){const n=this.clone();return 0===this.startLine&&(n.startColumn+=e),0===this.endLine&&(n.endColumn+=e),n.startLine+=t,n.endLine+=t,n}rebaseAfterTextEdit(t,e){console.assert(t.startLine===e.startLine),console.assert(t.startColumn===e.startColumn);const n=this.clone();if(!this.follows(t))return n;const i=e.endLine-t.endLine,s=e.endColumn-t.endColumn;return n.startLine+=i,n.endLine+=i,n.startLine===e.endLine&&(n.startColumn+=s),n.endLine===e.endLine&&(n.endColumn+=s),n}toString(){return JSON.stringify(this)}containsLocation(t,e){return this.startLine===this.endLine?this.startLine===t&&this.startColumn<=e&&e<this.endColumn:this.startLine===t?this.startColumn<=e:this.endLine===t?e<this.endColumn:this.startLine<t&&t<this.endLine}get start(){return{lineNumber:this.startLine,columnNumber:this.startColumn}}get end(){return{lineNumber:this.endLine,columnNumber:this.endColumn}}intersection(t){let{startLine:e,startColumn:n}=this;e<t.startLine?(e=t.startLine,n=t.startColumn):e===t.startLine&&(n=Math.max(n,t.startColumn));let{endLine:i,endColumn:s}=this;return i>t.endLine?(i=t.endLine,s=t.endColumn):i===t.endLine&&(s=Math.min(s,t.endColumn)),e>i||e===i&&n>=s?new o(0,0,0,0):new o(e,n,i,s)}}class a{offset;length;constructor(t,e){this.offset=t,this.length=e}}var u=Object.freeze({__proto__:null,TextRange:o,SourceRange:a});class h{valueInternal;lineEndingsInternal;constructor(t){this.valueInternal=t}lineEndings(){return this.lineEndingsInternal||(this.lineEndingsInternal=e.StringUtilities.findLineEndingIndexes(this.valueInternal)),this.lineEndingsInternal}value(){return this.valueInternal}lineCount(){return this.lineEndings().length}offsetFromPosition(t,e){return(t?this.lineEndings()[t-1]+1:0)+e}positionFromOffset(t){const n=this.lineEndings(),i=e.ArrayUtilities.lowerBound(n,t,e.ArrayUtilities.DEFAULT_COMPARATOR);return{lineNumber:i,columnNumber:t-(i&&n[i-1]+1)}}lineAt(t){const e=this.lineEndings(),n=t>0?e[t-1]+1:0,i=e[t];let s=this.valueInternal.substring(n,i);return s.length>0&&"\r"===s.charAt(s.length-1)&&(s=s.substring(0,s.length-1)),s}toSourceRange(t){const e=this.offsetFromPosition(t.startLine,t.startColumn),n=this.offsetFromPosition(t.endLine,t.endColumn);return new a(e,n-e)}toTextRange(t){const e=new r(this.lineEndings()),n=o.createFromLocation(0,0);return e.resetTo(t.offset),n.startLine=e.lineNumber(),n.startColumn=e.columnNumber(),e.advance(t.offset+t.length),n.endLine=e.lineNumber(),n.endColumn=e.columnNumber(),n}replaceRange(t,e){const n=this.toSourceRange(t);return this.valueInternal.substring(0,n.offset)+e+this.valueInternal.substring(n.offset+n.length)}extract(t){const e=this.toSourceRange(t);return this.valueInternal.substr(e.offset,e.length)}}var c=Object.freeze({__proto__:null,Text:h});const d={get _keyValueFilterRegex(){return/(?:^|\s)(\-)?([\w\-]+):([^\s]+)/},get _regexFilterRegex(){return/(?:^|\s)(\-)?\/([^\s]+)\//},get _textFilterRegex(){return/(?:^|\s)(\-)?([^\s]+)/},get _SpaceCharRegex(){return/\s/},isSpaceChar:function(t){return d._SpaceCharRegex.test(t)},lineIndent:function(t){let e=0;for(;e<t.length&&d.isSpaceChar(t.charAt(e));)++e;return t.substr(0,e)},splitStringByRegexes(t,e){const n=[],i=[];for(let t=0;t<e.length;t++){const n=e[t];n.global?i.push(n):i.push(new RegExp(n.source,n.flags?n.flags+"g":"g"))}return function t(e,s,r){if(s>=i.length)return void n.push({value:e,position:r,regexIndex:-1,captureGroups:[]});const l=i[s];let o,a=0;l.lastIndex=0;for(;null!==(o=l.exec(e));){const i=e.substring(a,o.index);i&&t(i,s+1,r+a);const l=o[0];n.push({value:l,position:r+o.index,regexIndex:s,captureGroups:o.slice(1)}),a=o.index+l.length}const u=e.substring(a);u&&t(u,s+1,r+a)}(t,0,0),n}};const m=function(t,n,s,r){const l=e.StringUtilities.createSearchRegex(n,s,r),o=new h(t),a=[];for(let t=0;t<o.lineCount();++t){const e=o.lineAt(t);l.lastIndex=0;const n=l.exec(e);n&&a.push(new i(t,e,n.index))}return a};var f=Object.freeze({__proto__:null,Utils:d,FilterParser:class{keys;constructor(t){this.keys=t}static cloneFilter(t){return{key:t.key,text:t.text,regex:t.regex,negative:t.negative}}parse(t){const e=d.splitStringByRegexes(t,[d._keyValueFilterRegex,d._regexFilterRegex,d._textFilterRegex]),n=[];for(const{regexIndex:t,captureGroups:i}of e)if(-1!==t)if(0===t){const t=i[0],e=i[1],s=i[2];-1!==this.keys.indexOf(e)?n.push({key:e,regex:void 0,text:s,negative:Boolean(t)}):n.push({key:void 0,regex:void 0,text:`${e}:${s}`,negative:Boolean(t)})}else if(1===t){const t=i[0],e=i[1];try{n.push({key:void 0,regex:new RegExp(e,"i"),text:void 0,negative:Boolean(t)})}catch(i){n.push({key:void 0,regex:void 0,text:`/${e}/`,negative:Boolean(t)})}}else if(2===t){const t=i[0],e=i[1];n.push({key:void 0,regex:void 0,text:e,negative:Boolean(t)})}return n}},BalancedJSONTokenizer:class{callback;index;balance;buffer;findMultiple;closingDoubleQuoteRegex;lastBalancedIndex;constructor(t,e){this.callback=t,this.index=0,this.balance=0,this.buffer="",this.findMultiple=e||!1,this.closingDoubleQuoteRegex=/[^\\](?:\\\\)*"/g}write(t){this.buffer+=t;const e=this.buffer.length,n=this.buffer;let i;for(i=this.index;i<e;++i){const t=n[i];if('"'===t){if(this.closingDoubleQuoteRegex.lastIndex=i,!this.closingDoubleQuoteRegex.test(n))break;i=this.closingDoubleQuoteRegex.lastIndex-1}else if("{"===t)++this.balance;else if("}"===t){if(--this.balance,this.balance<0)return this.reportBalanced(),!1;if(!this.balance&&(this.lastBalancedIndex=i+1,!this.findMultiple))break}else if("]"===t&&!this.balance)return this.reportBalanced(),!1}return this.index=i,this.reportBalanced(),!0}reportBalanced(){this.lastBalancedIndex&&(this.callback(this.buffer.slice(0,this.lastBalancedIndex)),this.buffer=this.buffer.slice(this.lastBalancedIndex),this.index-=this.lastBalancedIndex,this.lastBalancedIndex=0)}remainder(){return this.buffer}},isMinified:function(t){let e=0;for(let n=0;n<t.length;++e){let e=t.indexOf("\n",n);e<0&&(e=t.length),n=e+1}return(t.length-e)/e>=80},performSearchInContent:m});class g{contentURLInternal;contentTypeInternal;lazyContent;constructor(t,e,n){this.contentURLInternal=t,this.contentTypeInternal=e,this.lazyContent=n}static fromString(t,e,n){return new g(t,e,(()=>Promise.resolve({content:n,isEncoded:!1})))}contentURL(){return this.contentURLInternal}contentType(){return this.contentTypeInternal}requestContent(){return this.lazyContent()}async searchInContent(t,e,n){const{content:i}=await this.lazyContent();return i?m(i,t,e,n):[]}}var L=Object.freeze({__proto__:null,StaticContentProvider:g});export{n as CodeMirrorUtils,s as ContentProvider,L as StaticContentProvider,c as Text,l as TextCursor,u as TextRange,f as TextUtils};
