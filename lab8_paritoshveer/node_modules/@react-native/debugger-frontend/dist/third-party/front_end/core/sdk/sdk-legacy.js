import*as e from"./sdk.js";self.SDK=self.SDK||{},SDK=SDK||{},SDK.CPUProfileDataModel=e.CPUProfileDataModel.CPUProfileDataModel,SDK.CPUProfilerModel=e.CPUProfilerModel.CPUProfilerModel,SDK.CPUThrottlingManager=e.CPUThrottlingManager.CPUThrottlingManager,SDK.CPUThrottlingManager.CPUThrottlingRates=e.CPUThrottlingManager.CPUThrottlingRates,SDK.cssMetadata=e.CSSMetadata.cssMetadata,SDK.CSSModel=e.CSSModel.CSSModel,SDK.CSSModel.Events=e.CSSModel.Events,SDK.CSSLocation=e.CSSModel.CSSLocation,SDK.CSSProperty=e.CSSProperty.CSSProperty,SDK.CSSStyleDeclaration=e.CSSStyleDeclaration.CSSStyleDeclaration,SDK.CSSStyleDeclaration.Type=e.CSSStyleDeclaration.Type,SDK.MainConnection=e.Connections.MainConnection,SDK.ConsoleModel=e.ConsoleModel.ConsoleModel,SDK.ConsoleMessage=e.ConsoleModel.ConsoleMessage,SDK.ConsoleModel.Events=e.ConsoleModel.Events,SDK.ConsoleMessage.MessageSource=e.ConsoleModel.MessageSource,SDK.ConsoleMessage.MessageType=e.ConsoleModel.MessageType,SDK.ConsoleMessage.MessageLevel=e.ConsoleModel.MessageLevel,SDK.ConsoleMessage.FrontendMessageType=e.ConsoleModel.FrontendMessageType,SDK.ConsoleMessage.FrontendMessageSource=e.ConsoleModel.FrontendMessageSource,SDK.Cookie=e.Cookie.Cookie,SDK.CookieParser=e.CookieParser.CookieParser,SDK.DOMDebuggerModel=e.DOMDebuggerModel.DOMDebuggerModel,SDK.DOMModel=e.DOMModel.DOMModel,SDK.DOMModel.Events=e.DOMModel.Events,SDK.DeferredDOMNode=e.DOMModel.DeferredDOMNode,SDK.DOMDocument=e.DOMModel.DOMDocument,SDK.DOMNode=e.DOMModel.DOMNode,SDK.DebuggerModel=e.DebuggerModel.DebuggerModel,SDK.DebuggerModel.PauseOnExceptionsState=e.DebuggerModel.PauseOnExceptionsState,SDK.DebuggerModel.Events=e.DebuggerModel.Events,SDK.DebuggerModel.BreakReason=Protocol.Debugger.PausedEventReason,SDK.DebuggerModel.Location=e.DebuggerModel.Location,SDK.DebuggerModel.CallFrame=e.DebuggerModel.CallFrame,SDK.DebuggerPausedDetails=e.DebuggerModel.DebuggerPausedDetails,SDK.FilmStripModel=e.FilmStripModel.FilmStripModel,SDK.HeapProfilerModel=e.HeapProfilerModel.HeapProfilerModel,SDK.IsolateManager=e.IsolateManager.IsolateManager,SDK.IsolateManager.MemoryTrend=e.IsolateManager.MemoryTrend,SDK.NetworkManager=e.NetworkManager.NetworkManager,SDK.NetworkManager.Events=e.NetworkManager.Events,SDK.NetworkManager.OfflineConditions=e.NetworkManager.OfflineConditions,SDK.NetworkManager.Fast3GConditions=e.NetworkManager.Fast3GConditions,SDK.NetworkDispatcher=e.NetworkManager.NetworkDispatcher,SDK.MultitargetNetworkManager=e.NetworkManager.MultitargetNetworkManager,SDK.MultitargetNetworkManager.InterceptedRequest=e.NetworkManager.InterceptedRequest,SDK.NetworkRequest=e.NetworkRequest.NetworkRequest,SDK.NetworkRequest.Events=e.NetworkRequest.Events,SDK.NetworkRequest.WebSocketFrameType=e.NetworkRequest.WebSocketFrameType,SDK.OverlayModel=e.OverlayModel.OverlayModel,SDK.PerformanceMetricsModel=e.PerformanceMetricsModel.PerformanceMetricsModel,SDK.ProfileTreeModel=e.ProfileTreeModel.ProfileTreeModel,SDK.RemoteObject=e.RemoteObject.RemoteObject,SDK.Resource=e.Resource.Resource,SDK.ResourceTreeModel=e.ResourceTreeModel.ResourceTreeModel,SDK.ResourceTreeModel.Events=e.ResourceTreeModel.Events,SDK.ResourceTreeFrame=e.ResourceTreeModel.ResourceTreeFrame,SDK.RuntimeModel=e.RuntimeModel.RuntimeModel,SDK.RuntimeModel.Events=e.RuntimeModel.Events,SDK.ExecutionContext=e.RuntimeModel.ExecutionContext,SDK.Script=e.Script.Script,SDK.SecurityOriginManager=e.SecurityOriginManager.SecurityOriginManager,SDK.StorageBucketsModel=e.StorageBucketsModel.StorageBucketsModel,SDK.StorageKeyManager=e.StorageKeyManager.StorageKeyManager,SDK.SecurityOriginManager.Events=e.SecurityOriginManager.Events,SDK.ServiceWorkerCacheModel=e.ServiceWorkerCacheModel.ServiceWorkerCacheModel,SDK.ServiceWorkerManager=e.ServiceWorkerManager.ServiceWorkerManager,SDK.SourceMap=e.SourceMap.SourceMap,SDK.SourceMapManager=e.SourceMapManager.SourceMapManager,SDK.SourceMapManager.Events=e.SourceMapManager.Events,SDK.Target=e.Target.Target,SDK.Target.Type=e.Target.Type,SDK.TargetManager=e.TargetManager.TargetManager,SDK.TargetManager.Events=e.TargetManager.Events,SDK.TargetManager.Observer=e.TargetManager.Observer,SDK.TracingManager=e.TracingManager.TracingManager,SDK.TracingModel=e.TracingModel.TracingModel,SDK.TracingModel.Phase=e.TracingModel.Phase,SDK.TracingModel.LegacyTopLevelEventCategory=e.TracingModel.LegacyTopLevelEventCategory,SDK.TracingModel.DevToolsMetadataEventCategory=e.TracingModel.DevToolsMetadataEventCategory,SDK.TracingModel.Event=e.TracingModel.Event,self.SDK.targetManager=e.TargetManager.TargetManager.instance(),self.SDK.isolateManager=e.IsolateManager.IsolateManager.instance({forceNew:!0}),self.SDK.domModelUndoStack=e.DOMModel.DOMModelUndoStack.instance();
