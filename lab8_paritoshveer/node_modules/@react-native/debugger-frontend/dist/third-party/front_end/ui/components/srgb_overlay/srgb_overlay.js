import*as e from"../../../core/common/common.js";import*as t from"../helpers/helpers.js";import*as o from"../render_coordinator/render_coordinator.js";import*as r from"../../lit-html/lit-html.js";const s=new CSSStyleSheet;s.replaceSync(":host{position:absolute;left:0;top:0;width:100%;height:100%}.gamut-line{stroke:rgb(255 255 255/50%);fill:none}.label{position:absolute;bottom:3px;margin-right:5px;color:rgb(255 255 255/50%)}\n/*# sourceURL=srgbOverlay.css */\n");const n=o.RenderCoordinator.RenderCoordinator.instance();function l(t){const o=[0,0,0,0];e.Color.hsva2rgba([...t,1],o);const r=e.ColorConverter.ColorConverter.displayP3ToXyzd50(o[0],o[1],o[2]);return e.ColorConverter.ColorConverter.xyzd50ToSrgb(r[0],r[1],r[2]).every((e=>e+.001>=0&&e-.001<=1))}class i extends HTMLElement{static litTagName=r.literal`devtools-spectrum-srgb-overlay`;#e=this.attachShadow({mode:"open"});constructor(){super(),this.#e.adoptedStyleSheets=[s]}#t({hue:e,width:t,height:o}){if(0===t||0===o)return null;const r=1/window.devicePixelRatio,s=[];let n=0;for(let i=0;i<o;i+=r){const a=1-i/o;for(;n<t;n+=r){if(!l([e,n/t,a])){s.push({x:n,y:i});break}}}if(0===s.length)return null;const i=s[s.length-1];return i.x<t&&s.push({y:i.y,x:t}),s}#o(e,t){let o=1/0,r=null;for(const s of e)Math.abs(t-s.y)<=o&&(o=Math.abs(t-s.y),r=s);return r}render({hue:e,width:t,height:o}){return n.write("Srgb Overlay render",(()=>{const s=this.#t({hue:e,width:t,height:o});if(!s||0===s.length)return;const n=this.#o(s,o-13);n&&r.render(r.html` <span class="label" style="right: ${t-n.x}px">sRGB</span> <svg> <polyline points="${s.map((e=>`${e.x.toFixed(2)},${e.y.toFixed(2)}`)).join(" ")}" class="gamut-line"/> </svg> `,this.#e,{host:this})}))}}t.CustomElements.defineComponent("devtools-spectrum-srgb-overlay",i);var a=Object.freeze({__proto__:null,SrgbOverlay:i});export{a as SrgbOverlay};
