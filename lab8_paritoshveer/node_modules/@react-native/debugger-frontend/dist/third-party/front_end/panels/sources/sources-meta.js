import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as i from"../../core/i18n/i18n.js";import*as o from"../../core/root/root.js";import*as n from"../../core/sdk/sdk.js";import*as a from"../../models/breakpoints/breakpoints.js";import*as s from"../../models/workspace/workspace.js";import*as r from"../../ui/legacy/components/object_ui/object_ui.js";import*as c from"../../ui/legacy/components/quick_open/quick_open.js";import*as l from"../../ui/legacy/legacy.js";const g={showSources:"Show Sources",sources:"Sources",showFilesystem:"Show Filesystem",filesystem:"Filesystem",showSnippets:"Show Snippets",snippets:"Snippets",showSearch:"Show Search",search:"Search",showQuickSource:"Show Quick source",quickSource:"Quick source",showThreads:"Show Threads",threads:"Threads",showScope:"Show Scope",scope:"Scope",showWatch:"Show Watch",watch:"Watch",showBreakpoints:"Show Breakpoints",breakpoints:"Breakpoints",pauseScriptExecution:"Pause script execution",resumeScriptExecution:"Resume script execution",stepOverNextFunctionCall:"Step over next function call",stepIntoNextFunctionCall:"Step into next function call",step:"Step",stepOutOfCurrentFunction:"Step out of current function",runSnippet:"Run snippet",deactivateBreakpoints:"Deactivate breakpoints",activateBreakpoints:"Activate breakpoints",addSelectedTextToWatches:"Add selected text to watches",evaluateSelectedTextInConsole:"Evaluate selected text in console",switchFile:"Switch file",rename:"Rename",closeAll:"Close All",jumpToPreviousEditingLocation:"Jump to previous editing location",jumpToNextEditingLocation:"Jump to next editing location",closeTheActiveTab:"Close the active tab",goToLine:"Go to line",goToAFunctionDeclarationruleSet:"Go to a function declaration/rule set",toggleBreakpoint:"Toggle breakpoint",toggleBreakpointEnabled:"Toggle breakpoint enabled",toggleBreakpointInputWindow:"Toggle breakpoint input window",save:"Save",saveAll:"Save all",createNewSnippet:"Create new snippet",addFolderToWorkspace:"Add folder to workspace",previousCallFrame:"Previous call frame",nextCallFrame:"Next call frame",incrementCssUnitBy:"Increment CSS unit by {PH1}",decrementCssUnitBy:"Decrement CSS unit by {PH1}",searchInAnonymousAndContent:"Search in anonymous and content scripts",doNotSearchInAnonymousAndContent:"Do not search in anonymous and content scripts",automaticallyRevealFilesIn:"Automatically reveal files in sidebar",doNotAutomaticallyRevealFilesIn:"Do not automatically reveal files in sidebar",enableJavascriptSourceMaps:"Enable JavaScript source maps",disableJavascriptSourceMaps:"Disable JavaScript source maps",enableTabMovesFocus:"Enable tab moves focus",disableTabMovesFocus:"Disable tab moves focus",detectIndentation:"Detect indentation",doNotDetectIndentation:"Do not detect indentation",autocompletion:"Autocompletion",enableAutocompletion:"Enable autocompletion",disableAutocompletion:"Disable autocompletion",bracketMatching:"Bracket matching",enableBracketMatching:"Enable bracket matching",disableBracketMatching:"Disable bracket matching",codeFolding:"Code folding",enableCodeFolding:"Enable code folding",disableCodeFolding:"Disable code folding",showWhitespaceCharacters:"Show whitespace characters:",doNotShowWhitespaceCharacters:"Do not show whitespace characters",none:"None",showAllWhitespaceCharacters:"Show all whitespace characters",all:"All",showTrailingWhitespaceCharacters:"Show trailing whitespace characters",trailing:"Trailing",displayVariableValuesInlineWhile:"Display variable values inline while debugging",doNotDisplayVariableValuesInline:"Do not display variable values inline while debugging",enableCssSourceMaps:"Enable CSS source maps",disableCssSourceMaps:"Disable CSS source maps",allowScrollingPastEndOfFile:"Allow scrolling past end of file",disallowScrollingPastEndOfFile:"Disallow scrolling past end of file",wasmAutoStepping:"When debugging wasm with debug information, do not pause on wasm bytecode if possible",enableWasmAutoStepping:"Enable wasm auto-stepping",disableWasmAutoStepping:"Disable wasm auto-stepping",goTo:"Go to",line:"Line",symbol:"Symbol",open:"Open",file:"File",disableAutoFocusOnDebuggerPaused:"Do not focus Sources panel when triggering a breakpoint",enableAutoFocusOnDebuggerPaused:"Focus Sources panel when triggering a breakpoint",toggleNavigatorSidebar:"Toggle navigator sidebar",toggleDebuggerSidebar:"Toggle debugger sidebar",nextEditorTab:"Next editor",previousEditorTab:"Previous editor"},d=i.i18n.registerUIStrings("panels/sources/sources-meta.ts",g),u=i.i18n.getLazilyComputedLocalizedString.bind(void 0,d);let S,p;async function y(){return S||(S=await import("./sources.js")),S}async function w(){return p||(p=await import("./components/components.js")),p}function b(e){return void 0===S?[]:e(S)}l.ViewManager.registerViewExtension({location:"panel",id:"sources",commandPrompt:u(g.showSources),title:u(g.sources),order:30,loadView:async()=>(await y()).SourcesPanel.SourcesPanel.instance()}),l.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-files",commandPrompt:u(g.showFilesystem),title:u(g.filesystem),order:3,persistence:"permanent",loadView:async()=>(await y()).SourcesNavigator.FilesNavigatorView.instance(),condition:o.Runtime.ConditionName.NOT_SOURCES_HIDE_ADD_FOLDER}),l.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-snippets",commandPrompt:u(g.showSnippets),title:u(g.snippets),order:6,persistence:"permanent",loadView:async()=>(await y()).SourcesNavigator.SnippetsNavigatorView.instance()}),l.ViewManager.registerViewExtension({location:"drawer-view",id:"sources.search-sources-tab",commandPrompt:u(g.showSearch),title:u(g.search),order:7,persistence:"closeable",loadView:async()=>(await y()).SearchSourcesView.SearchSourcesView.instance()}),l.ViewManager.registerViewExtension({location:"drawer-view",id:"sources.quick",commandPrompt:u(g.showQuickSource),title:u(g.quickSource),persistence:"closeable",order:1e3,loadView:async()=>(await y()).SourcesPanel.WrapperView.instance()}),l.ViewManager.registerViewExtension({id:"sources.threads",commandPrompt:u(g.showThreads),title:u(g.threads),persistence:"permanent",condition:o.Runtime.ConditionName.NOT_SOURCES_HIDE_ADD_FOLDER,loadView:async()=>(await y()).ThreadsSidebarPane.ThreadsSidebarPane.instance()}),l.ViewManager.registerViewExtension({id:"sources.scopeChain",commandPrompt:u(g.showScope),title:u(g.scope),persistence:"permanent",loadView:async()=>(await y()).ScopeChainSidebarPane.ScopeChainSidebarPane.instance()}),l.ViewManager.registerViewExtension({id:"sources.watch",commandPrompt:u(g.showWatch),title:u(g.watch),persistence:"permanent",loadView:async()=>(await y()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),hasToolbar:!0}),l.ViewManager.registerViewExtension({id:"sources.jsBreakpoints",commandPrompt:u(g.showBreakpoints),title:u(g.breakpoints),persistence:"permanent",loadView:async()=>(await w()).BreakpointsView.BreakpointsView.instance().wrapper}),l.ActionRegistration.registerActionExtension({category:l.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.toggle-pause",iconClass:"pause",toggleable:!0,toggledIconClass:"resume",loadActionDelegate:async()=>(await y()).SourcesPanel.RevealingActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView,l.ShortcutRegistry.ForwardedShortcut])),options:[{value:!0,title:u(g.pauseScriptExecution)},{value:!1,title:u(g.resumeScriptExecution)}],bindings:[{shortcut:"F8",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+\\"},{shortcut:"F5",keybindSets:["vsCode"]},{shortcut:"Shift+F5",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+\\"}]}),l.ActionRegistration.registerActionExtension({category:l.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.step-over",loadActionDelegate:async()=>(await y()).SourcesPanel.ActionDelegate.instance(),title:u(g.stepOverNextFunctionCall),iconClass:"step-over",contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F10",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+'"},{platform:"mac",shortcut:"Meta+'"}]}),l.ActionRegistration.registerActionExtension({category:l.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.step-into",loadActionDelegate:async()=>(await y()).SourcesPanel.ActionDelegate.instance(),title:u(g.stepIntoNextFunctionCall),iconClass:"step-into",contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F11",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+;"},{platform:"mac",shortcut:"Meta+;"}]}),l.ActionRegistration.registerActionExtension({category:l.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.step",loadActionDelegate:async()=>(await y()).SourcesPanel.ActionDelegate.instance(),title:u(g.step),iconClass:"step",contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F9",keybindSets:["devToolsDefault"]}]}),l.ActionRegistration.registerActionExtension({category:l.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.step-out",loadActionDelegate:async()=>(await y()).SourcesPanel.ActionDelegate.instance(),title:u(g.stepOutOfCurrentFunction),iconClass:"step-out",contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Shift+F11",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Shift+Ctrl+;"},{platform:"mac",shortcut:"Shift+Meta+;"}]}),l.ActionRegistration.registerActionExtension({actionId:"debugger.run-snippet",category:l.ActionRegistration.ActionCategory.DEBUGGER,loadActionDelegate:async()=>(await y()).SourcesPanel.ActionDelegate.instance(),title:u(g.runSnippet),iconClass:"play",contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Enter"},{platform:"mac",shortcut:"Meta+Enter"}]}),l.ActionRegistration.registerActionExtension({category:l.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.toggle-breakpoints-active",iconClass:"breakpoint-crossed",toggledIconClass:"breakpoint-crossed-filled",toggleable:!0,loadActionDelegate:async()=>(await y()).SourcesPanel.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),options:[{value:!0,title:u(g.deactivateBreakpoints)},{value:!1,title:u(g.activateBreakpoints)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+F8"},{platform:"mac",shortcut:"Meta+F8"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.add-to-watch",loadActionDelegate:async()=>(await y()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),category:l.ActionRegistration.ActionCategory.DEBUGGER,title:u(g.addSelectedTextToWatches),contextTypes:()=>b((e=>[e.UISourceCodeFrame.UISourceCodeFrame])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+A"},{platform:"mac",shortcut:"Meta+Shift+A"}]}),l.ActionRegistration.registerActionExtension({actionId:"debugger.evaluate-selection",category:l.ActionRegistration.ActionCategory.DEBUGGER,loadActionDelegate:async()=>(await y()).SourcesPanel.ActionDelegate.instance(),title:u(g.evaluateSelectedTextInConsole),contextTypes:()=>b((e=>[e.UISourceCodeFrame.UISourceCodeFrame])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.switch-file",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.switchFile),loadActionDelegate:async()=>(await y()).SourcesView.SwitchFileActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+O"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.rename",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.rename),bindings:[{platform:"windows,linux",shortcut:"F2"},{platform:"mac",shortcut:"Enter"}]}),l.ActionRegistration.registerActionExtension({category:l.ActionRegistration.ActionCategory.SOURCES,actionId:"sources.close-all",loadActionDelegate:async()=>(await y()).SourcesView.ActionDelegate.instance(),title:u(g.closeAll)}),l.ActionRegistration.registerActionExtension({actionId:"sources.jump-to-previous-location",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.jumpToPreviousEditingLocation),loadActionDelegate:async()=>(await y()).SourcesView.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+Minus"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.jump-to-next-location",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.jumpToNextEditingLocation),loadActionDelegate:async()=>(await y()).SourcesView.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+Plus"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.close-editor-tab",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.closeTheActiveTab),loadActionDelegate:async()=>(await y()).SourcesView.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+w"},{shortcut:"Ctrl+W",keybindSets:["vsCode"]},{platform:"windows",shortcut:"Ctrl+F4",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.next-editor-tab",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.nextEditorTab),loadActionDelegate:async()=>(await y()).SourcesView.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+PageDown",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+PageDown",keybindSets:["devToolsDefault","vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.previous-editor-tab",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.previousEditorTab),loadActionDelegate:async()=>(await y()).SourcesView.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+PageUp",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+PageUp",keybindSets:["devToolsDefault","vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.go-to-line",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.goToLine),loadActionDelegate:async()=>(await y()).SourcesView.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Ctrl+g",keybindSets:["devToolsDefault","vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.go-to-member",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.goToAFunctionDeclarationruleSet),loadActionDelegate:async()=>(await y()).SourcesView.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+o",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+o",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+T",keybindSets:["vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+T",keybindSets:["vsCode"]},{shortcut:"F12",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"debugger.toggle-breakpoint",category:l.ActionRegistration.ActionCategory.DEBUGGER,title:u(g.toggleBreakpoint),bindings:[{platform:"windows,linux",shortcut:"Ctrl+b",keybindSets:["devToolsDefault"]},{platform:"mac",shortcut:"Meta+b",keybindSets:["devToolsDefault"]},{shortcut:"F9",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"debugger.toggle-breakpoint-enabled",category:l.ActionRegistration.ActionCategory.DEBUGGER,title:u(g.toggleBreakpointEnabled),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+b"},{platform:"mac",shortcut:"Meta+Shift+b"}]}),l.ActionRegistration.registerActionExtension({actionId:"debugger.breakpoint-input-window",category:l.ActionRegistration.ActionCategory.DEBUGGER,title:u(g.toggleBreakpointInputWindow),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Alt+b"},{platform:"mac",shortcut:"Meta+Alt+b"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.save",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.save),loadActionDelegate:async()=>(await y()).SourcesView.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+s",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+s",keybindSets:["devToolsDefault","vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.save-all",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.saveAll),loadActionDelegate:async()=>(await y()).SourcesView.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+s"},{platform:"mac",shortcut:"Meta+Alt+s"},{platform:"windows,linux",shortcut:"Ctrl+K S",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Alt+S",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({category:l.ActionRegistration.ActionCategory.SOURCES,actionId:"sources.create-snippet",loadActionDelegate:async()=>(await y()).SourcesNavigator.ActionDelegate.instance(),title:u(g.createNewSnippet)}),t.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()||l.ActionRegistration.registerActionExtension({category:l.ActionRegistration.ActionCategory.SOURCES,actionId:"sources.add-folder-to-workspace",loadActionDelegate:async()=>(await y()).SourcesNavigator.ActionDelegate.instance(),iconClass:"plus",title:u(g.addFolderToWorkspace),condition:o.Runtime.ConditionName.NOT_SOURCES_HIDE_ADD_FOLDER}),l.ActionRegistration.registerActionExtension({category:l.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.previous-call-frame",loadActionDelegate:async()=>(await y()).CallStackSidebarPane.ActionDelegate.instance(),title:u(g.previousCallFrame),contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Ctrl+,"}]}),l.ActionRegistration.registerActionExtension({category:l.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.next-call-frame",loadActionDelegate:async()=>(await y()).CallStackSidebarPane.ActionDelegate.instance(),title:u(g.nextCallFrame),contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Ctrl+."}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.search",title:u(g.search),loadActionDelegate:async()=>(await y()).SearchSourcesView.ActionDelegate.instance(),category:l.ActionRegistration.ActionCategory.SOURCES,bindings:[{platform:"mac",shortcut:"Meta+Alt+F",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+J",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Shift+F",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Shift+J",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.increment-css",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.incrementCssUnitBy,{PH1:1}),bindings:[{shortcut:"Alt+Up"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.increment-css-by-ten",title:u(g.incrementCssUnitBy,{PH1:10}),category:l.ActionRegistration.ActionCategory.SOURCES,bindings:[{shortcut:"Alt+PageUp"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.decrement-css",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.decrementCssUnitBy,{PH1:1}),bindings:[{shortcut:"Alt+Down"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.decrement-css-by-ten",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.decrementCssUnitBy,{PH1:10}),bindings:[{shortcut:"Alt+PageDown"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.toggle-navigator-sidebar",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.toggleNavigatorSidebar),loadActionDelegate:async()=>(await y()).SourcesPanel.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+y",keybindSets:["devToolsDefault"]},{platform:"mac",shortcut:"Meta+Shift+y",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+b",keybindSets:["vsCode"]},{platform:"windows,linux",shortcut:"Meta+b",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.toggle-debugger-sidebar",category:l.ActionRegistration.ActionCategory.SOURCES,title:u(g.toggleDebuggerSidebar),loadActionDelegate:async()=>(await y()).SourcesPanel.ActionDelegate.instance(),contextTypes:()=>b((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+h"},{platform:"mac",shortcut:"Meta+Shift+h"}]}),e.Settings.registerSettingExtension({settingName:"navigatorGroupByFolder",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!0}),e.Settings.registerSettingExtension({settingName:"navigatorGroupByAuthored",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!1}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.searchInAnonymousAndContent),settingName:"searchInAnonymousAndContentScripts",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:u(g.searchInAnonymousAndContent)},{value:!1,title:u(g.doNotSearchInAnonymousAndContent)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.automaticallyRevealFilesIn),settingName:"autoRevealInNavigator",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:u(g.automaticallyRevealFilesIn)},{value:!1,title:u(g.doNotAutomaticallyRevealFilesIn)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.enableJavascriptSourceMaps),settingName:"jsSourceMapsEnabled",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:u(g.enableJavascriptSourceMaps)},{value:!1,title:u(g.disableJavascriptSourceMaps)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.enableTabMovesFocus),settingName:"textEditorTabMovesFocus",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:u(g.enableTabMovesFocus)},{value:!1,title:u(g.disableTabMovesFocus)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.detectIndentation),settingName:"textEditorAutoDetectIndent",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:u(g.detectIndentation)},{value:!1,title:u(g.doNotDetectIndentation)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.autocompletion),settingName:"textEditorAutocompletion",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:u(g.enableAutocompletion)},{value:!1,title:u(g.disableAutocompletion)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,title:u(g.bracketMatching),settingName:"textEditorBracketMatching",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:u(g.enableBracketMatching)},{value:!1,title:u(g.disableBracketMatching)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.codeFolding),settingName:"textEditorCodeFolding",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:u(g.enableCodeFolding)},{value:!1,title:u(g.disableCodeFolding)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.showWhitespaceCharacters),settingName:"showWhitespacesInEditor",settingType:e.Settings.SettingType.ENUM,defaultValue:"original",options:[{title:u(g.doNotShowWhitespaceCharacters),text:u(g.none),value:"none"},{title:u(g.showAllWhitespaceCharacters),text:u(g.all),value:"all"},{title:u(g.showTrailingWhitespaceCharacters),text:u(g.trailing),value:"trailing"}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.displayVariableValuesInlineWhile),settingName:"inlineVariableValues",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:u(g.displayVariableValuesInlineWhile)},{value:!1,title:u(g.doNotDisplayVariableValuesInline)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.enableAutoFocusOnDebuggerPaused),settingName:"autoFocusOnDebuggerPausedEnabled",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:u(g.enableAutoFocusOnDebuggerPaused)},{value:!1,title:u(g.disableAutoFocusOnDebuggerPaused)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.enableCssSourceMaps),settingName:"cssSourceMapsEnabled",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:u(g.enableCssSourceMaps)},{value:!1,title:u(g.disableCssSourceMaps)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Synced,title:u(g.allowScrollingPastEndOfFile),settingName:"allowScrollPastEof",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:u(g.allowScrollingPastEndOfFile)},{value:!1,title:u(g.disallowScrollingPastEndOfFile)}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.SOURCES,storageType:e.Settings.SettingStorageType.Local,title:u(g.wasmAutoStepping),settingName:"wasmAutoStepping",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:u(g.enableWasmAutoStepping)},{value:!1,title:u(g.disableWasmAutoStepping)}]}),l.ViewManager.registerLocationResolver({name:"navigator-view",category:l.ViewManager.ViewLocationCategory.SOURCES,loadResolver:async()=>(await y()).SourcesPanel.SourcesPanel.instance()}),l.ViewManager.registerLocationResolver({name:"sources.sidebar-top",category:l.ViewManager.ViewLocationCategory.SOURCES,loadResolver:async()=>(await y()).SourcesPanel.SourcesPanel.instance()}),l.ViewManager.registerLocationResolver({name:"sources.sidebar-bottom",category:l.ViewManager.ViewLocationCategory.SOURCES,loadResolver:async()=>(await y()).SourcesPanel.SourcesPanel.instance()}),l.ViewManager.registerLocationResolver({name:"sources.sidebar-tabs",category:l.ViewManager.ViewLocationCategory.SOURCES,loadResolver:async()=>(await y()).SourcesPanel.SourcesPanel.instance()}),l.ContextMenu.registerProvider({contextTypes:()=>[s.UISourceCode.UISourceCode,s.UISourceCode.UILocation,n.RemoteObject.RemoteObject,n.NetworkRequest.NetworkRequest,...b((e=>[e.UISourceCodeFrame.UISourceCodeFrame]))],loadProvider:async()=>(await y()).SourcesPanel.SourcesPanel.instance(),experiment:void 0}),l.ContextMenu.registerProvider({loadProvider:async()=>(await y()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),contextTypes:()=>[r.ObjectPropertiesSection.ObjectPropertyTreeElement],experiment:void 0}),l.ContextMenu.registerProvider({contextTypes:()=>b((e=>[e.UISourceCodeFrame.UISourceCodeFrame])),loadProvider:async()=>(await y()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),experiment:void 0}),l.ContextMenu.registerProvider({loadProvider:async()=>(await y()).ScopeChainSidebarPane.OpenLinearMemoryInspector.instance(),experiment:void 0,contextTypes:()=>[r.ObjectPropertiesSection.ObjectPropertyTreeElement]}),e.Revealer.registerRevealer({contextTypes:()=>[s.UISourceCode.UILocation],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>(await y()).SourcesPanel.UILocationRevealer.instance()}),e.Revealer.registerRevealer({contextTypes:()=>[n.DebuggerModel.Location],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>(await y()).SourcesPanel.DebuggerLocationRevealer.instance()}),e.Revealer.registerRevealer({contextTypes:()=>[s.UISourceCode.UISourceCode],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>(await y()).SourcesPanel.UISourceCodeRevealer.instance()}),e.Revealer.registerRevealer({contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>(await y()).SourcesPanel.DebuggerPausedDetailsRevealer.instance()}),e.Revealer.registerRevealer({contextTypes:()=>[a.BreakpointManager.BreakpointLocation],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>(await y()).DebuggerPlugin.BreakpointLocationRevealer.instance()}),l.Toolbar.registerToolbarItem({actionId:"sources.add-folder-to-workspace",location:l.Toolbar.ToolbarItemLocation.FILES_NAVIGATION_TOOLBAR,showLabel:!0,condition:o.Runtime.ConditionName.NOT_SOURCES_HIDE_ADD_FOLDER,loadItem:void 0,order:void 0,separator:void 0}),l.Context.registerListener({contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await w()).BreakpointsView.BreakpointsSidebarController.instance()}),l.Context.registerListener({contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await y()).CallStackSidebarPane.CallStackSidebarPane.instance()}),l.Context.registerListener({contextTypes:()=>[n.DebuggerModel.CallFrame],loadListener:async()=>(await y()).ScopeChainSidebarPane.ScopeChainSidebarPane.instance()}),l.ContextMenu.registerItem({location:l.ContextMenu.ItemLocation.NAVIGATOR_MENU_DEFAULT,actionId:"quickOpen.show",order:void 0}),l.ContextMenu.registerItem({location:l.ContextMenu.ItemLocation.MAIN_MENU_DEFAULT,actionId:"sources.search",order:void 0}),c.FilteredListWidget.registerProvider({prefix:"@",iconName:"symbol",iconWidth:"16px",provider:async()=>new((await y()).OutlineQuickOpen.OutlineQuickOpen),titlePrefix:u(g.goTo),titleSuggestion:u(g.symbol)}),c.FilteredListWidget.registerProvider({prefix:":",iconName:"colon",iconWidth:"20px",provider:async()=>new((await y()).GoToLineQuickOpen.GoToLineQuickOpen),titlePrefix:u(g.goTo),titleSuggestion:u(g.line)}),c.FilteredListWidget.registerProvider({prefix:"",iconName:"document",iconWidth:"16px",provider:async()=>new((await y()).OpenFileQuickOpen.OpenFileQuickOpen),titlePrefix:u(g.open),titleSuggestion:u(g.file)});
