import*as e from"./timeline.js";self.Timeline=self.Timeline||{},Timeline=Timeline||{},Timeline.CLSLinkifier=Timeline.CLSLinkifier||{},Timeline.CLSLinkifier.CLSRect=e.CLSLinkifier.CLSRect,Timeline.CLSLinkifier.Linkifier=e.CLSLinkifier.Linkifier,Timeline.CountersGraph=e.CountersGraph.CountersGraph,Timeline.CountersGraph.Counter=e.CountersGraph.Counter,Timeline.CountersGraph.CounterUI=e.CountersGraph.CounterUI,Timeline.CountersGraph.Calculator=e.CountersGraph.Calculator,Timeline.EventsTimelineTreeView=e.EventsTimelineTreeView.EventsTimelineTreeView,Timeline.EventsTimelineTreeView.Filters=e.EventsTimelineTreeView.Filters,Timeline.PerformanceModel=e.PerformanceModel.PerformanceModel,Timeline.PerformanceModel.Events=e.PerformanceModel.Events,Timeline.TimelineController=e.TimelineController.TimelineController,Timeline.TimelineController.Client=e.TimelineController.Client,Timeline.TimelineDetailsView=e.TimelineDetailsView.TimelineDetailsView,Timeline.TimelineDetailsView.Tab=e.TimelineDetailsView.Tab,Timeline.TimelineEventOverview=e.TimelineEventOverview.TimelineEventOverview,Timeline.TimelineEventOverviewNetwork=e.TimelineEventOverview.TimelineEventOverviewNetwork,Timeline.TimelineEventOverviewCPUActivity=e.TimelineEventOverview.TimelineEventOverviewCPUActivity,Timeline.TimelineEventOverviewResponsiveness=e.TimelineEventOverview.TimelineEventOverviewResponsiveness,Timeline.TimelineFilmStripOverview=e.TimelineEventOverview.TimelineFilmStripOverview,Timeline.TimelineEventOverviewMemory=e.TimelineEventOverview.TimelineEventOverviewMemory,Timeline.Quantizer=e.TimelineEventOverview.Quantizer,Timeline.TimelineFilters={},Timeline.TimelineFilters.IsLong=e.TimelineFilters.IsLong,Timeline.TimelineFilters.Category=e.TimelineFilters.Category,Timeline.TimelineFilters.RegExp=e.TimelineFilters.TimelineRegExp,Timeline.TimelineFlameChartDataProvider=e.TimelineFlameChartDataProvider.TimelineFlameChartDataProvider,Timeline.TimelineFlameChartDataProvider.InstantEventVisibleDurationMs=e.TimelineFlameChartDataProvider.InstantEventVisibleDurationMs,Timeline.TimelineFlameChartDataProvider.Events=e.TimelineFlameChartDataProvider.Events,Timeline.TimelineFlameChartDataProvider.EntryType=e.TimelineFlameChartDataProvider.EntryType,Timeline.TimelineFlameChartNetworkDataProvider=e.TimelineFlameChartNetworkDataProvider.TimelineFlameChartNetworkDataProvider,Timeline.TimelineFlameChartView=e.TimelineFlameChartView.TimelineFlameChartView,Timeline.TimelineFlameChartView.Selection=e.TimelineFlameChartView.Selection,Timeline.TimelineFlameChartView._ColorBy=e.TimelineFlameChartView.ColorBy,Timeline.FlameChartStyle=e.TimelineFlameChartView.FlameChartStyle,Timeline.TimelineFlameChartMarker=e.TimelineFlameChartView.TimelineFlameChartMarker,Timeline.TimelineHistoryManager=e.TimelineHistoryManager.TimelineHistoryManager,Timeline.TimelineHistoryManager.DropDown=e.TimelineHistoryManager.DropDown,Timeline.TimelineHistoryManager.ToolbarButton=e.TimelineHistoryManager.ToolbarButton,Timeline.TimelineLayersView=e.TimelineLayersView.TimelineLayersView,Timeline.TimelineLoader=e.TimelineLoader.TimelineLoader,Timeline.TimelineLoader.TransferChunkLengthBytes=e.TimelineLoader.TransferChunkLengthBytes,Timeline.TimelineLoader.Client=e.TimelineLoader.Client,Timeline.TimelineLoader.State=e.TimelineLoader.State,Timeline.TimelinePaintProfilerView=e.TimelinePaintProfilerView.TimelinePaintProfilerView,Timeline.TimelinePaintImageView=e.TimelinePaintProfilerView.TimelinePaintImageView,Timeline.TimelinePanel=e.TimelinePanel.TimelinePanel,Timeline.TimelinePanel.State=e.TimelinePanel.State,Timeline.TimelinePanel.rowHeight=e.TimelinePanel.rowHeight,Timeline.TimelinePanel.headerHeight=e.TimelinePanel.headerHeight,Timeline.TimelinePanel.StatusPane=e.TimelinePanel.StatusPane,Timeline.TimelinePanel.ActionDelegate=e.TimelinePanel.ActionDelegate,Timeline.TimelineSelection=e.TimelineSelection.TimelineSelection,Timeline.TimelineModeViewDelegate=e.TimelinePanel.TimelineModeViewDelegate,Timeline.LoadTimelineHandler=e.TimelinePanel.LoadTimelineHandler,Timeline.TimelineTreeView=e.TimelineTreeView.TimelineTreeView,Timeline.TimelineTreeView.GridNode=e.TimelineTreeView.GridNode,Timeline.TimelineTreeView.TreeGridNode=e.TimelineTreeView.TreeGridNode,Timeline.AggregatedTimelineTreeView=e.TimelineTreeView.AggregatedTimelineTreeView,Timeline.CallTreeTimelineTreeView=e.TimelineTreeView.CallTreeTimelineTreeView,Timeline.BottomUpTimelineTreeView=e.TimelineTreeView.BottomUpTimelineTreeView,Timeline.TimelineStackView=e.TimelineTreeView.TimelineStackView,Timeline.TimelineUIUtils=e.TimelineUIUtils.TimelineUIUtils,Timeline.TimelineUIUtils.NetworkCategory=e.TimelineUIUtils.NetworkCategory,Timeline.TimelineUIUtils._aggregatedStatsKey=e.TimelineUIUtils.aggregatedStatsKey,Timeline.TimelineUIUtils.InvalidationsGroupElement=e.TimelineUIUtils.InvalidationsGroupElement,Timeline.TimelineUIUtils._previewElementSymbol=e.TimelineUIUtils.previewElementSymbol,Timeline.TimelineUIUtils.EventDispatchTypeDescriptor=e.TimelineUIUtils.EventDispatchTypeDescriptor,Timeline.TimelineUIUtils._categoryBreakdownCacheSymbol=e.TimelineUIUtils.categoryBreakdownCacheSymbol,Timeline.TimelineRecordStyle=e.TimelineUIUtils.TimelineRecordStyle,Timeline.TimelineCategory=e.TimelineUIUtils.TimelineCategory,Timeline.TimelineDetailsContentHelper=e.TimelineUIUtils.TimelineDetailsContentHelper,Timeline.UIDevtoolsController=e.UIDevtoolsController.UIDevtoolsController,Timeline.UIDevtoolsUtils=e.UIDevtoolsUtils.UIDevtoolsUtils,Timeline.UIDevtoolsUtils.RecordType=e.UIDevtoolsUtils.RecordType;
