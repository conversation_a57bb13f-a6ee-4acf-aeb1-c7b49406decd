import*as e from"../../core/i18n/i18n.js";import*as t from"../../ui/legacy/legacy.js";import*as i from"../../core/common/common.js";import*as n from"../../core/root/root.js";const s={devices:"Devices",showDevices:"Show Devices"},o=e.i18n.registerUIStrings("panels/settings/emulation/emulation-meta.ts",s),a=e.i18n.getLazilyComputedLocalizedString.bind(void 0,o);let r;t.ViewManager.registerViewExtension({location:"settings-view",commandPrompt:a(s.showDevices),title:a(s.devices),order:30,loadView:async()=>(await async function(){return r||(r=await import("./emulation/emulation.js")),r}()).DevicesSettingsTab.DevicesSettingsTab.instance(),id:"devices",settings:["standardEmulatedDeviceList","customEmulatedDeviceList"]});const c={shortcuts:"Shortcuts",preferences:"Preferences",experiments:"Experiments",ignoreList:"Ignore List",showShortcuts:"Show Shortcuts",showPreferences:"Show Preferences",showExperiments:"Show Experiments",showIgnoreList:"Show Ignore List",settings:"Settings",documentation:"Documentation"},g=e.i18n.registerUIStrings("panels/settings/settings-meta.ts",c),d=e.i18n.getLazilyComputedLocalizedString.bind(void 0,g);let m;async function l(){return m||(m=await import("./settings.js")),m}t.ViewManager.registerViewExtension({location:"settings-view",id:"preferences",title:d(c.preferences),commandPrompt:d(c.showPreferences),order:0,loadView:async()=>(await l()).SettingsScreen.GenericSettingsTab.instance()}),t.ViewManager.registerViewExtension({location:"settings-view",id:"experiments",title:d(c.experiments),commandPrompt:d(c.showExperiments),order:3,experiment:n.Runtime.ExperimentName.ALL,loadView:async()=>(await l()).SettingsScreen.ExperimentsSettingsTab.instance()}),t.ViewManager.registerViewExtension({location:"settings-view",id:"blackbox",title:d(c.ignoreList),commandPrompt:d(c.showIgnoreList),order:4,loadView:async()=>(await l()).FrameworkIgnoreListSettingsTab.FrameworkIgnoreListSettingsTab.instance()}),t.ViewManager.registerViewExtension({location:"settings-view",id:"keybinds",title:d(c.shortcuts),commandPrompt:d(c.showShortcuts),order:100,loadView:async()=>(await l()).KeybindsSettingsTab.KeybindsSettingsTab.instance()}),t.ActionRegistration.registerActionExtension({category:t.ActionRegistration.ActionCategory.SETTINGS,actionId:"settings.show",title:d(c.settings),loadActionDelegate:async()=>(await l()).SettingsScreen.ActionDelegate.instance(),iconClass:"gear",bindings:[{shortcut:"F1",keybindSets:["devToolsDefault"]},{shortcut:"Shift+?"},{platform:"windows,linux",shortcut:"Ctrl+,",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+,",keybindSets:["vsCode"]}]}),t.ActionRegistration.registerActionExtension({category:t.ActionRegistration.ActionCategory.SETTINGS,actionId:"settings.documentation",title:d(c.documentation),loadActionDelegate:async()=>(await l()).SettingsScreen.ActionDelegate.instance()}),t.ActionRegistration.registerActionExtension({category:t.ActionRegistration.ActionCategory.SETTINGS,actionId:"settings.shortcuts",title:d(c.shortcuts),loadActionDelegate:async()=>(await l()).SettingsScreen.ActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+K Ctrl+S",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+K Meta+S",keybindSets:["vsCode"]}]}),t.ViewManager.registerLocationResolver({name:"settings-view",category:t.ViewManager.ViewLocationCategory.SETTINGS,loadResolver:async()=>(await l()).SettingsScreen.SettingsScreen.instance()}),i.Revealer.registerRevealer({contextTypes:()=>[i.Settings.Setting,n.Runtime.Experiment],loadRevealer:async()=>(await l()).SettingsScreen.Revealer.instance(),destination:void 0}),t.ContextMenu.registerItem({location:t.ContextMenu.ItemLocation.MAIN_MENU_FOOTER,actionId:"settings.shortcuts",order:void 0}),t.ContextMenu.registerItem({location:t.ContextMenu.ItemLocation.MAIN_MENU_HELP_DEFAULT,actionId:"settings.documentation",order:void 0});
