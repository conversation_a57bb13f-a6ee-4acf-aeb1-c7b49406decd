import*as t from"../../core/common/common.js";import*as e from"../../core/host/host.js";import*as i from"../../core/i18n/i18n.js";import*as n from"../../core/root/root.js";import*as s from"../../ui/components/icon_button/icon_button.js";import*as o from"../../ui/legacy/components/utils/utils.js";import*as r from"../../ui/legacy/legacy.js";import*as a from"./components/components.js";import{highlightElement as c}from"../utils/utils.js";import*as l from"../../core/platform/platform.js";const d=new CSSStyleSheet;d.replaceSync('.settings-window-main{color:var(--color-text-primary);background-color:var(--color-background);padding:11px 0 0}.settings-content{overflow-y:auto;overflow-x:hidden;margin:8px 8px 8px 0;padding:0 4px;flex:auto}.settings-container{width:100%;column-width:288px}.settings-block{display:block;padding-bottom:9px;width:288px;break-inside:avoid}.settings-tab.settings-container{column-width:308px}.settings-tab .settings-block{margin-left:20px}.settings-line{padding-bottom:5px;margin-bottom:5px}.settings-key-cell{display:inline-block;width:153px;white-space:nowrap;text-align:right;vertical-align:middle;padding-right:6px}.settings-cell{display:inline-block;width:135px;vertical-align:middle}.settings-section-title{font-size:120%;text-align:left}.settings-combine-keys{margin:0 0.3em;font-size:9px}fieldset{margin:0;padding:0;border:none}.experiments-filter{padding-top:1px;display:flex;align-items:center}label{padding-right:8px;padding-bottom:8px}.settings-tab p{margin:12px 0}.settings-block p p{padding-left:30px}.settings-select{align-items:center;display:grid}.settings-experiments-warning-subsection-warning{color:var(--color-accent-red)}.settings-experiments-warning-subsection-message{color:inherit}.settings-content input[type="checkbox"]{margin:1px 7px 1px 2px}.settings-window-title{font-size:18px;color:var(--color-text-primary);padding:0 0 5px 13px}.settings-container-wrapper{position:absolute;top:31px;left:0;right:0;bottom:0;overflow:auto;padding-top:9px;border-top:1px solid var(--color-details-hairline)}.settings-tab.settings-content{margin:0;padding:0}.settings-tab-container{flex:auto;overflow:hidden}.settings-tab-container header{padding:0 0 6px}#experiments-tab-content .settings-container{column-width:auto}#experiments-tab-content .settings-block{width:auto;margin-left:0;margin-right:10px}.settings-tab-container header > h1{font-size:18px;font-weight:normal;margin:0;padding-bottom:3px;white-space:nowrap}.settings-tab .settings-section-title{margin-left:-20px;color:var(--color-text-secondary)}.settings-tab .settings-block label:hover{color:var(--color-text-secondary)}.settings-tab .settings-block fieldset:disabled label:hover{color:inherit}.settings-tab select{margin-left:10px;width:80%}.settings-experiment{display:grid;grid-template-columns:auto min-content 1fr}.settings-experiment .devtools-link{display:flex!important;align-items:center}.settings-experiment .devtools-link:has(.link-icon){outline-offset:0}.experiment-label{margin-right:2px}.settings-experiment-unstable{color:var(--color-text-secondary)}.settings-experiment .feedback-link{color:var(--color-primary-old);text-decoration-line:underline;margin-left:4px}.tabbed-pane-content slot::slotted(.widget){overflow:visible!important}@media (forced-colors: active){.settings-window-title{color:canvastext}.tabbed-pane-header-tab{background:ButtonFace}.tabbed-pane-header-tab-title{color:canvastext}}@media (forced-colors: active) and (prefers-color-scheme: dark){.tabbed-pane-header-tab.selected{background:ButtonFace}.tabbed-pane-header-tab.selected .tabbed-pane-header-tab-title{color:HighlightText}}\n/*# sourceURL=settingsScreen.css */\n');const h={settings:"Settings",shortcuts:"Shortcuts",preferences:"Preferences",restoreDefaultsAndReload:"Restore defaults and reload",experiments:"Experiments",theseExperimentsCouldBeUnstable:"These experiments could be unstable or unreliable and may require you to restart DevTools.",theseExperimentsAreParticularly:"These experiments are particularly unstable. Enable at your own risk.",warning:"WARNING:",oneOrMoreSettingsHaveChanged:"One or more settings have changed which requires a reload to take effect.",filterExperimentsLabel:"Filter",noResults:"No experiments match the filter",learnMore:"Learn more",sendFeedback:"Send feedback"},g=i.i18n.registerUIStrings("panels/settings/SettingsScreen.ts",h),p=i.i18n.getLocalizedString.bind(void 0,g);let u,m,b,S,y;class x extends r.Widget.VBox{tabbedLocation;keybindsTab;reportTabOnReveal;constructor(){super(!0),this.contentElement.classList.add("settings-window-main"),this.contentElement.classList.add("vbox");const t=document.createElement("div"),e=r.Utils.createShadowRootWithCoreStyles(t,{cssFile:[d],delegatesFocus:void 0}).createChild("div","settings-window-title");r.ARIAUtils.markAsHeading(e,1),e.textContent=p(h.settings),this.tabbedLocation=r.ViewManager.ViewManager.instance().createTabbedLocation((()=>x.revealSettingsScreen()),"settings-view");const i=this.tabbedLocation.tabbedPane();i.registerCSSFiles([d]),i.leftToolbar().appendToolbarItem(new r.Toolbar.ToolbarItem(t)),i.setShrinkableTabs(!1),i.makeVerticalTabLayout();const n=r.ViewManager.ViewManager.instance().view("keybinds");n&&n.widget().then((t=>{this.keybindsTab=t})),i.show(this.contentElement),i.selectTab("preferences"),i.addEventListener(r.TabbedPane.Events.TabInvoked,this.tabInvoked,this),this.reportTabOnReveal=!1}static instance(t={forceNew:null}){const{forceNew:e}=t;return u&&!e||(u=new x),u}static revealSettingsScreen(){const t=x.instance();if(t.isShowing())return t;t.reportTabOnReveal=!0;const e=new r.Dialog.Dialog;return e.contentElement.tabIndex=-1,e.addCloseButton(),e.setOutsideClickCallback((()=>{})),e.setPointerEventsBehavior("PierceGlassPane"),e.setOutsideTabIndexBehavior(r.Dialog.OutsideTabIndexBehavior.PreserveMainViewTabIndex),t.show(e.contentElement),e.setEscapeKeyCallback(t.onEscapeKeyPressed.bind(t)),e.setMarginBehavior("NoMargin"),e.show(),t}static async showSettingsScreen(t={name:void 0,focusTabHeader:void 0}){const{name:e,focusTabHeader:i}=t,n=x.revealSettingsScreen();n.selectTab(e||"preferences");const s=n.tabbedLocation.tabbedPane();await s.waitForTabElementUpdate(),i?s.focusSelectedTabHeader():s.focus()}resolveLocation(t){return this.tabbedLocation}selectTab(t){this.tabbedLocation.tabbedPane().selectTab(t,!0)}tabInvoked(t){const e=t.data;if(!e.isUserGesture)return;const i=e.prevTabId,n=e.tabId;!this.reportTabOnReveal&&i&&i===n||(this.reportTabOnReveal=!1,this.reportSettingsPanelShown(n))}reportSettingsPanelShown(t){t!==p(h.shortcuts)?e.userMetrics.settingsPanelShown(t):e.userMetrics.settingsPanelShown("shortcuts")}onEscapeKeyPressed(t){"keybinds"===this.tabbedLocation.tabbedPane().selectedTabId&&this.keybindsTab&&this.keybindsTab.onEscapeKeyPressed(t)}wasShown(){super.wasShown(),this.registerCSSFiles([d])}}class f extends r.Widget.VBox{containerElement;constructor(t,e){super(),this.element.classList.add("settings-tab-container"),e&&(this.element.id=e);const i=this.element.createChild("header");r.UIUtils.createTextChild(i.createChild("h1"),t),this.containerElement=this.element.createChild("div","settings-container-wrapper").createChild("div","settings-tab settings-content settings-container")}appendSection(t){const e=this.containerElement.createChild("div","settings-block");if(t){r.ARIAUtils.markAsGroup(e);const i=e.createChild("div","settings-section-title");i.textContent=t,r.ARIAUtils.markAsHeading(i,2),r.ARIAUtils.setLabel(e,t)}return e}}class k extends f{syncSection=new a.SyncSection.SyncSection;settingToControl=new Map;constructor(){super(p(h.preferences),"preferences-tab-content");const e=[t.Settings.SettingCategory.NONE,t.Settings.SettingCategory.APPEARANCE,t.Settings.SettingCategory.SOURCES,t.Settings.SettingCategory.ELEMENTS,t.Settings.SettingCategory.NETWORK,t.Settings.SettingCategory.PERFORMANCE,t.Settings.SettingCategory.MEMORY,t.Settings.SettingCategory.CONSOLE,t.Settings.SettingCategory.EXTENSIONS,t.Settings.SettingCategory.PERSISTENCE,t.Settings.SettingCategory.DEBUGGER,t.Settings.SettingCategory.GLOBAL,t.Settings.SettingCategory.SYNC],i=t.Settings.getRegisteredSettings().sort(((t,e)=>t.order&&e.order?t.order-e.order:t.order?-1:e.order?1:0));for(const t of e){const e=i.filter((e=>e.category===t&&k.isSettingVisible(e)));this.createSectionElement(t,e)}this.appendSection().appendChild(r.UIUtils.createTextButton(p(h.restoreDefaultsAndReload),(function(){t.Settings.Settings.instance().clearAll(),o.Reload.reload()})))}static instance(t={forceNew:null}){const{forceNew:e}=t;return m&&!e||(m=new k),m}static isSettingVisible(t){const e=t.titleMac&&t.titleMac(),i=t.title&&t.title();return Boolean((e||i)&&t.category)}wasShown(){super.wasShown(),this.updateSyncSection()}updateSyncSection(){e.InspectorFrontendHost.InspectorFrontendHostInstance.getSyncInformation((e=>{this.syncSection.data={syncInfo:e,syncSetting:t.Settings.moduleSetting("sync_preferences")}}))}createExtensionSection(e){const i=t.Settings.SettingCategory.EXTENSIONS,n=o.Linkifier.LinkHandlerSettingUI.instance().settingElement();if(n){this.createStandardSectionElement(i,e).appendChild(n)}}createSectionElement(e,i){e===t.Settings.SettingCategory.EXTENSIONS?this.createExtensionSection(i):e===t.Settings.SettingCategory.SYNC&&i.length>0?this.containerElement.appendChild(this.syncSection):i.length>0&&this.createStandardSectionElement(e,i)}createStandardSectionElement(e,i){const n=t.Settings.getLocalizedSettingsCategory(e),s=this.appendSection(n);for(const e of i){const i=t.Settings.Settings.instance().moduleSetting(e.settingName),n=r.SettingsUI.createControlForSetting(i);n&&(this.settingToControl.set(i,n),s.appendChild(n))}return s}highlightObject(e){if(e instanceof t.Settings.Setting){const t=this.settingToControl.get(e);t&&c(t)}}}class w extends f{#t;#e;#i;experimentToControl=new Map;constructor(){super(p(h.experiments),"experiments-tab-content");const t=this.appendSection();t.classList.add("experiments-filter");const e=t.createChild("label");e.textContent=p(h.filterExperimentsLabel),this.#i=r.UIUtils.createInput("","text"),r.ARIAUtils.bindLabelToControl(e,this.#i),t.appendChild(this.#i),this.#i.addEventListener("input",(()=>this.renderExperiments(this.#i.value.toLowerCase())),!1),this.setFilter("")}renderExperiments(t){this.experimentToControl.clear(),this.#t&&this.#t.remove(),this.#e&&this.#e.remove();const e=n.Runtime.experiments.allConfigurableExperiments().sort(),i=e.filter((e=>e.unstable&&e.title.toLowerCase().includes(t))),s=e.filter((e=>!e.unstable&&e.title.toLowerCase().includes(t)));if(s.length){this.#t=this.appendSection();const t=p(h.theseExperimentsCouldBeUnstable);this.#t.appendChild(this.createExperimentsWarningSubsection(t));for(const t of s)this.#t.appendChild(this.createExperimentCheckbox(t))}if(i.length){this.#e=this.appendSection();const t=p(h.theseExperimentsAreParticularly);this.#e.appendChild(this.createExperimentsWarningSubsection(t));for(const t of i)this.#e.appendChild(this.createExperimentCheckbox(t))}if(!s.length&&!i.length){this.#t=this.appendSection();this.#t.createChild("span").textContent=p(h.noResults)}}static instance(t={forceNew:null}){const{forceNew:e}=t;return b&&!e||(b=new w),b}createExperimentsWarningSubsection(t){const e=document.createElement("div");e.createChild("span","settings-experiments-warning-subsection-warning").textContent=p(h.warning),r.UIUtils.createTextChild(e," ");return e.createChild("span","settings-experiments-warning-subsection-message").textContent=t,e}createExperimentCheckbox(t){const i=r.UIUtils.CheckboxLabel.create(t.title,t.isEnabled());i.classList.add("experiment-label");const n=i.checkboxElement;n.name=t.name,n.addEventListener("click",(function(){t.setEnabled(n.checked),e.userMetrics.experimentChanged(t.name,t.isEnabled()),r.InspectorView.InspectorView.instance().displayReloadRequiredWarning(p(h.oneOrMoreSettingsHaveChanged))}),!1);const o=document.createElement("p");if(this.experimentToControl.set(t,o),o.classList.add("settings-experiment"),t.unstable&&!t.isEnabled()&&o.classList.add("settings-experiment-unstable"),o.appendChild(i),t.docLink){const e=r.XLink.XLink.create(t.docLink);e.textContent="",e.setAttribute("aria-label",p(h.learnMore));const i=new s.Icon.Icon;i.data={iconName:"help",color:"var(--icon-default)",width:"16px",height:"16px"},i.classList.add("link-icon"),e.prepend(i),o.appendChild(e)}if(t.feedbackLink){const e=r.XLink.XLink.create(t.feedbackLink);e.textContent=p(h.sendFeedback),e.classList.add("feedback-link"),o.appendChild(e)}return o}highlightObject(t){if(t instanceof n.Runtime.Experiment){const e=this.experimentToControl.get(t);e&&c(e)}}setFilter(t){this.#i.value=t,this.#i.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!0}))}}class v{static instance(t={forceNew:null}){const{forceNew:e}=t;return S&&!e||(S=new v),S}handleAction(t,i){switch(i){case"settings.show":return x.showSettingsScreen({focusTabHeader:!0}),!0;case"settings.documentation":return e.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(r.UIUtils.addReferrerToURL("https://developer.chrome.com/docs/devtools/")),!0;case"settings.shortcuts":return x.showSettingsScreen({name:"keybinds",focusTabHeader:!0}),!0}return!1}}class C{static instance(t={forceNew:!1}){const{forceNew:e}=t;return y&&!e||(y=new C),y}reveal(i){if(i instanceof n.Runtime.Experiment)return e.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront(),x.showSettingsScreen({name:"experiments"}).then((()=>w.instance().highlightObject(i))),Promise.resolve();console.assert(i instanceof t.Settings.Setting);const s=i;for(const n of t.Settings.getRegisteredSettings())if(k.isSettingVisible(n)&&n.settingName===s.name)return e.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront(),x.showSettingsScreen().then((()=>k.instance().highlightObject(i))),Promise.resolve();for(const t of r.ViewManager.getRegisteredViewExtensions()){const n=t.viewId();if("settings-view"!==t.location())continue;const o=t.settings();if(o&&-1!==o.indexOf(s.name))return e.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront(),x.showSettingsScreen({name:n}).then((async()=>{const e=await t.widget();e instanceof f&&e.highlightObject(i)})),Promise.resolve()}return Promise.reject()}}var E=Object.freeze({__proto__:null,SettingsScreen:x,GenericSettingsTab:k,ExperimentsSettingsTab:w,ActionDelegate:v,Revealer:C});const I=new CSSStyleSheet;I.replaceSync(":host{overflow:hidden}.header{padding:0 0 6px;border-bottom:var(--legacy-divider-border);font-size:18px;font-weight:normal;flex:none}.intro{margin-top:10px}.ignore-list-option{flex:none;padding:3px 6px;min-height:30px}.ignore-list-option devtools-icon{margin-bottom:-1px}.ignore-list-option-group{margin-top:16px;margin-bottom:3px;margin-left:8px;flex-shrink:0}.add-button{margin:10px 2px;align-self:flex-start;flex:none}.ignore-list{max-width:500px;flex:0 1 auto}.ignore-list-global-enable{padding:3px 0;color:var(--color-text-secondary);font-size:120%;margin-top:20px}.ignore-list-item{padding:3px 5px;height:30px;display:flex;align-items:center;position:relative;flex:auto 1 1}.ignore-list-pattern{flex:auto;min-width:100px}.ignore-list-item .ignore-list-pattern{white-space:nowrap;text-overflow:ellipsis;user-select:none;color:var(--color-text-primary);overflow:hidden}.ignore-list-edit-row{flex:none;display:flex;flex-direction:row;margin:6px 5px;align-items:center}.ignore-list-edit-row input,\n.ignore-list-edit-row select{width:100%;text-align:inherit}.ignore-list-options{margin-left:22px;display:flex;flex-direction:column}.ignore-list-options.ignore-listing-disabled{opacity:30%}.list:has(.ignore-list-empty),\n.list:has(.ignore-list-edit-row),\n.list:has(.ignore-list-item){border:none}.editor-container:has(.ignore-list-edit-row){background:var(--color-background-elevation-0)}.ignore-list.list-editing ~ .add-button{display:none}.devtools-link:has(devtools-icon){margin-left:6px}\n/*# sourceURL=frameworkIgnoreListSettingsTab.css */\n");const L={frameworkIgnoreList:"Framework Ignore List",debuggerWillSkipThroughThe:"Debugger will skip through the scripts and will not stop on exceptions thrown by them.",ignoreListContentScripts:"Content scripts injected by extensions",automaticallyIgnoreListKnownThirdPartyScripts:"Known third-party scripts from source maps",enableIgnoreListing:"Enable Ignore Listing",enableIgnoreListingTooltip:"Uncheck to disable all ignore listing",generalExclusionRules:"General exclusion rules:",customExclusionRules:"Custom exclusion rules:",addPattern:"Add pattern...",addFilenamePattern:"Add filename pattern",ignoreScriptsWhoseNamesMatchS:"Ignore scripts whose names match ''{PH1}''",pattern:"Add Pattern",patternCannotBeEmpty:"Pattern cannot be empty",patternAlreadyExists:"Pattern already exists",patternMustBeAValidRegular:"Pattern must be a valid regular expression",learnMore:"Learn more"},T=i.i18n.registerUIStrings("panels/settings/FrameworkIgnoreListSettingsTab.ts",L),A=i.i18n.getLocalizedString.bind(void 0,T);let R;class U extends r.Widget.VBox{list;setting;editor;constructor(){super(!0);const e=this.contentElement.createChild("div","header");e.textContent=A(L.frameworkIgnoreList),r.ARIAUtils.markAsHeading(e,1),this.contentElement.createChild("div","intro").textContent=A(L.debuggerWillSkipThroughThe);const i=t.Settings.Settings.instance().moduleSetting("enableIgnoreListing"),n=this.contentElement.createChild("div","ignore-list-global-enable");n.appendChild(r.SettingsUI.createSettingCheckbox(A(L.enableIgnoreListing),i,!0)),r.Tooltip.Tooltip.install(n,A(L.enableIgnoreListingTooltip));const o=this.contentElement.createChild("div","ignore-list-options");o.createChild("div","ignore-list-option-group").textContent=A(L.generalExclusionRules);o.createChild("div","ignore-list-option").appendChild(r.SettingsUI.createSettingCheckbox(A(L.ignoreListContentScripts),t.Settings.Settings.instance().moduleSetting("skipContentScripts"),!0));const a=o.createChild("div","ignore-list-option");a.appendChild(r.SettingsUI.createSettingCheckbox(A(L.automaticallyIgnoreListKnownThirdPartyScripts),t.Settings.Settings.instance().moduleSetting("automaticallyIgnoreListKnownThirdPartyScripts"),!0));const c=r.XLink.XLink.create("http://goo.gle/skip-third-party");c.textContent="",c.setAttribute("aria-label",A(L.learnMore));const l=new s.Icon.Icon;l.data={iconName:"help",color:"var(--icon-default)",width:"16px",height:"16px"},c.prepend(l),a.appendChild(c),o.createChild("div","ignore-list-option-group").textContent=A(L.customExclusionRules),this.list=new r.ListWidget.ListWidget(this),this.list.element.classList.add("ignore-list");const d=document.createElement("div");d.classList.add("ignore-list-empty"),this.list.setEmptyPlaceholder(d),this.list.show(o);const h=r.UIUtils.createTextButton(A(L.addPattern),this.addButtonClicked.bind(this),"add-button");function g(){i.get()?o.classList.remove("ignore-listing-disabled"):o.classList.add("ignore-listing-disabled")}r.ARIAUtils.setLabel(h,A(L.addFilenamePattern)),o.appendChild(h),this.setting=t.Settings.Settings.instance().moduleSetting("skipStackFramesPattern"),this.setting.addChangeListener(this.settingUpdated,this),this.setDefaultFocusedElement(h),i.addChangeListener(g),g()}static instance(t={forceNew:null}){const{forceNew:e}=t;return R&&!e||(R=new U),R}wasShown(){super.wasShown(),this.list.registerCSSFiles([I]),this.registerCSSFiles([I]),this.settingUpdated()}settingUpdated(){this.list.clear();const t=this.setting.getAsArray();for(let e=0;e<t.length;++e)this.list.appendItem(t[e],!0)}addButtonClicked(){this.list.addNewItem(this.setting.getAsArray().length,{pattern:"",disabled:!1})}renderItem(t,e){const i=document.createElement("div"),n=this.setting,s=r.UIUtils.CheckboxLabel.create(t.pattern,!t.disabled),o=A(L.ignoreScriptsWhoseNamesMatchS,{PH1:t.pattern});return r.Tooltip.Tooltip.install(s,o),s.checkboxElement.ariaLabel=o,s.checkboxElement.addEventListener("change",(function(){const e=!s.checkboxElement.checked;t.disabled!==e&&(t.disabled=e,t.disabledForUrl=void 0,n.setAsArray(n.getAsArray()))}),!1),i.appendChild(s),i.classList.add("ignore-list-item"),i}removeItemRequested(t,e){const i=this.setting.getAsArray();i.splice(e,1),this.setting.setAsArray(i)}commitEdit(t,e,i){t.pattern=e.control("pattern").value.trim();const n=this.setting.getAsArray();i&&n.push(t),this.setting.setAsArray(n)}beginEdit(t){const e=this.createEditor();return e.control("pattern").value=t.pattern,e}createEditor(){if(this.editor)return this.editor;const t=new r.ListWidget.Editor;this.editor=t;const e=t.contentElement();e.createChild("div","ignore-list-edit-row").createChild("div","ignore-list-pattern").textContent=A(L.pattern);const i=e.createChild("div","ignore-list-edit-row"),n=t.createInput("pattern","text","/framework\\.js$",function(t,e,i){const n=i.value.trim(),s=this.setting.getAsArray();if(!n.length)return{valid:!1,errorMessage:A(L.patternCannotBeEmpty)};for(let t=0;t<s.length;++t)if(t!==e&&s[t].pattern===n)return{valid:!1,errorMessage:A(L.patternAlreadyExists)};let o;try{o=new RegExp(n)}catch(t){}if(!o)return{valid:!1,errorMessage:A(L.patternMustBeAValidRegular)};return{valid:!0,errorMessage:void 0}}.bind(this));return r.ARIAUtils.setLabel(n,A(L.pattern)),i.createChild("div","ignore-list-pattern").appendChild(n),t}}var F=Object.freeze({__proto__:null,FrameworkIgnoreListSettingsTab:U});const K=new CSSStyleSheet;K.replaceSync('header{padding:0 0 6px;border-bottom:1px solid var(--color-details-hairline);flex:none;margin-bottom:25px}h1{font-size:18px;font-weight:normal;padding-bottom:3px;margin:0}[role="list"],\n.widget.vbox{min-width:300px}.keybinds-key{padding:0.1em 0.6em;border:1px solid var(--color-details-hairline);font-size:11px;background-color:var(--color-background-elevation-1);color:var(--color-text-primary);box-shadow:var(--box-shadow-outline-color);border-radius:3px;display:inline-block;margin:0 0.1em;text-shadow:0 1px 0 var(--color-background);line-height:1.5;white-space:nowrap}.keybinds-list-item{min-height:30px;display:grid;grid-template-rows:repeat(auto-fit,minmax(30px,auto));grid-template-columns:1fr 30px 2fr 30px 30px;flex:auto 1 1}.keybinds-list-item:focus-visible{background-color:var(--legacy-focus-bg-color)}.keybinds-list-item:not(.keybinds-category-header){padding:4px 0 4px 20px}.keybinds-list-item.keybinds-editing{background-color:var(--color-background-elevation-2)}.keybinds-list-text.keybinds-action-name{padding-top:7px;grid-row:1/3}.keybinds-shortcut,\n.keybinds-info{grid-row:auto;grid-column:3/span 1}.keybinds-shortcut.devtools-link{align-items:center;margin-left:3px}.keybinds-info .devtools-link{padding-top:6px}.keybinds-error{color:var(--color-accent-red)}.keybinds-list-item.keybinds-editing .keybinds-shortcut{display:flex}.keybinds-modified{grid-column:2/span 1;margin-top:2px}.keybinds-list-item button{border:none;padding:0;background:transparent}.keybinds-list-item button:hover .icon-mask{background-color:var(--color-text-primary)}.keybinds-list-item button:focus-visible{background-color:var(--legacy-focus-bg-color)}.keybinds-list-item button[disabled]{opacity:40%}.keybinds-confirm-button{grid-column:-2/span 1}.keybinds-cancel-button{grid-column:-1/span 1}.keybinds-edit-button{display:none;grid-row:1/span 1;grid-column:4/span 1}.keybinds-list-item:not(.keybinds-editing):hover .keybinds-edit-button,\n.keybinds-list-item:not(.keybinds-editing):focus-within .keybinds-edit-button{display:inline-block}.keybinds-list-text{padding:3px 0;user-select:none;color:var(--color-text-primary);text-align:start;position:relative;margin-right:0}.keybinds-category-header{font-weight:bold;line-height:30px;white-space:nowrap}.keybinds-category-header:not(:nth-child(2)){border-top:1px solid var(--color-details-hairline)}.keybinds-list-item:not(.keybinds-category-header):hover,\n.keybinds-list-item:not(.keybinds-editing):focus-within{background:var(--color-background-elevation-1)}.keybinds-set-select{text-align:right;margin-bottom:25px}.keybinds-set-select label p{display:inline;color:var(--color-text-primary)}.keybinds-set-select select{margin-left:6px}button.text-button{width:fit-content;align-self:flex-end}.keybinds-list-text input{margin:0 2px}.keybinds-list-text:has(.keybinds-delete-button){grid-column:3/-1}.docs-link.devtools-link{align-self:flex-start;min-height:2em;line-height:2em}.keybinds-footer{display:flex;flex-wrap:wrap;justify-content:space-between;min-height:fit-content;margin-top:10px}\n/*# sourceURL=keybindsSettingsTab.css */\n');const M={shortcuts:"Shortcuts",matchShortcutsFromPreset:"Match shortcuts from preset",keyboardShortcutsList:"Keyboard shortcuts list",shortcutModified:"Shortcut modified",noShortcutForAction:"No shortcut for action",addAShortcut:"Add a shortcut",confirmChanges:"Confirm changes",discardChanges:"Discard changes",removeShortcut:"Remove shortcut",editShortcut:"Edit shortcut",shortcutsCannotContainOnly:"Shortcuts cannot contain only modifier keys.",thisShortcutIsInUseByS:"This shortcut is in use by {PH1}: {PH2}.",RestoreDefaultShortcuts:"Restore default shortcuts",FullListOfDevtoolsKeyboard:"Full list of DevTools keyboard shortcuts and gestures",ResetShortcutsForAction:"Reset shortcuts for action"},P=i.i18n.registerUIStrings("panels/settings/KeybindsSettingsTab.ts",M),B=i.i18n.getLocalizedString.bind(void 0,P);let N;class O extends r.Widget.VBox{items;list;editingItem;editingRow;constructor(){super(!0);this.contentElement.createChild("header").createChild("h1").textContent=B(M.shortcuts);const e=t.Settings.Settings.instance().moduleSetting("activeKeybindSet"),i=t.Settings.Settings.instance().moduleSetting("userShortcuts");i.addChangeListener(this.update,this),e.addChangeListener(this.update,this);const n=r.SettingsUI.createControlForSetting(e,B(M.matchShortcutsFromPreset));n&&(n.classList.add("keybinds-set-select"),this.contentElement.appendChild(n)),this.items=new r.ListModel.ListModel,this.list=new r.ListControl.ListControl(this.items,this,r.ListControl.ListMode.NonViewport),this.items.replaceAll(this.createListItems()),r.ARIAUtils.markAsList(this.list.element),this.contentElement.appendChild(this.list.element),r.ARIAUtils.setLabel(this.list.element,B(M.keyboardShortcutsList));const s=this.contentElement.createChild("div");s.classList.add("keybinds-footer");const o=r.XLink.XLink.create("https://developer.chrome.com/docs/devtools/shortcuts/",B(M.FullListOfDevtoolsKeyboard));o.classList.add("docs-link"),s.appendChild(o),s.appendChild(r.UIUtils.createTextButton(B(M.RestoreDefaultShortcuts),(()=>{i.set([]),e.set(r.ShortcutRegistry.DefaultShortcutSetting)}))),this.editingItem=null,this.editingRow=null,this.update()}static instance(t={forceNew:null}){const{forceNew:e}=t;return N&&!e||(N=new O),N}createElementForItem(t){let e=document.createElement("div");if("string"==typeof t)r.ARIAUtils.setLevel(e,1),e.classList.add("keybinds-category-header"),e.textContent=r.ActionRegistration.getLocalizedActionCategory(t);else{const i=new D(t,this,t===this.editingItem);e=i.element,r.ARIAUtils.setLevel(e,2),t===this.editingItem&&(this.editingRow=i)}return e.classList.add("keybinds-list-item"),r.ARIAUtils.markAsListitem(e),e.tabIndex=t===this.list.selectedItem()&&t!==this.editingItem?0:-1,e}commitChanges(t,i){for(const[t,n]of i)t.type!==r.KeyboardShortcut.Type.UnsetShortcut&&(r.ShortcutRegistry.ShortcutRegistry.instance().removeShortcut(t),n||e.userMetrics.actionTaken(e.UserMetrics.Action.ShortcutRemoved)),n&&(r.ShortcutRegistry.ShortcutRegistry.instance().registerUserShortcut(t.changeKeys(n).changeType(r.KeyboardShortcut.Type.UserShortcut)),t.type===r.KeyboardShortcut.Type.UnsetShortcut?e.userMetrics.actionTaken(e.UserMetrics.Action.UserShortcutAdded):e.userMetrics.actionTaken(e.UserMetrics.Action.ShortcutModified));this.stopEditing(t)}heightForItem(t){return 0}isItemSelectable(t){return!0}selectedItemChanged(t,e,i,n){i&&(i.tabIndex=-1),n&&(e===this.editingItem&&this.editingRow?this.editingRow.focus():(n.tabIndex=0,this.list.element.hasFocus()&&n.focus()),this.setDefaultFocusedElement(n))}updateSelectedItemARIA(t,e){return!0}startEditing(t){this.list.selectItem(t),this.editingItem&&this.stopEditing(this.editingItem),r.UIUtils.markBeingEdited(this.list.element,!0),this.editingItem=t,this.list.refreshItem(t)}stopEditing(t){r.UIUtils.markBeingEdited(this.list.element,!1),this.editingItem=null,this.editingRow=null,this.list.refreshItem(t),this.focus()}createListItems(){const t=r.ActionRegistry.ActionRegistry.instance().actions().sort(((t,e)=>t.category()<e.category()?-1:t.category()>e.category()?1:t.id()<e.id()?-1:t.id()>e.id()?1:0)),e=[];let i;return t.forEach((t=>{"elements.toggle-element-search"!==t.id()&&(i!==t.category()&&e.push(t.category()),e.push(t),i=t.category())})),e}onEscapeKeyPressed(t){const e=l.DOMUtilities.deepActiveElement(document);this.editingRow&&e&&"INPUT"===e.nodeName&&this.editingRow.onEscapeKeyPressed(t)}update(){this.editingItem&&this.stopEditing(this.editingItem),this.list.refreshAllItems(),this.list.selectedItem()||this.list.selectItem(this.items.at(0))}willHide(){this.editingItem&&this.stopEditing(this.editingItem)}wasShown(){super.wasShown(),this.registerCSSFiles([K])}}class D{isEditing;settingsTab;item;element;editedShortcuts;shortcutInputs;shortcuts;elementToFocus;confirmButton;addShortcutLinkContainer;errorMessageElement;secondKeyTimeout;constructor(t,e,i){this.isEditing=Boolean(i),this.settingsTab=e,this.item=t,this.element=document.createElement("div"),this.editedShortcuts=new Map,this.shortcutInputs=new Map,this.shortcuts=r.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction(t.id()),this.elementToFocus=null,this.confirmButton=null,this.addShortcutLinkContainer=null,this.errorMessageElement=null,this.secondKeyTimeout=null,this.update()}focus(){this.elementToFocus&&this.elementToFocus.focus()}update(){this.element.removeChildren(),this.elementToFocus=null,this.shortcutInputs.clear(),this.element.classList.toggle("keybinds-editing",this.isEditing),this.element.createChild("div","keybinds-action-name keybinds-list-text").textContent=this.item.title(),this.shortcuts.forEach(this.createShortcutRow,this),0===this.shortcuts.length&&this.createEmptyInfo(),this.isEditing&&this.setupEditor()}createEmptyInfo(){if(r.ShortcutRegistry.ShortcutRegistry.instance().actionHasDefaultShortcut(this.item.id())){const t=r.Icon.Icon.create("keyboard-pen","keybinds-modified");r.ARIAUtils.setLabel(t,B(M.shortcutModified)),this.element.appendChild(t)}if(!this.isEditing){const t=this.element.createChild("div","keybinds-shortcut keybinds-list-text");r.ARIAUtils.setLabel(t,B(M.noShortcutForAction)),n.Runtime.experiments.isEnabled("keyboardShortcutEditor")&&this.element.appendChild(this.createEditButton())}}setupEditor(){this.addShortcutLinkContainer=this.element.createChild("div","keybinds-shortcut devtools-link");const t=this.addShortcutLinkContainer.createChild("span","devtools-link");t.textContent=B(M.addAShortcut),t.tabIndex=0,r.ARIAUtils.markAsLink(t),self.onInvokeElement(t,this.addShortcut.bind(this)),this.elementToFocus||(this.elementToFocus=t),this.errorMessageElement=this.element.createChild("div","keybinds-info keybinds-error hidden"),r.ARIAUtils.markAsAlert(this.errorMessageElement),this.element.appendChild(this.createIconButton(B(M.ResetShortcutsForAction),"undo","",this.resetShortcutsToDefaults.bind(this))),this.confirmButton=this.createIconButton(B(M.confirmChanges),"checkmark","keybinds-confirm-button",(()=>this.settingsTab.commitChanges(this.item,this.editedShortcuts))),this.element.appendChild(this.confirmButton),this.element.appendChild(this.createIconButton(B(M.discardChanges),"cross","keybinds-cancel-button",(()=>this.settingsTab.stopEditing(this.item)))),this.element.addEventListener("keydown",(t=>{l.KeyboardUtilities.isEscKey(t)&&(this.settingsTab.stopEditing(this.item),t.consume(!0))}))}addShortcut(){const t=new r.KeyboardShortcut.KeyboardShortcut([],this.item.id(),r.KeyboardShortcut.Type.UnsetShortcut);this.shortcuts.push(t),this.update();const e=this.shortcutInputs.get(t);e&&e.focus()}createShortcutRow(t,e){if(this.editedShortcuts.has(t)&&!this.editedShortcuts.get(t))return;let i;t.type===r.KeyboardShortcut.Type.UnsetShortcut||t.isDefault()||(i=r.Icon.Icon.create("keyboard-pen","keybinds-modified"),r.ARIAUtils.setLabel(i,B(M.shortcutModified)),this.element.appendChild(i));const s=this.element.createChild("div","keybinds-shortcut keybinds-list-text");if(this.isEditing){const e=s.createChild("input","harmony-input");e.spellcheck=!1,e.maxLength=0,this.shortcutInputs.set(t,e),this.elementToFocus||(this.elementToFocus=e),e.value=t.title();const i=this.editedShortcuts.get(t);i&&(e.value=this.shortcutInputTextForDescriptors(i)),e.addEventListener("keydown",this.onShortcutInputKeyDown.bind(this,t,e)),e.addEventListener("blur",(()=>{null!==this.secondKeyTimeout&&(clearTimeout(this.secondKeyTimeout),this.secondKeyTimeout=null)})),s.appendChild(this.createIconButton(B(M.removeShortcut),"bin","keybinds-delete-button",(()=>{const e=this.shortcuts.indexOf(t);t.isDefault()||this.shortcuts.splice(e,1),this.editedShortcuts.set(t,null),this.update(),this.focus(),this.validateInputs()})))}else{t.descriptors.flatMap((t=>t.name.split(" + "))).forEach((t=>{s.createChild("span","keybinds-key").textContent=t})),n.Runtime.experiments.isEnabled("keyboardShortcutEditor")&&0===e&&this.element.appendChild(this.createEditButton())}}createEditButton(){return this.createIconButton(B(M.editShortcut),"edit","keybinds-edit-button",(()=>this.settingsTab.startEditing(this.item)))}createIconButton(t,e,i,n){const s=document.createElement("button");return s.setAttribute("title",t),s.appendChild(r.Icon.Icon.create(e)),s.addEventListener("click",n),r.ARIAUtils.setLabel(s,t),i&&s.classList.add(i),s}onShortcutInputKeyDown(t,e,i){if("Tab"!==i.key){const n=this.descriptorForEvent(i),s=this.editedShortcuts.get(t)||[];this.editedShortcuts.set(t,s);const o=2===s.length&&r.KeyboardShortcut.KeyboardShortcut.isModifier(s[1].key);2===s.length&&!o&&s.splice(0,2),this.secondKeyTimeout?(clearTimeout(this.secondKeyTimeout),this.secondKeyTimeout=null,s.push(n)):o?s[1]=n:r.KeyboardShortcut.KeyboardShortcut.isModifier(n.key)?s[0]=n:(s[0]=n,this.secondKeyTimeout=window.setTimeout((()=>{this.secondKeyTimeout=null}),r.ShortcutRegistry.KeyTimeout)),e.value=this.shortcutInputTextForDescriptors(s),this.validateInputs(),i.consume(!0)}}descriptorForEvent(t){const e=r.KeyboardShortcut.KeyboardShortcut.makeKeyFromEvent(t),i=r.KeyboardShortcut.KeyboardShortcut.keyCodeAndModifiersFromKey(e);let n=r.KeyboardShortcut.Keys[t.key]||r.KeyboardShortcut.KeyBindings[t.key];if(!n&&!/^[a-z]$/i.test(t.key)){const e=t.code;n=r.KeyboardShortcut.Keys[e]||r.KeyboardShortcut.KeyBindings[e],e.startsWith("Digit")?n=e.slice(5):e.startsWith("Key")&&(n=e.slice(3))}return r.KeyboardShortcut.KeyboardShortcut.makeDescriptor(n||t.key,i.modifiers)}shortcutInputTextForDescriptors(t){return t.map((t=>t.name)).join(" ")}resetShortcutsToDefaults(){this.editedShortcuts.clear();for(const t of this.shortcuts)if(t.type===r.KeyboardShortcut.Type.UnsetShortcut){const e=this.shortcuts.indexOf(t);this.shortcuts.splice(e,1)}else t.type===r.KeyboardShortcut.Type.UserShortcut&&this.editedShortcuts.set(t,null);r.ShortcutRegistry.ShortcutRegistry.instance().disabledDefaultsForAction(this.item.id()).forEach((t=>{this.shortcuts.includes(t)||(this.shortcuts.push(t),this.editedShortcuts.set(t,t.descriptors))})),this.update(),this.focus()}onEscapeKeyPressed(t){const e=l.DOMUtilities.deepActiveElement(document);for(const[i,n]of this.shortcutInputs.entries())e===n&&this.onShortcutInputKeyDown(i,n,t)}validateInputs(){const t=this.confirmButton,e=this.errorMessageElement;t&&e&&(t.disabled=!1,e.classList.add("hidden"),this.shortcutInputs.forEach(((i,n)=>{const s=this.editedShortcuts.get(n);if(!s)return;if(s.some((t=>r.KeyboardShortcut.KeyboardShortcut.isModifier(t.key))))return t.disabled=!0,i.classList.add("error-input"),r.ARIAUtils.setInvalid(i,!0),e.classList.remove("hidden"),void(e.textContent=B(M.shortcutsCannotContainOnly));const o=r.ShortcutRegistry.ShortcutRegistry.instance().actionsForDescriptors(s).filter((t=>t!==this.item.id()));if(o.length){t.disabled=!0,i.classList.add("error-input"),r.ARIAUtils.setInvalid(i,!0),e.classList.remove("hidden");const n=r.ActionRegistry.ActionRegistry.instance().action(o[0]);if(!n)return;const s=n.title(),a=n.category();e.textContent=B(M.thisShortcutIsInUseByS,{PH1:a,PH2:s})}else i.classList.remove("error-input"),r.ARIAUtils.setInvalid(i,!1)})))}}var H=Object.freeze({__proto__:null,KeybindsSettingsTab:O,ShortcutListItem:D});export{F as FrameworkIgnoreListSettingsTab,H as KeybindsSettingsTab,E as SettingsScreen};
