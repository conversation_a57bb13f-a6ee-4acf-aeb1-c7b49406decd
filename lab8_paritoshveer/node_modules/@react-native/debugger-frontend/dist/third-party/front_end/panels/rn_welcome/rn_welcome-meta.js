import*as e from"../../core/i18n/i18n.js";import*as o from"../../core/root/root.js";import*as n from"../../ui/legacy/legacy.js";const i={rnWelcome:"⚛️ Welcome",showRnWelcome:"Show React Native Welcome panel"},r=e.i18n.registerUIStrings("panels/rn_welcome/rn_welcome-meta.ts",i),t=e.i18n.getLazilyComputedLocalizedString.bind(void 0,r);let m;n.ViewManager.registerViewExtension({location:"panel",id:"rn-welcome",title:t(i.rnWelcome),commandPrompt:t(i.showRnWelcome),order:-10,persistence:"permanent",loadView:async()=>(await async function(){return m||(m=await import("./rn_welcome.js")),m}()).RNWelcome.RNWelcomeImpl.instance(),experiment:o.Runtime.ExperimentName.REACT_NATIVE_SPECIFIC_UI});
