import*as e from"../../core/i18n/i18n.js";import*as t from"../../core/sdk/sdk.js";import*as i from"../../core/common/common.js";import*as s from"../../core/host/host.js";import*as r from"../network/forward/forward.js";import*as n from"../../ui/legacy/legacy.js";const o={theSecurityOfThisPageIsUnknown:"The security of this page is unknown.",thisPageIsNotSecure:"This page is not secure.",thisPageIsSecureValidHttps:"This page is secure (valid HTTPS).",thisPageIsNotSecureBrokenHttps:"This page is not secure (broken HTTPS).",cipherWithMAC:"{PH1} with {PH2}",keyExchangeWithGroup:"{PH1} with {PH2}"},a=e.i18n.registerUIStrings("panels/security/SecurityModel.ts",o),c=e.i18n.getLocalizedString.bind(void 0,a),l=e.i18n.getLazilyComputedLocalizedString.bind(void 0,a);class u extends t.SDKModel.SDKModel{dispatcher;securityAgent;constructor(e){super(e),this.dispatcher=new x(this),this.securityAgent=e.securityAgent(),e.registerSecurityDispatcher(this.dispatcher),this.securityAgent.invoke_enable()}resourceTreeModel(){return this.target().model(t.ResourceTreeModel.ResourceTreeModel)}networkManager(){return this.target().model(t.NetworkManager.NetworkManager)}static SecurityStateComparator(e,t){const i=h();return(e&&i.get(e)||0)-(t&&i.get(t)||0)}}let d=null;const h=()=>{if(!d){d=new Map;const e=["info","insecure-broken","insecure","neutral","secure","unknown"];for(let t=0;t<e.length;t++)d.set(e[t],t+1)}return d};var p;t.SDKModel.SDKModel.register(u,{capabilities:t.Target.Capability.Security,autostart:!1}),function(e){e.VisibleSecurityStateChanged="VisibleSecurityStateChanged"}(p||(p={}));const g={unknown:l(o.theSecurityOfThisPageIsUnknown),insecure:l(o.thisPageIsNotSecure),neutral:l(o.thisPageIsNotSecure),secure:l(o.thisPageIsSecureValidHttps),"insecure-broken":l(o.thisPageIsNotSecureBrokenHttps)};class m{securityState;certificateSecurityState;safetyTipInfo;securityStateIssueIds;constructor(e,t,i,s){this.securityState=e,this.certificateSecurityState=t?new y(t):null,this.safetyTipInfo=i?new S(i):null,this.securityStateIssueIds=s}}class y{protocol;keyExchange;keyExchangeGroup;cipher;mac;certificate;subjectName;issuer;validFrom;validTo;certificateNetworkError;certificateHasWeakSignature;certificateHasSha1Signature;modernSSL;obsoleteSslProtocol;obsoleteSslKeyExchange;obsoleteSslCipher;obsoleteSslSignature;constructor(e){this.protocol=e.protocol,this.keyExchange=e.keyExchange,this.keyExchangeGroup=e.keyExchangeGroup||null,this.cipher=e.cipher,this.mac=e.mac||null,this.certificate=e.certificate,this.subjectName=e.subjectName,this.issuer=e.issuer,this.validFrom=e.validFrom,this.validTo=e.validTo,this.certificateNetworkError=e.certificateNetworkError||null,this.certificateHasWeakSignature=e.certificateHasWeakSignature,this.certificateHasSha1Signature=e.certificateHasSha1Signature,this.modernSSL=e.modernSSL,this.obsoleteSslProtocol=e.obsoleteSslProtocol,this.obsoleteSslKeyExchange=e.obsoleteSslKeyExchange,this.obsoleteSslCipher=e.obsoleteSslCipher,this.obsoleteSslSignature=e.obsoleteSslSignature}isCertificateExpiringSoon(){const e=new Date(1e3*this.validTo).getTime();return e<new Date(Date.now()).setHours(48)&&e>Date.now()}getKeyExchangeName(){return this.keyExchangeGroup?this.keyExchange?c(o.keyExchangeWithGroup,{PH1:this.keyExchange,PH2:this.keyExchangeGroup}):this.keyExchangeGroup:this.keyExchange}getCipherFullName(){return this.mac?c(o.cipherWithMAC,{PH1:this.cipher,PH2:this.mac}):this.cipher}}class S{safetyTipStatus;safeUrl;constructor(e){this.safetyTipStatus=e.safetyTipStatus,this.safeUrl=e.safeUrl||null}}class w{securityState;title;summary;description;certificate;mixedContentType;recommendations;constructor(e,t,i,s,r=[],n="none",o=[]){this.securityState=e,this.title=t,this.summary=i,this.description=s,this.certificate=r,this.mixedContentType=n,this.recommendations=o}}class x{model;constructor(e){this.model=e}securityStateChanged(e){}visibleSecurityStateChanged({visibleSecurityState:e}){const t=new m(e.securityState,e.certificateSecurityState||null,e.safetyTipInfo||null,e.securityStateIssueIds);this.model.dispatchEventToListeners(p.VisibleSecurityStateChanged,t)}certificateError(e){}}var v=Object.freeze({__proto__:null,SecurityModel:u,get Events(){return p},SummaryMessages:g,PageVisibleSecurityState:m,CertificateSecurityState:y,SecurityStyleExplanation:w});const b=new CSSStyleSheet;b.replaceSync(".lock-icon,\n.security-property{height:16px;width:16px;-webkit-mask-image:var(--image-file-securityIcons);-webkit-mask-size:80px 32px;background-color:var(--color-text-disabled)}.lock-icon-secure{-webkit-mask-position:0 0;background-color:var(--color-accent-green)}.lock-icon-unknown,\n.lock-icon-neutral{-webkit-mask-position:-16px 0;background-color:var(--color-text-primary)}@media (-webkit-min-device-pixel-ratio: 1.1){.lock-icon-unknown,\n  .lock-icon-neutral{background-color:var(--color-text-secondary)}}.lock-icon-insecure{-webkit-mask-position:-32px 0;background-color:var(--color-text-secondary)}.lock-icon-insecure-broken{-webkit-mask-position:-32px 0;background-color:var(--color-accent-red)}.security-property-secure{-webkit-mask-position:0 -16px;background-color:var(--color-accent-green;)}.security-property-neutral{-webkit-mask-position:-16px -16px;background-color:var(--color-accent-red);.security-property-insecure{-webkit-mask-position:-32px -16px;background-color:var(--color-accent-red)}.security-property-insecure-broken{-webkit-mask-position:-32px -16px;background-color:var(--color-accent-red)}.security-property-info{-webkit-mask-position:-48px -16px;background-color:rgb(0 0 0/50%)}.security-property-unknown{-webkit-mask-position:-64px -16px;background-color:rgb(0 0 0/50%)}.url-scheme-secure{color:var(--color-accent-green)}.url-scheme-neutral,\n  .url-scheme-insecure,\n  .url-scheme-insecure-broken{color:var(--color-accent-red)}.url-scheme-separator{color:var(--color-text-disabled)}@media (forced-colors: active){.lock-icon,\n    .security-property,\n    .url-scheme-neutral,\n    .url-scheme-insecure,\n    .url-scheme-insecure-broken{forced-color-adjust:none}.lock-icon-unknown,\n    .lock-icon-neutral{background-color:Highlight}.security-property-info,\n    .security-property-unknown{background-color:canvastext}.tree-outline:not(.hide-selection-when-blurred) li.selected .security-property-info,\n    .tree-outline:not(.hide-selection-when-blurred) li.selected .security-property-unknown{background-color:HighlightText}}}\n/*# sourceURL=lockIcon.css */\n");const f=new CSSStyleSheet;f.replaceSync(".devtools-link{display:inline-block}.security-main-view{overflow-x:hidden;overflow-y:auto;background-color:var(--color-background-elevation-1)}.security-main-view > div{flex-shrink:0}.security-summary-section-title{font-size:15px;margin:12px 16px;user-select:text}.lock-spectrum{margin:8px 16px;display:flex;align-items:flex-start}.security-summary .lock-icon{flex:none;width:16px;height:16px;margin:0}.security-summary .lock-icon-neutral{margin:0 16px}.security-summary:not(.security-summary-secure) .lock-icon-secure,\n.security-summary:not(.security-summary-neutral) .lock-icon-neutral,\n.security-summary:not(.security-summary-insecure) .lock-icon-insecure,\n.security-summary:not(.security-summary-insecure-broken) .lock-icon-insecure-broken{background-color:var(--color-text-disabled)}@media (forced-colors: active){.security-summary-neutral .lock-icon-neutral{background-color:Highlight}.security-summary:not(.security-summary-secure) .lock-icon-secure,\n  .security-summary:not(.security-summary-neutral) .lock-icon-neutral,\n  .security-summary:not(.security-summary-insecure) .lock-icon-insecure,\n  .security-summary:not(.security-summary-insecure-broken) .lock-icon-insecure-broken{background-color:canvastext}}.triangle-pointer-container{margin:8px 24px 0;padding:0}.triangle-pointer-wrapper{transform:translateX(0);transition:transform 0.3s}.triangle-pointer{width:12px;height:12px;margin-bottom:-6px;margin-left:-6px;transform:rotate(-45deg);border-style:solid;border-width:1px 1px 0 0;background:var(--color-background);border-color:var(--color-background-elevation-1)}.security-summary-secure .triangle-pointer-wrapper{transform:translateX(0)}.security-summary-neutral .triangle-pointer-wrapper{transform:translateX(32px)}.security-summary-insecure .triangle-pointer-wrapper{transform:translateX(64px)}.security-summary-insecure-broken .triangle-pointer-wrapper{transform:translateX(64px)}.security-summary-text{padding:16px 24px;border-style:solid;border-width:1px 0;font-size:15px;background:var(--color-background);border-color:var(--color-background-elevation-1);user-select:text}.security-summary-secure .triangle-pointer,\n.security-summary-secure .security-summary-text,\n.security-explanation-title-secure{color:var(--color-accent-green)}.security-summary-insecure-broken .triangle-pointer,\n.security-summary-insecure-broken .security-summary-text,\n.security-explanation-title-neutral,\n.security-explanation-title-insecure,\n.security-explanation-title-insecure-broken{color:var(--color-accent-red)}.security-explanation-list{padding-bottom:16px}.security-explanation-list:empty{border-bottom:none;padding:0}.security-explanations-main{margin-top:-5px;background-color:var(--color-background);border-bottom:1px solid var(--color-background-elevation-1)}.security-explanations-extra{background-color:transparent}.security-explanation{padding:11px;display:flex;white-space:nowrap;border:none;color:var(--color-text-secondary)}.security-explanation-text{flex:auto;white-space:normal;max-width:400px}.security-explanation .security-property{flex:none;width:16px;height:16px;margin-right:16px}.security-explanation-title{color:var(--color-text-secondary);margin-top:1px;margin-bottom:8px}.security-mixed-content{margin-top:8px}.security-explanation-recommendations{padding-inline-start:16px}.security-explanation-recommendations > li{margin-bottom:4px}\n/*# sourceURL=mainView.css */\n");const k=new CSSStyleSheet;k.replaceSync(".title-section{padding:16px 0 24px;border-bottom:1px solid var(--color-background-elevation-2)}.title-section-header{padding-left:16px;padding-bottom:10px;font-size:14px}.security-origin-view{overflow-x:hidden;overflow-y:scroll;display:block;user-select:text}.security-origin-view .origin-view-section{border-bottom:1px solid var(--color-background-elevation-2);padding:12px 6px 12px 24px;font-size:12px}.origin-view-notes{margin-top:2px;color:var(--color-text-secondary)}.origin-view-section-notes{margin-top:6px;color:var(--color-text-secondary)}.security-origin-view .origin-display{font-size:12px;padding-left:32px;display:flex;align-items:center}.title-section > .view-network-button{padding:6px 0 0 16px}.security-origin-view .origin-display .security-property{display:inline-block;vertical-align:middle;position:absolute;left:13px}.security-origin-view .origin-view-section-title{margin-top:4px;margin-bottom:4px;font-weight:bold}.security-origin-view .details-table{border-spacing:0}.security-origin-view .details-table-row{white-space:nowrap;overflow:hidden;line-height:22px;vertical-align:top}.security-origin-view .details-table-row > td{padding:0}.security-origin-view .details-table-row > td:first-child{color:var(--color-text-secondary);width:calc(120px + 1em);text-align:right;padding-right:1em}.security-origin-view .details-table-row > td:nth-child(2){white-space:normal}.security-origin-view .sct-details .details-table .details-table-row:last-child td:last-child{border-bottom:1px solid var(--color-background-elevation-2);padding-bottom:10px}.security-origin-view .sct-details .details-table:last-child .details-table-row:last-child td:last-child{border-bottom:none;padding-bottom:0}.security-origin-view .details-toggle{margin-left:126px}.security-origin-view .sct-toggle{margin-left:145px;padding-top:5px}.security-origin-view .details-table .empty-san{color:var(--color-text-disabled)}.security-origin-view .details-table .san-entry{display:block}.security-origin-view .truncated-san .truncated-entry{display:none}.origin-button{margin-top:4px;margin-left:0}.origin-view-section:last-child{border-bottom:none}.devtools-link{display:inline-flex}\n/*# sourceURL=originView.css */\n");const C=new CSSStyleSheet;C.replaceSync(".tree-outline{padding:0}.tree-outline li{display:flex;flex-direction:row;align-items:center;padding:2px 5px;overflow:hidden;margin:2px 0;border-top:1px solid transparent;white-space:nowrap}.tree-outline .lock-icon,\n.tree-outline .security-property{margin-right:4px;flex:none}.tree-outline li.selected:focus .lock-icon,\n.tree-outline .security-sidebar-tree-item.selected:focus .icon{background-color:var(--legacy-selection-fg-color)}@media (forced-colors: active){.tree-outline .lock-icon,\n  .tree-outline .security-property{forced-color-adjust:none}.tree-outline li.selected:focus .lock-icon,\n  .tree-outline .security-sidebar-tree-item.selected:focus .icon{background-color:HighlightText}}.tree-outline .security-main-view-sidebar-tree-item{border-bottom:1px solid var(--color-background-elevation-2);padding:16px 0}.tree-outline li.security-sidebar-origins{padding:1px 8px 1px 13px;margin-top:1em;margin-bottom:0.5em;color:var(--color-text-secondary);border-top:none;line-height:16px;text-shadow:var(--color-background-opacity-80) 0 1px 0}.tree-outline ol{padding-left:0}.tree-outline li::before{content:none}.tree-outline .security-main-view-sidebar-tree-item,\n.tree-outline .security-sidebar-origins,\n.tree-outline li.security-sidebar-origins + .children > li{padding-left:16px}.security-sidebar-tree-item{padding:2px 0}.security-sidebar-tree-item .title{overflow:hidden;margin-right:5px}.security-main-view-reload-message .tree-element-title{color:var(--color-text-secondary);padding-left:8px}\n/*# sourceURL=sidebar.css */\n");const T={overview:"Overview",mainOrigin:"Main origin",nonsecureOrigins:"Non-secure origins",secureOrigins:"Secure origins",unknownCanceled:"Unknown / canceled",reloadToViewDetails:"Reload to view details",mainOriginSecure:"Main origin (secure)",mainOriginNonsecure:"Main origin (non-secure)",securityOverview:"Security overview",secure:"Secure",info:"Info",notSecure:"Not secure",viewCertificate:"View certificate",notSecureBroken:"Not secure (broken)",thisPageIsDangerousFlaggedBy:"This page is dangerous (flagged by Google Safe Browsing).",flaggedByGoogleSafeBrowsing:"Flagged by Google Safe Browsing",toCheckThisPagesStatusVisit:"To check this page's status, visit g.co/safebrowsingstatus.",thisIsAnErrorPage:"This is an error page.",thisPageIsInsecureUnencrypted:"This page is insecure (unencrypted HTTP).",thisPageHasANonhttpsSecureOrigin:"This page has a non-HTTPS secure origin.",thisPageIsSuspicious:"This page is suspicious",chromeHasDeterminedThatThisSiteS:"Chrome has determined that this site could be fake or fraudulent.",ifYouBelieveThisIsShownIn:"If you believe this is shown in error please visit https://g.co/chrome/lookalike-warnings.",possibleSpoofingUrl:"Possible spoofing URL",thisSitesHostnameLooksSimilarToP:"This site's hostname looks similar to {PH1}. Attackers sometimes mimic sites by making small, hard-to-see changes to the domain name.",ifYouBelieveThisIsShownInErrorSafety:"If you believe this is shown in error please visit https://g.co/chrome/lookalike-warnings.",thisPageIsSuspiciousFlaggedBy:"This page is suspicious (flagged by Chrome).",certificate:"Certificate",insecureSha:"insecure (SHA-1)",theCertificateChainForThisSite:"The certificate chain for this site contains a certificate signed using SHA-1.",subjectAlternativeNameMissing:"`Subject Alternative Name` missing",theCertificateForThisSiteDoesNot:"The certificate for this site does not contain a `Subject Alternative Name` extension containing a domain name or IP address.",missing:"missing",thisSiteIsMissingAValidTrusted:"This site is missing a valid, trusted certificate ({PH1}).",validAndTrusted:"valid and trusted",theConnectionToThisSiteIsUsingA:"The connection to this site is using a valid, trusted server certificate issued by {PH1}.",publickeypinningBypassed:"Public-Key-Pinning bypassed",publickeypinningWasBypassedByA:"Public-Key-Pinning was bypassed by a local root certificate.",certificateExpiresSoon:"Certificate expires soon",theCertificateForThisSiteExpires:"The certificate for this site expires in less than 48 hours and needs to be renewed.",connection:"Connection",secureConnectionSettings:"secure connection settings",theConnectionToThisSiteIs:"The connection to this site is encrypted and authenticated using {PH1}, {PH2}, and {PH3}.",sIsObsoleteEnableTlsOrLater:"{PH1} is obsolete. Enable TLS 1.2 or later.",rsaKeyExchangeIsObsoleteEnableAn:"RSA key exchange is obsolete. Enable an ECDHE-based cipher suite.",sIsObsoleteEnableAnAesgcmbased:"{PH1} is obsolete. Enable an AES-GCM-based cipher suite.",theServerSignatureUsesShaWhichIs:"The server signature uses SHA-1, which is obsolete. Enable a SHA-2 signature algorithm instead. (Note this is different from the signature in the certificate.)",obsoleteConnectionSettings:"obsolete connection settings",resources:"Resources",activeMixedContent:"active mixed content",youHaveRecentlyAllowedNonsecure:"You have recently allowed non-secure content (such as scripts or iframes) to run on this site.",mixedContent:"mixed content",thisPageIncludesHttpResources:"This page includes HTTP resources.",nonsecureForm:"non-secure form",thisPageIncludesAFormWithA:'This page includes a form with a non-secure "action" attribute.',activeContentWithCertificate:"active content with certificate errors",youHaveRecentlyAllowedContent:"You have recently allowed content loaded with certificate errors (such as scripts or iframes) to run on this site.",contentWithCertificateErrors:"content with certificate errors",thisPageIncludesResourcesThat:"This page includes resources that were loaded with certificate errors.",allServedSecurely:"all served securely",allResourcesOnThisPageAreServed:"All resources on this page are served securely.",blockedMixedContent:"Blocked mixed content",yourPageRequestedNonsecure:"Your page requested non-secure resources that were blocked.",reloadThePageToRecordRequestsFor:"Reload the page to record requests for HTTP resources.",viewDRequestsInNetworkPanel:"{n, plural, =1 {View # request in Network Panel} other {View # requests in Network Panel}}",origin:"Origin",viewRequestsInNetworkPanel:"View requests in Network Panel",protocol:"Protocol",keyExchange:"Key exchange",cipher:"Cipher",serverSignature:"Server signature",encryptedClientHello:"Encrypted ClientHello",certificateTransparency:"Certificate Transparency",subject:"Subject",validFrom:"Valid from",validUntil:"Valid until",issuer:"Issuer",openFullCertificateDetails:"Open full certificate details",sct:"SCT",logName:"Log name",logId:"Log ID",validationStatus:"Validation status",source:"Source",issuedAt:"Issued at",hashAlgorithm:"Hash algorithm",signatureAlgorithm:"Signature algorithm",signatureData:"Signature data",showFullDetails:"Show full details",hideFullDetails:"Hide full details",thisRequestCompliesWithChromes:"This request complies with `Chrome`'s Certificate Transparency policy.",thisRequestDoesNotComplyWith:"This request does not comply with `Chrome`'s Certificate Transparency policy.",thisResponseWasLoadedFromCache:"This response was loaded from cache. Some security details might be missing.",theSecurityDetailsAboveAreFrom:"The security details above are from the first inspected response.",thisOriginIsANonhttpsSecure:"This origin is a non-HTTPS secure origin.",yourConnectionToThisOriginIsNot:"Your connection to this origin is not secure.",noSecurityInformation:"No security information",noSecurityDetailsAreAvailableFor:"No security details are available for this origin.",na:"(n/a)",showLess:"Show less",showMoreSTotal:"Show more ({PH1} total)",unknownField:"unknown",enabled:"enabled"},I=e.i18n.registerUIStrings("panels/security/SecurityPanel.ts",T),E=e.i18n.getLocalizedString.bind(void 0,I);let A;const R=new Map([[513,"RSA with SHA-1"],[1025,"RSA with SHA-256"],[1281,"RSA with SHA-384"],[1537,"RSA with SHA-512"],[1027,"ECDSA with SHA-256"],[1283,"ECDSA with SHA-384"],[2052,"RSA-PSS with SHA-256"],[2053,"RSA-PSS with SHA-384"],[2054,"RSA-PSS with SHA-512"]]);class P extends n.Panel.PanelWithSidebar{mainView;sidebarMainViewElement;sidebarTree;lastResponseReceivedForLoaderId;origins;filterRequestCounts;visibleView;eventListeners;securityModel;constructor(){super("security"),this.mainView=new F(this);const e=document.createElement("span");e.classList.add("title"),e.textContent=E(T.overview),this.sidebarMainViewElement=new H(e,this.setVisibleView.bind(this,this.mainView),"security-main-view-sidebar-tree-item","lock-icon"),this.sidebarMainViewElement.tooltip=e.textContent,this.sidebarTree=new L(this.sidebarMainViewElement,this.showOrigin.bind(this)),this.panelSidebarElement().appendChild(this.sidebarTree.element),this.lastResponseReceivedForLoaderId=new Map,this.origins=new Map,this.filterRequestCounts=new Map,this.visibleView=null,this.eventListeners=[],this.securityModel=null,t.TargetManager.TargetManager.instance().observeModels(u,this,{scoped:!0}),t.TargetManager.TargetManager.instance().addModelListener(t.ResourceTreeModel.ResourceTreeModel,t.ResourceTreeModel.Events.PrimaryPageChanged,this.onPrimaryPageChanged,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return A&&!t||(A=new P),A}static createCertificateViewerButtonForOrigin(e,i){const r=n.UIUtils.createTextButton(e,(async e=>{e.consume();const r=await t.NetworkManager.MultitargetNetworkManager.instance().getCertificate(i);r.length>0&&s.InspectorFrontendHost.InspectorFrontendHostInstance.showCertificateViewer(r)}),"origin-button");return n.ARIAUtils.markAsButton(r),r}static createCertificateViewerButtonForCert(e,t){const i=n.UIUtils.createTextButton(e,(e=>{e.consume(),s.InspectorFrontendHost.InspectorFrontendHostInstance.showCertificateViewer(t)}),"origin-button");return n.ARIAUtils.markAsButton(i),i}static createHighlightedUrl(e,t){const i="://",s=e.indexOf(i);if(-1===s){const t=document.createElement("span");return t.textContent=e,t}const r=document.createElement("span"),n=e.substr(0,s),o=e.substr(s+i.length);return r.createChild("span","url-scheme-"+t).textContent=n,r.createChild("span","url-scheme-separator").textContent=i,r.createChild("span").textContent=o,r}updateVisibleSecurityState(e){this.sidebarMainViewElement.setSecurityState(e.securityState),this.mainView.updateVisibleSecurityState(e)}onVisibleSecurityStateChanged({data:e}){this.updateVisibleSecurityState(e)}selectAndSwitchToMainView(){this.sidebarMainViewElement.select(!0)}showOrigin(e){const t=this.origins.get(e);t&&(t.originView||(t.originView=new D(this,e,t)),this.setVisibleView(t.originView))}wasShown(){super.wasShown(),this.visibleView||this.selectAndSwitchToMainView()}focus(){this.sidebarTree.focus()}setVisibleView(e){this.visibleView!==e&&(this.visibleView&&this.visibleView.detach(),this.visibleView=e,e&&this.splitWidget().setMainWidget(e))}onResponseReceived(e){const t=e.data.request;t.resourceType()===i.ResourceType.resourceTypes.Document&&t.loaderId&&this.lastResponseReceivedForLoaderId.set(t.loaderId,t)}processRequest(e){const t=i.ParsedURL.ParsedURL.extractOrigin(e.url());if(!t)return;let s=e.securityState();"blockable"!==e.mixedContentType&&"optionally-blockable"!==e.mixedContentType||(s="insecure");const r=this.origins.get(t);if(r){const i=r.securityState;if(r.securityState=this.securityStateMin(i,s),i!==r.securityState){const i=e.securityDetails();i&&(r.securityDetails=i),this.sidebarTree.updateOrigin(t,s),r.originView&&r.originView.setSecurityState(s)}}else{const i={securityState:s,securityDetails:e.securityDetails(),loadedFromCache:e.cached(),originView:void 0};this.origins.set(t,i),this.sidebarTree.addOrigin(t,s)}}onRequestFinished(e){const t=e.data;this.updateFilterRequestCounts(t),this.processRequest(t)}updateFilterRequestCounts(e){if("none"===e.mixedContentType)return;let t=r.UIFilter.MixedContentFilterValues.All;e.wasBlocked()?t=r.UIFilter.MixedContentFilterValues.Blocked:"blockable"===e.mixedContentType?t=r.UIFilter.MixedContentFilterValues.BlockOverridden:"optionally-blockable"===e.mixedContentType&&(t=r.UIFilter.MixedContentFilterValues.Displayed);const i=this.filterRequestCounts.get(t);i?this.filterRequestCounts.set(t,i+1):this.filterRequestCounts.set(t,1),this.mainView.refreshExplanations()}filterRequestCount(e){return this.filterRequestCounts.get(e)||0}securityStateMin(e,t){return u.SecurityStateComparator(e,t)<0?e:t}modelAdded(e){if(e.target()!==e.target().outermostTarget())return;this.securityModel=e;const s=e.resourceTreeModel(),r=e.networkManager();this.eventListeners.length&&i.EventTarget.removeEventListeners(this.eventListeners),this.eventListeners=[e.addEventListener(p.VisibleSecurityStateChanged,this.onVisibleSecurityStateChanged,this),s.addEventListener(t.ResourceTreeModel.Events.InterstitialShown,this.onInterstitialShown,this),s.addEventListener(t.ResourceTreeModel.Events.InterstitialHidden,this.onInterstitialHidden,this),r.addEventListener(t.NetworkManager.Events.ResponseReceived,this.onResponseReceived,this),r.addEventListener(t.NetworkManager.Events.RequestFinished,this.onRequestFinished,this)],s.isInterstitialShowing&&this.onInterstitialShown()}modelRemoved(e){this.securityModel===e&&(this.securityModel=null,i.EventTarget.removeEventListeners(this.eventListeners))}onPrimaryPageChanged(e){const{frame:t}=e.data,s=this.lastResponseReceivedForLoaderId.get(t.loaderId);this.selectAndSwitchToMainView(),this.sidebarTree.clearOrigins(),this.origins.clear(),this.lastResponseReceivedForLoaderId.clear(),this.filterRequestCounts.clear(),this.mainView.refreshExplanations();const r=i.ParsedURL.ParsedURL.extractOrigin(s?s.url():t.url);this.sidebarTree.setMainOrigin(r),s&&this.processRequest(s)}onInterstitialShown(){this.selectAndSwitchToMainView(),this.sidebarTree.toggleOriginsList(!0)}onInterstitialHidden(){this.sidebarTree.toggleOriginsList(!1)}}class L extends n.TreeOutline.TreeOutlineInShadow{showOriginInPanel;mainOrigin;originGroupTitles;originGroups;elementsByOrigin;mainViewReloadMessage;constructor(e,t){super(),this.appendChild(e),this.registerCSSFiles([b,C]),this.showOriginInPanel=t,this.mainOrigin=null,this.originGroupTitles=new Map([[M.MainOrigin,E(T.mainOrigin)],[M.NonSecure,E(T.nonsecureOrigins)],[M.Secure,E(T.secureOrigins)],[M.Unknown,E(T.unknownCanceled)]]),this.originGroups=new Map;for(const e of Object.values(M)){const t=this.createOriginGroupElement(this.originGroupTitles.get(e));this.originGroups.set(e,t),this.appendChild(t)}this.mainViewReloadMessage=new n.TreeOutline.TreeElement(E(T.reloadToViewDetails)),this.mainViewReloadMessage.selectable=!1,this.mainViewReloadMessage.listItemElement.classList.add("security-main-view-reload-message");this.originGroups.get(M.MainOrigin).appendChild(this.mainViewReloadMessage),this.clearOriginGroups(),this.elementsByOrigin=new Map}originGroupTitle(e){return this.originGroupTitles.get(e)}originGroupElement(e){return this.originGroups.get(e)}createOriginGroupElement(e){const t=new n.TreeOutline.TreeElement(e,!0);return t.selectable=!1,t.setCollapsible(!1),t.expand(),t.listItemElement.classList.add("security-sidebar-origins"),n.ARIAUtils.setLabel(t.childrenListElement,e),t}toggleOriginsList(e){for(const t of this.originGroups.values())t.hidden=e}addOrigin(e,t){this.mainViewReloadMessage.hidden=!0;const i=new H(P.createHighlightedUrl(e,t),this.showOriginInPanel.bind(this,e),"security-sidebar-tree-item","security-property");i.tooltip=e,this.elementsByOrigin.set(e,i),this.updateOrigin(e,t)}setMainOrigin(e){this.mainOrigin=e}updateOrigin(e,t){const i=this.elementsByOrigin.get(e);let s;if(i.setSecurityState(t),e===this.mainOrigin)s=this.originGroups.get(M.MainOrigin),s.title=E("secure"===t?T.mainOriginSecure:T.mainOriginNonsecure),n.ARIAUtils.setLabel(s.childrenListElement,s.title);else switch(t){case"secure":s=this.originGroupElement(M.Secure);break;case"unknown":s=this.originGroupElement(M.Unknown);break;default:s=this.originGroupElement(M.NonSecure)}const r=i.parent;r!==s&&(r&&(r.removeChild(i),0===r.childCount()&&(r.hidden=!0)),s.appendChild(i),s.hidden=!1)}clearOriginGroups(){for(const[e,t]of this.originGroups)if(e===M.MainOrigin){for(let e=t.childCount()-1;e>0;e--)t.removeChildAtIndex(e);t.title=this.originGroupTitle(M.MainOrigin),t.hidden=!1,this.mainViewReloadMessage.hidden=!1}else t.removeChildren(),t.hidden=!0}clearOrigins(){this.clearOriginGroups(),this.elementsByOrigin.clear()}wasShown(){}}var M;!function(e){e.MainOrigin="MainOrigin",e.NonSecure="NonSecure",e.Secure="Secure",e.Unknown="Unknown"}(M||(M={}));class H extends n.TreeOutline.TreeElement{selectCallback;cssPrefix;iconElement;securityStateInternal;constructor(e,t,i,s){super("",!1),this.selectCallback=t,this.cssPrefix=s,this.listItemElement.classList.add(i),this.iconElement=this.listItemElement.createChild("div","icon"),this.iconElement.classList.add(this.cssPrefix),this.listItemElement.appendChild(e),this.securityStateInternal=null,this.setSecurityState("unknown")}setSecurityState(e){this.securityStateInternal&&this.iconElement.classList.remove(this.cssPrefix+"-"+this.securityStateInternal),this.securityStateInternal=e,this.iconElement.classList.add(this.cssPrefix+"-"+e)}securityState(){return this.securityStateInternal}onselect(){return this.selectCallback(),!0}}class F extends n.Widget.VBox{panel;summarySection;securityExplanationsMain;securityExplanationsExtra;lockSpectrum;summaryText;explanations;securityState;constructor(e){super(!0),this.setMinimumSize(200,100),this.contentElement.classList.add("security-main-view"),this.panel=e,this.summarySection=this.contentElement.createChild("div","security-summary"),this.securityExplanationsMain=this.contentElement.createChild("div","security-explanation-list security-explanations-main"),this.securityExplanationsExtra=this.contentElement.createChild("div","security-explanation-list security-explanations-extra");const t=this.summarySection.createChild("div","security-summary-section-title");t.textContent=E(T.securityOverview),n.ARIAUtils.markAsHeading(t,1);const i=this.summarySection.createChild("div","lock-spectrum");this.lockSpectrum=new Map([["secure",i.createChild("div","lock-icon lock-icon-secure")],["neutral",i.createChild("div","lock-icon lock-icon-neutral")],["insecure",i.createChild("div","lock-icon lock-icon-insecure")]]),n.Tooltip.Tooltip.install(this.getLockSpectrumDiv("secure"),E(T.secure)),n.Tooltip.Tooltip.install(this.getLockSpectrumDiv("neutral"),E(T.info)),n.Tooltip.Tooltip.install(this.getLockSpectrumDiv("insecure"),E(T.notSecure)),this.summarySection.createChild("div","triangle-pointer-container").createChild("div","triangle-pointer-wrapper").createChild("div","triangle-pointer"),this.summaryText=this.summarySection.createChild("div","security-summary-text"),n.ARIAUtils.markAsHeading(this.summaryText,2),this.explanations=null,this.securityState=null}getLockSpectrumDiv(e){const t=this.lockSpectrum.get(e);if(!t)throw new Error(`Invalid argument: ${e}`);return t}addExplanation(e,t){const i=e.createChild("div","security-explanation");i.classList.add("security-explanation-"+t.securityState),i.createChild("div","security-property").classList.add("security-property-"+t.securityState);const s=i.createChild("div","security-explanation-text"),r=s.createChild("div","security-explanation-title");if(t.title?(r.createChild("span").textContent=t.title+" - ",r.createChild("span","security-explanation-title-"+t.securityState).textContent=t.summary):r.textContent=t.summary,s.createChild("div").textContent=t.description,t.certificate.length&&s.appendChild(P.createCertificateViewerButtonForCert(E(T.viewCertificate),t.certificate)),t.recommendations&&t.recommendations.length){const e=s.createChild("ul","security-explanation-recommendations");for(const i of t.recommendations)e.createChild("li").textContent=i}return s}updateVisibleSecurityState(e){this.summarySection.classList.remove("security-summary-"+this.securityState),this.securityState=e.securityState,this.summarySection.classList.add("security-summary-"+this.securityState),"insecure"===this.securityState?(this.getLockSpectrumDiv("insecure").classList.add("lock-icon-insecure"),this.getLockSpectrumDiv("insecure").classList.remove("lock-icon-insecure-broken"),n.Tooltip.Tooltip.install(this.getLockSpectrumDiv("insecure"),E(T.notSecure))):"insecure-broken"===this.securityState&&(this.getLockSpectrumDiv("insecure").classList.add("lock-icon-insecure-broken"),this.getLockSpectrumDiv("insecure").classList.remove("lock-icon-insecure"),n.Tooltip.Tooltip.install(this.getLockSpectrumDiv("insecure"),E(T.notSecureBroken)));const{summary:t,explanations:i}=this.getSecuritySummaryAndExplanations(e);this.summaryText.textContent=t||g[this.securityState](),this.explanations=this.orderExplanations(i),this.refreshExplanations()}getSecuritySummaryAndExplanations(e){const{securityState:t,securityStateIssueIds:i}=e;let s;const r=[];if(s=this.explainSafetyTipSecurity(e,s,r),i.includes("malicious-content"))s=E(T.thisPageIsDangerousFlaggedBy),r.unshift(new w("insecure",void 0,E(T.flaggedByGoogleSafeBrowsing),E(T.toCheckThisPagesStatusVisit)));else{if(i.includes("is-error-page")&&(null===e.certificateSecurityState||null===e.certificateSecurityState.certificateNetworkError))return s=E(T.thisIsAnErrorPage),{summary:s,explanations:r};"insecure-broken"===t&&i.includes("scheme-is-not-cryptographic")&&(s=s||E(T.thisPageIsInsecureUnencrypted))}return i.includes("scheme-is-not-cryptographic")?("neutral"!==t||i.includes("insecure-origin")||(s=E(T.thisPageHasANonhttpsSecureOrigin)),{summary:s,explanations:r}):(this.explainCertificateSecurity(e,r),this.explainConnectionSecurity(e,r),this.explainContentSecurity(e,r),{summary:s,explanations:r})}explainSafetyTipSecurity(e,t,i){const{securityStateIssueIds:s,safetyTipInfo:r}=e,n=[];if(s.includes("bad_reputation")){const e=`${E(T.chromeHasDeterminedThatThisSiteS)}\n\n${E(T.ifYouBelieveThisIsShownIn)}`;n.push({summary:E(T.thisPageIsSuspicious),description:e})}else if(s.includes("lookalike")&&r&&r.safeUrl){const e=new URL(r.safeUrl).hostname,t=`${E(T.thisSitesHostnameLooksSimilarToP,{PH1:e})}\n\n${E(T.ifYouBelieveThisIsShownInErrorSafety)}`;n.push({summary:E(T.possibleSpoofingUrl),description:t})}return n.length>0&&(t=t||E(T.thisPageIsSuspiciousFlaggedBy),i.push(new w("insecure",void 0,n[0].summary,n[0].description))),t}explainCertificateSecurity(e,t){const{certificateSecurityState:i,securityStateIssueIds:s}=e,r=E(T.certificate);if(i&&i.certificateHasSha1Signature){const e=E(T.insecureSha),s=E(T.theCertificateChainForThisSite);i.certificateHasWeakSignature?t.push(new w("insecure",r,e,s,i.certificate,"none")):t.push(new w("neutral",r,e,s,i.certificate,"none"))}i&&s.includes("cert-missing-subject-alt-name")&&t.push(new w("insecure",r,E(T.subjectAlternativeNameMissing),E(T.theCertificateForThisSiteDoesNot),i.certificate,"none")),i&&null!==i.certificateNetworkError?t.push(new w("insecure",r,E(T.missing),E(T.thisSiteIsMissingAValidTrusted,{PH1:i.certificateNetworkError}),i.certificate,"none")):i&&!i.certificateHasSha1Signature&&t.push(new w("secure",r,E(T.validAndTrusted),E(T.theConnectionToThisSiteIsUsingA,{PH1:i.issuer}),i.certificate,"none")),s.includes("pkp-bypassed")&&t.push(new w("info",r,E(T.publickeypinningBypassed),E(T.publickeypinningWasBypassedByA))),i&&i.isCertificateExpiringSoon()&&t.push(new w("info",void 0,E(T.certificateExpiresSoon),E(T.theCertificateForThisSiteExpires)))}explainConnectionSecurity(e,t){const i=e.certificateSecurityState;if(!i)return;const s=E(T.connection);if(i.modernSSL)return void t.push(new w("secure",s,E(T.secureConnectionSettings),E(T.theConnectionToThisSiteIs,{PH1:i.protocol,PH2:i.getKeyExchangeName(),PH3:i.getCipherFullName()})));const r=[];i.obsoleteSslProtocol&&r.push(E(T.sIsObsoleteEnableTlsOrLater,{PH1:i.protocol})),i.obsoleteSslKeyExchange&&r.push(E(T.rsaKeyExchangeIsObsoleteEnableAn)),i.obsoleteSslCipher&&r.push(E(T.sIsObsoleteEnableAnAesgcmbased,{PH1:i.cipher})),i.obsoleteSslSignature&&r.push(E(T.theServerSignatureUsesShaWhichIs)),t.push(new w("info",s,E(T.obsoleteConnectionSettings),E(T.theConnectionToThisSiteIs,{PH1:i.protocol,PH2:i.getKeyExchangeName(),PH3:i.getCipherFullName()}),void 0,void 0,r))}explainContentSecurity(e,t){let i=!0;const s=E(T.resources),r=e.securityStateIssueIds;r.includes("ran-mixed-content")&&(i=!1,t.push(new w("insecure",s,E(T.activeMixedContent),E(T.youHaveRecentlyAllowedNonsecure),[],"blockable"))),r.includes("displayed-mixed-content")&&(i=!1,t.push(new w("neutral",s,E(T.mixedContent),E(T.thisPageIncludesHttpResources),[],"optionally-blockable"))),r.includes("contained-mixed-form")&&(i=!1,t.push(new w("neutral",s,E(T.nonsecureForm),E(T.thisPageIncludesAFormWithA)))),null!==e.certificateSecurityState&&null!==e.certificateSecurityState.certificateNetworkError||(r.includes("ran-content-with-cert-error")&&(i=!1,t.push(new w("insecure",s,E(T.activeContentWithCertificate),E(T.youHaveRecentlyAllowedContent)))),r.includes("displayed-content-with-cert-errors")&&(i=!1,t.push(new w("neutral",s,E(T.contentWithCertificateErrors),E(T.thisPageIncludesResourcesThat))))),i&&(r.includes("scheme-is-not-cryptographic")||t.push(new w("secure",s,E(T.allServedSecurely),E(T.allResourcesOnThisPageAreServed))))}orderExplanations(e){if(0===e.length)return e;const t=["insecure","neutral","secure","info"],i=[];for(const s of t)i.push(...e.filter((e=>e.securityState===s)));return i}refreshExplanations(){if(this.securityExplanationsMain.removeChildren(),this.securityExplanationsExtra.removeChildren(),this.explanations){for(const e of this.explanations)if("info"===e.securityState)this.addExplanation(this.securityExplanationsExtra,e);else switch(e.mixedContentType){case"blockable":this.addMixedContentExplanation(this.securityExplanationsMain,e,r.UIFilter.MixedContentFilterValues.BlockOverridden);break;case"optionally-blockable":this.addMixedContentExplanation(this.securityExplanationsMain,e,r.UIFilter.MixedContentFilterValues.Displayed);break;default:this.addExplanation(this.securityExplanationsMain,e)}if(this.panel.filterRequestCount(r.UIFilter.MixedContentFilterValues.Blocked)>0){const e={securityState:"info",summary:E(T.blockedMixedContent),description:E(T.yourPageRequestedNonsecure),mixedContentType:"blockable",certificate:[],title:""};this.addMixedContentExplanation(this.securityExplanationsMain,e,r.UIFilter.MixedContentFilterValues.Blocked)}}}addMixedContentExplanation(e,t,i){const s=this.addExplanation(e,t),r=this.panel.filterRequestCount(i);if(!r){return void(s.createChild("div","security-mixed-content").textContent=E(T.reloadThePageToRecordRequestsFor))}const o=s.createChild("div","security-mixed-content devtools-link");n.ARIAUtils.markAsLink(o),o.tabIndex=0,o.textContent=E(T.viewDRequestsInNetworkPanel,{n:r}),o.addEventListener("click",this.showNetworkFilter.bind(this,i)),o.addEventListener("keydown",(e=>{"Enter"===e.key&&this.showNetworkFilter(i,e)}))}showNetworkFilter(e,t){t.consume(),i.Revealer.reveal(r.UIFilter.UIRequestFilter.filters([{filterType:r.UIFilter.FilterType.MixedContent,filterValue:e}]))}wasShown(){super.wasShown(),this.registerCSSFiles([b,f])}}class D extends n.Widget.VBox{panel;originLockIcon;constructor(t,s,o){super(),this.panel=t,this.setMinimumSize(200,100),this.element.classList.add("security-origin-view");const a=this.element.createChild("div","title-section"),c=a.createChild("div","title-section-header");c.textContent=E(T.origin),n.ARIAUtils.markAsHeading(c,1);const l=a.createChild("div","origin-display");this.originLockIcon=l.createChild("span","security-property"),this.originLockIcon.classList.add("security-property-"+o.securityState),l.appendChild(P.createHighlightedUrl(s,o.securityState));const u=a.createChild("div","view-network-button"),d=n.UIUtils.createTextButton(E(T.viewRequestsInNetworkPanel),(e=>{e.consume();const t=new i.ParsedURL.ParsedURL(s);i.Revealer.reveal(r.UIFilter.UIRequestFilter.filters([{filterType:r.UIFilter.FilterType.Domain,filterValue:t.host},{filterType:r.UIFilter.FilterType.Scheme,filterValue:t.scheme}]))}));if(u.appendChild(d),n.ARIAUtils.markAsLink(d),o.securityDetails){const t=this.element.createChild("div","origin-view-section"),i=t.createChild("div","origin-view-section-title");i.textContent=E(T.connection),n.ARIAUtils.markAsHeading(i,2);let r=new V;if(t.appendChild(r.element()),r.addRow(E(T.protocol),o.securityDetails.protocol),o.securityDetails.keyExchange&&o.securityDetails.keyExchangeGroup?r.addRow(E(T.keyExchange),o.securityDetails.keyExchange+" with "+o.securityDetails.keyExchangeGroup):o.securityDetails.keyExchange?r.addRow(E(T.keyExchange),o.securityDetails.keyExchange):o.securityDetails.keyExchangeGroup&&r.addRow(E(T.keyExchange),o.securityDetails.keyExchangeGroup),o.securityDetails.serverSignatureAlgorithm){let e=R.get(o.securityDetails.serverSignatureAlgorithm);e??=E(T.unknownField)+" ("+o.securityDetails.serverSignatureAlgorithm+")",r.addRow(E(T.serverSignature),e)}r.addRow(E(T.cipher),o.securityDetails.cipher+(o.securityDetails.mac?" with "+o.securityDetails.mac:"")),o.securityDetails.encryptedClientHello&&r.addRow(E(T.encryptedClientHello),E(T.enabled));const a=this.element.createChild("div","origin-view-section"),c=a.createChild("div","origin-view-section-title");c.textContent=E(T.certificate),n.ARIAUtils.markAsHeading(c,2);const l=o.securityDetails.signedCertificateTimestampList.length,u=o.securityDetails.certificateTransparencyCompliance;let d;if(l||"unknown"!==u){d=this.element.createChild("div","origin-view-section");const e=d.createChild("div","origin-view-section-title");e.textContent=E(T.certificateTransparency),n.ARIAUtils.markAsHeading(e,2)}const h=this.createSanDiv(o.securityDetails.sanList),p=new Date(1e3*o.securityDetails.validFrom).toUTCString(),g=new Date(1e3*o.securityDetails.validTo).toUTCString();if(r=new V,a.appendChild(r.element()),r.addRow(E(T.subject),o.securityDetails.subjectName),r.addRow(e.i18n.lockedString("SAN"),h),r.addRow(E(T.validFrom),p),r.addRow(E(T.validUntil),g),r.addRow(E(T.issuer),o.securityDetails.issuer),r.addRow("",P.createCertificateViewerButtonForOrigin(E(T.openFullCertificateDetails),s)),!d)return;const m=new V;m.element().classList.add("sct-summary"),d.appendChild(m.element());for(let e=0;e<l;e++){const t=o.securityDetails.signedCertificateTimestampList[e];m.addRow(E(T.sct),t.logDescription+" ("+t.origin+", "+t.status+")")}const y=d.createChild("div","sct-details");y.classList.add("hidden");for(let e=0;e<l;e++){const t=new V;y.appendChild(t.element());const i=o.securityDetails.signedCertificateTimestampList[e];t.addRow(E(T.logName),i.logDescription),t.addRow(E(T.logId),i.logId.replace(/(.{2})/g,"$1 ")),t.addRow(E(T.validationStatus),i.status),t.addRow(E(T.source),i.origin),t.addRow(E(T.issuedAt),new Date(i.timestamp).toUTCString()),t.addRow(E(T.hashAlgorithm),i.hashAlgorithm),t.addRow(E(T.signatureAlgorithm),i.signatureAlgorithm),t.addRow(E(T.signatureData),i.signatureData.replace(/(.{2})/g,"$1 "))}if(l){function e(){let e;const i=!y.classList.contains("hidden");e=E(i?T.showFullDetails:T.hideFullDetails),t.textContent=e,n.ARIAUtils.setLabel(t,e),n.ARIAUtils.setExpanded(t,!i),m.element().classList.toggle("hidden"),y.classList.toggle("hidden")}const t=n.UIUtils.createTextButton(E(T.showFullDetails),e,"details-toggle");d.appendChild(t)}switch(u){case"compliant":d.createChild("div","origin-view-section-notes").textContent=E(T.thisRequestCompliesWithChromes);break;case"not-compliant":d.createChild("div","origin-view-section-notes").textContent=E(T.thisRequestDoesNotComplyWith)}const S=this.element.createChild("div","origin-view-section origin-view-notes");o.loadedFromCache&&(S.createChild("div").textContent=E(T.thisResponseWasLoadedFromCache)),S.createChild("div").textContent=E(T.theSecurityDetailsAboveAreFrom)}else if("secure"===o.securityState){const e=this.element.createChild("div","origin-view-section"),t=e.createChild("div","origin-view-section-title");t.textContent=E(T.secure),n.ARIAUtils.markAsHeading(t,2),e.createChild("div").textContent=E(T.thisOriginIsANonhttpsSecure)}else if("unknown"!==o.securityState){const e=this.element.createChild("div","origin-view-section"),t=e.createChild("div","origin-view-section-title");t.textContent=E(T.notSecure),n.ARIAUtils.markAsHeading(t,2),e.createChild("div").textContent=E(T.yourConnectionToThisOriginIsNot)}else{const e=this.element.createChild("div","origin-view-section"),t=e.createChild("div","origin-view-section-title");t.textContent=E(T.noSecurityInformation),n.ARIAUtils.markAsHeading(t,2),e.createChild("div").textContent=E(T.noSecurityDetailsAreAvailableFor)}}createSanDiv(e){const t=document.createElement("div");if(0===e.length)t.textContent=E(T.na),t.classList.add("empty-san");else{const i=2,s=e.length>i+1;for(let r=0;r<e.length;r++){const n=t.createChild("span","san-entry");n.textContent=e[r],s&&r>=i&&n.classList.add("truncated-entry")}if(s){function i(){const i=t.classList.contains("truncated-san");let r;i?(t.classList.remove("truncated-san"),r=E(T.showLess)):(t.classList.add("truncated-san"),r=E(T.showMoreSTotal,{PH1:e.length})),s.textContent=r,n.ARIAUtils.setLabel(s,r),n.ARIAUtils.setExpanded(s,i)}const s=n.UIUtils.createTextButton(E(T.showMoreSTotal,{PH1:e.length}),i);t.appendChild(s),i()}}return t}setSecurityState(e){for(const e of Array.prototype.slice.call(this.originLockIcon.classList))e.startsWith("security-property-")&&this.originLockIcon.classList.remove(e);this.originLockIcon.classList.add("security-property-"+e)}wasShown(){super.wasShown(),this.registerCSSFiles([k,b])}}class V{elementInternal;constructor(){this.elementInternal=document.createElement("table"),this.elementInternal.classList.add("details-table")}element(){return this.elementInternal}addRow(e,t){const i=this.elementInternal.createChild("tr","details-table-row");i.createChild("td").textContent=e;const s=i.createChild("td");"string"==typeof t?s.textContent=t:s.appendChild(t)}}var O=Object.freeze({__proto__:null,SecurityPanel:P,SecurityPanelSidebarTree:L,get OriginGroup(){return M},SecurityPanelSidebarTreeElement:H,SecurityMainView:F,SecurityOriginView:D,SecurityDetailsTable:V});export{v as SecurityModel,O as SecurityPanel};
