import"../shell/shell.js";import*as e from"../../core/sdk/sdk.js";import*as t from"../../ui/legacy/legacy.js";import*as i from"../../core/i18n/i18n.js";import*as o from"../../core/root/root.js";import*as n from"../../core/common/common.js";import*as r from"../../models/issues_manager/issues_manager.js";import*as a from"../../models/workspace/workspace.js";import*as s from"../../panels/network/forward/forward.js";import*as c from"../../panels/mobile_throttling/mobile_throttling.js";import*as l from"../../ui/legacy/components/utils/utils.js";import*as g from"../main/main.js";const d={showEventListenerBreakpoints:"Show Event Listener Breakpoints",eventListenerBreakpoints:"Event Listener Breakpoints",showCspViolationBreakpoints:"Show CSP Violation Breakpoints",cspViolationBreakpoints:"CSP Violation Breakpoints",showXhrfetchBreakpoints:"Show XHR/fetch Breakpoints",xhrfetchBreakpoints:"XHR/fetch Breakpoints",showDomBreakpoints:"Show DOM Breakpoints",domBreakpoints:"DOM Breakpoints",showGlobalListeners:"Show Global Listeners",globalListeners:"Global Listeners",page:"Page",showPage:"Show Page",overrides:"Overrides",showOverrides:"Show Overrides",contentScripts:"Content scripts",showContentScripts:"Show Content scripts"},w=i.i18n.registerUIStrings("panels/browser_debugger/browser_debugger-meta.ts",d),p=i.i18n.getLazilyComputedLocalizedString.bind(void 0,w);let m,u;async function R(){return m||(m=await import("../../panels/browser_debugger/browser_debugger.js")),m}async function y(){return u||(u=await import("../../panels/sources/sources.js")),u}t.ViewManager.registerViewExtension({loadView:async()=>(await R()).EventListenerBreakpointsSidebarPane.EventListenerBreakpointsSidebarPane.instance(),id:"sources.eventListenerBreakpoints",location:"sources.sidebar-bottom",commandPrompt:p(d.showEventListenerBreakpoints),title:p(d.eventListenerBreakpoints),order:9,persistence:"permanent"}),t.ViewManager.registerViewExtension({loadView:async()=>(await R()).CSPViolationBreakpointsSidebarPane.CSPViolationBreakpointsSidebarPane.instance(),id:"sources.cspViolationBreakpoints",location:"sources.sidebar-bottom",commandPrompt:p(d.showCspViolationBreakpoints),title:p(d.cspViolationBreakpoints),order:10,persistence:"permanent"}),t.ViewManager.registerViewExtension({loadView:async()=>(await R()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance(),id:"sources.xhrBreakpoints",location:"sources.sidebar-bottom",commandPrompt:p(d.showXhrfetchBreakpoints),title:p(d.xhrfetchBreakpoints),order:5,persistence:"permanent",hasToolbar:!0}),t.ViewManager.registerViewExtension({loadView:async()=>(await R()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"sources.domBreakpoints",location:"sources.sidebar-bottom",commandPrompt:p(d.showDomBreakpoints),title:p(d.domBreakpoints),order:7,persistence:"permanent"}),t.ViewManager.registerViewExtension({loadView:async()=>(await R()).ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane.instance(),id:"sources.globalListeners",location:"sources.sidebar-bottom",commandPrompt:p(d.showGlobalListeners),title:p(d.globalListeners),order:8,persistence:"permanent",hasToolbar:!0}),t.ViewManager.registerViewExtension({loadView:async()=>(await R()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"elements.domBreakpoints",location:"elements-sidebar",commandPrompt:p(d.showDomBreakpoints),title:p(d.domBreakpoints),order:6,persistence:"permanent"}),t.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:p(d.page),commandPrompt:p(d.showPage),order:2,persistence:"permanent",loadView:async()=>(await y()).SourcesNavigator.NetworkNavigatorView.instance()}),t.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-overrides",title:p(d.overrides),commandPrompt:p(d.showOverrides),order:4,persistence:"permanent",loadView:async()=>(await y()).SourcesNavigator.OverridesNavigatorView.instance()}),t.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-contentScripts",title:p(d.contentScripts),commandPrompt:p(d.showContentScripts),order:5,persistence:"permanent",loadView:async()=>(await y()).SourcesNavigator.ContentScriptsNavigatorView.instance()}),t.ContextMenu.registerProvider({contextTypes:()=>[e.DOMModel.DOMNode],loadProvider:async()=>(await R()).DOMBreakpointsSidebarPane.ContextMenuProvider.instance(),experiment:void 0}),t.Context.registerListener({contextTypes:()=>[e.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await R()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance()}),t.Context.registerListener({contextTypes:()=>[e.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await R()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance()});const A={developerResources:"Developer Resources",showDeveloperResources:"Show Developer Resources"},h=i.i18n.registerUIStrings("panels/developer_resources/developer_resources-meta.ts",A),S=i.i18n.getLazilyComputedLocalizedString.bind(void 0,h);let k;t.ViewManager.registerViewExtension({location:"drawer-view",id:"resource-loading-pane",title:S(A.developerResources),commandPrompt:S(A.showDeveloperResources),order:100,persistence:"closeable",experiment:o.Runtime.ExperimentName.DEVELOPER_RESOURCES_VIEW,loadView:async()=>new((await async function(){return k||(k=await import("../../panels/developer_resources/developer_resources.js")),k}()).DeveloperResourcesView.DeveloperResourcesView)});const P={issues:"Issues",showIssues:"Show Issues",cspViolations:"CSP Violations",showCspViolations:"Show CSP Violations"},v=i.i18n.registerUIStrings("panels/issues/issues-meta.ts",P),E=i.i18n.getLazilyComputedLocalizedString.bind(void 0,v);let T;async function C(){return T||(T=await import("../../panels/issues/issues.js")),T}t.ViewManager.registerViewExtension({location:"drawer-view",id:"issues-pane",title:E(P.issues),commandPrompt:E(P.showIssues),order:100,persistence:"closeable",loadView:async()=>(await C()).IssuesPane.IssuesPane.instance()}),t.ViewManager.registerViewExtension({location:"drawer-view",id:"csp-violations-pane",title:E(P.cspViolations),commandPrompt:E(P.showCspViolations),order:100,persistence:"closeable",loadView:async()=>(await C()).CSPViolationsView.CSPViolationsView.instance(),experiment:o.Runtime.ExperimentName.CSP_VIOLATIONS_VIEW}),n.Revealer.registerRevealer({contextTypes:()=>[r.Issue.Issue],destination:n.Revealer.RevealerDestination.ISSUES_VIEW,loadRevealer:async()=>(await C()).IssueRevealer.IssueRevealer.instance()});const f={resetView:"Reset view",switchToPanMode:"Switch to pan mode",switchToRotateMode:"Switch to rotate mode",zoomIn:"Zoom in",zoomOut:"Zoom out",panOrRotateUp:"Pan or rotate up",panOrRotateDown:"Pan or rotate down",panOrRotateLeft:"Pan or rotate left",panOrRotateRight:"Pan or rotate right"},b=i.i18n.registerUIStrings("panels/layer_viewer/layer_viewer-meta.ts",f),N=i.i18n.getLazilyComputedLocalizedString.bind(void 0,b);t.ActionRegistration.registerActionExtension({actionId:"layers.reset-view",category:t.ActionRegistration.ActionCategory.LAYERS,title:N(f.resetView),bindings:[{shortcut:"0"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.pan-mode",category:t.ActionRegistration.ActionCategory.LAYERS,title:N(f.switchToPanMode),bindings:[{shortcut:"x"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.rotate-mode",category:t.ActionRegistration.ActionCategory.LAYERS,title:N(f.switchToRotateMode),bindings:[{shortcut:"v"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.zoom-in",category:t.ActionRegistration.ActionCategory.LAYERS,title:N(f.zoomIn),bindings:[{shortcut:"Shift+Plus"},{shortcut:"NumpadPlus"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.zoom-out",category:t.ActionRegistration.ActionCategory.LAYERS,title:N(f.zoomOut),bindings:[{shortcut:"Shift+Minus"},{shortcut:"NumpadMinus"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.up",category:t.ActionRegistration.ActionCategory.LAYERS,title:N(f.panOrRotateUp),bindings:[{shortcut:"Up"},{shortcut:"w"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.down",category:t.ActionRegistration.ActionCategory.LAYERS,title:N(f.panOrRotateDown),bindings:[{shortcut:"Down"},{shortcut:"s"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.left",category:t.ActionRegistration.ActionCategory.LAYERS,title:N(f.panOrRotateLeft),bindings:[{shortcut:"Left"},{shortcut:"a"}]}),t.ActionRegistration.registerActionExtension({actionId:"layers.right",category:t.ActionRegistration.ActionCategory.LAYERS,title:N(f.panOrRotateRight),bindings:[{shortcut:"Right"},{shortcut:"d"}]});const x={throttling:"Throttling",showThrottling:"Show Throttling",goOffline:"Go offline",device:"device",throttlingTag:"throttling",enableSlowGThrottling:"Enable slow `3G` throttling",enableFastGThrottling:"Enable fast `3G` throttling",goOnline:"Go online"},V=i.i18n.registerUIStrings("panels/mobile_throttling/mobile_throttling-meta.ts",x),L=i.i18n.getLazilyComputedLocalizedString.bind(void 0,V);let I;async function D(){return I||(I=await import("../../panels/mobile_throttling/mobile_throttling.js")),I}t.ViewManager.registerViewExtension({location:"settings-view",id:"throttling-conditions",title:L(x.throttling),commandPrompt:L(x.showThrottling),order:35,loadView:async()=>(await D()).ThrottlingSettingsTab.ThrottlingSettingsTab.instance(),settings:["customNetworkConditions"]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-offline",category:t.ActionRegistration.ActionCategory.NETWORK,title:L(x.goOffline),loadActionDelegate:async()=>(await D()).ThrottlingManager.ActionDelegate.instance(),tags:[L(x.device),L(x.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-low-end-mobile",category:t.ActionRegistration.ActionCategory.NETWORK,title:L(x.enableSlowGThrottling),loadActionDelegate:async()=>(await D()).ThrottlingManager.ActionDelegate.instance(),tags:[L(x.device),L(x.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-mid-tier-mobile",category:t.ActionRegistration.ActionCategory.NETWORK,title:L(x.enableFastGThrottling),loadActionDelegate:async()=>(await D()).ThrottlingManager.ActionDelegate.instance(),tags:[L(x.device),L(x.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-online",category:t.ActionRegistration.ActionCategory.NETWORK,title:L(x.goOnline),loadActionDelegate:async()=>(await D()).ThrottlingManager.ActionDelegate.instance(),tags:[L(x.device),L(x.throttlingTag)]}),n.Settings.registerSettingExtension({storageType:n.Settings.SettingStorageType.Synced,settingName:"customNetworkConditions",settingType:n.Settings.SettingType.ARRAY,defaultValue:[]});const M={showNetwork:"Show Network",network:"Network",showNetworkRequestBlocking:"Show Network request blocking",networkRequestBlocking:"Network request blocking",showNetworkConditions:"Show Network conditions",networkConditions:"Network conditions",diskCache:"disk cache",networkThrottling:"network throttling",showSearch:"Show Search",search:"Search",recordNetworkLog:"Record network log",stopRecordingNetworkLog:"Stop recording network log",hideRequestDetails:"Hide request details",colorcodeResourceTypes:"Color-code resource types",colorCode:"color code",resourceType:"resource type",colorCodeByResourceType:"Color code by resource type",useDefaultColors:"Use default colors",groupNetworkLogByFrame:"Group network log by frame",netWork:"network",frame:"frame",group:"group",groupNetworkLogItemsByFrame:"Group network log items by frame",dontGroupNetworkLogItemsByFrame:"Don't group network log items by frame",clear:"Clear network log"},O=i.i18n.registerUIStrings("panels/network/network-meta.ts",M),B=i.i18n.getLazilyComputedLocalizedString.bind(void 0,O);let _;async function F(){return _||(_=await import("../../panels/network/network.js")),_}function j(e){return void 0===_?[]:e(_)}t.ViewManager.registerViewExtension({location:"panel",id:"network",commandPrompt:B(M.showNetwork),title:B(M.network),order:40,condition:o.Runtime.ConditionName.REACT_NATIVE_UNSTABLE_NETWORK_PANEL,loadView:async()=>(await F()).NetworkPanel.NetworkPanel.instance()}),t.ViewManager.registerViewExtension({location:"drawer-view",id:"network.blocked-urls",commandPrompt:B(M.showNetworkRequestBlocking),title:B(M.networkRequestBlocking),persistence:"closeable",order:60,loadView:async()=>(await F()).BlockedURLsPane.BlockedURLsPane.instance()}),t.ViewManager.registerViewExtension({location:"drawer-view",id:"network.config",commandPrompt:B(M.showNetworkConditions),title:B(M.networkConditions),persistence:"closeable",order:40,tags:[B(M.diskCache),B(M.networkThrottling),i.i18n.lockedLazyString("useragent"),i.i18n.lockedLazyString("user agent"),i.i18n.lockedLazyString("user-agent")],loadView:async()=>(await F()).NetworkConfigView.NetworkConfigView.instance()}),t.ViewManager.registerViewExtension({location:"network-sidebar",id:"network.search-network-tab",commandPrompt:B(M.showSearch),title:B(M.search),persistence:"permanent",loadView:async()=>(await F()).NetworkPanel.SearchNetworkView.instance()}),t.ActionRegistration.registerActionExtension({actionId:"network.toggle-recording",category:t.ActionRegistration.ActionCategory.NETWORK,iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>j((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>(await F()).NetworkPanel.ActionDelegate.instance(),options:[{value:!0,title:B(M.recordNetworkLog)},{value:!1,title:B(M.stopRecordingNetworkLog)}],bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"network.clear",category:t.ActionRegistration.ActionCategory.NETWORK,title:B(M.clear),iconClass:"clear",loadActionDelegate:async()=>(await F()).NetworkPanel.ActionDelegate.instance(),contextTypes:()=>j((e=>[e.NetworkPanel.NetworkPanel])),bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),t.ActionRegistration.registerActionExtension({actionId:"network.hide-request-details",category:t.ActionRegistration.ActionCategory.NETWORK,title:B(M.hideRequestDetails),contextTypes:()=>j((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>(await F()).NetworkPanel.ActionDelegate.instance(),bindings:[{shortcut:"Esc"}]}),t.ActionRegistration.registerActionExtension({actionId:"network.search",category:t.ActionRegistration.ActionCategory.NETWORK,title:B(M.search),contextTypes:()=>j((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>(await F()).NetworkPanel.ActionDelegate.instance(),bindings:[{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]}]}),n.Settings.registerSettingExtension({category:n.Settings.SettingCategory.NETWORK,storageType:n.Settings.SettingStorageType.Synced,title:B(M.colorcodeResourceTypes),settingName:"networkColorCodeResourceTypes",settingType:n.Settings.SettingType.BOOLEAN,defaultValue:!1,tags:[B(M.colorCode),B(M.resourceType)],options:[{value:!0,title:B(M.colorCodeByResourceType)},{value:!1,title:B(M.useDefaultColors)}]}),n.Settings.registerSettingExtension({category:n.Settings.SettingCategory.NETWORK,storageType:n.Settings.SettingStorageType.Synced,title:B(M.groupNetworkLogByFrame),settingName:"network.group-by-frame",settingType:n.Settings.SettingType.BOOLEAN,defaultValue:!1,tags:[B(M.netWork),B(M.frame),B(M.group)],options:[{value:!0,title:B(M.groupNetworkLogItemsByFrame)},{value:!1,title:B(M.dontGroupNetworkLogItemsByFrame)}]}),t.ViewManager.registerLocationResolver({name:"network-sidebar",category:t.ViewManager.ViewLocationCategory.NETWORK,loadResolver:async()=>(await F()).NetworkPanel.NetworkPanel.instance()}),t.ContextMenu.registerProvider({contextTypes:()=>[e.NetworkRequest.NetworkRequest,e.Resource.Resource,a.UISourceCode.UISourceCode],loadProvider:async()=>(await F()).NetworkPanel.ContextMenuProvider.instance(),experiment:void 0}),n.Revealer.registerRevealer({contextTypes:()=>[e.NetworkRequest.NetworkRequest],destination:n.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>(await F()).NetworkPanel.RequestRevealer.instance()}),n.Revealer.registerRevealer({contextTypes:()=>[s.UIRequestLocation.UIRequestLocation],loadRevealer:async()=>(await F()).NetworkPanel.RequestLocationRevealer.instance(),destination:void 0}),n.Revealer.registerRevealer({contextTypes:()=>[s.NetworkRequestId.NetworkRequestId],destination:n.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>(await F()).NetworkPanel.RequestIdRevealer.instance()}),n.Revealer.registerRevealer({contextTypes:()=>[s.UIFilter.UIRequestFilter],destination:n.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>(await F()).NetworkPanel.NetworkLogWithFilterRevealer.instance()});const U={application:"Application",showApplication:"Show Application",pwa:"pwa",clearSiteData:"Clear site data",clearSiteDataIncludingThirdparty:"Clear site data (including third-party cookies)",startRecordingEvents:"Start recording events",stopRecordingEvents:"Stop recording events"},W=i.i18n.registerUIStrings("panels/application/application-meta.ts",U),z=i.i18n.getLazilyComputedLocalizedString.bind(void 0,W);let q;async function G(){return q||(q=await import("../../panels/application/application.js")),q}t.ViewManager.registerViewExtension({location:"panel",id:"resources",title:z(U.application),commandPrompt:z(U.showApplication),order:70,loadView:async()=>(await G()).ResourcesPanel.ResourcesPanel.instance(),tags:[z(U.pwa)]}),t.ActionRegistration.registerActionExtension({category:t.ActionRegistration.ActionCategory.RESOURCES,actionId:"resources.clear",title:z(U.clearSiteData),loadActionDelegate:async()=>(await G()).StorageView.ActionDelegate.instance()}),t.ActionRegistration.registerActionExtension({category:t.ActionRegistration.ActionCategory.RESOURCES,actionId:"resources.clear-incl-third-party-cookies",title:z(U.clearSiteDataIncludingThirdparty),loadActionDelegate:async()=>(await G()).StorageView.ActionDelegate.instance()}),t.ActionRegistration.registerActionExtension({actionId:"background-service.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>void 0===q?[]:(e=>[e.BackgroundServiceView.BackgroundServiceView])(q),loadActionDelegate:async()=>(await G()).BackgroundServiceView.ActionDelegate.instance(),category:t.ActionRegistration.ActionCategory.BACKGROUND_SERVICES,options:[{value:!0,title:z(U.startRecordingEvents)},{value:!1,title:z(U.stopRecordingEvents)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),n.Revealer.registerRevealer({contextTypes:()=>[e.Resource.Resource],destination:n.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>(await G()).ResourcesPanel.ResourceRevealer.instance()}),n.Revealer.registerRevealer({contextTypes:()=>[e.ResourceTreeModel.ResourceTreeFrame],destination:n.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>(await G()).ResourcesPanel.FrameDetailsRevealer.instance()});const K={performance:"Performance",showPerformance:"Show Performance",javascriptProfiler:"JavaScript Profiler",showJavascriptProfiler:"Show JavaScript Profiler",record:"Record",stop:"Stop",startProfilingAndReloadPage:"Start profiling and reload page",saveProfile:"Save profile…",loadProfile:"Load profile…",previousFrame:"Previous frame",nextFrame:"Next frame",showRecentTimelineSessions:"Show recent timeline sessions",previousRecording:"Previous recording",nextRecording:"Next recording",hideChromeFrameInLayersView:"Hide `chrome` frame in Layers view",startStopRecording:"Start/stop recording"},Y=i.i18n.registerUIStrings("panels/timeline/timeline-meta.ts",K),H=i.i18n.getLazilyComputedLocalizedString.bind(void 0,Y);let J,X;async function Z(){return J||(J=await import("../../panels/timeline/timeline.js")),J}async function Q(){return X||(X=await import("../../panels/profiler/profiler.js")),X}function $(e){return void 0===J?[]:e(J)}t.ViewManager.registerViewExtension({location:"panel",id:"timeline",title:H(K.performance),commandPrompt:H(K.showPerformance),order:50,loadView:async()=>(await Z()).TimelinePanel.TimelinePanel.instance()}),t.ViewManager.registerViewExtension({location:"panel",id:"js_profiler",title:H(K.javascriptProfiler),commandPrompt:H(K.showJavascriptProfiler),persistence:"closeable",order:65,experiment:o.Runtime.ExperimentName.JS_PROFILER_TEMP_ENABLE,loadView:async()=>(await Q()).ProfilesPanel.JSProfilerPanel.instance()}),t.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:t.ActionRegistration.ActionCategory.PERFORMANCE,iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>$((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>(await Z()).TimelinePanel.ActionDelegate.instance(),options:[{value:!0,title:H(K.record)},{value:!1,title:H(K.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>$((e=>[e.TimelinePanel.TimelinePanel])),category:t.ActionRegistration.ActionCategory.PERFORMANCE,title:H(K.startProfilingAndReloadPage),loadActionDelegate:async()=>(await Z()).TimelinePanel.ActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]}),t.ActionRegistration.registerActionExtension({category:t.ActionRegistration.ActionCategory.PERFORMANCE,actionId:"timeline.save-to-file",contextTypes:()=>$((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>(await Z()).TimelinePanel.ActionDelegate.instance(),title:H(K.saveProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+S"},{platform:"mac",shortcut:"Meta+S"}]}),t.ActionRegistration.registerActionExtension({category:t.ActionRegistration.ActionCategory.PERFORMANCE,actionId:"timeline.load-from-file",contextTypes:()=>$((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>(await Z()).TimelinePanel.ActionDelegate.instance(),title:H(K.loadProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+O"},{platform:"mac",shortcut:"Meta+O"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-previous-frame",category:t.ActionRegistration.ActionCategory.PERFORMANCE,title:H(K.previousFrame),contextTypes:()=>$((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>(await Z()).TimelinePanel.ActionDelegate.instance(),bindings:[{shortcut:"["}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-next-frame",category:t.ActionRegistration.ActionCategory.PERFORMANCE,title:H(K.nextFrame),contextTypes:()=>$((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>(await Z()).TimelinePanel.ActionDelegate.instance(),bindings:[{shortcut:"]"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>(await Z()).TimelinePanel.ActionDelegate.instance(),category:t.ActionRegistration.ActionCategory.PERFORMANCE,title:H(K.showRecentTimelineSessions),contextTypes:()=>$((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.previous-recording",category:t.ActionRegistration.ActionCategory.PERFORMANCE,loadActionDelegate:async()=>(await Z()).TimelinePanel.ActionDelegate.instance(),title:H(K.previousRecording),contextTypes:()=>$((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Left"},{platform:"mac",shortcut:"Meta+Left"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.next-recording",category:t.ActionRegistration.ActionCategory.PERFORMANCE,loadActionDelegate:async()=>(await Z()).TimelinePanel.ActionDelegate.instance(),title:H(K.nextRecording),contextTypes:()=>$((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Right"},{platform:"mac",shortcut:"Meta+Right"}]}),t.ActionRegistration.registerActionExtension({actionId:"profiler.js-toggle-recording",category:t.ActionRegistration.ActionCategory.JAVASCRIPT_PROFILER,title:H(K.startStopRecording),iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>void 0===X?[]:(e=>[e.ProfilesPanel.JSProfilerPanel])(X),loadActionDelegate:async()=>(await Q()).ProfilesPanel.JSProfilerPanel.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),n.Settings.registerSettingExtension({category:n.Settings.SettingCategory.PERFORMANCE,storageType:n.Settings.SettingStorageType.Synced,title:H(K.hideChromeFrameInLayersView),settingName:"frameViewerHideChromeWindow",settingType:n.Settings.SettingType.BOOLEAN,defaultValue:!1}),n.Linkifier.registerLinkifier({contextTypes:()=>$((e=>[e.CLSLinkifier.CLSRect])),loadLinkifier:async()=>(await Z()).CLSLinkifier.Linkifier.instance()}),t.ContextMenu.registerItem({location:t.ContextMenu.ItemLocation.TIMELINE_MENU_OPEN,actionId:"timeline.load-from-file",order:10}),t.ContextMenu.registerItem({location:t.ContextMenu.ItemLocation.TIMELINE_MENU_OPEN,actionId:"timeline.save-to-file",order:15});const ee={main:"Main"},te=i.i18n.registerUIStrings("entrypoints/worker_app/WorkerMain.ts",ee),ie=i.i18n.getLocalizedString.bind(void 0,te);let oe;class ne{static instance(e={forceNew:null}){const{forceNew:t}=e;return oe&&!t||(oe=new ne),oe}async run(){e.Connections.initMainConnection((async()=>{await e.TargetManager.TargetManager.instance().maybeAttachInitialTarget()||e.TargetManager.TargetManager.instance().createTarget("main",ie(ee.main),e.Target.Type.ServiceWorker,null)}),l.TargetDetachedDialog.TargetDetachedDialog.webSocketConnectionLost),new c.NetworkPanelIndicator.NetworkPanelIndicator}}n.Runnable.registerEarlyInitializationRunnable(ne.instance),e.ChildTargetManager.ChildTargetManager.install((async({target:t,waitingForDebugger:i})=>{if(t.parentTarget()||t.type()!==e.Target.Type.ServiceWorker||!i)return;const o=t.model(e.DebuggerModel.DebuggerModel);o&&(o.isReadyToPause()||await o.once(e.DebuggerModel.Events.DebuggerIsReadyToPause),o.pause())})),self.runtime=o.Runtime.Runtime.instance({forceNew:!0}),new g.MainImpl.MainImpl;
