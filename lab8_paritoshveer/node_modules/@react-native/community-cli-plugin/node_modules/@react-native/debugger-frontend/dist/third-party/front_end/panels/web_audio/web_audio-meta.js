import*as i from"../../core/i18n/i18n.js";import*as e from"../../ui/legacy/legacy.js";const o={webaudio:"WebAudio",audio:"audio",showWebaudio:"Show WebAudio"},a=i.i18n.registerUIStrings("panels/web_audio/web_audio-meta.ts",o),t=i.i18n.getLazilyComputedLocalizedString.bind(void 0,a);let d;e.ViewManager.registerViewExtension({location:"drawer-view",id:"web-audio",title:t(o.webaudio),commandPrompt:t(o.showWebaudio),persistence:"closeable",order:100,loadView:async()=>(await async function(){return d||(d=await import("./web_audio.js")),d}()).WebAudioView.WebAudioView.instance(),tags:[t(o.audio)]});
