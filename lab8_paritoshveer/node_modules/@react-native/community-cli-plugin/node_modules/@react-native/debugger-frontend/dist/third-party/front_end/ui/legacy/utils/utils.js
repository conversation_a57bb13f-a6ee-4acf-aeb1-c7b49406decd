import*as e from"../theme_support/theme_support.js";import*as o from"../../../core/platform/platform.js";function r(e){const r=e.target,i=r?r.ownerDocument:null,t=i?o.DOMUtilities.deepActiveElement(i):null;!function(e){for(;e&&!e.__widget;)e=e.parentNodeOrShadowHost();if(!e)return;let o=e.__widget;for(;o&&o.parentWidget();){const e=o.parentWidget();if(!e)break;e.defaultFocusedChild=o,o=e}}(t),function(e){e=e&&e.parentNodeOrShadowHost();const o=customElements.get("x-widget");let r=null;for(;e;)o&&e instanceof o&&(r&&(e.defaultFocusedElement=r),r=e),e=e.parentNodeOrShadowHost()}(t)}var i={cssContent:":root{--icon-action:var(--color-primary-bright);--icon-arrow-main-thread:var(--color-primary-bright);--icon-checkmark-green:var(--color-tertiary-bright);--icon-checkmark-purple:var(--color-purple-bright);--icon-checkmark-red:var(--color-error-bright);--icon-contrast-issue:var(--color-error-bright);--icon-default:var(--color-on-surface-variant);--icon-default-hover:var(--color-on-surface);--icon-disabled:color-mix(in sRGB,var(--color-on-surface-variant) 55%,transparent);--icon-error:var(--color-error-bright);--icon-file-authored:var(--color-orange-bright);--icon-file-default:var(--color-on-surface-variant);--icon-file-document:var(--color-primary-bright);--icon-file-font:var(--color-turqoise-bright);--icon-file-image:var(--color-tertiary-bright);--icon-file-script:var(--color-orange-bright);--icon-file-styles:var(--color-purple-bright);--icon-fold-marker:var(--color-on-surface-variant);--icon-folder-authored:var(--color-orange);--icon-folder-primary:var(--color-on-surface-variant);--icon-folder-deployed:var(--color-primary-bright);--icon-folder-workspace:var(--color-orange);--icon-force-white:var(--color-on-primary);--icon-gap-default:var(--color-background);--icon-gap-force-white:var(--legacy-selection-bg-color);--icon-gap-inactive:var(--legacy-selection-inactive-bg-color);--icon-gap-toolbar:var(--color-background-elevation-1);--icon-gap-toolbar-hover:var(--color-background-elevation-2);--icon-info:var(--color-primary-bright);--icon-link:var(--color-primary-bright);--icon-no-request:var(--color-orange-bright);--icon-primary:var(--color-primary-bright);--icon-record:var(--color-error-bright);--icon-request-response:var(--color-primary-bright);--icon-request:var(--color-on-surface-variant);--icon-security-lock:var(--color-tertiary-bright);--icon-css:var(--color-purple-bright);--icon-css-hover:var(--color-purple);--icon-status-code-ok:var(--color-tertiary-bright);--icon-stop:var(--color-error-bright);--icon-toggled:var(--color-primary-bright);--icon-warning:var(--color-orange-bright)}"},t={cssContent:'*{box-sizing:border-box;min-width:0;min-height:0}:root{height:100%;overflow:hidden;--legacy-accent-color:#1a73e8;--legacy-accent-fg-color:#1a73e8;--legacy-accent-color-hover:#3b86e8;--legacy-accent-fg-color-hover:#1567d3;--legacy-active-control-bg-color:#5a5a5a;--legacy-focus-bg-color:hsl(214deg 40% 92%);--legacy-focus-ring-inactive-shadow-color:#e0e0e0;--legacy-input-validation-error:#db1600;--legacy-toolbar-hover-bg-color:#eaeaea;--legacy-selection-fg-color:#fff;--legacy-selection-bg-color:var(--legacy-accent-color);--legacy-selection-inactive-fg-color:#5a5a5a;--legacy-selection-inactive-bg-color:#dadada;--legacy-tab-selected-fg-color:#333;--legacy-divider-border:1px solid var(--color-details-hairline);--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-focus-ring-active-shadow:0 0 0 1px var(--legacy-accent-color);--legacy-item-selection-bg-color:#cfe8fc;--legacy-item-selection-inactive-bg-color:#e0e0e0;--item-hover-color:rgb(56 121 217/10%);--monospace-font-size:10px;--monospace-font-family:monospace;--source-code-font-size:11px;--source-code-font-family:monospace;--override-force-white-icons-background:#fafafa}.-theme-with-dark-background{color-scheme:dark;--legacy-accent-color:#0e639c;--legacy-accent-fg-color:#ccc;--legacy-accent-fg-color-hover:#fff;--legacy-accent-color-hover:rgb(17 119 187);--legacy-active-control-bg-color:#cdcdcd;--legacy-focus-bg-color:hsl(214deg 19% 27%);--legacy-focus-ring-inactive-shadow-color:#5a5a5a;--legacy-toolbar-hover-bg-color:#202020;--legacy-selection-fg-color:#cdcdcd;--legacy-selection-inactive-fg-color:#cdcdcd;--legacy-selection-inactive-bg-color:hsl(0deg 0% 28%);--legacy-tab-selected-fg-color:#eaeaea;--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-item-selection-bg-color:hsl(207deg 88% 22%);--legacy-item-selection-inactive-bg-color:#454545}body{height:100%;width:100%;position:relative;overflow:hidden;margin:0;cursor:default;font-family:".SFNSDisplay-Regular","Helvetica Neue","Lucida Grande",sans-serif;font-size:12px;tab-size:4;user-select:none;color:var(--color-text-primary);background:var(--color-background)}.platform-linux{font-family:Roboto,Ubuntu,Arial,sans-serif}.platform-mac{font-family:".SFNSDisplay-Regular","Helvetica Neue","Lucida Grande",sans-serif}.platform-mac,\n.platform-linux{--override-text-color:rgb(48 57 66);color:var(--override-text-color)}.platform-windows{font-family:"Segoe UI",Tahoma,sans-serif}:focus{outline-width:0}.platform-mac,\n:host-context(.platform-mac){--monospace-font-size:11px;--monospace-font-family:menlo,monospace;--source-code-font-size:11px;--source-code-font-family:menlo,monospace}.platform-windows,\n:host-context(.platform-windows){--monospace-font-size:12px;--monospace-font-family:consolas,lucida console,courier new,monospace;--source-code-font-size:12px;--source-code-font-family:consolas,lucida console,courier new,monospace}.platform-linux,\n:host-context(.platform-linux){--monospace-font-size:11px;--monospace-font-family:dejavu sans mono,monospace;--source-code-font-size:11px;--source-code-font-family:dejavu sans mono,monospace}.-theme-with-dark-background .platform-linux,\n.-theme-with-dark-background .platform-mac,\n:host-context(.-theme-with-dark-background) .platform-linux,\n:host-context(.-theme-with-dark-background) .platform-mac{--override-text-color:rgb(189 198 207)}.monospace{font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)!important}.source-code{font-family:var(--source-code-font-family);font-size:var(--source-code-font-size)!important;white-space:pre-wrap}img{-webkit-user-drag:none}iframe,\na img{border:none}.fill{position:absolute;top:0;left:0;right:0;bottom:0}iframe.fill{width:100%;height:100%}.widget{position:relative;flex:auto;contain:style}.hbox{display:flex;flex-direction:row!important;position:relative}.vbox{display:flex;flex-direction:column!important;position:relative}.view-container > .toolbar{border-bottom:1px solid var(--color-details-hairline)}.flex-auto{flex:auto}.flex-none{flex:none}.flex-centered{display:flex;align-items:center;justify-content:center}.overflow-auto{overflow:auto;background-color:var(--color-background)}iframe.widget{position:absolute;width:100%;height:100%;left:0;right:0;top:0;bottom:0}.hidden{display:none!important}.highlighted-search-result{--override-highlighted-search-result-background-color:rgb(255 255 0/80%);border-radius:1px;background-color:var(--override-highlighted-search-result-background-color);outline:1px solid var(--override-highlighted-search-result-background-color)}.-theme-with-dark-background .highlighted-search-result,\n:host-context(.-theme-with-dark-background) .highlighted-search-result{--override-highlighted-search-result-background-color:hsl(133deg 100% 30%);color:#333}.link{cursor:pointer;text-decoration:underline;color:var(--color-link);outline-offset:2px}button,\ninput,\nselect{font-family:inherit;font-size:inherit}select option,\nselect optgroup,\ninput{background-color:var(--color-background)}input{color:inherit}input::placeholder{--override-input-placeholder-color:rgb(0 0 0/54%);color:var(--override-input-placeholder-color)}.-theme-with-dark-background input::placeholder,\n:host-context(.-theme-with-dark-background) input::placeholder{--override-input-placeholder-color:rgb(230 230 230/54%)}:host-context(.-theme-with-dark-background) input[type="checkbox"]:not(.-theme-preserve){accent-color:var(--color-checkbox-accent-color)}.harmony-input:not([type]),\n.harmony-input[type="number"],\n.harmony-input[type="text"]{padding:3px 6px;height:24px;border:none;box-shadow:var(--legacy-focus-ring-inactive-shadow)}.harmony-input:not([type]).error-input,\n.harmony-input[type="number"].error-input,\n.harmony-input[type="text"].error-input,\n.harmony-input:not([type]):invalid,\n.harmony-input[type="number"]:invalid,\n.harmony-input[type="text"]:invalid{box-shadow:0 0 0 1px var(--color-red)}.harmony-input:not([type]):not(.error-input):not(:invalid):hover,\n.harmony-input[type="number"]:not(.error-input):not(:invalid):hover,\n.harmony-input[type="text"]:not(.error-input):not(:invalid):hover{box-shadow:var(--legacy-focus-ring-inactive-shadow)}.harmony-input:not([type]):not(.error-input):not(:invalid):focus,\n.harmony-input[type="number"]:not(.error-input):not(:invalid):focus,\n.harmony-input[type="text"]:not(.error-input):not(:invalid):focus{box-shadow:var(--legacy-focus-ring-active-shadow)}.highlighted-search-result.current-search-result{--override-current-search-result-background-color:rgb(255 127 0/80%);border-radius:1px;padding:1px;margin:-1px;background-color:var(--override-current-search-result-background-color)}.dimmed{opacity:60%}.editing{box-shadow:var(--drop-shadow);background-color:var(--color-background);text-overflow:clip!important;padding-left:2px;margin-left:-2px;padding-right:2px;margin-right:-2px;margin-bottom:-1px;padding-bottom:1px;opacity:100%!important}.editing,\n.editing *{color:var(--color-text-primary)!important;text-decoration:none!important}.chrome-select{--override-chrome-select-border-color:rgb(0 0 0/20%);appearance:none;user-select:none;border:1px solid var(--override-chrome-select-border-color);border-radius:2px;color:var(--color-text-primary);font:inherit;margin:0;outline:none;padding-right:20px;padding-left:6px;background-image:var(--image-file-arrow-drop-down-light);background-color:var(--color-background-elevation-0);background-position:right center;background-repeat:no-repeat;min-height:24px;min-width:80px}.chrome-select:disabled{opacity:38%}.-theme-with-dark-background .chrome-select,\n:host-context(.-theme-with-dark-background) .chrome-select{--override-chrome-select-border-color:rgb(230 230 230/20%);background-image:var(--image-file-arrow-drop-down-dark)}.chrome-select:enabled:active,\n.chrome-select:enabled:focus,\n.chrome-select:enabled:hover{--override-select-box-shadow:0 0 0 2px var(--color-button-outline-focus);background-color:var(--color-background-elevation-1);box-shadow:var(--override-select-box-shadow)}.chrome-select:enabled:active{background-color:var(--color-background-elevation-2)}.chrome-select:enabled:focus{border-color:transparent}@media (forced-colors: active) and (prefers-color-scheme: light){.chrome-select{background-image:var(--image-file-arrow-drop-down-light)}.-theme-with-dark-background .chrome-select,\n  :host-context(.-theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-light)}}@media (forced-colors: active) and (prefers-color-scheme: dark){.chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}.-theme-with-dark-background .chrome-select,\n  :host-context(.-theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}}.-theme-with-dark-background .chrome-select:enabled:active,\n.-theme-with-dark-background .chrome-select:enabled:focus,\n.-theme-with-dark-background .chrome-select:enabled:hover,\n:host-context(.-theme-with-dark-background) .chrome-select:enabled:active,\n:host-context(.-theme-with-dark-background) .chrome-select:enabled:focus,\n:host-context(.-theme-with-dark-background) .chrome-select:enabled:hover{--override-select-box-shadow:0 0 0 2px var(--color-button-outline-focus)}.chrome-select-label{margin:0 22px;flex:none}.chrome-select-label p p{margin-top:0;color:var(--color-text-secondary)}.settings-select{margin:0}.chrome-select optgroup,\n.chrome-select option{background-color:var(--color-background-elevation-1);color:var(--color-text-primary)}.gray-info-message{text-align:center;font-style:italic;padding:6px;color:var(--color-text-secondary);white-space:nowrap}span[is="dt-icon-label"]{flex:none}.full-widget-dimmed-banner a{color:inherit}.full-widget-dimmed-banner{color:var(--color-text-secondary);background-color:var(--color-background);display:flex;justify-content:center;align-items:center;text-align:center;padding:20px;position:absolute;top:0;right:0;bottom:0;left:0;font-size:13px;overflow:auto;z-index:500}[is="ui-icon"]{display:inline-block;flex-shrink:0}.-theme-with-dark-background [is="ui-icon"].icon-invert,\n:host-context(.-theme-with-dark-background) [is="ui-icon"].icon-invert{filter:invert(80%) hue-rotate(180deg)}[is="ui-icon"].icon-mask{background-color:var(--icon-default);-webkit-mask-position:var(--spritesheet-position)}.-theme-with-dark-background [is="ui-icon"].icon-mask,\n:host-context(.-theme-with-dark-background) [is="ui-icon"].icon-mask{--override-icon-mask-background-color:rgb(145 145 145)}[is="ui-icon"]:not(.icon-mask){background-position:var(--spritesheet-position)}.spritesheet-smallicons:not(.icon-mask){background-image:var(--image-file-smallIcons)}.spritesheet-smallicons.icon-mask{-webkit-mask-image:var(--image-file-smallIcons)}.spritesheet-largeicons:not(.icon-mask){background-image:var(--image-file-largeIcons)}.spritesheet-largeicons.icon-mask{-webkit-mask-image:var(--image-file-largeIcons)}.spritesheet-mediumicons:not(.icon-mask){background-image:var(--image-file-mediumIcons)}.spritesheet-mediumicons.icon-mask{-webkit-mask-image:var(--image-file-mediumIcons)}.spritesheet-arrowicons{background-image:var(--image-file-popoverArrows)}.spritesheet-3d-center.icon-mask{-webkit-mask-image:var(--image-file-3d-center)}.spritesheet-3d-pan.icon-mask{-webkit-mask-image:var(--image-file-3d-pan)}.spritesheet-3d-rotate.icon-mask{-webkit-mask-image:var(--image-file-3d-rotate)}.spritesheet-arrow-up-down-circle.icon-mask{-webkit-mask-image:var(--image-file-arrow-up-down-circle)}.spritesheet-arrow-up-down.icon-mask{-webkit-mask-image:var(--image-file-arrow-up-down)}.spritesheet-bell.icon-mask{-webkit-mask-image:var(--image-file-bell)}.spritesheet-bezier-curve-filled.icon-mask{-webkit-mask-image:var(--image-file-bezier-curve-filled)}.spritesheet-bin.icon-mask{-webkit-mask-image:var(--image-file-bin)}.spritesheet-bottom-panel-close.icon-mask{-webkit-mask-image:var(--image-file-bottom-panel-close)}.spritesheet-bottom-panel-open.icon-mask{-webkit-mask-image:var(--image-file-bottom-panel-open)}.spritesheet-brackets.icon-mask{-webkit-mask-image:var(--image-file-brackets)}.spritesheet-breakpoint-crossed-filled.icon-mask{-webkit-mask-image:var(--image-file-breakpoint-crossed-filled)}.spritesheet-breakpoint-crossed.icon-mask{-webkit-mask-image:var(--image-file-breakpoint-crossed)}.spritesheet-brush-filled.icon-mask{-webkit-mask-image:var(--image-file-brush-filled)}.spritesheet-brush.icon-mask{-webkit-mask-image:var(--image-file-brush)}.spritesheet-bug.icon-mask{-webkit-mask-image:var(--image-file-bug)}.spritesheet-check-double.icon-mask{-webkit-mask-image:var(--image-file-check-double)}.spritesheet-checkmark.icon-mask{-webkit-mask-image:var(--image-file-checkmark)}.spritesheet-chevron-double-right.icon-mask{-webkit-mask-image:var(--image-file-chevron-double-right)}.spritesheet-chevron-down.icon-mask{-webkit-mask-image:var(--image-file-chevron-down)}.spritesheet-chevron-up.icon-mask{-webkit-mask-image:var(--image-file-chevron-up)}.spritesheet-clear-list.icon-mask{-webkit-mask-image:var(--image-file-clear-list)}.spritesheet-clear.icon-mask{-webkit-mask-image:var(--image-file-clear)}.spritesheet-cloud.icon-mask{-webkit-mask-image:var(--image-file-cloud)}.spritesheet-code-circle.icon-mask{-webkit-mask-image:var(--image-file-code-circle)}.spritesheet-color-picker-filled.icon-mask{-webkit-mask-image:var(--image-file-color-picker-filled)}.spritesheet-color-picker.icon-mask{-webkit-mask-image:var(--image-file-color-picker)}.spritesheet-cookie.icon-mask{-webkit-mask-image:var(--image-file-cookie)}.spritesheet-copy.icon-mask{-webkit-mask-image:var(--image-file-copy)}.spritesheet-credit-card.icon-mask{-webkit-mask-image:var(--image-file-credit-card)}.spritesheet-cross-circle.icon-mask{-webkit-mask-image:var(--image-file-cross-circle)}.spritesheet-cross.icon-mask{-webkit-mask-image:var(--image-file-cross)}.spritesheet-custom-typography.icon-mask{-webkit-mask-image:var(--image-file-custom-typography)}.spritesheet-database.icon-mask{-webkit-mask-image:var(--image-file-database)}.spritesheet-device-fold.icon-mask{-webkit-mask-image:var(--image-file-device-fold)}.spritesheet-devices.icon-mask{-webkit-mask-image:var(--image-file-devices)}.spritesheet-dock-bottom.icon-mask{-webkit-mask-image:var(--image-file-dock-bottom)}.spritesheet-dock-left.icon-mask{-webkit-mask-image:var(--image-file-dock-left)}.spritesheet-dock-right.icon-mask{-webkit-mask-image:var(--image-file-dock-right)}.spritesheet-dock-window.icon-mask{-webkit-mask-image:var(--image-file-dock-window)}.spritesheet-document.icon-mask{-webkit-mask-image:var(--image-file-document)}.spritesheet-dots-vertical.icon-mask{-webkit-mask-image:var(--image-file-dots-vertical)}.spritesheet-download.icon-mask{-webkit-mask-image:var(--image-file-download)}.spritesheet-edit.icon-mask{-webkit-mask-image:var(--image-file-edit)}.spritesheet-experiment-check.icon-mask{-webkit-mask-image:var(--image-file-experiment-check)}.spritesheet-eye.icon-mask{-webkit-mask-image:var(--image-file-eye)}.spritesheet-filter-clear.icon-mask{-webkit-mask-image:var(--image-file-filter-clear)}.spritesheet-filter-filled.icon-mask{-webkit-mask-image:var(--image-file-filter-filled)}.spritesheet-filter.icon-mask{-webkit-mask-image:var(--image-file-filter)}.spritesheet-frame-crossed.icon-mask{-webkit-mask-image:var(--image-file-frame-crossed)}.spritesheet-frame.icon-mask{-webkit-mask-image:var(--image-file-frame)}.spritesheet-gear-filled.icon-mask{-webkit-mask-image:var(--image-file-gear-filled)}.spritesheet-gear.icon-mask{-webkit-mask-image:var(--image-file-gear)}.spritesheet-gears.icon-mask{-webkit-mask-image:var(--image-file-gears)}.spritesheet-heap-snapshot.icon-mask{-webkit-mask-image:var(--image-file-heap-snapshot)}.spritesheet-heap-snapshots.icon-mask{-webkit-mask-image:var(--image-file-heap-snapshots)}.spritesheet-iframe-crossed.icon-mask{-webkit-mask-image:var(--image-file-iframe-crossed)}.spritesheet-iframe.icon-mask{-webkit-mask-image:var(--image-file-iframe)}.spritesheet-import.icon-mask{-webkit-mask-image:var(--image-file-import)}.spritesheet-info.icon-mask{-webkit-mask-image:var(--image-file-info)}.spritesheet-keyboard-pen.icon-mask{-webkit-mask-image:var(--image-file-keyboard-pen)}.spritesheet-layers-filled.icon-mask{-webkit-mask-image:var(--image-file-layers-filled)}.spritesheet-layers.icon-mask{-webkit-mask-image:var(--image-file-layers)}.spritesheet-left-panel-close.icon-mask{-webkit-mask-image:var(--image-file-left-panel-close)}.spritesheet-left-panel-open.icon-mask{-webkit-mask-image:var(--image-file-left-panel-open)}.spritesheet-list.icon-mask{-webkit-mask-image:var(--image-file-list)}.spritesheet-pause.icon-mask{-webkit-mask-image:var(--image-file-pause)}.spritesheet-play.icon-mask{-webkit-mask-image:var(--image-file-play)}.spritesheet-plus.icon-mask{-webkit-mask-image:var(--image-file-plus)}.spritesheet-popup.icon-mask{-webkit-mask-image:var(--image-file-popup)}.spritesheet-profile.icon-mask{-webkit-mask-image:var(--image-file-profile)}.spritesheet-record-start.icon-mask{-webkit-mask-image:var(--image-file-record-start)}.spritesheet-record-stop.icon-mask{-webkit-mask-image:var(--image-file-record-stop)}.spritesheet-refresh.icon-mask{-webkit-mask-image:var(--image-file-refresh)}.spritesheet-replace.icon-mask{-webkit-mask-image:var(--image-file-replace)}.spritesheet-resume.icon-mask{-webkit-mask-image:var(--image-file-resume)}.spritesheet-right-panel-close.icon-mask{-webkit-mask-image:var(--image-file-right-panel-close)}.spritesheet-right-panel-open.icon-mask{-webkit-mask-image:var(--image-file-right-panel-open)}.spritesheet-screen-rotation.icon-mask{-webkit-mask-image:var(--image-file-screen-rotation)}.spritesheet-search.icon-mask{-webkit-mask-image:var(--image-file-search)}.spritesheet-select-element.icon-mask{-webkit-mask-image:var(--image-file-select-element)}.spritesheet-shadow.icon-mask{-webkit-mask-image:var(--image-file-shadow)}.spritesheet-step-into.icon-mask{-webkit-mask-image:var(--image-file-step-into)}.spritesheet-step-out.icon-mask{-webkit-mask-image:var(--image-file-step-out)}.spritesheet-step-over.icon-mask{-webkit-mask-image:var(--image-file-step-over)}.spritesheet-step.icon-mask{-webkit-mask-image:var(--image-file-step)}.spritesheet-stop.icon-mask{-webkit-mask-image:var(--image-file-stop)}.spritesheet-sync.icon-mask{-webkit-mask-image:var(--image-file-sync)}.spritesheet-table.icon-mask{-webkit-mask-image:var(--image-file-table)}.spritesheet-top-panel-close.icon-mask{-webkit-mask-image:var(--image-file-top-panel-close)}.spritesheet-top-panel-open.icon-mask{-webkit-mask-image:var(--image-file-top-panel-open)}.spritesheet-replay.icon-mask{-webkit-mask-image:var(--image-file-replay)}.spritesheet-triangle-bottom-right.icon-mask{-webkit-mask-image:var(--image-file-triangle-bottom-right)}.spritesheet-triangle-down.icon-mask{-webkit-mask-image:var(--image-file-triangle-down)}.spritesheet-triangle-left.icon-mask{-webkit-mask-image:var(--image-file-triangle-left)}.spritesheet-triangle-right.icon-mask{-webkit-mask-image:var(--image-file-triangle-right)}.spritesheet-triangle-up.icon-mask{-webkit-mask-image:var(--image-file-triangle-up)}.spritesheet-undo.icon-mask{-webkit-mask-image:var(--image-file-undo)}.spritesheet-warning.icon-mask{-webkit-mask-image:var(--image-file-warning)}.spritesheet-watch.icon-mask{-webkit-mask-image:var(--image-file-watch)}.force-white-icons [is="ui-icon"].spritesheet-smallicons,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-smallicons,\n[is="ui-icon"].force-white-icons.spritesheet-smallicons,\n.-theme-preserve{-webkit-mask-image:var(--image-file-smallIcons);-webkit-mask-position:var(--spritesheet-position);background:var(--override-force-white-icons-background)!important}.force-white-icons [is="ui-icon"].spritesheet-largeicons,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-largeicons,\n[is="ui-icon"].force-white-icons.spritesheet-largeicons,\n.-theme-preserve{-webkit-mask-image:var(--image-file-largeIcons);-webkit-mask-position:var(--spritesheet-position);background:var(--override-force-white-icons-background)!important}.force-white-icons [is="ui-icon"].spritesheet-mediumicons,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-mediumicons,\n[is="ui-icon"].force-white-icons.spritesheet-mediumicons,\n.-theme-preserve{-webkit-mask-image:var(--image-file-mediumIcons);-webkit-mask-position:var(--spritesheet-position);background:var(--override-force-white-icons-background)!important}.force-white-icons [is="ui-icon"].spritesheet-arrow-up-down,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-arrow-up-down,\n[is="ui-icon"].force-white-icons.spritesheet-arrow-up-down,\n.-theme-preserve{-webkit-mask-image:var(--image-file-arrow-up-down);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-bell,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-bell,\n[is="ui-icon"].force-white-icons.spritesheet-bell,\n.-theme-preserve{-webkit-mask-image:var(--image-file-bell);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-bug,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-bug,\n[is="ui-icon"].force-white-icons.spritesheet-bug,\n.-theme-preserve{-webkit-mask-image:var(--image-file-bug);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-cloud,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-cloud,\n[is="ui-icon"].force-white-icons.spritesheet-cloud,\n.-theme-preserve{-webkit-mask-image:var(--image-file-cloud);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-cookie,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-cookie,\n[is="ui-icon"].force-white-icons.spritesheet-cookie,\n.-theme-preserve{-webkit-mask-image:var(--image-file-cookie);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-credit-card,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-credit-card,\n[is="ui-icon"].force-white-icons.spritesheet-credit-card,\n.-theme-preserve{-webkit-mask-image:var(--image-file-credit-card);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-cross-circle,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-cross-circle,\n[is="ui-icon"].force-white-icons.spritesheet-cross-circle,\n.-theme-preserve{-webkit-mask-image:var(--image-file-cross-circle);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-database,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-database,\n[is="ui-icon"].force-white-icons.spritesheet-database,\n.-theme-preserve{-webkit-mask-image:var(--image-file-database);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-dock-bottom,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-dock-bottom,\n[is="ui-icon"].force-white-icons.spritesheet-dock-bottom,\n.-theme-preserve{-webkit-mask-image:var(--image-file-dock-bottom);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-dock-left,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-dock-left,\n[is="ui-icon"].force-white-icons.spritesheet-dock-left,\n.-theme-preserve{-webkit-mask-image:var(--image-file-dock-left);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-dock-right,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-dock-right,\n[is="ui-icon"].force-white-icons.spritesheet-dock-right,\n.-theme-preserve{-webkit-mask-image:var(--image-file-dock-right);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-dock-window,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-dock-window,\n[is="ui-icon"].force-white-icons.spritesheet-dock-window,\n.-theme-preserve{-webkit-mask-image:var(--image-file-dock-window);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-document,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-document,\n[is="ui-icon"].force-white-icons.spritesheet-document,\n.-theme-preserve{-webkit-mask-image:var(--image-file-document);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-frame-crossed,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-frame-crossed,\n[is="ui-icon"].force-white-icons.spritesheet-frame-crossed,\n.-theme-preserve{-webkit-mask-image:var(--image-file-frame-crossed);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-frame,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-frame,\n[is="ui-icon"].force-white-icons.spritesheet-frame,\n.-theme-preserve{-webkit-mask-image:var(--image-file-frame);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-gears,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-gears,\n[is="ui-icon"].force-white-icons.spritesheet-gears,\n.-theme-preserve{-webkit-mask-image:var(--image-file-gears);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-heap-snapshot,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-heap-snapshot,\n[is="ui-icon"].force-white-icons.spritesheet-heap-snapshot,\n.-theme-preserve{-webkit-mask-image:var(--image-file-heap-snapshot);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-heap-snapshots,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-heap-snapshots,\n[is="ui-icon"].force-white-icons.spritesheet-heap-snapshots,\n.-theme-preserve{-webkit-mask-image:var(--image-file-heap-snapshots);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-iframe-crossed,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-iframe-crossed,\n[is="ui-icon"].force-white-icons.spritesheet-iframe-crossed,\n.-theme-preserve{-webkit-mask-image:var(--image-file-iframe-crossed);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-iframe,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-iframe,\n[is="ui-icon"].force-white-icons.spritesheet-iframe,\n.-theme-preserve{-webkit-mask-image:var(--image-file-iframe);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-info,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-info,\n[is="ui-icon"].force-white-icons.spritesheet-info,\n.-theme-preserve{-webkit-mask-image:var(--image-file-info);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-list,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-list,\n[is="ui-icon"].force-white-icons.spritesheet-list,\n.-theme-preserve{-webkit-mask-image:var(--image-file-list);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-popup,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-popup,\n[is="ui-icon"].force-white-icons.spritesheet-popup,\n.-theme-preserve{-webkit-mask-image:var(--image-file-popup);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-profile,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-profile,\n[is="ui-icon"].force-white-icons.spritesheet-profile,\n.-theme-preserve{-webkit-mask-image:var(--image-file-profile);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-sync,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-sync,\n[is="ui-icon"].force-white-icons.spritesheet-sync,\n.-theme-preserve{-webkit-mask-image:var(--image-file-sync);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-table,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-table,\n[is="ui-icon"].force-white-icons.spritesheet-table,\n.-theme-preserve{-webkit-mask-image:var(--image-file-table);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-triangle-right,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-triangle-right,\n[is="ui-icon"].force-white-icons.spritesheet-triangle-right,\n.-theme-preserve{-webkit-mask-image:var(--image-file-triangle-right);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-warning,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-warning,\n[is="ui-icon"].force-white-icons.spritesheet-warning,\n.-theme-preserve{-webkit-mask-image:var(--image-file-warning);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.force-white-icons [is="ui-icon"].spritesheet-watch,\n:host-context(.force-white-icons) [is="ui-icon"].spritesheet-watch,\n[is="ui-icon"].force-white-icons.spritesheet-watch,\n.-theme-preserve{-webkit-mask-image:var(--image-file-watch);-webkit-mask-position:var(--spritesheet-position);background:var(--icon-force-white)!important}.dot::before{content:url("Images/empty.svg");width:6px;height:6px;border-radius:50%;outline:1px solid var(--icon-gap-default);left:9px;position:absolute;top:9px;z-index:1}.green::before{background-color:var(--color-tertiary-bright)}.purple::before{background-color:var(--color-purple-bright)}.expandable-inline-button{background-color:var(--color-background-elevation-2);color:var(--color-text-primary);cursor:pointer;border-radius:3px}.undisplayable-text,\n.expandable-inline-button{padding:1px 3px;margin:0 2px;font-size:11px;font-family:sans-serif;white-space:nowrap;display:inline-block}.undisplayable-text::after,\n.expandable-inline-button::after{content:attr(data-text)}.undisplayable-text{color:var(--color-text-disabled);font-style:italic}.expandable-inline-button:hover,\n.expandable-inline-button:focus-visible{background-color:var(--color-background-elevation-1)}.expandable-inline-button:focus-visible{background-color:var(--color-background-elevation-1)}::selection{--override-selection-background-color:rgb(141 199 248/60%);background-color:var(--override-selection-background-color)}.-theme-with-dark-background *::selection,\n:host-context(.-theme-with-dark-background) *::selection{background-color:rgb(93 93 93/60%)}.reload-warning{align-self:center;margin-left:10px}button.link{border:none;background:none;padding:3px}button.link:focus-visible{--override-link-focus-background-color:rgb(0 0 0/8%);background-color:var(--override-link-focus-background-color);border-radius:2px}.-theme-with-dark-background button.link:focus-visible,\n:host-context(.-theme-with-dark-background) button.link:focus-visible{--override-link-focus-background-color:rgb(230 230 230/8%)}@media (forced-colors: active){.dimmed,\n  .chrome-select:disabled{opacity:100%}[is="ui-icon"].icon-mask,\n  .force-white-icons [is="ui-icon"].spritesheet-smallicons,\n  :host-context(.force-white-icons) [is="ui-icon"].spritesheet-smallicons,\n  [is="ui-icon"].force-white-icons.spritesheet-smallicons,\n  .force-white-icons [is="ui-icon"].spritesheet-largeicons,\n  :host-context(.force-white-icons) [is="ui-icon"].spritesheet-largeicons,\n  [is="ui-icon"].force-white-icons.spritesheet-largeicons,\n  .force-white-icons [is="ui-icon"].spritesheet-mediumicons,\n  :host-context(.force-white-icons) [is="ui-icon"].spritesheet-mediumicons,\n  [is="ui-icon"].force-white-icons.spritesheet-mediumicons,\n  .-theme-preserve{forced-color-adjust:none;background-color:ButtonText}.harmony-input:not([type]),\n  .harmony-input[type="number"],\n  .harmony-input[type="text"]{border:1px solid ButtonText}.harmony-input:not([type]):focus,\n  .harmony-input[type="number"]:focus,\n  .harmony-input[type="text"]:focus{border:1px solid Highlight}}input.custom-search-input::-webkit-search-cancel-button{appearance:none;cursor:pointer;width:16px;height:15px;margin-right:0;background-position:-32px 32px;background-image:var(--image-file-mediumIcons)}.spinner::before{display:block;width:var(--dimension,24px);height:var(--dimension,24px);border:var(--override-spinner-size,3px) solid var(--override-spinner-color,var(--color-text-secondary));border-radius:12px;clip:rect(0,var(--clip-size,15px),var(--clip-size,15px),0);content:"";position:absolute;animation:spinner-animation 1s linear infinite;box-sizing:border-box}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}.adorner-container{display:inline-block}.adorner-container.hidden{display:none}.adorner-container devtools-adorner{margin-left:3px}:host-context(.-theme-with-dark-background) devtools-adorner{--override-adorner-background-color:rgb(var(--color-syntax-2-rgb)/15%);--override-adorner-border-color:rgb(var(--color-syntax-2-rgb)/50%);--override-adorner-focus-border-color:var(--color-syntax-2);--override-adorner-active-background-color:var(--color-syntax-8)}.panel{display:flex;overflow:hidden;position:absolute;top:0;left:0;right:0;bottom:0;z-index:0;background-color:var(--color-background)}.panel-sidebar{overflow-x:hidden;background-color:var(--color-background-elevation-1)}iframe.extension{flex:auto;width:100%;height:100%}iframe.panel.extension{display:block;height:100%}@media (forced-colors: active){:root{--legacy-accent-color:Highlight;--legacy-focus-ring-inactive-shadow-color:ButtonText}}'},a={cssContent:".text-button{margin:2px;height:24px;font-size:12px;border:1px solid var(--color-background-elevation-2);border-radius:4px;padding:0 12px;font-weight:500;color:var(--legacy-accent-fg-color);background-color:var(--color-background);flex:none;white-space:nowrap}.text-button:disabled{opacity:38%}.text-button:not(:disabled):focus,\n.text-button:not(:disabled):hover,\n.text-button:not(:disabled):active{background-color:var(--color-background-elevation-1);box-shadow:0 1px 2px var(--divider-line)}.text-button:not(:disabled):active{background-color:var(--color-background-elevation-2)}.text-button:not(:disabled):focus{box-shadow:0 1px 2px var(--divider-line),0 0 0 2px var(--color-primary-variant)}.text-button:not(:disabled):not(.running):focus,\n.text-button:not(:disabled):not(.running):hover,\n.text-button:not(:disabled):not(.running):active{color:var(--legacy-accent-fg-color-hover)}.text-button.primary-button{--override-text-button-color:#fff;background-color:var(--legacy-accent-color);border:none;color:var(--override-text-button-color)}.text-button.link-style{background:none;border:none;padding:0!important;font:inherit;cursor:pointer;height:18px}.text-button.primary-button:not(:disabled):focus,\n.text-button.primary-button:not(:disabled):hover,\n.text-button.primary-button:not(:disabled):active{--override-text-button-color:#fff;background-color:var(--legacy-accent-color-hover);color:var(--override-text-button-color)}.-theme-with-dark-background .text-button:not(.primary-button):not(:disabled):focus,\n.-theme-with-dark-background .text-button:not(.primary-button):not(:disabled):hover,\n.-theme-with-dark-background .text-button:not(.primary-button):not(:disabled):active{--override-dark-background-color:#313131;--override-dark-box-shadow-color:rgb(0 0 0/10%);background-color:var(--override-dark-background-color);box-shadow:0 1px 2px var(--override-dark-box-shadow-color)}.-theme-with-dark-background .text-button:not(.primary-button):not(:disabled):focus{box-shadow:0 1px 2px var(--override-dark-box-shadow-color),0 0 0 2px var(--color-primary-variant)}.-theme-with-dark-background .text-button:not(.primary-button):not(:disabled):active{--override-dark-mode-active-background-color:#3e3e3e;background-color:var(--override-dark-mode-active-background-color)}@media (forced-colors: active){.text-button{background-color:ButtonFace;color:ButtonText;border-color:ButtonText}.text-button:disabled{forced-color-adjust:none;opacity:100%;background:ButtonFace;border-color:GrayText;color:GrayText}.text-button:not(:disabled):focus{forced-color-adjust:none;background-color:ButtonFace;color:Highlight!important;border-color:Highlight;outline:2px solid ButtonText;box-shadow:var(--legacy-focus-ring-active-shadow)}.text-button:not(:disabled):hover,\n  .text-button:not(:disabled):active{forced-color-adjust:none;background-color:Highlight;color:HighlightText!important;box-shadow:var(--legacy-accent-color)}.text-button.primary-button{forced-color-adjust:none;background-color:Highlight;color:HighlightText;border:1px solid Highlight}.text-button.primary-button:not(:disabled):focus{background-color:Highlight;color:HighlightText!important;border-color:ButtonText}.text-button.primary-button:not(:disabled):hover,\n  .text-button.primary-button:not(:disabled):active{background-color:HighlightText;color:Highlight!important;border-color:Highlight}}"},c={cssContent:":root{--color-primary-old:rgb(26 115 232);--color-primary-variant:rgb(66 133 244);--color-background:rgb(***********);--color-background-inverted:rgb(0 0 0);--color-background-inverted-opacity-0:rgb(0 0 0/0%);--color-background-inverted-opacity-2:rgb(0 0 0/2%);--color-background-inverted-opacity-30:rgb(0 0 0/30%);--color-background-inverted-opacity-50:rgb(0 0 0/50%);--color-background-opacity-50:rgb(***********/50%);--color-background-opacity-80:rgb(***********/80%);--color-background-elevation-0:rgb(248 249 249);--color-background-elevation-1:rgb(241 243 244);--color-background-elevation-2:rgb(222 225 230);--color-background-elevation-dark-only:var(--color-background);--color-background-highlight:rgb(218 220 224);--divider-line:rgb(0 0 0/10%);--color-background-hover-overlay:rgb(56 121 217/10%);--color-selection-highlight:rgb(56 121 217/30%);--color-selection-highlight-border:rgb(16 81 177);--color-match-highlight:rgb(56 121 217/20%);--color-text-primary:rgb(32 33 36);--color-text-secondary:rgb(95 99 104);--color-text-secondary-selected:rgb(***********);--color-text-disabled:rgb(***********);--color-details-hairline:rgb(***********);--color-details-hairline-light:rgb(***********);--color-accent-red:rgb(217 48 37);--color-red:rgb(238 68 47);--color-accent-green:rgb(24 128 56);--color-accent-green-background:rgb(24 128 56/10%);--color-green:rgb(99 172 190);--color-link:var(--color-primary-old);--color-syntax-1:rgb(200 0 0);--color-syntax-2:rgb(136 18 128);--color-syntax-3:rgb(26 26 166);--color-syntax-4:rgb(153 69 0);--color-syntax-5:rgb(***********);--color-syntax-6:rgb(35 110 37);--color-syntax-7:rgb(48 57 66);--color-syntax-8:rgb(***********);--drop-shadow:0 0 0 1px rgb(0 0 0/5%),0 2px 4px rgb(0 0 0/20%),0 2px 6px rgb(0 0 0/10%);--drop-shadow-depth-1:0 1px 2px rgb(60 64 67/30%),0 1px 3px 1px rgb(60 64 67/15%);--drop-shadow-depth-2:0 1px 2px rgb(60 64 67/30%),0 2px 6px 2px rgb(60 64 67/15%);--drop-shadow-depth-3:0 4px 8px 3px rgb(60 64 67/15%),0 1px 3px rgb(60 64 67/30%);--drop-shadow-depth-4:0 6px 10px 4px rgb(60 64 67/15%),0 2px 3px rgb(60 64 67/30%);--drop-shadow-depth-5:0 8px 12px 6px rgb(60 64 67/15%),0 4px 4px rgb(60 64 67/30%);--box-shadow-outline-color:rgb(0 0 0/50%);--color-scrollbar-mac:rgb(***********/60%);--color-scrollbar-mac-hover:rgb(64 64 64/60%);--color-scrollbar-other:rgb(0 0 0/50%);--color-scrollbar-other-hover:rgb(0 0 0/50%);--lighthouse-red:rgb(255 78 67);--lighthouse-orange:rgb(255 164 0);--lighthouse-green:rgb(12 206 106);--issue-color-red:rgb(235 57 65);--issue-color-yellow:rgb(242 153 0);--issue-color-blue:rgb(26 115 232);--input-outline:rgb(***********);--color-error-text:#f00;--color-error-border:hsl(0deg 100% 92%);--color-error-background:hsl(0deg 100% 97%);--color-checkbox-accent-color:var(--color-primary-old);--color-image-preview-background:rgb(***********);--color-input-outline:rgb(218 220 224);--color-input-outline-active:rgb(26 115 232);--color-input-outline-error:rgb(217 48 37);--color-input-outline-disabled:rgba(***********/20%);--color-input-text-disabled:rgba(***********/50%);--color-button-outline-focus:rgb(26 115 232/50%);--color-button-primary-background-hovering:rgb(77 134 225/100%);--color-button-primary-background-pressed:rgb(88 132 205);--color-button-primary-text:rgb(***********);--color-button-primary-text-hover:rgb(218 220 224);--color-button-secondary-background-hovering:rgb(26 115 232/10%);--color-button-secondary-background-pressed:rgb(26 92 178/25%);--color-button-secondary-border:rgb(218 220 224);--color-iconbutton-hover:rgb(0 0 0/10%);--color-iconbutton-pressed:rgb(0 0 0/15%);--color-ic-file-document:rgb(39 116 240);--color-ic-file-image:rgb(46 184 83);--color-ic-file-font:rgb(18 192 226);--color-ic-file-script:rgb(240 179 0);--color-ic-file-stylesheet:rgb(174 82 255);--color-ic-file-webbundle:rgb(***********);--color-ic-file-default:rgb(***********);--color-token-variable:inherit;--color-token-property:inherit;--color-token-type:rgb(0 136 119);--color-token-definition:var(--color-token-attribute-value);--color-token-variable-special:rgb(0 85 170);--color-token-builtin:rgb(50 0 170);--color-token-keyword:rgb(171 13 144);--color-token-number:rgb(50 0 255);--color-token-string:rgb(170 17 17);--color-token-string-special:rgb(200 0 0);--color-token-atom:rgb(34 17 153);--color-token-tag:rgb(136 18 128);--color-token-attribute:rgb(153 69 0);--color-token-attribute-value:rgb(26 26 168);--color-token-comment:rgb(0 117 0);--color-token-meta:rgb(85 85 85);--color-token-deleted:rgb(221 68 68);--color-token-inserted:rgb(34 153 34);--color-token-pseudo-element:rgb(17 85 204);--color-secondary-cursor:#c0c0c0;--color-line-number:hsl(0deg 0% 46%);--color-matching-bracket-underline:rgb(0 0 0/50%);--color-matching-bracket-background:rgb(0 0 0/7%);--color-nonmatching-bracket-underline:rgb(255 0 0/50%);--color-nonmatching-bracket-background:rgb(255 0 0/7%);--color-editor-selection:#cfe8fc;--color-editor-selection-blurred:#e0e0e0;--color-trailing-whitespace:rgb(255 0 0/5%);--color-selected-option:#fff;--color-selected-option-background:#1a73e8;--color-selected-option-outline:rgb(***********/50%);--color-highlighted-line:rgb(255 255 0/50%);--color-completion-hover:rgb(56 121 217/10%);--color-search-match-border:rgb(***********);--color-selected-search-match:var(--color-text-primary);--color-selected-search-match-background:rgb(241 234 0);--color-coverage-used:#63acbe;--color-execution-line-background:rgb(0 59 255/10%);--color-execution-line-outline:rgb(64 115 244);--color-execution-token-background:rgb(15 72 252/35%);--color-continue-to-location:rgb(5 65 255/10%);--color-continue-to-location-hover:rgb(15 72 252/35%);--color-continue-to-location-hover-border:rgb(121 141 254);--color-continue-to-location-async:rgb(58 162 8/30%);--color-continue-to-location-async-hover:rgb(75 179 6/55%);--color-continue-to-location-async-hover-border:rgb(100 154 100);--color-evaluated-expression:rgb(255 255 11/25%);--color-evaluated-expression-border:rgb(163 41 34);--color-variable-values:#ffe3c7;--color-non-breakable-line:rgb(***********/40%);--color-on-surface:rgb(32 33 36);--color-on-surface-variant:rgb(110 110 110);--color-primary:rgb(11 87 208);--color-primary-bright:rgb(27 110 243);--color-on-primary:rgb(***********);--color-orange:rgb(169 91 18);--color-orange-bright:rgb(232 143 33);--color-tertiary:rgb(20 108 46);--color-tertiary-bright:rgb(25 134 57);--color-error:rgb(179 38 30);--color-error-bright:rgb(220 54 46);--color-purple:rgb(140 30 211);--color-purple-bright:rgb(167 67 238);--color-turqoise:rgb(0 103 127);--color-turqoise-bright:rgb(0 152 196);--color-yellow:rgb(121 89 0);--color-yellow-bright:rgb(186 130 14)}.-theme-with-dark-background{--color-primary-old:rgb(***********);--color-primary-variant:rgb(102 157 246);--color-background:rgb(32 33 36);--color-background-inverted:rgb(***********);--color-background-inverted-opacity-2:rgb(***********/2%);--color-background-inverted-opacity-30:rgb(***********/30%);--color-background-inverted-opacity-50:rgb(***********/50%);--color-background-opacity-50:rgb(32 33 36/50%);--color-background-opacity-80:rgb(32 33 36/80%);--color-background-elevation-0:rgb(32 32 35);--color-background-elevation-1:rgb(41 42 45);--color-background-elevation-2:rgb(53 54 58);--color-background-elevation-dark-only:var(--color-background-elevation-1);--color-background-highlight:rgb(69 69 69);--divider-line:rgb(***********/10%);--color-background-hover-overlay:rgb(56 121 217/10%);--color-selection-highlight:rgb(251 202 70/20%);--color-selection-highlight-border:rgb(251 202 70);--color-match-highlight:rgb(56 121 217/35%);--color-text-primary:rgb(***********);--color-text-secondary:rgb(***********);--color-text-secondary-selected:rgb(***********);--color-text-disabled:rgb(***********);--color-details-hairline:rgb(73 76 80);--color-details-hairline-light:rgb(54 57 59);--color-accent-red:rgb(***********);--color-red:rgb(237 78 76);--color-accent-green:rgb(***********);--color-accent-green-background:rgb(***********/20%);--color-link:var(--color-primary-old);--color-syntax-1:rgb(53 212 199);--color-syntax-2:rgb(93 176 215);--color-syntax-2-rgb:93 176 215;--color-syntax-3:rgb(***********);--color-syntax-4:rgb(***********);--color-syntax-5:rgb(***********);--color-syntax-6:rgb(***********);--color-syntax-7:rgb(***********);--color-syntax-8:rgb(93 176 215);--drop-shadow:0 0 0 1px rgb(***********/20%),0 2px 4px 2px rgb(0 0 0/20%),0 2px 6px 2px rgb(0 0 0/10%);--drop-shadow-depth-1:0 1px 2px rgb(0 0 0/30%),0 1px 3px 1px rgb(0 0 0/15%);--drop-shadow-depth-2:0 1px 2px rgb(0 0 0/30%),0 2px 6px 2px rgb(0 0 0/15%);--drop-shadow-depth-3:0 4px 8px 3px rgb(0 0 0/15%),0 1px 3px rgb(0 0 0/30%);--drop-shadow-depth-4:0 6px 10px 4px rgb(0 0 0/15%),0 2px 3px rgb(0 0 0/30%);--drop-shadow-depth-5:0 8px 12px 6px rgb(0 0 0/15%),0 4px 4px rgb(0 0 0/30%);--box-shadow-outline-color:rgb(0 0 0/50%);--color-scrollbar-mac:rgb(51 51 51);--color-scrollbar-mac-hover:rgb(75 76 79);--color-scrollbar-other:rgb(51 51 51);--color-scrollbar-other-hover:rgb(75 76 79);--color-error-text:hsl(0deg 100% 75%);--color-error-border:rgb(92 0 0);--color-error-background:hsl(0deg 100% 8%);--color-checkbox-accent-color:rgb(255 165 0);--color-input-outline:rgb(60 64 67);--color-input-outline-active:rgb(***********);--color-input-outline-error:rgb(***********);--color-input-outline-disabled:rgba(189 193 198/20%);--color-input-text-disabled:rgba(***********/70%);--color-button-outline-focus:rgb(***********/75%);--color-button-primary-background-hovering:rgb(174 203 250/100%);--color-button-primary-background-pressed:rgb(210 227 252/100%);--color-button-primary-text:rgb(0 0 0);--color-button-primary-text-hover:rgb(60 61 65);--color-button-secondary-background-hovering:rgb(***********/15%);--color-button-secondary-background-pressed:rgb(***********/23%);--color-button-secondary-border:rgb(60 61 65);--color-iconbutton-hover:rgb(***********/12%);--color-iconbutton-pressed:rgb(***********/20%);--color-ic-file-document:rgb(39 116 240);--color-ic-file-image:rgb(30 142 62);--color-ic-file-font:rgb(18 181 203);--color-ic-file-script:rgb(234 134 0);--color-ic-file-stylesheet:rgb(161 66 244);--color-ic-file-webbundle:rgb(***********);--color-ic-file-default:rgb(***********);--color-token-variable:rgb(***********);--color-token-property:rgb(210 192 87);--color-token-type:var(--color-token-tag);--color-token-definition:var(--color-token-tag);--color-token-builtin:rgb(***********);--color-token-variable-special:rgb(0 85 170);--color-token-keyword:rgb(***********);--color-token-string:rgb(242 139 84);--color-token-string-special:var(--color-token-string);--color-token-atom:rgb(***********);--color-token-number:var(--color-token-atom);--color-token-comment:var(--color-syntax-6);--color-token-tag:rgb(93 176 215);--color-token-attribute:rgb(***********);--color-token-attribute-value:rgb(***********);--color-token-meta:rgb(221 251 85);--color-token-pseudo-element:rgb(***********);--color-secondary-cursor:rgb(63 63 63);--color-line-number:rgb(***********);--color-matching-bracket-underline:rgb(***********);--color-matching-bracket-background:initial;--color-nonmatching-bracket-underline:rgb(255 26 26);--color-nonmatching-bracket-background:initial;--color-editor-selection:hsl(207deg 88% 22%);--color-editor-selection-blurred:#454545;--color-trailing-whitespace:rgb(255 0 0/5%);--color-selected-option:#fff;--color-selected-option-background:#0e639c;--color-selected-option-outline:rgb(0 0 0/50%);--color-highlighted-line:hsl(133deg 100% 30%/50%);--color-completion-hover:rgb(56 121 217/10%);--color-search-match-border:rgb(***********);--color-selected-search-match:#eee;--color-selected-search-match-background:hsl(133deg 100% 30%);--color-coverage-used:rgb(65 138 156);--color-execution-line-background:rgb(0 154 17/30%);--color-execution-line-outline:#33cc6b;--color-execution-token-background:rgb(132 237 78/20%);--color-continue-to-location:rgb(0 173 56/35%);--color-continue-to-location-hover:rgb(0 173 56/35%);--color-continue-to-location-hover-border:#33cc6b;--color-continue-to-location-async:rgb(87 238 0/20%);--color-continue-to-location-async-hover:rgb(128 241 48/50%);--color-continue-to-location-async-hover-border:rgb(101 155 101);--color-evaluated-expression:rgb(71 70 0/75%);--color-evaluated-expression-border:rgb(221 99 92);--color-variable-values:rgb(56 28 0);--color-non-breakable-line:rgb(127 127 127/40%);--color-on-surface:rgb(***********);--color-on-surface-variant:rgb(145 145 145);--color-primary:rgb(168 199 250);--color-primary-bright:rgb(124 172 248);--color-on-primary:rgb(***********);--color-orange:rgb(255 184 113);--color-orange-bright:rgb(238 152 54);--color-tertiary:rgb(109 213 140);--color-tertiary-bright:rgb(55 190 95);--color-error:rgb(242 184 181);--color-error-bright:rgb(228 105 98);--color-purple:rgb(226 182 255);--color-purple-bright:rgb(209 144 255);--color-turqoise:rgb(92 213 251);--color-turqoise-bright:rgb(56 185 222);--color-yellow:rgb(251 188 5);--color-yellow-bright:rgb(217 162 0)}"};function n(o){e.ThemeSupport.instance().appendStyle(o,i),e.ThemeSupport.instance().appendStyle(o,t),e.ThemeSupport.instance().appendStyle(o,a),e.ThemeSupport.instance().appendStyle(o,c),e.ThemeSupport.instance().injectHighlightStyleSheets(o),e.ThemeSupport.instance().injectCustomStyleSheets(o)}function s(o,i={delegatesFocus:void 0,cssFile:void 0}){const{cssFile:t,delegatesFocus:a}=i,c=o.attachShadow({mode:"open",delegatesFocus:a});return n(c),t&&("cssContent"in t?e.ThemeSupport.instance().appendStyle(c,t):c.adoptedStyleSheets=t),c.addEventListener("focus",r,!0),c}let l;function g(){l=void 0}function p(e){if("number"==typeof l)return l;if(!e)return 16;const o=e.createElement("div"),r=e.createElement("div");return o.setAttribute("style","display: block; width: 100px; height: 100px; overflow: scroll;"),r.setAttribute("style","height: 200px"),o.appendChild(r),e.body.appendChild(o),l=o.offsetWidth-o.clientWidth,e.body.removeChild(o),l}function b(e,o,r){return self.customElements.define(o,class extends r{constructor(){super(),this.setAttribute("is",o)}},{extends:e}),()=>document.createElement(e,{is:o})}export{s as createShadowRootWithCoreStyles,r as focusChanged,n as injectCoreStyles,p as measuredScrollbarWidth,b as registerCustomElement,g as resetMeasuredScrollbarWidthForTest};
