import*as e from"../../core/platform/platform.js";import"../../core/common/common.js";import*as n from"../../core/protocol_client/protocol_client.js";import*as t from"../../core/root/root.js";import*as r from"../../models/bindings/bindings.js";import*as o from"../../models/workspace/workspace.js";import*as s from"../../ui/components/code_highlighter/code_highlighter.js";import*as i from"../../ui/legacy/legacy.js";function a(){return!self.testRunner||Boolean(t.Runtime.Runtime.queryParam("debugFrontend"))}self.Platform=self.Platform||{},self.Platform.StringUtilities=e.StringUtilities,self.Platform.MapUtilities=e.MapUtilities,self.Platform.ArrayUtilities=e.ArrayUtilities,self.Platform.DOMUtilities=e.DOMUtilities,self.createPlainTextSearchRegex=e.StringUtilities.createPlainTextSearchRegex,String.sprintf=e.StringUtilities.sprintf,String.regexSpecialCharacters=e.StringUtilities.regexSpecialCharacters,String.caseInsensetiveComparator=e.StringUtilities.caseInsensetiveComparator,self.onerror=(e,n,t,r,o)=>{l("TEST ENDED IN ERROR: "+o.stack),p()},self.addEventListener("unhandledrejection",(e=>{l(`PROMISE FAILURE: ${e.reason.stack}`),p()})),a()||(console.log=(...e)=>{l(`log: ${e}`)},console.error=(...e)=>{l(`error: ${e}`)},console.info=(...e)=>{l(`info: ${e}`)},console.assert=(e,...n)=>{e||l(`ASSERTION FAILURE: ${n.join(" ")}`)});let u=[],c=e=>{u.push(String(e))};function l(e){c(e)}let d=!1,f=()=>{d||(d=!0,function(){Array.prototype.forEach.call(document.documentElement.childNodes,(e=>e.remove()));const e=document.createElement("div");e.style&&(e.style.whiteSpace="pre",e.style.height="10px",e.style.overflow="hidden");document.documentElement.appendChild(e);for(let n=0;n<u.length;n++)e.appendChild(document.createTextNode(u[n])),e.appendChild(document.createElement("br"));u=[]}(),self.testRunner.notifyDone())};function p(){f()}self.TestRunner=self.TestRunner||{};const m=new Map([["panels/animation","animation"],["panels/browser_debugger","browser_debugger"],["panels/changes","changes"],["panels/console","console"],["panels/elements","elements"],["panels/emulation","emulation"],["panels/mobile_throttling","mobile_throttling"],["panels/network","network"],["panels/profiler","profiler"],["panels/application","resources"],["panels/search","search"],["panels/sources","sources"],["panels/snippets","snippets"],["panels/settings","settings"],["panels/timeline","timeline"],["panels/web_audio","web_audio"],["models/persistence","persistence"],["models/workspace_diff","workspace_diff"],["entrypoints/main","main"],["third_party/diff","diff"],["ui/legacy/components/inline_editor","inline_editor"],["ui/legacy/components/data_grid","data_grid"],["ui/legacy/components/perf_ui","perf_ui"],["ui/legacy/components/source_frame","source_frame"],["ui/legacy/components/color_picker","color_picker"],["ui/legacy/components/cookie_table","cookie_table"],["ui/legacy/components/quick_open","quick_open"],["ui/legacy/components/utils","components"]]);function g(e,n){return function(){if(!e)return;const t=this;try{return e.apply(t,arguments)}catch(t){l("Exception while running: "+e+"\n"+(t.stack||t)),n?g(n)():p()}}}function T(e){return async function(){if(!e)return;const n=this;try{return await e.apply(n,arguments)}catch(n){l("Exception while running: "+e+"\n"+(n.stack||n)),p()}}}function R(e){function n(n){let t=0;for(;n&&n!==e;)"OL"!==n.nodeName||n.classList&&n.classList.contains("object-properties-section")||++t,n=n.parentNode;return Array(4*t+1).join(" ")}let t="",r=e,o=!1;for(;r.traverseNextNode(e);)if(r=r.traverseNextNode(e),r.nodeType===Node.TEXT_NODE&&r.parentNode?.nodeType!==Node.DOCUMENT_FRAGMENT_NODE)t+=r.nodeValue;else if("LI"===r.nodeName||"TR"===r.nodeName)o?o=!1:t+="\n"+n(r);else{if("STYLE"===r.nodeName){r=r.traverseNextNode(e);continue}r.classList&&r.classList.contains("object-properties-section")&&(o=!0)}return t}async function h(e,n){const t=await v(e);g(n)(t.result.value,t.exceptionDetails)}let y=0;async function v(e){const r=(new Error).stack.split("at "),o=t.Runtime.Runtime.queryParam("test"),s=r.reduce(((e,n)=>n.includes(o)?n:e),r[r.length-2]).trim().split("/"),i=s[s.length-1].slice(0,-1).split(":"),a=i[0],u=`test://evaluations/${y++}/`+a,c=parseInt(i[1],10);-1===(e="\n".repeat(c-1)+e).indexOf("sourceURL=")&&(e+=`//# sourceURL=${u}`);const d=await TestRunner.RuntimeAgent.invoke_evaluate({expression:e,objectGroup:"console"}),f=d[n.InspectorBackend.ProtocolError];return f?(l("Error: "+f),void p()):d}async function w(e,t){const r=await TestRunner.RuntimeAgent.invoke_evaluate({expression:e,objectGroup:"console",userGesture:t});if(!r[n.InspectorBackend.ProtocolError])return r.result.value;l("Error: "+(r.exceptionDetails&&r.exceptionDetails.text||"exception from evaluateInPageAnonymously.")),p()}async function S(e){const t=await TestRunner.RuntimeAgent.invoke_evaluate({expression:e,objectGroup:"console",includeCommandLineAPI:!1,awaitPromise:!0}),r=t[n.InspectorBackend.ProtocolError];if(!r&&!t.exceptionDetails)return t.result.value;let o="Error: ";r?o+=r:t.exceptionDetails&&(o+=t.exceptionDetails.text,t.exceptionDetails.exception&&(o+=" "+t.exceptionDetails.exception.description)),l(o),p()}function E(e){if(!e.includes("<base")){const n=/(<!DOCTYPE.*?>)/i,t=`<base href="${j()}">`;e=e.match(n)?e.replace(n,"$1"+t):t+e}return w(`document.write(\`${e=e.replace(/'/g,"\\'").replace(/\n/g,"\\n")}\`);document.close();`)}const x={formatAsTypeName:e=>"<"+typeof e+">",formatAsTypeNameOrNull:e=>null===e?"null":x.formatAsTypeName(e),formatAsRecentTime(e){if("object"!=typeof e||!(e instanceof Date))return x.formatAsTypeName(e);const n=Date.now()-e;return 0<=n&&n<18e5?"<plausible>":e},formatAsURL(e){if(!e)return e;const n=e.lastIndexOf("devtools/");return n<0?e:".../"+e.substr(n)},formatAsDescription:e=>e?'"'+e.replace(/^function [gs]et /,"function ")+'"':e};function P(e,n,t,r){t=t||"",l((r=r||t)+"{");const o=Object.keys(e);o.sort();for(let r=0;r<o.length;++r){const s=o[r];if(!e.hasOwnProperty(s))continue;const i="    "+t+s+" : ",a=e[s];if(n&&n[s]){const e=n[s];if("skip"!==e){l(i+(0,x[e])(a))}}else D(a,n,"    "+t,i)}l(t+"}")}function A(e,n,t,r){t=t||"",l((r=r||t)+"[");for(let r=0;r<e.length;++r)D(e[r],n,t+"    ");l(t+"]")}function D(e,n,t,r){(r=r||t)&&r.length>80?l(r+"was skipped due to prefix length limit"):null===e?l(r+"null"):e&&e.constructor&&"Array"===e.constructor.name?A(e,n,t,r):"object"==typeof e?P(e,n,t,r):l("string"==typeof e?r+'"'+e+'"':r+e)}function M(e,n,t){return t=t||function(){return!0},new Promise((r=>{n.addEventListener(e,(function o(s){if(!t(s.data))return;n.removeEventListener(e,o),r(s.data)}))}))}function b(e){return e.executionContexts().length?Promise.resolve(e.executionContexts()[0]):e.once(SDK.RuntimeModel.Events.ExecutionContextCreated)}let k;function N(e,n){k=g(n),TestRunner.resourceTreeModel.addEventListener(SDK.ResourceTreeModel.Events.Load,L),w("window.location.replace('"+e+"')")}function L(){TestRunner.resourceTreeModel.removeEventListener(SDK.ResourceTreeModel.Events.Load,L),_()}function C(e){I(!1,void 0,e)}function I(e,n,t){k=g(t),TestRunner.resourceTreeModel.addEventListener(SDK.ResourceTreeModel.Events.Load,O),TestRunner.resourceTreeModel.reloadPage(e,n)}function O(){TestRunner.resourceTreeModel.removeEventListener(SDK.ResourceTreeModel.Events.Load,O),l("Page reloaded."),_()}async function _(){if(await b(TestRunner.runtimeModel),k){const e=k;k=void 0,e()}}function K(e,n,t){if(e===n)return;let r;throw r=t?"Failure ("+t+"):":"Failure:",new Error(r+" expected <"+e+"> found <"+n+">")}function j(e=""){const n=t.Runtime.Runtime.queryParam("inspected_test")||t.Runtime.Runtime.queryParam("test");return new URL(e,n+"/../").href}async function U(){const e=Root.Runtime.queryParam("test");if(a())return n=console.log,c=n,f=()=>console.log("Test completed"),void(self.test=async function(){await import(e)});var n;try{await import(e)}catch(e){l("TEST ENDED EARLY DUE TO UNCAUGHT ERROR:"),l(e&&e.stack||e),l("=== DO NOT COMMIT THIS INTO -expected.txt ==="),p()}}self.testRunner,TestRunner.StringOutputStream=class{constructor(e){this.callback=e,this.buffer=""}async open(e){return!0}async write(e){this.buffer+=e}async close(){this.callback(this.buffer)}},TestRunner.MockSetting=class{constructor(e){this.value=e}get(){return this.value}set(e){this.value=e}},TestRunner.formatters=x,TestRunner.completeTest=p,TestRunner.addResult=l,TestRunner.addResults=function(e){if(e)for(let n=0,t=e.length;n<t;++n)l(e[n])},TestRunner.runTests=function(e){!function n(){const t=e.shift();if(!t)return void p();l("\ntest: "+t.name);let r=t();r instanceof Promise||(r=Promise.resolve());r.then(n)}()},TestRunner.addSniffer=function(e,n,t,r){t=g(t);const o=e[n];if("function"!=typeof o)throw new Error("Cannot find method to override: "+n);e[n]=function(s){let i;try{i=o.apply(this,arguments)}finally{r||(e[n]=o)}try{Array.prototype.push.call(arguments,i),t.apply(this,arguments)}catch(e){throw new Error("Exception in overriden method '"+n+"': "+e)}return i}},TestRunner.addSnifferPromise=function(e,n){return new Promise((function(t,r){const o=e[n];"function"==typeof o?e[n]=function(s){let i;try{i=o.apply(this,arguments)}finally{e[n]=o}try{Array.prototype.push.call(arguments,i),t.apply(this,arguments)}catch(e){r("Exception in overridden method '"+n+"': "+e),p()}return i}:r("Cannot find method to override: "+n)}))},TestRunner.showPanel=function(e){return i.ViewManager.ViewManager.instance().showView(e)},TestRunner.createKeyEvent=function(e,n,t,r,o){return new KeyboardEvent("keydown",{key:e,bubbles:!0,cancelable:!0,ctrlKey:Boolean(n),altKey:Boolean(t),shiftKey:Boolean(r),metaKey:Boolean(o)})},TestRunner.safeWrap=g,TestRunner.textContentWithLineBreaks=R,TestRunner.textContentWithLineBreaksTrimmed=function(e){return R(e).replace(/\s{3,}/g," ")},TestRunner.textContentWithoutStyles=function(e){let n="",t=e;for(;t.traverseNextNode(e);)t=t.traverseNextNode(e,"DEVTOOLS-CSS-LENGTH"===t.tagName||"DEVTOOLS-ICON"===t.tagName),t.nodeType===Node.TEXT_NODE?n+=t.nodeValue:"STYLE"===t.nodeName&&(t=t.traverseNextNode(e));return n},TestRunner.evaluateInPagePromise=function(e){return new Promise((n=>h(e,n)))},TestRunner.callFunctionInPageAsync=function(e,n){return S(e+"("+(n=n||[]).map((e=>JSON.stringify(e))).join(",")+")")},TestRunner.evaluateInPageWithTimeout=function(e,n){w("setTimeout(unescape('"+escape(e)+"'), 1)",n)},TestRunner.evaluateFunctionInOverlay=function(e,n){const t='internals.evaluateInInspectorOverlay("(" + '+e+' + ")()")';TestRunner.runtimeModel.executionContexts()[0].evaluate({expression:t,objectGroup:"",includeCommandLineAPI:!1,silent:!1,returnByValue:!0,generatePreview:!1},!1,!1).then((e=>{n(e.object.value)}))},TestRunner.check=function(e,n){e||l("FAIL: "+n)},TestRunner.deprecatedRunAfterPendingDispatches=function(e){ProtocolClient.test.deprecatedRunAfterPendingDispatches(e)},TestRunner.loadHTML=E,TestRunner.addScriptTag=function(e){return S(`\n    (function(){\n      let script = document.createElement('script');\n      script.src = '${e}';\n      document.head.append(script);\n      return new Promise(f => script.onload = f);\n    })();\n  `)},TestRunner.addStylesheetTag=function(e){return S(`\n    (function(){\n      const link = document.createElement('link');\n      link.rel = 'stylesheet';\n      link.href = '${e}';\n      link.onload = onload;\n      document.head.append(link);\n      let resolve;\n      const promise = new Promise(r => resolve = r);\n      function onload() {\n        // TODO(chenwilliam): It shouldn't be necessary to force\n        // style recalc here but some tests rely on it.\n        window.getComputedStyle(document.body).color;\n        resolve();\n      }\n      return promise;\n    })();\n  `)},TestRunner.addIframe=function(e,n={}){return n.id=n.id||"",n.name=n.name||"",S(`\n    (function(){\n      const iframe = document.createElement('iframe');\n      iframe.src = '${e}';\n      iframe.id = '${n.id}';\n      iframe.name = '${n.name}';\n      document.body.appendChild(iframe);\n      return new Promise(f => iframe.onload = f);\n    })();\n  `)},TestRunner.markStep=function(e){l("\nRunning: "+e)},TestRunner.startDumpingProtocolMessages=function(){ProtocolClient.test.dumpProtocol=self.testRunner.logToStderr.bind(self.testRunner)},TestRunner.addScriptForFrame=function(e,n,t){n+="\n//# sourceURL="+e;const r=TestRunner.runtimeModel.executionContexts().find((e=>e.frameId===t.id));TestRunner.RuntimeAgent.evaluate(n,"console",!1,!1,r.id)},TestRunner.addObject=P,TestRunner.addArray=A,TestRunner.dumpDeepInnerHTML=function(e){!function e(n,t){const r=[];if(t.nodeType===Node.TEXT_NODE)return void(t.parentElement&&"STYLE"===t.parentElement.nodeName||l(t.nodeValue));r.push("<"+t.nodeName);const o=t.attributes;for(let e=0;o&&e<o.length;++e)r.push(o[e].name+"="+o[e].value);r.push(">"),l(n+r.join(" "));for(let r=t.firstChild;r;r=r.nextSibling)e(n+"    ",r);t.shadowRoot&&e(n+"    ",t.shadowRoot),l(n+"</"+t.nodeName+">")}("",e)},TestRunner.deepTextContent=function e(n){if(!n)return"";if(n.nodeType===Node.TEXT_NODE&&n.nodeValue)return n.parentElement&&"STYLE"===n.parentElement.nodeName?"":n.nodeValue;let t="";const r=n.childNodes;for(let n=0;n<r.length;++n)t+=e(r[n]);return n.shadowRoot&&(t+=e(n.shadowRoot)),t},TestRunner.dump=D,TestRunner.waitForEvent=M,TestRunner.waitForTarget=function(e){e=e||(e=>!0);for(const n of self.SDK.targetManager.targets())if(e(n))return Promise.resolve(n);return new Promise((n=>{const t={targetAdded:function(r){e(r)&&(self.SDK.targetManager.unobserveTargets(t),n(r))},targetRemoved:function(){}};self.SDK.targetManager.observeTargets(t)}))},TestRunner.waitForTargetRemoved=function(e){return new Promise((n=>{const t={targetRemoved:function(r){r===e&&(self.SDK.targetManager.unobserveTargets(t),n(r))},targetAdded:function(){}};self.SDK.targetManager.observeTargets(t)}))},TestRunner.waitForExecutionContext=b,TestRunner.waitForExecutionContextDestroyed=function(e){const n=e.runtimeModel;return-1===n.executionContexts().indexOf(e)?Promise.resolve():M(SDK.RuntimeModel.Events.ExecutionContextDestroyed,n,(n=>n===e))},TestRunner.assertGreaterOrEqual=function(e,n,t){e<n&&l("FAILED: "+(t?t+": ":"")+e+" < "+n)},TestRunner.navigate=N,TestRunner.navigatePromise=function(e){return new Promise((n=>N(e,n)))},TestRunner.hardReloadPage=function(e){I(!0,void 0,e)},TestRunner.reloadPage=C,TestRunner.reloadPageWithInjectedScript=function(e,n){I(!1,e,n)},TestRunner.reloadPagePromise=function(){return new Promise((e=>C(e)))},TestRunner.pageLoaded=O,TestRunner.waitForPageLoad=function(e){TestRunner.resourceTreeModel.addEventListener(SDK.ResourceTreeModel.Events.Load,(function n(){TestRunner.resourceTreeModel.removeEventListener(SDK.ResourceTreeModel.Events.Load,n),e()}))},TestRunner.runWhenPageLoads=function(e){const n=k;k=g((function(){n&&n(),e()}))},TestRunner.runTestSuite=function(e){const n=e.slice();!function e(){if(!n.length)return void p();const t=n.shift();l(""),l("Running: "+/function\s([^(]*)/.exec(t)[1]),g(t)(e)}()},TestRunner.assertEquals=K,TestRunner.assertTrue=function(e,n){K(!0,Boolean(e),n)},TestRunner.override=function(e,n,t,r){t=g(t);const o=e[n];if("function"!=typeof o)throw new Error("Cannot find method to override: "+n);return e[n]=function(s){try{return t.apply(this,arguments)}catch(e){throw new Error("Exception in overriden method '"+n+"': "+e)}finally{r||(e[n]=o)}},o},TestRunner.clearSpecificInfoFromStackFrames=function(e){let n=e.replace(/\(file:\/\/\/(?:[^)]+\)|[\w\/:-]+)/g,"(...)");return n=n.replace(/\(http:\/\/(?:[^)]+\)|[\w\/:-]+)/g,"(...)"),n=n.replace(/\(test:\/\/(?:[^)]+\)|[\w\/:-]+)/g,"(...)"),n=n.replace(/\(<anonymous>:[^)]+\)/g,"(...)"),n=n.replace(/VM\d+/g,"VM"),n.replace(/\s*at[^()]+\(native\)/g,"")},TestRunner.hideInspectorView=function(){i.InspectorView.InspectorView.instance().element.setAttribute("style","display:none !important")},TestRunner.mainFrame=function(){return TestRunner.resourceTreeModel.mainFrame},TestRunner.waitForUISourceCode=function(e,n){function t(t){return(!n||t.project().type()===n)&&(!(!n&&t.project().type()===o.Workspace.projectTypes.Service)&&!(e&&!t.url().endsWith(e)))}for(const n of self.Workspace.workspace.uiSourceCodes())if(e&&t(n))return Promise.resolve(n);return M(o.Workspace.Events.UISourceCodeAdded,self.Workspace.workspace,t)},TestRunner.waitForUISourceCodeRemoved=function(e){self.Workspace.workspace.once(o.Workspace.Events.UISourceCodeRemoved).then(e)},TestRunner.url=j,TestRunner.dumpSyntaxHighlight=function(e,n){const t=document.createElement("span");return t.textContent=e,s.CodeHighlighter.highlightNode(t,n).then((function(){const n=[];for(let e=0;e<t.childNodes.length;e++)t.childNodes[e].getAttribute?n.push(t.childNodes[e].getAttribute("class")):n.push("*");l(e+": "+n.join(", "))}))},TestRunner.loadLegacyModule=async function(e){let n=e;for(const[t,r]of m.entries())r===e&&(n=t);await import(`../../${n}/${n.split("/").reverse()[0]}-legacy.js`)},TestRunner.evaluateInPageRemoteObject=async function(e){const n=await v(e);return TestRunner.runtimeModel.createRemoteObject(n.result)},TestRunner.evaluateInPage=h,TestRunner.evaluateInPageAnonymously=w,TestRunner.evaluateInPageAsync=S,TestRunner.deprecatedInitAsync=async function(e){await TestRunner.RuntimeAgent.invoke_evaluate({expression:e,objectGroup:"console"})},TestRunner.runAsyncTestSuite=async function(e){for(const n of e)l(""),l("Running: "+/function\s([^(]*)/.exec(n)[1]),await T(n)();p()},TestRunner.dumpInspectedPageElementText=async function(e){l(await S(`document.querySelector('${e}').innerText`))},TestRunner.waitForPendingLiveLocationUpdates=async function(){await r.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().pendingLiveLocationChangesPromise(),await r.CSSWorkspaceBinding.CSSWorkspaceBinding.instance().pendingLiveLocationChangesPromise()},TestRunner.findLineEndingIndexes=function(e){const n=function(e,n){const t=[];let r=e.indexOf(n);for(;-1!==r;)t.push(r),r=e.indexOf(n,r+n.length);return t}(e,"\n");return n.push(e.length),n},TestRunner.selectTextInTextNode=function(e,n,t){n=n||0,t=t||e.textContent.length,n<0&&(n=t+n);const r=e.getComponentSelection();r.removeAllRanges();const o=e.ownerDocument.createRange();return o.setStart(e,n),o.setEnd(e,t),r.addRange(o),e},TestRunner.isScrolledToBottom=i.UIUtils.isScrolledToBottom,self.Platform=self.Platform||{},self.Platform.StringUtilities=e.StringUtilities;let F=!1;class W{targetAdded(e){if("main"===e.id()&&"frame"===e.type()||"tab"===e.parentTarget()?.type()&&"frame"===e.type()&&!e.targetInfo()?.subtype?.length){if(function(e){self.TestRunner.BrowserAgent=e.browserAgent(),self.TestRunner.CSSAgent=e.cssAgent(),self.TestRunner.DeviceOrientationAgent=e.deviceOrientationAgent(),self.TestRunner.DOMAgent=e.domAgent(),self.TestRunner.DOMDebuggerAgent=e.domdebuggerAgent(),self.TestRunner.DebuggerAgent=e.debuggerAgent(),self.TestRunner.EmulationAgent=e.emulationAgent(),self.TestRunner.HeapProfilerAgent=e.heapProfilerAgent(),self.TestRunner.InputAgent=e.inputAgent(),self.TestRunner.InspectorAgent=e.inspectorAgent(),self.TestRunner.NetworkAgent=e.networkAgent(),self.TestRunner.OverlayAgent=e.overlayAgent(),self.TestRunner.PageAgent=e.pageAgent(),self.TestRunner.ProfilerAgent=e.profilerAgent(),self.TestRunner.RuntimeAgent=e.runtimeAgent(),self.TestRunner.TargetAgent=e.targetAgent(),self.TestRunner.networkManager=e.model(SDK.NetworkManager),self.TestRunner.securityOriginManager=e.model(SDK.SecurityOriginManager),self.TestRunner.storageKeyManager=e.model(SDK.StorageKeyManager),self.TestRunner.resourceTreeModel=e.model(SDK.ResourceTreeModel),self.TestRunner.debuggerModel=e.model(SDK.DebuggerModel),self.TestRunner.runtimeModel=e.model(SDK.RuntimeModel),self.TestRunner.domModel=e.model(SDK.DOMModel),self.TestRunner.domDebuggerModel=e.model(SDK.DOMDebuggerModel),self.TestRunner.cssModel=e.model(SDK.CSSModel),self.TestRunner.cpuProfilerModel=e.model(SDK.CPUProfilerModel),self.TestRunner.overlayModel=e.model(SDK.OverlayModel),self.TestRunner.serviceWorkerManager=e.model(SDK.ServiceWorkerManager),self.TestRunner.tracingManager=e.model(SDK.TracingManager),self.TestRunner.mainTarget=e}(e),F)return;F=!0,E(`\n        <head>\n          <base href="${j()}">\n        </head>\n        <body>\n        </body>\n      `).then((()=>U()))}}targetRemoved(e){}}SDK.targetManager.observeTargets(new W);const B=self.TestRunner;export{B as TestRunner,W as _TestObserver,U as _executeTestScript};
