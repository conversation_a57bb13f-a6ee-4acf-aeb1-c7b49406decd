import*as e from"../helpers/helpers.js";import*as t from"../../lit-html/lit-html.js";import*as i from"../input/input.js";import*as n from"../../../core/common/common.js";import*as s from"../icon_button/icon_button.js";const a=new CSSStyleSheet;a.replaceSync(":host{padding:0;margin:0}input{height:12px;width:12px;min-height:12px;min-width:12px}label{display:inline-flex;align-items:center;overflow:hidden;text-overflow:ellipsis}p{margin:12px 0}\n/*# sourceURL=settingCheckbox.css */\n");const o=new CSSStyleSheet;o.replaceSync(".clickable{cursor:pointer}devtools-icon{vertical-align:text-bottom;padding-left:2px}\n/*# sourceURL=settingDeprecationWarning.css */\n");class r extends HTMLElement{static litTagName=t.literal`devtools-setting-deprecation-warning`;#e=this.attachShadow({mode:"open"});connectedCallback(){this.#e.adoptedStyleSheets=[o]}set data(e){this.#t(e)}#t({disabled:e,warning:i,experiment:a}){const o={clickable:!1};let r;e&&a&&(o.clickable=!0,r=()=>{n.Revealer.reveal(a)}),t.render(t.html`<${s.Icon.Icon.litTagName} class="${t.Directives.classMap(o)}" .data="${{iconName:"info",color:"var(--icon-default)",width:"16px"}}" title="${i}" @click="${r}"></${s.Icon.Icon.litTagName}>`,this.#e,{host:this})}}e.CustomElements.defineComponent("devtools-setting-deprecation-warning",r);var c=Object.freeze({__proto__:null,SettingDeprecationWarning:r});class l extends HTMLElement{static litTagName=t.literal`setting-checkbox`;#e=this.attachShadow({mode:"open"});#i;#n=!1;#s;connectedCallback(){this.#e.adoptedStyleSheets=[i.checkboxStyles,a]}set data(e){this.#s&&this.#i&&this.#i.removeChangeListener(this.#s.listener),this.#i=e.setting,this.#n=Boolean(e.disabled),this.#s=this.#i.addChangeListener((()=>{this.#t()})),this.#t()}#a(){if(this.#i?.deprecation)return t.html`<${r.litTagName} .data="${this.#i.deprecation}"></${r.litTagName}>`}#t(){if(!this.#i)throw new Error('No "Setting" object provided for rendering');const e=this.#a();t.render(t.html` <p> <label> <input type="checkbox" .checked="${this.#i.get()}" ?disabled="${this.#n||this.#i.disabled()}" @change="${this.#o}" aria-label="${this.#i.title()}"> ${this.#i.title()}${e} </label> `,this.#e,{host:this})}#o(e){this.#i?.set(e.target.checked)}}e.CustomElements.defineComponent("setting-checkbox",l);var h=Object.freeze({__proto__:null,SettingCheckbox:l});export{h as SettingCheckbox,c as SettingDeprecationWarning};
