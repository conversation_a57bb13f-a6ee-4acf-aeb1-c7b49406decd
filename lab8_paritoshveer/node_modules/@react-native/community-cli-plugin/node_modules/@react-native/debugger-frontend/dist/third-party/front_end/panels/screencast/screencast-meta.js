import*as o from"../../core/common/common.js";import*as e from"../../ui/legacy/legacy.js";let r;async function t(){return r||(r=await import("./screencast.js")),r}e.Toolbar.registerToolbarItem({loadItem:async()=>(await t()).ScreencastApp.ToolbarButtonProvider.instance(),order:1,location:e.Toolbar.ToolbarItemLocation.MAIN_TOOLBAR_LEFT,showLabel:void 0,condition:void 0,separator:void 0,actionId:void 0}),o.AppProvider.registerAppProvider({loadAppProvider:async()=>(await t()).ScreencastApp.ScreencastAppProvider.instance(),order:1,condition:void 0}),e.ContextMenu.registerItem({location:e.ContextMenu.ItemLocation.MAIN_MENU,order:10,actionId:"components.request-app-banner"});
