import*as t from"../../core/common/common.js";import*as e from"../../core/i18n/i18n.js";import*as i from"../../ui/legacy/legacy.js";import*as o from"../../core/host/host.js";import*as n from"../../core/sdk/sdk.js";const a=new CSSStyleSheet;let r;a.replaceSync(":host{overflow:hidden}.header{padding:0 0 6px;border-bottom:1px solid var(--color-details-hairline);font-size:18px;font-weight:normal;flex:none}.add-locations-button{flex:none;margin:10px 2px;min-width:140px;align-self:flex-start}.locations-list{max-width:600px;min-width:340px;flex:auto}.locations-list-item{padding:3px 6px;height:30px;display:flex;align-items:center;position:relative;flex:auto 1 1}.locations-list-text{white-space:nowrap;text-overflow:ellipsis;flex-basis:170px;user-select:none;color:var(--color-text-primary);position:relative;overflow:hidden}.locations-list-title{text-align:start}.locations-list-title-text{overflow:hidden;flex:auto;white-space:nowrap;text-overflow:ellipsis}.locations-list-separator{flex:0 0 1px;background-color:var(--color-details-hairline);height:30px;margin:0 4px}.locations-list-separator-invisible{visibility:hidden;height:100%!important}.locations-edit-row{display:flex;flex-direction:row;margin:6px 5px}.locations-edit-row input{width:100%;text-align:inherit}.locations-input-container{padding:1px}\n/*# sourceURL=locationsSettingsTab.css */\n");const l={customLocations:"Custom locations",locationName:"Location name",lat:"Lat",long:"Long",timezoneId:"Timezone ID",locale:"Locale",latitude:"Latitude",longitude:"Longitude",locationNameCannotBeEmpty:"Location name cannot be empty",locationNameMustBeLessThanS:"Location name must be less than {PH1} characters",latitudeMustBeANumber:"Latitude must be a number",latitudeMustBeGreaterThanOrEqual:"Latitude must be greater than or equal to {PH1}",latitudeMustBeLessThanOrEqualToS:"Latitude must be less than or equal to {PH1}",longitudeMustBeANumber:"Longitude must be a number",longitudeMustBeGreaterThanOr:"Longitude must be greater than or equal to {PH1}",longitudeMustBeLessThanOrEqualTo:"Longitude must be less than or equal to {PH1}",timezoneIdMustContainAlphabetic:"Timezone ID must contain alphabetic characters",localeMustContainAlphabetic:"Locale must contain alphabetic characters",addLocation:"Add location..."},s=e.i18n.registerUIStrings("panels/sensors/LocationsSettingsTab.ts",l),c=e.i18n.getLocalizedString.bind(void 0,s);class d extends i.Widget.VBox{list;customSetting;editor;constructor(){super(!0),this.contentElement.createChild("div","header").textContent=c(l.customLocations);const e=i.UIUtils.createTextButton(c(l.addLocation),this.addButtonClicked.bind(this),"add-locations-button");this.contentElement.appendChild(e),this.list=new i.ListWidget.ListWidget(this),this.list.element.classList.add("locations-list"),this.list.show(this.contentElement),this.customSetting=t.Settings.Settings.instance().moduleSetting("emulation.locations");const o=this.customSetting.get().map((t=>function(t,e){if(!t.title){const i=e.find((e=>e.lat===t.lat&&e.long===t.long&&e.timezoneId===t.timezoneId&&e.locale===t.locale));if(i)return i;console.error("Could not determine a location setting title")}return t}(t,this.customSetting.defaultValue)));this.customSetting.set(o),this.customSetting.addChangeListener(this.locationsUpdated,this),this.setDefaultFocusedElement(e)}static instance(){return r||(r=new d),r}wasShown(){super.wasShown(),this.registerCSSFiles([a]),this.list.registerCSSFiles([a]),this.locationsUpdated()}locationsUpdated(){this.list.clear();const t=this.customSetting.get();for(const e of t)this.list.appendItem(e,!0);this.list.appendSeparator()}addButtonClicked(){this.list.addNewItem(this.customSetting.get().length,{title:"",lat:0,long:0,timezoneId:"",locale:""})}renderItem(t,e){const o=document.createElement("div");o.classList.add("locations-list-item");const n=o.createChild("div","locations-list-text locations-list-title").createChild("div","locations-list-title-text");return n.textContent=t.title,i.Tooltip.Tooltip.install(n,t.title),o.createChild("div","locations-list-separator"),o.createChild("div","locations-list-text").textContent=String(t.lat),o.createChild("div","locations-list-separator"),o.createChild("div","locations-list-text").textContent=String(t.long),o.createChild("div","locations-list-separator"),o.createChild("div","locations-list-text").textContent=t.timezoneId,o.createChild("div","locations-list-separator"),o.createChild("div","locations-list-text").textContent=t.locale,o}removeItemRequested(t,e){const i=this.customSetting.get();i.splice(e,1),this.customSetting.set(i)}commitEdit(t,e,i){t.title=e.control("title").value.trim();const o=e.control("lat").value.trim();t.lat=o?parseFloat(o):0;const n=e.control("long").value.trim();t.long=n?parseFloat(n):0;const a=e.control("timezoneId").value.trim();t.timezoneId=a;const r=e.control("locale").value.trim();t.locale=r;const l=this.customSetting.get();i&&l.push(t),this.customSetting.set(l)}beginEdit(t){const e=this.createEditor();return e.control("title").value=t.title,e.control("lat").value=String(t.lat),e.control("long").value=String(t.long),e.control("timezoneId").value=t.timezoneId,e.control("locale").value=t.locale,e}createEditor(){if(this.editor)return this.editor;const t=new i.ListWidget.Editor;this.editor=t;const e=t.contentElement(),o=e.createChild("div","locations-edit-row");o.createChild("div","locations-list-text locations-list-title").textContent=c(l.locationName),o.createChild("div","locations-list-separator locations-list-separator-invisible"),o.createChild("div","locations-list-text").textContent=c(l.lat),o.createChild("div","locations-list-separator locations-list-separator-invisible"),o.createChild("div","locations-list-text").textContent=c(l.long),o.createChild("div","locations-list-separator locations-list-separator-invisible"),o.createChild("div","locations-list-text").textContent=c(l.timezoneId),o.createChild("div","locations-list-separator locations-list-separator-invisible"),o.createChild("div","locations-list-text").textContent=c(l.locale);const n=e.createChild("div","locations-edit-row");n.createChild("div","locations-list-text locations-list-title locations-input-container").appendChild(t.createInput("title","text",c(l.locationName),(function(t,e,i){const o=i.value.trim();let n;o.length?o.length>50&&(n=c(l.locationNameMustBeLessThanS,{PH1:50})):n=c(l.locationNameCannotBeEmpty);if(n)return{valid:!1,errorMessage:n};return{valid:!0,errorMessage:void 0}}))),n.createChild("div","locations-list-separator locations-list-separator-invisible");let a=n.createChild("div","locations-list-text locations-input-container");return a.appendChild(t.createInput("lat","text",c(l.latitude),(function(t,e,i){const o=i.value.trim(),n=Number(o);if(!o)return{valid:!0,errorMessage:void 0};let a;Number.isNaN(n)?a=c(l.latitudeMustBeANumber):parseFloat(o)<-90?a=c(l.latitudeMustBeGreaterThanOrEqual,{PH1:-90}):parseFloat(o)>90&&(a=c(l.latitudeMustBeLessThanOrEqualToS,{PH1:90}));if(a)return{valid:!1,errorMessage:a};return{valid:!0,errorMessage:void 0}}))),n.createChild("div","locations-list-separator locations-list-separator-invisible"),a=n.createChild("div","locations-list-text locations-list-text-longitude locations-input-container"),a.appendChild(t.createInput("long","text",c(l.longitude),(function(t,e,i){const o=i.value.trim(),n=Number(o);if(!o)return{valid:!0,errorMessage:void 0};let a;Number.isNaN(n)?a=c(l.longitudeMustBeANumber):parseFloat(o)<-180?a=c(l.longitudeMustBeGreaterThanOr,{PH1:-180}):parseFloat(o)>180&&(a=c(l.longitudeMustBeLessThanOrEqualTo,{PH1:180}));if(a)return{valid:!1,errorMessage:a};return{valid:!0,errorMessage:void 0}}))),n.createChild("div","locations-list-separator locations-list-separator-invisible"),a=n.createChild("div","locations-list-text locations-input-container"),a.appendChild(t.createInput("timezoneId","text",c(l.timezoneId),(function(t,e,i){const o=i.value.trim();if(""===o||/[a-zA-Z]/.test(o))return{valid:!0,errorMessage:void 0};return{valid:!1,errorMessage:c(l.timezoneIdMustContainAlphabetic)}}))),n.createChild("div","locations-list-separator locations-list-separator-invisible"),a=n.createChild("div","locations-list-text locations-input-container"),a.appendChild(t.createInput("locale","text",c(l.locale),(function(t,e,i){const o=i.value.trim();if(""===o||/[a-zA-Z]{2}/.test(o))return{valid:!0,errorMessage:void 0};return{valid:!1,errorMessage:c(l.localeMustContainAlphabetic)}}))),t}}var h=Object.freeze({__proto__:null,LocationsSettingsTab:d});const p=new CSSStyleSheet;p.replaceSync('.sensors-view{padding:12px;display:block}.sensors-view input{width:100%;max-width:120px;margin:-5px 10px 0 0;text-align:end}.sensors-view input[readonly]{background-color:var(--color-background-elevation-1)}.sensors-view fieldset{border:none;padding:10px 0;flex:0 0 auto;margin:0}.sensors-view fieldset[disabled]{opacity:50%}.orientation-axis-input-container input{max-width:120px}.sensors-view input:focus::-webkit-input-placeholder{color:transparent!important}.sensors-view .chrome-select{width:200px}.sensors-group-title{width:80px;line-height:24px}.sensors-group{display:flex;flex-wrap:wrap;margin-bottom:10px}.geo-fields{flex:2 0 200px}.latlong-group{display:flex;margin-bottom:10px}.latlong-title{width:70px}.timezone-error{margin-left:10px;color:var(--legacy-input-validation-error)}.orientation-content{display:flex;flex-wrap:wrap}.orientation-fields{margin-right:10px}.orientation-stage{--override-gradient-color-1:#e1f5fe;--override-gradient-color-2:#b0ebf3;--override-gradient-color-3:#def6f9;perspective:700px;perspective-origin:50% 50%;width:160px;height:150px;background:linear-gradient(var(--override-gradient-color-1) 0%,var(--override-gradient-color-1) 64%,var(--override-gradient-color-2) 64%,var(--override-gradient-color-3) 100%);transition:0.2s ease opacity,0.2s ease filter;overflow:hidden;margin-bottom:10px}.-theme-with-dark-background .orientation-stage,\n:host-context(.-theme-with-dark-background) .orientation-stage{--override-gradient-color-1:rgb(1 21 30);--override-gradient-color-2:rgb(12 71 79);--override-gradient-color-3:rgb(6 30 33)}.orientation-stage.disabled{filter:grayscale();opacity:50%}.orientation-element,\n.orientation-element::before,\n.orientation-element::after{position:absolute;box-sizing:border-box;transform-style:preserve-3d;background:no-repeat;background-size:cover;backface-visibility:hidden}.orientation-box{width:62px;height:122px;left:0;right:0;top:0;bottom:0;margin:auto;transform:rotate3d(1,0,0,90deg)}.orientation-layer{width:100%;height:100%;transform-style:preserve-3d}.orientation-box.is-animating,\n.is-animating .orientation-layer{transition:transform 300ms cubic-bezier(0.4,0,0.2,1) 0ms}.orientation-front,\n.orientation-back{width:62px;height:122px;border-radius:8px}.orientation-front{background-image:var(--image-file-accelerometer-front)}.orientation-back{transform:rotateY(180deg) translateZ(8px);background-image:var(--image-file-accelerometer-back)}.orientation-left,\n.orientation-right{width:8px;height:106px;top:8px;background-position:center center}.orientation-left{left:-8px;transform-origin:right center;transform:rotateY(-90deg);background-image:var(--image-file-accelerometer-left)}.orientation-right{right:-8px;transform-origin:left center;transform:rotateY(90deg);background-image:var(--image-file-accelerometer-right)}.orientation-left::before,\n.orientation-left::after,\n.orientation-right::before,\n.orientation-right::after{content:"";width:8px;height:6px}.orientation-left::before,\n.orientation-left::after{background-image:var(--image-file-accelerometer-left)}.orientation-right::before,\n.orientation-right::after{background-image:var(--image-file-accelerometer-right)}.orientation-left::before,\n.orientation-right::before{top:-6px;transform-origin:center bottom;transform:rotateX(26deg);background-position:center top}.orientation-left::after,\n.orientation-right::after{bottom:-6px;transform-origin:center top;transform:rotateX(-25deg);background-position:center bottom}.orientation-top,\n.orientation-bottom{width:50px;height:8px;left:8px;background-position:center center}.orientation-top{top:-8px;transform-origin:center bottom;transform:rotateX(90deg);background-image:var(--image-file-accelerometer-top)}.orientation-bottom{bottom:-8px;transform-origin:center top;transform:rotateX(-90deg);background-image:var(--image-file-accelerometer-bottom)}.orientation-top::before,\n.orientation-top::after,\n.orientation-bottom::before,\n.orientation-bottom::after{content:"";width:8px;height:8px}.orientation-top::before,\n.orientation-top::after{background-image:var(--image-file-accelerometer-top)}.orientation-bottom::before,\n.orientation-bottom::after{background-image:var(--image-file-accelerometer-bottom)}.orientation-top::before,\n.orientation-bottom::before{left:-6px;transform-origin:right center;transform:rotateY(-26deg);background-position:left center}.orientation-top::after,\n.orientation-bottom::after{right:-6px;transform-origin:left center;transform:rotateY(26deg);background-position:right center}.orientation-axis-input-container{margin-bottom:10px}.orientation-reset-button{min-width:80px}fieldset.device-orientation-override-section{margin:0;display:flex}.panel-section-separator{height:2px;margin-bottom:8px;background:var(--color-background-elevation-2)}button.text-button{margin:4px 0 0 10px}@media (forced-colors: active){.sensors-view fieldset[disabled]{opacity:100%}}.chrome-select-label{margin-bottom:16px}\n/*# sourceURL=sensors.css */\n');const u={location:"Location",noOverride:"No override",overrides:"Overrides",manage:"Manage",manageTheListOfLocations:"Manage the list of locations",other:"Other…",error:"Error",locationUnavailable:"Location unavailable",adjustWithMousewheelOrUpdownKeys:"Adjust with mousewheel or up/down keys. {PH1}: ±10, Shift: ±1, Alt: ±0.01",latitude:"Latitude",longitude:"Longitude",timezoneId:"Timezone ID",locale:"Locale",orientation:"Orientation",off:"Off",customOrientation:"Custom orientation",enableOrientationToRotate:"Enable orientation to rotate",shiftdragHorizontallyToRotate:"Shift+drag horizontally to rotate around the y-axis",deviceOrientationSetToAlphaSBeta:"Device orientation set to alpha: {PH1}, beta: {PH2}, gamma: {PH3}",reset:"Reset",resetDeviceOrientation:"Reset device orientation",forcesTouchInsteadOfClick:"Forces touch instead of click",forcesSelectedIdleStateEmulation:"Forces selected idle state emulation",presets:"Presets",portrait:"Portrait",portraitUpsideDown:"Portrait upside down",landscapeLeft:"Landscape left",landscapeRight:"Landscape right",displayUp:"Display up",displayDown:"Display down",alpha:"α (alpha)",beta:"β (beta)",gamma:"γ (gamma)"},m=e.i18n.registerUIStrings("panels/sensors/SensorsView.ts",u),g=e.i18n.getLocalizedString.bind(void 0,m);let v=null;class b extends i.Widget.VBox{LocationSetting;Location;LocationOverrideEnabled;fieldsetElement;timezoneError;locationSelectElement;latitudeInput;longitudeInput;timezoneInput;localeInput;latitudeSetter;longitudeSetter;timezoneSetter;localeSetter;localeError;customLocationsGroup;deviceOrientationSetting;deviceOrientation;deviceOrientationOverrideEnabled;deviceOrientationFieldset;stageElement;orientationSelectElement;alphaElement;betaElement;gammaElement;alphaSetter;betaSetter;gammaSetter;orientationLayer;boxElement;boxMatrix;mouseDownVector;originalBoxMatrix;constructor(){super(!0),this.contentElement.classList.add("sensors-view"),this.LocationSetting=t.Settings.Settings.instance().createSetting("emulation.locationOverride",""),this.Location=n.EmulationModel.Location.parseSetting(this.LocationSetting.get()),this.LocationOverrideEnabled=!1,this.createLocationSection(this.Location),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.deviceOrientationSetting=t.Settings.Settings.instance().createSetting("emulation.deviceOrientationOverride",""),this.deviceOrientation=n.EmulationModel.DeviceOrientation.parseSetting(this.deviceOrientationSetting.get()),this.deviceOrientationOverrideEnabled=!1,this.createDeviceOrientationSection(),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.appendTouchControl(),this.contentElement.createChild("div").classList.add("panel-section-separator"),this.appendIdleEmulator(),this.contentElement.createChild("div").classList.add("panel-section-separator")}static instance(){return v||(v=new b),v}wasShown(){super.wasShown(),this.registerCSSFiles([p])}createLocationSection(e){const a=this.contentElement.createChild("section","sensors-group"),r=i.UIUtils.createLabel(g(u.location),"sensors-group-title");a.appendChild(r);const l=a.createChild("div","geo-fields");let s=0;const c={title:g(u.noOverride),location:f.NoOverride};this.locationSelectElement=l.createChild("select","chrome-select"),i.ARIAUtils.bindLabelToControl(r,this.locationSelectElement),this.locationSelectElement.appendChild(new Option(c.title,c.location)),this.customLocationsGroup=this.locationSelectElement.createChild("optgroup"),this.customLocationsGroup.label=g(u.overrides);const d=t.Settings.Settings.instance().moduleSetting("emulation.locations"),h=i.UIUtils.createTextButton(g(u.manage),(()=>t.Revealer.reveal(d)));i.ARIAUtils.setLabel(h,g(u.manageTheListOfLocations)),l.appendChild(h);const p=()=>{if(this.customLocationsGroup){this.customLocationsGroup.removeChildren();for(const[t,i]of d.get().entries())this.customLocationsGroup.appendChild(new Option(i.title,JSON.stringify(i))),e.latitude===i.lat&&e.longitude===i.long&&(s=t+1)}};d.addChangeListener(p),p();const m={title:g(u.other),location:f.Custom};this.locationSelectElement.appendChild(new Option(m.title,m.location));const v=this.locationSelectElement.createChild("optgroup");v.label=g(u.error),v.appendChild(new Option(g(u.locationUnavailable),f.Unavailable)),this.locationSelectElement.selectedIndex=s,this.locationSelectElement.addEventListener("change",this.LocationSelectChanged.bind(this)),this.fieldsetElement=l.createChild("fieldset"),this.fieldsetElement.disabled=!this.LocationOverrideEnabled,this.fieldsetElement.id="location-override-section";const b=this.fieldsetElement.createChild("div","latlong-group"),x=this.fieldsetElement.createChild("div","latlong-group"),S=this.fieldsetElement.createChild("div","latlong-group"),E=this.fieldsetElement.createChild("div","latlong-group"),C=o.Platform.isMac()?"⌘":"Ctrl",I=g(u.adjustWithMousewheelOrUpdownKeys,{PH1:C});this.latitudeInput=i.UIUtils.createInput("","number"),b.appendChild(this.latitudeInput),this.latitudeInput.setAttribute("step","any"),this.latitudeInput.value="0",this.latitudeSetter=i.UIUtils.bindInput(this.latitudeInput,this.applyLocationUserInput.bind(this),n.EmulationModel.Location.latitudeValidator,!0,.1),this.latitudeSetter(String(e.latitude)),i.Tooltip.Tooltip.install(this.latitudeInput,I),b.appendChild(i.UIUtils.createLabel(g(u.latitude),"latlong-title",this.latitudeInput)),this.longitudeInput=i.UIUtils.createInput("","number"),x.appendChild(this.longitudeInput),this.longitudeInput.setAttribute("step","any"),this.longitudeInput.value="0",this.longitudeSetter=i.UIUtils.bindInput(this.longitudeInput,this.applyLocationUserInput.bind(this),n.EmulationModel.Location.longitudeValidator,!0,.1),this.longitudeSetter(String(e.longitude)),i.Tooltip.Tooltip.install(this.longitudeInput,I),x.appendChild(i.UIUtils.createLabel(g(u.longitude),"latlong-title",this.longitudeInput)),this.timezoneInput=i.UIUtils.createInput("","text"),S.appendChild(this.timezoneInput),this.timezoneInput.value="Europe/Berlin",this.timezoneSetter=i.UIUtils.bindInput(this.timezoneInput,this.applyLocationUserInput.bind(this),n.EmulationModel.Location.timezoneIdValidator,!1),this.timezoneSetter(e.timezoneId),S.appendChild(i.UIUtils.createLabel(g(u.timezoneId),"timezone-title",this.timezoneInput)),this.timezoneError=S.createChild("div","timezone-error"),this.localeInput=i.UIUtils.createInput("","text"),E.appendChild(this.localeInput),this.localeInput.value="en-US",this.localeSetter=i.UIUtils.bindInput(this.localeInput,this.applyLocationUserInput.bind(this),n.EmulationModel.Location.localeValidator,!1),this.localeSetter(e.locale),E.appendChild(i.UIUtils.createLabel(g(u.locale),"locale-title",this.localeInput)),this.localeError=E.createChild("div","locale-error")}LocationSelectChanged(){this.fieldsetElement.disabled=!1,this.timezoneError.textContent="";const t=this.locationSelectElement.options[this.locationSelectElement.selectedIndex].value;if(t===f.NoOverride)this.LocationOverrideEnabled=!1,this.clearFieldsetElementInputs(),this.fieldsetElement.disabled=!0;else if(t===f.Custom){this.LocationOverrideEnabled=!0;const t=n.EmulationModel.Location.parseUserInput(this.latitudeInput.value.trim(),this.longitudeInput.value.trim(),this.timezoneInput.value.trim(),this.localeInput.value.trim());if(!t)return;this.Location=t}else if(t===f.Unavailable)this.LocationOverrideEnabled=!0,this.Location=new n.EmulationModel.Location(0,0,"","",!0);else{this.LocationOverrideEnabled=!0;const e=JSON.parse(t);this.Location=new n.EmulationModel.Location(e.lat,e.long,e.timezoneId,e.locale,!1),this.latitudeSetter(e.lat),this.longitudeSetter(e.long),this.timezoneSetter(e.timezoneId),this.localeSetter(e.locale)}this.applyLocation(),t===f.Custom&&this.latitudeInput.focus()}applyLocationUserInput(){const t=n.EmulationModel.Location.parseUserInput(this.latitudeInput.value.trim(),this.longitudeInput.value.trim(),this.timezoneInput.value.trim(),this.localeInput.value.trim());t&&(this.timezoneError.textContent="",this.setSelectElementLabel(this.locationSelectElement,f.Custom),this.Location=t,this.applyLocation())}applyLocation(){this.LocationOverrideEnabled?this.LocationSetting.set(this.Location.toSetting()):this.LocationSetting.set("");for(const t of n.TargetManager.TargetManager.instance().models(n.EmulationModel.EmulationModel))t.emulateLocation(this.LocationOverrideEnabled?this.Location:null).catch((t=>{switch(t.type){case"emulation-set-timezone":this.timezoneError.textContent=t.message;break;case"emulation-set-locale":this.localeError.textContent=t.message}}))}clearFieldsetElementInputs(){this.latitudeSetter("0"),this.longitudeSetter("0"),this.timezoneSetter(""),this.localeSetter("")}createDeviceOrientationSection(){const t=this.contentElement.createChild("section","sensors-group"),e=i.UIUtils.createLabel(g(u.orientation),"sensors-group-title");t.appendChild(e);const o=t.createChild("div","orientation-content"),n=o.createChild("div","orientation-fields"),a={title:g(u.off),orientation:f.NoOverride},r={title:g(u.customOrientation),orientation:f.Custom},l=[{title:g(u.presets),value:[{title:g(u.portrait),orientation:"[0, 90, 0]"},{title:g(u.portraitUpsideDown),orientation:"[-180, -90, 0]"},{title:g(u.landscapeLeft),orientation:"[90, 0, -90]"},{title:g(u.landscapeRight),orientation:"[90, -180, -90]"},{title:g(u.displayUp),orientation:"[0, 0, 0]"},{title:g(u.displayDown),orientation:"[0, -180, 0]"}]}];this.orientationSelectElement=this.contentElement.createChild("select","chrome-select"),i.ARIAUtils.bindLabelToControl(e,this.orientationSelectElement),this.orientationSelectElement.appendChild(new Option(a.title,a.orientation)),this.orientationSelectElement.appendChild(new Option(r.title,r.orientation));for(let t=0;t<l.length;++t){const e=this.orientationSelectElement.createChild("optgroup");e.label=l[t].title;const i=l[t].value;for(let t=0;t<i.length;++t)e.appendChild(new Option(i[t].title,i[t].orientation))}this.orientationSelectElement.selectedIndex=0,n.appendChild(this.orientationSelectElement),this.orientationSelectElement.addEventListener("change",this.orientationSelectChanged.bind(this)),this.deviceOrientationFieldset=this.createDeviceOrientationOverrideElement(this.deviceOrientation),this.stageElement=o.createChild("div","orientation-stage"),this.orientationLayer=this.stageElement.createChild("div","orientation-layer"),this.boxElement=this.orientationLayer.createChild("section","orientation-box orientation-element"),this.boxElement.createChild("section","orientation-front orientation-element"),this.boxElement.createChild("section","orientation-top orientation-element"),this.boxElement.createChild("section","orientation-back orientation-element"),this.boxElement.createChild("section","orientation-left orientation-element"),this.boxElement.createChild("section","orientation-right orientation-element"),this.boxElement.createChild("section","orientation-bottom orientation-element"),i.UIUtils.installDragHandle(this.stageElement,this.onBoxDragStart.bind(this),(t=>{this.onBoxDrag(t)}),null,"-webkit-grabbing","-webkit-grab"),n.appendChild(this.deviceOrientationFieldset),this.enableOrientationFields(!0),this.setBoxOrientation(this.deviceOrientation,!1)}enableOrientationFields(t){t?(this.deviceOrientationFieldset.disabled=!0,this.stageElement.classList.add("disabled"),i.Tooltip.Tooltip.install(this.stageElement,g(u.enableOrientationToRotate))):(this.deviceOrientationFieldset.disabled=!1,this.stageElement.classList.remove("disabled"),i.Tooltip.Tooltip.install(this.stageElement,g(u.shiftdragHorizontallyToRotate)))}orientationSelectChanged(){const t=this.orientationSelectElement.options[this.orientationSelectElement.selectedIndex].value;if(this.enableOrientationFields(!1),t===f.NoOverride)this.deviceOrientationOverrideEnabled=!1,this.enableOrientationFields(!0);else if(t===f.Custom)this.deviceOrientationOverrideEnabled=!0,this.resetDeviceOrientation(),this.alphaElement.focus();else{const e=JSON.parse(t);this.deviceOrientationOverrideEnabled=!0,this.deviceOrientation=new n.EmulationModel.DeviceOrientation(e[0],e[1],e[2]),this.setDeviceOrientation(this.deviceOrientation,"selectPreset")}}applyDeviceOrientation(){this.deviceOrientationOverrideEnabled&&this.deviceOrientationSetting.set(this.deviceOrientation.toSetting());for(const t of n.TargetManager.TargetManager.instance().models(n.EmulationModel.EmulationModel))t.emulateDeviceOrientation(this.deviceOrientationOverrideEnabled?this.deviceOrientation:null)}setSelectElementLabel(t,e){const i=Array.prototype.map.call(t.options,(t=>t.value));t.selectedIndex=i.indexOf(e)}applyDeviceOrientationUserInput(){this.setDeviceOrientation(n.EmulationModel.DeviceOrientation.parseUserInput(this.alphaElement.value.trim(),this.betaElement.value.trim(),this.gammaElement.value.trim()),"userInput"),this.setSelectElementLabel(this.orientationSelectElement,f.Custom)}resetDeviceOrientation(){this.setDeviceOrientation(new n.EmulationModel.DeviceOrientation(0,90,0),"resetButton"),this.setSelectElementLabel(this.orientationSelectElement,"[0, 90, 0]")}setDeviceOrientation(t,e){if(!t)return;function o(t){return Math.round(1e4*t)/1e4}"userInput"!==e&&(this.alphaSetter(String(o(t.alpha))),this.betaSetter(String(o(t.beta))),this.gammaSetter(String(o(t.gamma))));const n="userDrag"!==e;this.setBoxOrientation(t,n),this.deviceOrientation=t,this.applyDeviceOrientation(),i.ARIAUtils.alert(g(u.deviceOrientationSetToAlphaSBeta,{PH1:t.alpha,PH2:t.beta,PH3:t.gamma}))}createAxisInput(t,e,o,n){const a=t.createChild("div","orientation-axis-input-container");return a.appendChild(e),a.appendChild(i.UIUtils.createLabel(o,"",e)),i.UIUtils.bindInput(e,this.applyDeviceOrientationUserInput.bind(this),n,!0)}createDeviceOrientationOverrideElement(t){const e=document.createElement("fieldset");e.classList.add("device-orientation-override-section");const o=e.createChild("td","orientation-inputs-cell");this.alphaElement=i.UIUtils.createInput("","number"),this.alphaElement.setAttribute("step","any"),this.alphaSetter=this.createAxisInput(o,this.alphaElement,g(u.alpha),n.EmulationModel.DeviceOrientation.alphaAngleValidator),this.alphaSetter(String(t.alpha)),this.betaElement=i.UIUtils.createInput("","number"),this.betaElement.setAttribute("step","any"),this.betaSetter=this.createAxisInput(o,this.betaElement,g(u.beta),n.EmulationModel.DeviceOrientation.betaAngleValidator),this.betaSetter(String(t.beta)),this.gammaElement=i.UIUtils.createInput("","number"),this.gammaElement.setAttribute("step","any"),this.gammaSetter=this.createAxisInput(o,this.gammaElement,g(u.gamma),n.EmulationModel.DeviceOrientation.gammaAngleValidator),this.gammaSetter(String(t.gamma));const a=i.UIUtils.createTextButton(g(u.reset),this.resetDeviceOrientation.bind(this),"orientation-reset-button");return i.ARIAUtils.setLabel(a,g(u.resetDeviceOrientation)),a.setAttribute("type","reset"),o.appendChild(a),e}setBoxOrientation(t,e){e?this.stageElement.classList.add("is-animating"):this.stageElement.classList.remove("is-animating");const{alpha:i,beta:o,gamma:n}=t;this.boxMatrix=(new DOMMatrixReadOnly).rotate(0,0,i).rotate(o,0,0).rotate(0,n,0),this.orientationLayer.style.transform=`rotateY(${i}deg) rotateX(${-o}deg) rotateZ(${n}deg)`}onBoxDrag(t){const e=this.calculateRadiusVector(t.x,t.y);if(!e)return!0;if(!this.mouseDownVector)return!0;let o,a;t.consume(!0),t.shiftKey?(o=new i.Geometry.Vector(0,0,1),a=(e.x-this.mouseDownVector.x)*E):(o=i.Geometry.crossProduct(this.mouseDownVector,e),a=i.Geometry.calculateAngle(this.mouseDownVector,e));const r=(new DOMMatrixReadOnly).rotateAxisAngle(-o.x,o.z,o.y,a).multiply(this.originalBoxMatrix),l=i.Geometry.EulerAngles.fromDeviceOrientationRotationMatrix(r),s=new n.EmulationModel.DeviceOrientation(l.alpha,l.beta,l.gamma);return this.setDeviceOrientation(s,"userDrag"),this.setSelectElementLabel(this.orientationSelectElement,f.Custom),!1}onBoxDragStart(t){return!!this.deviceOrientationOverrideEnabled&&(this.mouseDownVector=this.calculateRadiusVector(t.x,t.y),this.originalBoxMatrix=this.boxMatrix,!!this.mouseDownVector&&(t.consume(!0),!0))}calculateRadiusVector(t,e){const o=this.stageElement.getBoundingClientRect(),n=Math.max(o.width,o.height)/2,a=(t-o.left-o.width/2)/n,r=(e-o.top-o.height/2)/n,l=a*a+r*r;return l>.5?new i.Geometry.Vector(a,r,.5/Math.sqrt(l)):new i.Geometry.Vector(a,r,Math.sqrt(1-l))}appendTouchControl(){const e=this.contentElement.createChild("div","touch-section"),o=i.SettingsUI.createControlForSetting(t.Settings.Settings.instance().moduleSetting("emulation.touch"),g(u.forcesTouchInsteadOfClick));o&&e.appendChild(o)}appendIdleEmulator(){const e=this.contentElement.createChild("div","idle-section"),o=i.SettingsUI.createControlForSetting(t.Settings.Settings.instance().moduleSetting("emulation.idleDetection"),g(u.forcesSelectedIdleStateEmulation));o&&e.appendChild(o)}}const f={NoOverride:"noOverride",Custom:"custom",Unavailable:"unavailable"};let x;class S{handleAction(t,e){return i.ViewManager.ViewManager.instance().showView("sensors"),!0}static instance(t={forceNew:null}){const{forceNew:e}=t;return x&&!e||(x=new S),x}}const E=16;var C=Object.freeze({__proto__:null,SensorsView:b,NonPresetOptions:f,PresetOrientations:class{static get Orientations(){return[{title:g(u.presets),value:[{title:g(u.portrait),orientation:"[0, 90, 0]"},{title:g(u.portraitUpsideDown),orientation:"[-180, -90, 0]"},{title:g(u.landscapeLeft),orientation:"[90, 0, -90]"},{title:g(u.landscapeRight),orientation:"[90, -180, -90]"},{title:g(u.displayUp),orientation:"[0, 0, 0]"},{title:g(u.displayDown),orientation:"[0, -180, 0]"}]}]}},ShowActionDelegate:S,ShiftDragOrientationSpeed:E});export{h as LocationsSettingsTab,C as SensorsView};
