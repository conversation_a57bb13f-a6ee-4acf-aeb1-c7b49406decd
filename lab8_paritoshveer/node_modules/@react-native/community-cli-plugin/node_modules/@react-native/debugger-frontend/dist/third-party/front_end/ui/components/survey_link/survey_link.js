import*as e from"../../../core/common/common.js";import*as t from"../../lit-html/lit-html.js";import*as i from"../../../core/i18n/i18n.js";import*as n from"../helpers/helpers.js";import*as o from"../icon_button/icon_button.js";const r=new CSSStyleSheet;r.replaceSync(".link-icon{vertical-align:sub;margin-right:0.5ch}.link{padding:var(--issue-link-padding,4px 0 0 0);text-decoration:var(--issue-link-text-decoration,underline);cursor:pointer;font-size:var(--issue-link-font-size,14px);color:var(--color-link);outline-offset:2px;border:none;background:none;font-family:inherit}.link:focus:not(:focus-visible){outline:none}.pending-link{opacity:75%;pointer-events:none;cursor:default;text-decoration:none}.disabled-link{pointer-events:none;cursor:default;text-decoration:none}\n/*# sourceURL=surveyLink.css */\n");const s={openingSurvey:"Opening survey …",thankYouForYourFeedback:"Thank you for your feedback",anErrorOccurredWithTheSurvey:"An error occurred with the survey"},a=i.i18n.registerUIStrings("ui/components/survey_link/SurveyLink.ts",s),h=i.i18n.getLocalizedString.bind(void 0,a);class c extends HTMLElement{static litTagName=t.literal`devtools-survey-link`;#e=this.attachShadow({mode:"open"});#t="";#i=e.UIString.LocalizedEmptyString;#n=()=>{};#o=()=>{};#r="Checking";connectedCallback(){this.#e.adoptedStyleSheets=[r]}set data(e){this.#t=e.trigger,this.#i=e.promptText,this.#n=e.canShowSurvey,this.#o=e.showSurvey,this.#s()}#s(){this.#r="Checking",this.#n(this.#t,(({canShowSurvey:e})=>{this.#r=e?"ShowLink":"DontShowLink",this.#a()}))}#h(){this.#r="Sending",this.#a(),this.#o(this.#t,(({surveyShown:e})=>{this.#r=e?"SurveyShown":"Failed",this.#a()}))}#a(){if("Checking"===this.#r||"DontShowLink"===this.#r)return;let e=this.#i;"Sending"===this.#r?e=h(s.openingSurvey):"SurveyShown"===this.#r?e=h(s.thankYouForYourFeedback):"Failed"===this.#r&&(e=h(s.anErrorOccurredWithTheSurvey));let i="";"Sending"===this.#r?i="pending-link":"Failed"!==this.#r&&"SurveyShown"!==this.#r||(i="disabled-link");const n="ShowLink"!==this.#r,r=t.html` <button class="link ${i}" tabindex="${n?"-1":"0"}" .disabled="${n}" aria-disabled="${n}" @click="${this.#h}"> <${o.Icon.Icon.litTagName} class="link-icon" .data="${{iconName:"review",color:"var(--color-link)",width:"var(--issue-link-icon-size, 16px)",height:"var(--issue-link-icon-size, 16px)"}}"></${o.Icon.Icon.litTagName}>${e} </button> `;t.render(r,this.#e,{host:this})}}n.CustomElements.defineComponent("devtools-survey-link",c);var l=Object.freeze({__proto__:null,SurveyLink:c});export{l as SurveyLink};
