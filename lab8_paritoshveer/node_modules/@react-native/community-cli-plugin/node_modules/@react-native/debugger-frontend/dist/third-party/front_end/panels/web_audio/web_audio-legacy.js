import*as e from"./web_audio.js";self.WebAudio=self.WebAudio||{},WebAudio=WebAudio||{},WebAudio.GraphVisualizer=WebAudio.GraphVisualizer||{},WebAudio.GraphVisualizer.GraphStyle=WebAudio.GraphVisualizer.Style||{},WebAudio.GraphVisualizer.NodeRendererUtility=WebAudio.GraphVisualizer.NodeRendererUtility||{},WebAudio.ContextDetailBuilder=e.AudioContextContentBuilder.ContextDetailBuilder,WebAudio.AudioContextSummaryBuilder=e.AudioContextContentBuilder.AudioContextSummaryBuilder,WebAudio.AudioContextSelector=e.AudioContextSelector.AudioContextSelector,WebAudio.AudioContextSelector.Events=e.AudioContextSelector.Events,WebAudio.WebAudioModel=e.WebAudioModel.WebAudioModel,WebAudio.WebAudioModel.Events=e.WebAudioModel.Events,WebAudio.WebAudioView=e.WebAudioView.WebAudioView,WebAudio.GraphVisualizer.EdgeView=e.EdgeView.EdgeView,WebAudio.GraphVisualizer.generateEdgePortIdsByData=e.EdgeView.generateEdgePortIdsByData,WebAudio.GraphVisualizer.EdgeTypes=e.EdgeView.EdgeTypes,WebAudio.GraphVisualizer.GraphManager=e.GraphManager.GraphManager,WebAudio.GraphVisualizer.GraphStyle.PortPadding=e.GraphStyle.PortPadding,WebAudio.GraphVisualizer.GraphStyle.InputPortRadius=e.GraphStyle.InputPortRadius,WebAudio.GraphVisualizer.GraphStyle.AudioParamRadius=e.GraphStyle.AudioParamRadius,WebAudio.GraphVisualizer.GraphStyle.LeftMarginOfText=e.GraphStyle.LeftMarginOfText,WebAudio.GraphVisualizer.GraphStyle.RightMarginOfText=e.GraphStyle.RightMarginOfText,WebAudio.GraphVisualizer.GraphStyle.LeftSideTopPadding=e.GraphStyle.LeftSideTopPadding,WebAudio.GraphVisualizer.GraphStyle.BottomPaddingWithoutParam=e.GraphStyle.BottomPaddingWithoutParam,WebAudio.GraphVisualizer.GraphStyle.BottomPaddingWithParam=e.GraphStyle.BottomPaddingWithParam,WebAudio.GraphVisualizer.GraphStyle.ArrowHeadSize=e.GraphStyle.ArrowHeadSize,WebAudio.GraphVisualizer.GraphStyle.GraphPadding=e.GraphStyle.GraphPadding,WebAudio.GraphVisualizer.GraphStyle.GraphMargin=e.GraphStyle.GraphMargin,WebAudio.GraphVisualizer.GraphStyle.TotalInputPortHeight=e.GraphStyle.TotalInputPortHeight,WebAudio.GraphVisualizer.GraphStyle.TotalOutputPortHeight=e.GraphStyle.TotalOutputPortHeight,WebAudio.GraphVisualizer.GraphStyle.TotalParamPortHeight=e.GraphStyle.TotalParamPortHeight,WebAudio.GraphVisualizer.GraphStyle.NodeLabelFontStyle=e.GraphStyle.NodeLabelFontStyle,WebAudio.GraphVisualizer.GraphStyle.ParamLabelFontStyle=e.GraphStyle.ParamLabelFontStyle,WebAudio.GraphVisualizer.GraphStyle.GraphStyles=e.GraphStyle.GraphStyles,WebAudio.GraphVisualizer.GraphView=e.GraphView.GraphView,WebAudio.GraphVisualizer.GraphView.Events=e.GraphView.Events,WebAudio.GraphVisualizer.NodeRendererUtility.calculateInputPortXY=e.NodeRendererUtility.calculateInputPortXY,WebAudio.GraphVisualizer.NodeRendererUtility.calculateOutputPortXY=e.NodeRendererUtility.calculateOutputPortXY,WebAudio.GraphVisualizer.NodeRendererUtility.calculateParamPortXY=e.NodeRendererUtility.calculateParamPortXY,WebAudio.GraphVisualizer.NodeView=e.NodeView.NodeView,WebAudio.GraphVisualizer.PortTypes=e.GraphStyle.PortTypes,WebAudio.GraphVisualizer.NodeLabelGenerator=e.NodeView.NodeLabelGenerator,WebAudio.GraphVisualizer.generateInputPortId=e.NodeView.generateInputPortId,WebAudio.GraphVisualizer.generateOutputPortId=e.NodeView.generateOutputPortId,WebAudio.GraphVisualizer.generateParamPortId=e.NodeView.generateParamPortId,WebAudio.GraphVisualizer.measureTextWidth=e.NodeView.measureTextWidth;
