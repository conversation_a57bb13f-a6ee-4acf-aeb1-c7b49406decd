import*as e from"../../models/source_map_scopes/source_map_scopes.js";import*as o from"./sources.js";self.Sources=self.Sources||{},Sources=Sources||{},Sources.AddSourceMapURLDialog=o.AddSourceMapURLDialog.AddSourceMapURLDialog,Sources.BreakpointEditDialog=o.BreakpointEditDialog.BreakpointEditDialog,Sources.CallStackSidebarPane=o.CallStackSidebarPane.CallStackSidebarPane,Sources.CallStackSidebarPane._elementSymbol=o.CallStackSidebarPane.elementSymbol,Sources.CallStackSidebarPane._defaultMaxAsyncStackChainDepth=o.CallStackSidebarPane.defaultMaxAsyncStackChainDepth,Sources.CallStackSidebarPane.ActionDelegate=o.CallStackSidebarPane.ActionDelegate,Sources.CallStackSidebarPane.Item=o.CallStackSidebarPane.Item,Sources.CoveragePlugin=o.CoveragePlugin.CoveragePlugin,Sources.DebuggerPausedMessage=o.DebuggerPausedMessage.DebuggerPausedMessage,Sources.DebuggerPausedMessage.BreakpointTypeNouns=o.DebuggerPausedMessage.BreakpointTypeNouns,Sources.DebuggerPlugin=o.DebuggerPlugin.DebuggerPlugin,Sources.DebuggerPlugin.BreakpointDecoration=o.DebuggerPlugin.BreakpointDecoration,Sources.DebuggerPlugin.continueToLocationDecorationSymbol=o.DebuggerPlugin.continueToLocationDecorationSymbol,Sources.EditingLocationHistoryManager=o.EditingLocationHistoryManager.EditingLocationHistoryManager,Sources.EditingLocationHistoryManager.HistoryDepth=o.EditingLocationHistoryManager.HistoryDepth,Sources.EditingLocationHistoryEntry=o.EditingLocationHistoryManager.EditingLocationHistoryEntry,Sources.FilePathScoreFunction=o.FilePathScoreFunction.FilePathScoreFunction,Sources.FilteredUISourceCodeListProvider=o.FilteredUISourceCodeListProvider.FilteredUISourceCodeListProvider,Sources.GoToLineQuickOpen=o.GoToLineQuickOpen.GoToLineQuickOpen,Sources.InplaceFormatterEditorAction=o.InplaceFormatterEditorAction.InplaceFormatterEditorAction,Sources.NavigatorView=o.NavigatorView.NavigatorView,Sources.NavigatorView.Types=o.NavigatorView.Types,Sources.NavigatorFolderTreeElement=o.NavigatorView.NavigatorFolderTreeElement,Sources.NavigatorSourceTreeElement=o.NavigatorView.NavigatorSourceTreeElement,Sources.NavigatorTreeNode=o.NavigatorView.NavigatorTreeNode,Sources.NavigatorRootTreeNode=o.NavigatorView.NavigatorRootTreeNode,Sources.NavigatorUISourceCodeTreeNode=o.NavigatorView.NavigatorUISourceCodeTreeNode,Sources.NavigatorFolderTreeNode=o.NavigatorView.NavigatorFolderTreeNode,Sources.NavigatorGroupTreeNode=o.NavigatorView.NavigatorGroupTreeNode,Sources.OpenFileQuickOpen=o.OpenFileQuickOpen.OpenFileQuickOpen,Sources.OutlineQuickOpen=o.OutlineQuickOpen.OutlineQuickOpen,Sources.ScopeChainSidebarPane=o.ScopeChainSidebarPane.ScopeChainSidebarPane,Sources.ScopeChainSidebarPane.OpenLinearMemoryInspector=o.ScopeChainSidebarPane.OpenLinearMemoryInspector,Sources.SearchSourcesView=o.SearchSourcesView.SearchSourcesView,Sources.SearchSourcesView.ActionDelegate=o.SearchSourcesView.ActionDelegate,Sources.SnippetsPlugin=o.SnippetsPlugin.SnippetsPlugin,Sources.SourceMapNamesResolver={},Object.defineProperty(Sources.SourceMapNamesResolver,"_scopeResolvedForTest",{get:e.NamesResolver.getScopeResolvedForTest,set:e.NamesResolver.setScopeResolvedForTest}),Sources.SourceMapNamesResolver.resolveExpression=e.NamesResolver.resolveExpression,Sources.NetworkNavigatorView=o.SourcesNavigator.NetworkNavigatorView,Sources.FilesNavigatorView=o.SourcesNavigator.FilesNavigatorView,Sources.OverridesNavigatorView=o.SourcesNavigator.OverridesNavigatorView,Sources.ContentScriptsNavigatorView=o.SourcesNavigator.ContentScriptsNavigatorView,Sources.SnippetsNavigatorView=o.SourcesNavigator.SnippetsNavigatorView,Sources.ActionDelegate=o.SourcesNavigator.ActionDelegate,Sources.SourcesPanel=o.SourcesPanel.SourcesPanel,Sources.SourcesPanel._lastModificationTimeout=o.SourcesPanel.lastModificationTimeout,Sources.SourcesPanel.minToolbarWidth=o.SourcesPanel.minToolbarWidth,Sources.SourcesPanel.UILocationRevealer=o.SourcesPanel.UILocationRevealer,Sources.SourcesPanel.DebuggerLocationRevealer=o.SourcesPanel.DebuggerLocationRevealer,Sources.SourcesPanel.UISourceCodeRevealer=o.SourcesPanel.UISourceCodeRevealer,Sources.SourcesPanel.DebuggerPausedDetailsRevealer=o.SourcesPanel.DebuggerPausedDetailsRevealer,Sources.SourcesPanel.RevealingActionDelegate=o.SourcesPanel.RevealingActionDelegate,Sources.SourcesPanel.ActionDelegate=o.SourcesPanel.ActionDelegate,Sources.SourcesPanel.WrapperView=o.SourcesPanel.WrapperView,Sources.SourcesSearchScope=o.SourcesSearchScope.SourcesSearchScope,Sources.FileBasedSearchResult=o.SourcesSearchScope.FileBasedSearchResult,Sources.SourcesView=o.SourcesView.SourcesView,Sources.SourcesView.Events=o.SourcesView.Events,Sources.SourcesView.EditorAction=o.SourcesView.EditorAction,Sources.SourcesView.getRegisteredEditorActions=o.SourcesView.getRegisteredEditorActions,Sources.SourcesView.SwitchFileActionDelegate=o.SourcesView.SwitchFileActionDelegate,Sources.SourcesView.ActionDelegate=o.SourcesView.ActionDelegate,Sources.TabbedEditorContainer=o.TabbedEditorContainer.TabbedEditorContainer,Sources.TabbedEditorContainer.Events=o.TabbedEditorContainer.Events,Sources.TabbedEditorContainer.HistoryItem=o.TabbedEditorContainer.HistoryItem,Sources.TabbedEditorContainer.History=o.TabbedEditorContainer.History,Sources.TabbedEditorContainerDelegate=o.TabbedEditorContainer.TabbedEditorContainerDelegate,Sources.EditorContainerTabDelegate=o.TabbedEditorContainer.EditorContainerTabDelegate,Sources.ThreadsSidebarPane=o.ThreadsSidebarPane.ThreadsSidebarPane,Sources.UISourceCodeFrame=o.UISourceCodeFrame.UISourceCodeFrame,Sources.UISourceCodeFrame.RowMessage=o.UISourceCodeFrame.RowMessage,Sources.UISourceCodeFrame.RowMessageBucket=o.UISourceCodeFrame.RowMessageBucket,Sources.UISourceCodeFrame.Plugin=o.Plugin.Plugin,Sources.UISourceCodeFrame.Events=o.UISourceCodeFrame.Events,Sources.WatchExpressionsSidebarPane=o.WatchExpressionsSidebarPane.WatchExpressionsSidebarPane,Sources.WatchExpression=o.WatchExpressionsSidebarPane.WatchExpression;
