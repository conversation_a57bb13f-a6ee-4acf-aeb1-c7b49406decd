import*as e from"../../core/i18n/i18n.js";import*as t from"../../core/root/root.js";import*as n from"../../ui/legacy/legacy.js";const a={webauthn:"WebAuthn",showWebauthn:"Show WebAuthn"},i=e.i18n.registerUIStrings("panels/webauthn/webauthn-meta.ts",a),o=e.i18n.getLazilyComputedLocalizedString.bind(void 0,i);let r;n.ViewManager.registerViewExtension({location:"drawer-view",id:"webauthn-pane",title:o(a.web<PERSON>hn),commandPrompt:o(a.showWeb<PERSON>hn),order:100,persistence:"closeable",loadView:async()=>(await async function(){return r||(r=await import("./webauthn.js")),r}()).WebauthnPane.WebauthnPaneImpl.instance(),experiment:t.Runtime.ExperimentName.WEBAUTHN_PANE});
