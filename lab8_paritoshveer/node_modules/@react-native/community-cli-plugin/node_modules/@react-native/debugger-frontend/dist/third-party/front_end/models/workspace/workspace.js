import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as n from"../../core/i18n/i18n.js";import*as r from"../../core/platform/platform.js";import*as o from"../text_utils/text_utils.js";let s;class i extends e.ObjectWrapper.ObjectWrapper{saveCallbacks;constructor(){super(),this.saveCallbacks=new Map,t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.SavedURL,this.savedURL,this),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.CanceledSaveURL,this.canceledSavedURL,this),t.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(t.InspectorFrontendHostAPI.Events.AppendedToURL,this.appendedToURL,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return s&&!t||(s=new i),s}save(e,n,r){const o=new Promise((t=>this.saveCallbacks.set(e,t)));return t.InspectorFrontendHost.InspectorFrontendHostInstance.save(e,n,r),o}savedURL(e){const{url:t,fileSystemPath:n}=e.data,r=this.saveCallbacks.get(t);this.saveCallbacks.delete(t),r&&r({fileSystemPath:n})}canceledSavedURL({data:e}){const t=this.saveCallbacks.get(e);this.saveCallbacks.delete(e),t&&t(null)}append(e,n){t.InspectorFrontendHost.InspectorFrontendHostInstance.append(e,n)}close(e){t.InspectorFrontendHost.InspectorFrontendHostInstance.close(e)}appendedToURL({data:e}){this.dispatchEventToListeners(a.AppendedToURL,e)}}var a;(a||(a={})).AppendedToURL="AppendedToURL";var c,l=Object.freeze({__proto__:null,FileManager:i,get Events(){return a}});!function(e){e.Debugger="debugger",e.Formatter="formatter",e.Network="network",e.FileSystem="filesystem",e.ContentScripts="contentscripts",e.Service="service"}(c||(c={}));let d;class h extends e.ObjectWrapper.ObjectWrapper{projectsInternal;hasResourceContentTrackingExtensionsInternal;constructor(){super(),this.projectsInternal=new Map,this.hasResourceContentTrackingExtensionsInternal=!1}static instance(e={forceNew:null}){const{forceNew:t}=e;return d&&!t||(d=new h),d}static removeInstance(){d=void 0}uiSourceCode(e,t){const n=this.projectsInternal.get(e);return n?n.uiSourceCodeForURL(t):null}uiSourceCodeForURL(e){for(const t of this.projectsInternal.values()){const n=t.uiSourceCodeForURL(e);if(n)return n}return null}findCompatibleUISourceCodes(e){const t=e.url(),n=e.contentType(),r=[];for(const e of this.projectsInternal.values()){const o=e.uiSourceCodeForURL(t);o&&o.url()===t&&o.contentType()===n&&r.push(o)}return r}uiSourceCodesForProjectType(e){const t=[];for(const n of this.projectsInternal.values())n.type()===e&&t.push(...n.uiSourceCodes());return t}addProject(e){console.assert(!this.projectsInternal.has(e.id()),`A project with id ${e.id()} already exists!`),this.projectsInternal.set(e.id(),e),this.dispatchEventToListeners(u.ProjectAdded,e)}removeProject(e){this.projectsInternal.delete(e.id()),this.dispatchEventToListeners(u.ProjectRemoved,e)}project(e){return this.projectsInternal.get(e)||null}projects(){return[...this.projectsInternal.values()]}projectsForType(e){return this.projects().filter((function(t){return t.type()===e}))}uiSourceCodes(){const e=[];for(const t of this.projectsInternal.values())e.push(...t.uiSourceCodes());return e}setHasResourceContentTrackingExtensions(e){this.hasResourceContentTrackingExtensionsInternal=e}hasResourceContentTrackingExtensions(){return this.hasResourceContentTrackingExtensionsInternal}}var u;!function(e){e.UISourceCodeAdded="UISourceCodeAdded",e.UISourceCodeRemoved="UISourceCodeRemoved",e.UISourceCodeRenamed="UISourceCodeRenamed",e.WorkingCopyChanged="WorkingCopyChanged",e.WorkingCopyCommitted="WorkingCopyCommitted",e.WorkingCopyCommittedByUser="WorkingCopyCommittedByUser",e.ProjectAdded="ProjectAdded",e.ProjectRemoved="ProjectRemoved"}(u||(u={}));var p=Object.freeze({__proto__:null,get projectTypes(){return c},ProjectStore:class{workspaceInternal;idInternal;typeInternal;displayNameInternal;#e;constructor(e,t,n,r){this.workspaceInternal=e,this.idInternal=t,this.typeInternal=n,this.displayNameInternal=r,this.#e=new Map}id(){return this.idInternal}type(){return this.typeInternal}displayName(){return this.displayNameInternal}workspace(){return this.workspaceInternal}createUISourceCode(e,t){return new g(this,e,t)}addUISourceCode(e){const t=e.url();return!this.uiSourceCodeForURL(t)&&(this.#e.set(t,e),this.workspaceInternal.dispatchEventToListeners(u.UISourceCodeAdded,e),!0)}removeUISourceCode(e){const t=this.#e.get(e);void 0!==t&&(this.#e.delete(e),this.workspaceInternal.dispatchEventToListeners(u.UISourceCodeRemoved,t))}removeProject(){this.workspaceInternal.removeProject(this),this.#e.clear()}uiSourceCodeForURL(e){return this.#e.get(e)??null}uiSourceCodes(){return this.#e.values()}renameUISourceCode(t,n){const r=t.url(),o=t.parentURL()?e.ParsedURL.ParsedURL.urlFromParentUrlAndName(t.parentURL(),n):e.ParsedURL.ParsedURL.preEncodeSpecialCharactersInPath(n);this.#e.set(o,t),this.#e.delete(r)}rename(e,t,n){}excludeFolder(e){}deleteFile(e){}remove(){}indexContent(e){}},WorkspaceImpl:h,get Events(){return u}});const m={index:"(index)",thisFileWasChangedExternally:"This file was changed externally. Would you like to reload it?"},I=n.i18n.registerUIStrings("models/workspace/UISourceCode.ts",m),C=n.i18n.getLocalizedString.bind(void 0,I);class g extends e.ObjectWrapper.ObjectWrapper{projectInternal;urlInternal;originInternal;parentURLInternal;nameInternal;contentTypeInternal;requestContentPromise;decorations=new Map;hasCommitsInternal;messagesInternal;contentLoadedInternal;contentInternal;forceLoadOnCheckContentInternal;checkingContent;lastAcceptedContent;workingCopyInternal;workingCopyGetter;disableEditInternal;contentEncodedInternal;isKnownThirdPartyInternal;isUnconditionallyIgnoreListedInternal;constructor(t,n,o){super(),this.projectInternal=t,this.urlInternal=n;const s=e.ParsedURL.ParsedURL.fromString(n);s?(this.originInternal=s.securityOrigin(),this.parentURLInternal=e.ParsedURL.ParsedURL.concatenate(this.originInternal,s.folderPathComponents),!s.queryParams||s.lastPathComponent&&o.isFromSourceMap()?this.nameInternal=decodeURIComponent(s.lastPathComponent):this.nameInternal=s.lastPathComponent+"?"+s.queryParams):(this.originInternal=r.DevToolsPath.EmptyUrlString,this.parentURLInternal=r.DevToolsPath.EmptyUrlString,this.nameInternal=n),this.contentTypeInternal=o,this.requestContentPromise=null,this.hasCommitsInternal=!1,this.messagesInternal=null,this.contentLoadedInternal=!1,this.contentInternal=null,this.forceLoadOnCheckContentInternal=!1,this.checkingContent=!1,this.lastAcceptedContent=null,this.workingCopyInternal=null,this.workingCopyGetter=null,this.disableEditInternal=!1,this.isKnownThirdPartyInternal=!1,this.isUnconditionallyIgnoreListedInternal=!1}requestMetadata(){return this.projectInternal.requestMetadata(this)}name(){return this.nameInternal}mimeType(){return this.projectInternal.mimeType(this)}url(){return this.urlInternal}canononicalScriptId(){return`${this.contentTypeInternal.name()},${this.urlInternal}`}parentURL(){return this.parentURLInternal}origin(){return this.originInternal}fullDisplayName(){return this.projectInternal.fullDisplayName(this)}displayName(e){if(!this.nameInternal)return C(m.index);const t=this.nameInternal;return e?t:r.StringUtilities.trimEndWithMaxLength(t,100)}canRename(){return this.projectInternal.canRename()}rename(e){let t;const n=new Promise((e=>{t=e}));return this.projectInternal.rename(this,e,function(e,n,r,o){e&&this.updateName(n,r,o);t(e)}.bind(this)),n}remove(){this.projectInternal.deleteFile(this)}updateName(t,n,r){const o=this.urlInternal;this.nameInternal=t,this.urlInternal=n||e.ParsedURL.ParsedURL.relativePathToUrlString(t,o),r&&(this.contentTypeInternal=r),this.dispatchEventToListeners(y.TitleChanged,this),this.project().workspace().dispatchEventToListeners(u.UISourceCodeRenamed,{oldURL:o,uiSourceCode:this})}contentURL(){return this.url()}contentType(){return this.contentTypeInternal}project(){return this.projectInternal}requestContent({cachedWasmOnly:t}={}){return this.requestContentPromise?this.requestContentPromise:this.contentLoadedInternal?Promise.resolve(this.contentInternal):t&&"application/wasm"===this.mimeType()?Promise.resolve({content:"",isEncoded:!1,wasmDisassemblyInfo:new e.WasmDisassembly.WasmDisassembly([],[],[])}):(this.requestContentPromise=this.requestContentImpl(),this.requestContentPromise)}async requestContentImpl(){try{const e=await this.projectInternal.requestFileContent(this);this.contentLoadedInternal||(this.contentLoadedInternal=!0,this.contentInternal=e,this.contentEncodedInternal=e.isEncoded)}catch(e){this.contentLoadedInternal=!0,this.contentInternal={content:null,error:e?String(e):"",isEncoded:!1}}return this.contentInternal}#t(e){return e?e.isEncoded&&e.content?window.atob(e.content):e.content:null}async checkContentUpdated(){if(!this.contentLoadedInternal&&!this.forceLoadOnCheckContentInternal)return;if(!this.projectInternal.canSetFileContent()||this.checkingContent)return;this.checkingContent=!0;const t=await this.projectInternal.requestFileContent(this);if("error"in t)return;if(this.checkingContent=!1,null===t.content){const e=this.workingCopy();return this.contentCommitted("",!1),void this.setWorkingCopy(e)}if(this.lastAcceptedContent===t.content)return;if(this.#t(this.contentInternal)===this.#t(t))return void(this.lastAcceptedContent=null);if(!this.isDirty()||this.workingCopyInternal===t.content)return void this.contentCommitted(t.content,!1);await e.Revealer.reveal(this),await new Promise((e=>window.setTimeout(e,0)));window.confirm(C(m.thisFileWasChangedExternally))?this.contentCommitted(t.content,!1):this.lastAcceptedContent=t.content}forceLoadOnCheckContent(){this.forceLoadOnCheckContentInternal=!0}commitContent(e){this.projectInternal.canSetFileContent()&&this.projectInternal.setFileContent(this,e,!1),this.contentCommitted(e,!0)}contentCommitted(e,t){this.lastAcceptedContent=null,this.contentInternal={content:e,isEncoded:!1},this.contentLoadedInternal=!0,this.requestContentPromise=null,this.hasCommitsInternal=!0,this.innerResetWorkingCopy();const n={uiSourceCode:this,content:e,encoded:this.contentEncodedInternal};this.dispatchEventToListeners(y.WorkingCopyCommitted,n),this.projectInternal.workspace().dispatchEventToListeners(u.WorkingCopyCommitted,n),t&&this.projectInternal.workspace().dispatchEventToListeners(u.WorkingCopyCommittedByUser,n)}addRevision(e){this.commitContent(e)}hasCommits(){return this.hasCommitsInternal}workingCopy(){return this.workingCopyContent().content||""}workingCopyContent(){return this.workingCopyGetter&&(this.workingCopyInternal=this.workingCopyGetter(),this.workingCopyGetter=null),this.isDirty()?{content:this.workingCopyInternal,isEncoded:!1}:this.contentInternal?this.contentInternal:{content:"",isEncoded:!1}}resetWorkingCopy(){this.innerResetWorkingCopy(),this.workingCopyChanged()}innerResetWorkingCopy(){this.workingCopyInternal=null,this.workingCopyGetter=null}setWorkingCopy(e){this.workingCopyInternal=e,this.workingCopyGetter=null,this.workingCopyChanged()}setContent(e,t){this.contentEncodedInternal=t,this.projectInternal.canSetFileContent()&&this.projectInternal.setFileContent(this,e,t),this.contentCommitted(e,!0)}setWorkingCopyGetter(e){this.workingCopyGetter=e,this.workingCopyChanged()}workingCopyChanged(){this.removeAllMessages(),this.dispatchEventToListeners(y.WorkingCopyChanged,this),this.projectInternal.workspace().dispatchEventToListeners(u.WorkingCopyChanged,{uiSourceCode:this})}removeWorkingCopyGetter(){this.workingCopyGetter&&(this.workingCopyInternal=this.workingCopyGetter(),this.workingCopyGetter=null)}commitWorkingCopy(){this.isDirty()&&this.commitContent(this.workingCopy())}isDirty(){return null!==this.workingCopyInternal||null!==this.workingCopyGetter}isKnownThirdParty(){return this.isKnownThirdPartyInternal}markKnownThirdParty(){this.isKnownThirdPartyInternal=!0}isUnconditionallyIgnoreListed(){return this.isUnconditionallyIgnoreListedInternal}markAsUnconditionallyIgnoreListed(){this.isUnconditionallyIgnoreListedInternal=!0}extension(){return e.ParsedURL.ParsedURL.extractExtension(this.nameInternal)}content(){return this.contentInternal?.content||""}loadError(){return this.contentInternal&&"error"in this.contentInternal&&this.contentInternal.error||null}searchInContent(e,t,n){const r=this.content();return r?Promise.resolve(o.TextUtils.performSearchInContent(r,e,t,n)):this.projectInternal.searchInFileContent(this,e,t,n)}contentLoaded(){return this.contentLoadedInternal}uiLocation(e,t){return new k(this,e,t)}messages(){return this.messagesInternal?new Set(this.messagesInternal):new Set}addLineMessage(e,t,n,r,s){const i=o.TextRange.TextRange.createFromLocation(n,r||0),a=new v(e,t,s,i);return this.addMessage(a),a}addMessage(e){this.messagesInternal||(this.messagesInternal=new Set),this.messagesInternal.add(e),this.dispatchEventToListeners(y.MessageAdded,e)}removeMessage(e){this.messagesInternal?.delete(e)&&this.dispatchEventToListeners(y.MessageRemoved,e)}removeAllMessages(){if(this.messagesInternal){for(const e of this.messagesInternal)this.dispatchEventToListeners(y.MessageRemoved,e);this.messagesInternal=null}}setDecorationData(e,t){t!==this.decorations.get(e)&&(this.decorations.set(e,t),this.dispatchEventToListeners(y.DecorationChanged,e))}getDecorationData(e){return this.decorations.get(e)}disableEdit(){this.disableEditInternal=!0}editDisabled(){return this.disableEditInternal}}var y;!function(e){e.WorkingCopyChanged="WorkingCopyChanged",e.WorkingCopyCommitted="WorkingCopyCommitted",e.TitleChanged="TitleChanged",e.MessageAdded="MessageAdded",e.MessageRemoved="MessageRemoved",e.DecorationChanged="DecorationChanged"}(y||(y={}));class k{uiSourceCode;lineNumber;columnNumber;constructor(e,t,n){this.uiSourceCode=e,this.lineNumber=t,this.columnNumber=n}linkText(e=!1,t=!1){const n=this.uiSourceCode.displayName(e),r=this.lineAndColumnText(t);return r?n+":"+r:n}lineAndColumnText(e=!1){let t;return"application/wasm"===this.uiSourceCode.mimeType()?"number"==typeof this.columnNumber&&(t=`0x${this.columnNumber.toString(16)}`):(t=`${this.lineNumber+1}`,e&&"number"==typeof this.columnNumber&&(t+=":"+(this.columnNumber+1))),t}id(){return"number"==typeof this.columnNumber?this.uiSourceCode.project().id()+":"+this.uiSourceCode.url()+":"+this.lineNumber+":"+this.columnNumber:this.lineId()}lineId(){return this.uiSourceCode.project().id()+":"+this.uiSourceCode.url()+":"+this.lineNumber}toUIString(){return this.uiSourceCode.url()+":"+(this.lineNumber+1)}static comparator(e,t){return e.compareTo(t)}compareTo(e){return this.uiSourceCode.url()!==e.uiSourceCode.url()?this.uiSourceCode.url()>e.uiSourceCode.url()?1:-1:this.lineNumber!==e.lineNumber?this.lineNumber-e.lineNumber:this.columnNumber===e.columnNumber?0:"number"!=typeof this.columnNumber?-1:"number"!=typeof e.columnNumber?1:this.columnNumber-e.columnNumber}}class v{levelInternal;textInternal;range;clickHandlerInternal;constructor(e,t,n,r){this.levelInternal=e,this.textInternal=t,this.range=r??new o.TextRange.TextRange(0,0,0,0),this.clickHandlerInternal=n}level(){return this.levelInternal}text(){return this.textInternal}clickHandler(){return this.clickHandlerInternal}lineNumber(){return this.range.startLine}columnNumber(){return this.range.startColumn}isEqual(e){return this.text()===e.text()&&this.level()===e.level()&&this.range.equal(e.range)}}!function(e){let t;!function(e){e.Error="Error",e.Issue="Issue",e.Warning="Warning"}(t=e.Level||(e.Level={}))}(v||(v={}));var L=Object.freeze({__proto__:null,UISourceCode:g,get Events(){return y},UILocation:k,get Message(){return v},LineMarker:class{rangeInternal;typeInternal;dataInternal;constructor(e,t,n){this.rangeInternal=e,this.typeInternal=t,this.dataInternal=n}range(){return this.rangeInternal}type(){return this.typeInternal}data(){return this.dataInternal}},UISourceCodeMetadata:class{modificationTime;contentSize;constructor(e,t){this.modificationTime=e,this.contentSize=t}}});export{l as FileManager,L as UISourceCode,p as Workspace};
