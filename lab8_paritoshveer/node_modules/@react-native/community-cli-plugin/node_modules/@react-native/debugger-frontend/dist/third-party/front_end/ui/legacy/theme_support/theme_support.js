import*as e from"../../../core/common/common.js";var t={cssContent:'.webkit-css-property{color:var(--webkit-css-property-color,var(--color-syntax-1))}.webkit-html-comment{color:var(--color-token-comment)}.webkit-html-tag{color:var(--color-token-tag)}.webkit-html-tag-name,\n.webkit-html-close-tag-name{color:var(--color-token-tag)}.webkit-html-pseudo-element{color:var(--color-token-pseudo-element)}.webkit-html-js-node,\n.webkit-html-css-node{color:var(--color-text-primary);white-space:pre-wrap}.webkit-html-text-node{color:var(--color-text-primary);unicode-bidi:-webkit-isolate}.webkit-html-entity-value{background-color:rgb(0 0 0/15%);unicode-bidi:-webkit-isolate}.webkit-html-doctype{color:rgb(***********)}.webkit-html-attribute-name{color:var(--color-token-attribute);unicode-bidi:-webkit-isolate}.webkit-html-attribute-value{color:var(--color-token-attribute-value);unicode-bidi:-webkit-isolate;word-break:break-all}.devtools-link{color:var(--color-link);text-decoration:underline;outline-offset:2px}.elements-disclosure .devtools-link{color:var(--color-link)}.devtools-link [is="ui-icon"]{vertical-align:middle}.devtools-link [is="ui-icon"].icon-mask{background-color:var(--color-link)}:focus .selected .devtools-link [is="ui-icon"].icon-mask{background-color:var(--legacy-item-selection-bg-color)}.devtools-link:focus-visible{outline-width:unset}.devtools-link:not(.devtools-link-prevent-click){cursor:pointer}@media (forced-colors: active){.devtools-link:not(.devtools-link-prevent-click){forced-color-adjust:none;color:linktext}.devtools-link:focus-visible{background:Highlight;color:HighlightText}}'};let o;const n=new Map;class l extends EventTarget{setting;themeNameInternal="default";customSheets=new Set;computedRoot=e.Lazy.lazy((()=>window.getComputedStyle(document.documentElement)));constructor(e){super(),this.setting=e}static hasInstance(){return void 0!==o}static instance(e={forceNew:null,setting:null}){const{forceNew:t,setting:n}=e;if(!o||t){if(!n)throw new Error(`Unable to create theme support: setting must be provided: ${(new Error).stack}`);o=new l(n)}return o}getComputedValue(e,t=null){const o=t?window.getComputedStyle(t):this.computedRoot();if("symbol"==typeof o)throw new Error(`Computed value for property (${e}) could not be found on :root.`);let l=n.get(o);l||(l=new Map,n.set(o,l));let r=l.get(e);return r||(r=o.getPropertyValue(e).trim(),r&&l.set(e,r)),r}hasTheme(){return"default"!==this.themeNameInternal}themeName(){return this.themeNameInternal}injectHighlightStyleSheets(e){this.appendStyle(e,t)}appendStyle(e,{cssContent:t}){const o=document.createElement("style");o.textContent=t,e.appendChild(o)}injectCustomStyleSheets(e){for(const t of this.customSheets){const o=document.createElement("style");o.textContent=t,e.appendChild(o)}}addCustomStylesheet(e){this.customSheets.add(e)}applyTheme(e){const t=window.matchMedia("(forced-colors: active)").matches,o=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"default",l="systemPreferred"===this.setting.get()||t;this.themeNameInternal=l?o:this.setting.get();const i=e.documentElement.classList.contains("-theme-with-dark-background");e.documentElement.classList.toggle("-theme-with-dark-background","dark"===this.themeNameInternal);i!==e.documentElement.classList.contains("-theme-with-dark-background")&&(n.clear(),this.customSheets.clear(),this.dispatchEvent(new r))}}class r extends Event{static eventName="themechange";constructor(){super(r.eventName,{bubbles:!0,composed:!0})}}export{r as ThemeChangeEvent,l as ThemeSupport};
