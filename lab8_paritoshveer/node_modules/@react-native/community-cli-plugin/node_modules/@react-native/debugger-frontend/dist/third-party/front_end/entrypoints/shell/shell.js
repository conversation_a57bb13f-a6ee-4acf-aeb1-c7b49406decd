import"../../Images/Images.js";import*as e from"../../core/root/root.js";import"../../core/dom_extension/dom_extension.js";import*as t from"../../core/common/common.js";import*as o from"../../core/host/host.js";import*as i from"../../core/i18n/i18n.js";import*as n from"../../core/sdk/sdk.js";import*as a from"../../models/breakpoints/breakpoints.js";import*as s from"../../models/workspace/workspace.js";import*as r from"../../ui/legacy/components/object_ui/object_ui.js";import*as l from"../../ui/legacy/components/quick_open/quick_open.js";import*as c from"../../ui/legacy/legacy.js";import*as g from"../../models/workspace_diff/workspace_diff.js";import*as d from"../../ui/legacy/components/utils/utils.js";import"../main/main.js";self.Root=self.Root||{},Root=Root||{},Root.Runtime=e.Runtime.Runtime,Root.Runtime.experiments=e.Runtime.experiments,Root.Runtime.queryParam=e.Runtime.Runtime.queryParam,Root.runtime,Root.Runtime.Extension=e.Runtime.Extension,Root.Runtime.Module=e.Runtime.Module;const u={showSources:"Show Sources",sources:"Sources",showFilesystem:"Show Filesystem",filesystem:"Filesystem",showSnippets:"Show Snippets",snippets:"Snippets",showSearch:"Show Search",search:"Search",showQuickSource:"Show Quick source",quickSource:"Quick source",showThreads:"Show Threads",threads:"Threads",showScope:"Show Scope",scope:"Scope",showWatch:"Show Watch",watch:"Watch",showBreakpoints:"Show Breakpoints",breakpoints:"Breakpoints",pauseScriptExecution:"Pause script execution",resumeScriptExecution:"Resume script execution",stepOverNextFunctionCall:"Step over next function call",stepIntoNextFunctionCall:"Step into next function call",step:"Step",stepOutOfCurrentFunction:"Step out of current function",runSnippet:"Run snippet",deactivateBreakpoints:"Deactivate breakpoints",activateBreakpoints:"Activate breakpoints",addSelectedTextToWatches:"Add selected text to watches",evaluateSelectedTextInConsole:"Evaluate selected text in console",switchFile:"Switch file",rename:"Rename",closeAll:"Close All",jumpToPreviousEditingLocation:"Jump to previous editing location",jumpToNextEditingLocation:"Jump to next editing location",closeTheActiveTab:"Close the active tab",goToLine:"Go to line",goToAFunctionDeclarationruleSet:"Go to a function declaration/rule set",toggleBreakpoint:"Toggle breakpoint",toggleBreakpointEnabled:"Toggle breakpoint enabled",toggleBreakpointInputWindow:"Toggle breakpoint input window",save:"Save",saveAll:"Save all",createNewSnippet:"Create new snippet",addFolderToWorkspace:"Add folder to workspace",previousCallFrame:"Previous call frame",nextCallFrame:"Next call frame",incrementCssUnitBy:"Increment CSS unit by {PH1}",decrementCssUnitBy:"Decrement CSS unit by {PH1}",searchInAnonymousAndContent:"Search in anonymous and content scripts",doNotSearchInAnonymousAndContent:"Do not search in anonymous and content scripts",automaticallyRevealFilesIn:"Automatically reveal files in sidebar",doNotAutomaticallyRevealFilesIn:"Do not automatically reveal files in sidebar",enableJavascriptSourceMaps:"Enable JavaScript source maps",disableJavascriptSourceMaps:"Disable JavaScript source maps",enableTabMovesFocus:"Enable tab moves focus",disableTabMovesFocus:"Disable tab moves focus",detectIndentation:"Detect indentation",doNotDetectIndentation:"Do not detect indentation",autocompletion:"Autocompletion",enableAutocompletion:"Enable autocompletion",disableAutocompletion:"Disable autocompletion",bracketMatching:"Bracket matching",enableBracketMatching:"Enable bracket matching",disableBracketMatching:"Disable bracket matching",codeFolding:"Code folding",enableCodeFolding:"Enable code folding",disableCodeFolding:"Disable code folding",showWhitespaceCharacters:"Show whitespace characters:",doNotShowWhitespaceCharacters:"Do not show whitespace characters",none:"None",showAllWhitespaceCharacters:"Show all whitespace characters",all:"All",showTrailingWhitespaceCharacters:"Show trailing whitespace characters",trailing:"Trailing",displayVariableValuesInlineWhile:"Display variable values inline while debugging",doNotDisplayVariableValuesInline:"Do not display variable values inline while debugging",enableCssSourceMaps:"Enable CSS source maps",disableCssSourceMaps:"Disable CSS source maps",allowScrollingPastEndOfFile:"Allow scrolling past end of file",disallowScrollingPastEndOfFile:"Disallow scrolling past end of file",wasmAutoStepping:"When debugging wasm with debug information, do not pause on wasm bytecode if possible",enableWasmAutoStepping:"Enable wasm auto-stepping",disableWasmAutoStepping:"Disable wasm auto-stepping",goTo:"Go to",line:"Line",symbol:"Symbol",open:"Open",file:"File",disableAutoFocusOnDebuggerPaused:"Do not focus Sources panel when triggering a breakpoint",enableAutoFocusOnDebuggerPaused:"Focus Sources panel when triggering a breakpoint",toggleNavigatorSidebar:"Toggle navigator sidebar",toggleDebuggerSidebar:"Toggle debugger sidebar",nextEditorTab:"Next editor",previousEditorTab:"Previous editor"},S=i.i18n.registerUIStrings("panels/sources/sources-meta.ts",u),p=i.i18n.getLazilyComputedLocalizedString.bind(void 0,S);let m,y,w;async function h(){return m||(m=await import("../../panels/sources/sources.js")),m}async function v(){return y||(y=await import("../../panels/sources/components/components.js")),y}function A(e){return void 0===m?[]:e(m)}c.ViewManager.registerViewExtension({location:"panel",id:"sources",commandPrompt:p(u.showSources),title:p(u.sources),order:30,loadView:async()=>(await h()).SourcesPanel.SourcesPanel.instance()}),c.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-files",commandPrompt:p(u.showFilesystem),title:p(u.filesystem),order:3,persistence:"permanent",loadView:async()=>(await h()).SourcesNavigator.FilesNavigatorView.instance(),condition:e.Runtime.ConditionName.NOT_SOURCES_HIDE_ADD_FOLDER}),c.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-snippets",commandPrompt:p(u.showSnippets),title:p(u.snippets),order:6,persistence:"permanent",loadView:async()=>(await h()).SourcesNavigator.SnippetsNavigatorView.instance()}),c.ViewManager.registerViewExtension({location:"drawer-view",id:"sources.search-sources-tab",commandPrompt:p(u.showSearch),title:p(u.search),order:7,persistence:"closeable",loadView:async()=>(await h()).SearchSourcesView.SearchSourcesView.instance()}),c.ViewManager.registerViewExtension({location:"drawer-view",id:"sources.quick",commandPrompt:p(u.showQuickSource),title:p(u.quickSource),persistence:"closeable",order:1e3,loadView:async()=>(await h()).SourcesPanel.WrapperView.instance()}),c.ViewManager.registerViewExtension({id:"sources.threads",commandPrompt:p(u.showThreads),title:p(u.threads),persistence:"permanent",condition:e.Runtime.ConditionName.NOT_SOURCES_HIDE_ADD_FOLDER,loadView:async()=>(await h()).ThreadsSidebarPane.ThreadsSidebarPane.instance()}),c.ViewManager.registerViewExtension({id:"sources.scopeChain",commandPrompt:p(u.showScope),title:p(u.scope),persistence:"permanent",loadView:async()=>(await h()).ScopeChainSidebarPane.ScopeChainSidebarPane.instance()}),c.ViewManager.registerViewExtension({id:"sources.watch",commandPrompt:p(u.showWatch),title:p(u.watch),persistence:"permanent",loadView:async()=>(await h()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),hasToolbar:!0}),c.ViewManager.registerViewExtension({id:"sources.jsBreakpoints",commandPrompt:p(u.showBreakpoints),title:p(u.breakpoints),persistence:"permanent",loadView:async()=>(await v()).BreakpointsView.BreakpointsView.instance().wrapper}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.toggle-pause",iconClass:"pause",toggleable:!0,toggledIconClass:"resume",loadActionDelegate:async()=>(await h()).SourcesPanel.RevealingActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView,c.ShortcutRegistry.ForwardedShortcut])),options:[{value:!0,title:p(u.pauseScriptExecution)},{value:!1,title:p(u.resumeScriptExecution)}],bindings:[{shortcut:"F8",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+\\"},{shortcut:"F5",keybindSets:["vsCode"]},{shortcut:"Shift+F5",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+\\"}]}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.step-over",loadActionDelegate:async()=>(await h()).SourcesPanel.ActionDelegate.instance(),title:p(u.stepOverNextFunctionCall),iconClass:"step-over",contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F10",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+'"},{platform:"mac",shortcut:"Meta+'"}]}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.step-into",loadActionDelegate:async()=>(await h()).SourcesPanel.ActionDelegate.instance(),title:p(u.stepIntoNextFunctionCall),iconClass:"step-into",contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F11",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+;"},{platform:"mac",shortcut:"Meta+;"}]}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.step",loadActionDelegate:async()=>(await h()).SourcesPanel.ActionDelegate.instance(),title:p(u.step),iconClass:"step",contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F9",keybindSets:["devToolsDefault"]}]}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.step-out",loadActionDelegate:async()=>(await h()).SourcesPanel.ActionDelegate.instance(),title:p(u.stepOutOfCurrentFunction),iconClass:"step-out",contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Shift+F11",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Shift+Ctrl+;"},{platform:"mac",shortcut:"Shift+Meta+;"}]}),c.ActionRegistration.registerActionExtension({actionId:"debugger.run-snippet",category:c.ActionRegistration.ActionCategory.DEBUGGER,loadActionDelegate:async()=>(await h()).SourcesPanel.ActionDelegate.instance(),title:p(u.runSnippet),iconClass:"play",contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Enter"},{platform:"mac",shortcut:"Meta+Enter"}]}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.toggle-breakpoints-active",iconClass:"breakpoint-crossed",toggledIconClass:"breakpoint-crossed-filled",toggleable:!0,loadActionDelegate:async()=>(await h()).SourcesPanel.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),options:[{value:!0,title:p(u.deactivateBreakpoints)},{value:!1,title:p(u.activateBreakpoints)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+F8"},{platform:"mac",shortcut:"Meta+F8"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.add-to-watch",loadActionDelegate:async()=>(await h()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),category:c.ActionRegistration.ActionCategory.DEBUGGER,title:p(u.addSelectedTextToWatches),contextTypes:()=>A((e=>[e.UISourceCodeFrame.UISourceCodeFrame])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+A"},{platform:"mac",shortcut:"Meta+Shift+A"}]}),c.ActionRegistration.registerActionExtension({actionId:"debugger.evaluate-selection",category:c.ActionRegistration.ActionCategory.DEBUGGER,loadActionDelegate:async()=>(await h()).SourcesPanel.ActionDelegate.instance(),title:p(u.evaluateSelectedTextInConsole),contextTypes:()=>A((e=>[e.UISourceCodeFrame.UISourceCodeFrame])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.switch-file",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.switchFile),loadActionDelegate:async()=>(await h()).SourcesView.SwitchFileActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+O"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.rename",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.rename),bindings:[{platform:"windows,linux",shortcut:"F2"},{platform:"mac",shortcut:"Enter"}]}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.SOURCES,actionId:"sources.close-all",loadActionDelegate:async()=>(await h()).SourcesView.ActionDelegate.instance(),title:p(u.closeAll)}),c.ActionRegistration.registerActionExtension({actionId:"sources.jump-to-previous-location",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.jumpToPreviousEditingLocation),loadActionDelegate:async()=>(await h()).SourcesView.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+Minus"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.jump-to-next-location",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.jumpToNextEditingLocation),loadActionDelegate:async()=>(await h()).SourcesView.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+Plus"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.close-editor-tab",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.closeTheActiveTab),loadActionDelegate:async()=>(await h()).SourcesView.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+w"},{shortcut:"Ctrl+W",keybindSets:["vsCode"]},{platform:"windows",shortcut:"Ctrl+F4",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.next-editor-tab",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.nextEditorTab),loadActionDelegate:async()=>(await h()).SourcesView.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+PageDown",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+PageDown",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.previous-editor-tab",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.previousEditorTab),loadActionDelegate:async()=>(await h()).SourcesView.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+PageUp",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+PageUp",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.go-to-line",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.goToLine),loadActionDelegate:async()=>(await h()).SourcesView.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Ctrl+g",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.go-to-member",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.goToAFunctionDeclarationruleSet),loadActionDelegate:async()=>(await h()).SourcesView.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+o",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+o",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+T",keybindSets:["vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+T",keybindSets:["vsCode"]},{shortcut:"F12",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"debugger.toggle-breakpoint",category:c.ActionRegistration.ActionCategory.DEBUGGER,title:p(u.toggleBreakpoint),bindings:[{platform:"windows,linux",shortcut:"Ctrl+b",keybindSets:["devToolsDefault"]},{platform:"mac",shortcut:"Meta+b",keybindSets:["devToolsDefault"]},{shortcut:"F9",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"debugger.toggle-breakpoint-enabled",category:c.ActionRegistration.ActionCategory.DEBUGGER,title:p(u.toggleBreakpointEnabled),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+b"},{platform:"mac",shortcut:"Meta+Shift+b"}]}),c.ActionRegistration.registerActionExtension({actionId:"debugger.breakpoint-input-window",category:c.ActionRegistration.ActionCategory.DEBUGGER,title:p(u.toggleBreakpointInputWindow),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Alt+b"},{platform:"mac",shortcut:"Meta+Alt+b"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.save",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.save),loadActionDelegate:async()=>(await h()).SourcesView.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+s",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+s",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.save-all",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.saveAll),loadActionDelegate:async()=>(await h()).SourcesView.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+s"},{platform:"mac",shortcut:"Meta+Alt+s"},{platform:"windows,linux",shortcut:"Ctrl+K S",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Alt+S",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.SOURCES,actionId:"sources.create-snippet",loadActionDelegate:async()=>(await h()).SourcesNavigator.ActionDelegate.instance(),title:p(u.createNewSnippet)}),o.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()||c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.SOURCES,actionId:"sources.add-folder-to-workspace",loadActionDelegate:async()=>(await h()).SourcesNavigator.ActionDelegate.instance(),iconClass:"plus",title:p(u.addFolderToWorkspace),condition:e.Runtime.ConditionName.NOT_SOURCES_HIDE_ADD_FOLDER}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.previous-call-frame",loadActionDelegate:async()=>(await h()).CallStackSidebarPane.ActionDelegate.instance(),title:p(u.previousCallFrame),contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Ctrl+,"}]}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.DEBUGGER,actionId:"debugger.next-call-frame",loadActionDelegate:async()=>(await h()).CallStackSidebarPane.ActionDelegate.instance(),title:p(u.nextCallFrame),contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Ctrl+."}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.search",title:p(u.search),loadActionDelegate:async()=>(await h()).SearchSourcesView.ActionDelegate.instance(),category:c.ActionRegistration.ActionCategory.SOURCES,bindings:[{platform:"mac",shortcut:"Meta+Alt+F",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+J",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Shift+F",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Shift+J",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.increment-css",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.incrementCssUnitBy,{PH1:1}),bindings:[{shortcut:"Alt+Up"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.increment-css-by-ten",title:p(u.incrementCssUnitBy,{PH1:10}),category:c.ActionRegistration.ActionCategory.SOURCES,bindings:[{shortcut:"Alt+PageUp"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.decrement-css",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.decrementCssUnitBy,{PH1:1}),bindings:[{shortcut:"Alt+Down"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.decrement-css-by-ten",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.decrementCssUnitBy,{PH1:10}),bindings:[{shortcut:"Alt+PageDown"}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.toggle-navigator-sidebar",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.toggleNavigatorSidebar),loadActionDelegate:async()=>(await h()).SourcesPanel.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+y",keybindSets:["devToolsDefault"]},{platform:"mac",shortcut:"Meta+Shift+y",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+b",keybindSets:["vsCode"]},{platform:"windows,linux",shortcut:"Meta+b",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"sources.toggle-debugger-sidebar",category:c.ActionRegistration.ActionCategory.SOURCES,title:p(u.toggleDebuggerSidebar),loadActionDelegate:async()=>(await h()).SourcesPanel.ActionDelegate.instance(),contextTypes:()=>A((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+h"},{platform:"mac",shortcut:"Meta+Shift+h"}]}),t.Settings.registerSettingExtension({settingName:"navigatorGroupByFolder",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0}),t.Settings.registerSettingExtension({settingName:"navigatorGroupByAuthored",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.searchInAnonymousAndContent),settingName:"searchInAnonymousAndContentScripts",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:p(u.searchInAnonymousAndContent)},{value:!1,title:p(u.doNotSearchInAnonymousAndContent)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.automaticallyRevealFilesIn),settingName:"autoRevealInNavigator",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:p(u.automaticallyRevealFilesIn)},{value:!1,title:p(u.doNotAutomaticallyRevealFilesIn)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.enableJavascriptSourceMaps),settingName:"jsSourceMapsEnabled",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:p(u.enableJavascriptSourceMaps)},{value:!1,title:p(u.disableJavascriptSourceMaps)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.enableTabMovesFocus),settingName:"textEditorTabMovesFocus",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:p(u.enableTabMovesFocus)},{value:!1,title:p(u.disableTabMovesFocus)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.detectIndentation),settingName:"textEditorAutoDetectIndent",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:p(u.detectIndentation)},{value:!1,title:p(u.doNotDetectIndentation)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.autocompletion),settingName:"textEditorAutocompletion",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:p(u.enableAutocompletion)},{value:!1,title:p(u.disableAutocompletion)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,title:p(u.bracketMatching),settingName:"textEditorBracketMatching",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:p(u.enableBracketMatching)},{value:!1,title:p(u.disableBracketMatching)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.codeFolding),settingName:"textEditorCodeFolding",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:p(u.enableCodeFolding)},{value:!1,title:p(u.disableCodeFolding)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.showWhitespaceCharacters),settingName:"showWhitespacesInEditor",settingType:t.Settings.SettingType.ENUM,defaultValue:"original",options:[{title:p(u.doNotShowWhitespaceCharacters),text:p(u.none),value:"none"},{title:p(u.showAllWhitespaceCharacters),text:p(u.all),value:"all"},{title:p(u.showTrailingWhitespaceCharacters),text:p(u.trailing),value:"trailing"}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.displayVariableValuesInlineWhile),settingName:"inlineVariableValues",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:p(u.displayVariableValuesInlineWhile)},{value:!1,title:p(u.doNotDisplayVariableValuesInline)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.enableAutoFocusOnDebuggerPaused),settingName:"autoFocusOnDebuggerPausedEnabled",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:p(u.enableAutoFocusOnDebuggerPaused)},{value:!1,title:p(u.disableAutoFocusOnDebuggerPaused)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.enableCssSourceMaps),settingName:"cssSourceMapsEnabled",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:p(u.enableCssSourceMaps)},{value:!1,title:p(u.disableCssSourceMaps)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:p(u.allowScrollingPastEndOfFile),settingName:"allowScrollPastEof",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:p(u.allowScrollingPastEndOfFile)},{value:!1,title:p(u.disallowScrollingPastEndOfFile)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Local,title:p(u.wasmAutoStepping),settingName:"wasmAutoStepping",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:p(u.enableWasmAutoStepping)},{value:!1,title:p(u.disableWasmAutoStepping)}]}),c.ViewManager.registerLocationResolver({name:"navigator-view",category:c.ViewManager.ViewLocationCategory.SOURCES,loadResolver:async()=>(await h()).SourcesPanel.SourcesPanel.instance()}),c.ViewManager.registerLocationResolver({name:"sources.sidebar-top",category:c.ViewManager.ViewLocationCategory.SOURCES,loadResolver:async()=>(await h()).SourcesPanel.SourcesPanel.instance()}),c.ViewManager.registerLocationResolver({name:"sources.sidebar-bottom",category:c.ViewManager.ViewLocationCategory.SOURCES,loadResolver:async()=>(await h()).SourcesPanel.SourcesPanel.instance()}),c.ViewManager.registerLocationResolver({name:"sources.sidebar-tabs",category:c.ViewManager.ViewLocationCategory.SOURCES,loadResolver:async()=>(await h()).SourcesPanel.SourcesPanel.instance()}),c.ContextMenu.registerProvider({contextTypes:()=>[s.UISourceCode.UISourceCode,s.UISourceCode.UILocation,n.RemoteObject.RemoteObject,n.NetworkRequest.NetworkRequest,...A((e=>[e.UISourceCodeFrame.UISourceCodeFrame]))],loadProvider:async()=>(await h()).SourcesPanel.SourcesPanel.instance(),experiment:void 0}),c.ContextMenu.registerProvider({loadProvider:async()=>(await h()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),contextTypes:()=>[r.ObjectPropertiesSection.ObjectPropertyTreeElement],experiment:void 0}),c.ContextMenu.registerProvider({contextTypes:()=>A((e=>[e.UISourceCodeFrame.UISourceCodeFrame])),loadProvider:async()=>(await h()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),experiment:void 0}),c.ContextMenu.registerProvider({loadProvider:async()=>(await h()).ScopeChainSidebarPane.OpenLinearMemoryInspector.instance(),experiment:void 0,contextTypes:()=>[r.ObjectPropertiesSection.ObjectPropertyTreeElement]}),t.Revealer.registerRevealer({contextTypes:()=>[s.UISourceCode.UILocation],destination:t.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>(await h()).SourcesPanel.UILocationRevealer.instance()}),t.Revealer.registerRevealer({contextTypes:()=>[n.DebuggerModel.Location],destination:t.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>(await h()).SourcesPanel.DebuggerLocationRevealer.instance()}),t.Revealer.registerRevealer({contextTypes:()=>[s.UISourceCode.UISourceCode],destination:t.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>(await h()).SourcesPanel.UISourceCodeRevealer.instance()}),t.Revealer.registerRevealer({contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],destination:t.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>(await h()).SourcesPanel.DebuggerPausedDetailsRevealer.instance()}),t.Revealer.registerRevealer({contextTypes:()=>[a.BreakpointManager.BreakpointLocation],destination:t.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>(await h()).DebuggerPlugin.BreakpointLocationRevealer.instance()}),c.Toolbar.registerToolbarItem({actionId:"sources.add-folder-to-workspace",location:c.Toolbar.ToolbarItemLocation.FILES_NAVIGATION_TOOLBAR,showLabel:!0,condition:e.Runtime.ConditionName.NOT_SOURCES_HIDE_ADD_FOLDER,loadItem:void 0,order:void 0,separator:void 0}),c.Context.registerListener({contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await v()).BreakpointsView.BreakpointsSidebarController.instance()}),c.Context.registerListener({contextTypes:()=>[n.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await h()).CallStackSidebarPane.CallStackSidebarPane.instance()}),c.Context.registerListener({contextTypes:()=>[n.DebuggerModel.CallFrame],loadListener:async()=>(await h()).ScopeChainSidebarPane.ScopeChainSidebarPane.instance()}),c.ContextMenu.registerItem({location:c.ContextMenu.ItemLocation.NAVIGATOR_MENU_DEFAULT,actionId:"quickOpen.show",order:void 0}),c.ContextMenu.registerItem({location:c.ContextMenu.ItemLocation.MAIN_MENU_DEFAULT,actionId:"sources.search",order:void 0}),l.FilteredListWidget.registerProvider({prefix:"@",iconName:"symbol",iconWidth:"16px",provider:async()=>new((await h()).OutlineQuickOpen.OutlineQuickOpen),titlePrefix:p(u.goTo),titleSuggestion:p(u.symbol)}),l.FilteredListWidget.registerProvider({prefix:":",iconName:"colon",iconWidth:"20px",provider:async()=>new((await h()).GoToLineQuickOpen.GoToLineQuickOpen),titlePrefix:p(u.goTo),titleSuggestion:p(u.line)}),l.FilteredListWidget.registerProvider({prefix:"",iconName:"document",iconWidth:"16px",provider:async()=>new((await h()).OpenFileQuickOpen.OpenFileQuickOpen),titlePrefix:p(u.open),titleSuggestion:p(u.file)});const C={memory:"Memory",liveHeapProfile:"Live Heap Profile",startRecordingHeapAllocations:"Start recording heap allocations",stopRecordingHeapAllocations:"Stop recording heap allocations",startRecordingHeapAllocationsAndReload:"Start recording heap allocations and reload the page",startStopRecording:"Start/stop recording",showNativeFunctions:"Show native functions in JS Profile",showMemory:"Show Memory",showLiveHeapProfile:"Show Live Heap Profile"},E=i.i18n.registerUIStrings("panels/profiler/profiler-meta.ts",C),T=i.i18n.getLazilyComputedLocalizedString.bind(void 0,E);async function b(){return w||(w=await import("../../panels/profiler/profiler.js")),w}c.ViewManager.registerViewExtension({location:"panel",id:"heap_profiler",commandPrompt:T(C.showMemory),title:T(C.memory),order:60,loadView:async()=>(await b()).HeapProfilerPanel.HeapProfilerPanel.instance()}),c.ViewManager.registerViewExtension({location:"drawer-view",id:"live_heap_profile",commandPrompt:T(C.showLiveHeapProfile),title:T(C.liveHeapProfile),persistence:"closeable",order:100,loadView:async()=>(await b()).LiveHeapProfileView.LiveHeapProfileView.instance(),experiment:e.Runtime.ExperimentName.LIVE_HEAP_PROFILE}),c.ActionRegistration.registerActionExtension({actionId:"live-heap-profile.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,loadActionDelegate:async()=>(await b()).LiveHeapProfileView.ActionDelegate.instance(),category:c.ActionRegistration.ActionCategory.MEMORY,experiment:e.Runtime.ExperimentName.LIVE_HEAP_PROFILE,options:[{value:!0,title:T(C.startRecordingHeapAllocations)},{value:!1,title:T(C.stopRecordingHeapAllocations)}]}),c.ActionRegistration.registerActionExtension({actionId:"live-heap-profile.start-with-reload",iconClass:"refresh",loadActionDelegate:async()=>(await b()).LiveHeapProfileView.ActionDelegate.instance(),category:c.ActionRegistration.ActionCategory.MEMORY,experiment:e.Runtime.ExperimentName.LIVE_HEAP_PROFILE,title:T(C.startRecordingHeapAllocationsAndReload)}),c.ActionRegistration.registerActionExtension({actionId:"profiler.heap-toggle-recording",category:c.ActionRegistration.ActionCategory.MEMORY,iconClass:"record-start",title:T(C.startStopRecording),toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>void 0===w?[]:(e=>[e.HeapProfilerPanel.HeapProfilerPanel])(w),loadActionDelegate:async()=>(await b()).HeapProfilerPanel.HeapProfilerPanel.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.PERFORMANCE,storageType:t.Settings.SettingStorageType.Synced,title:T(C.showNativeFunctions),settingName:"showNativeFunctionsInJSProfile",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0}),c.ContextMenu.registerProvider({contextTypes:()=>[n.RemoteObject.RemoteObject],loadProvider:async()=>(await b()).HeapProfilerPanel.HeapProfilerPanel.instance(),experiment:void 0});const f={console:"Console",showConsole:"Show Console",clearConsole:"Clear console",clearConsoleHistory:"Clear console history",createLiveExpression:"Create live expression",hideNetworkMessages:"Hide network messages",showNetworkMessages:"Show network messages",selectedContextOnly:"Selected context only",onlyShowMessagesFromTheCurrent:"Only show messages from the current context (`top`, `iframe`, `worker`, extension)",showMessagesFromAllContexts:"Show messages from all contexts",logXmlhttprequests:"Log XMLHttpRequests",showTimestamps:"Show timestamps",hideTimestamps:"Hide timestamps",autocompleteFromHistory:"Autocomplete from history",doNotAutocompleteFromHistory:"Do not autocomplete from history",autocompleteOnEnter:"Accept autocomplete suggestion on Enter",doNotAutocompleteOnEnter:"Do not accept autocomplete suggestion on Enter",groupSimilarMessagesInConsole:"Group similar messages in console",doNotGroupSimilarMessagesIn:"Do not group similar messages in console",showCorsErrorsInConsole:"Show `CORS` errors in console",doNotShowCorsErrorsIn:"Do not show `CORS` errors in console",eagerEvaluation:"Eager evaluation",eagerlyEvaluateConsolePromptText:"Eagerly evaluate console prompt text",doNotEagerlyEvaluateConsole:"Do not eagerly evaluate console prompt text",evaluateTriggersUserActivation:"Treat code evaluation as user action",treatEvaluationAsUserActivation:"Treat evaluation as user activation",doNotTreatEvaluationAsUser:"Do not treat evaluation as user activation",expandConsoleTraceMessagesByDefault:"Automatically expand `console.trace()` messages",collapseConsoleTraceMessagesByDefault:"Do not automatically expand `console.trace()` messages"},R=i.i18n.registerUIStrings("panels/console/console-meta.ts",f),x=i.i18n.getLazilyComputedLocalizedString.bind(void 0,R);let N;async function D(){return N||(N=await import("../../panels/console/console.js")),N}c.ViewManager.registerViewExtension({location:"panel",id:"console",title:x(f.console),commandPrompt:x(f.showConsole),order:20,loadView:async()=>(await D()).ConsolePanel.ConsolePanel.instance()}),c.ViewManager.registerViewExtension({location:"drawer-view",id:"console-view",title:x(f.console),commandPrompt:x(f.showConsole),persistence:"permanent",order:0,loadView:async()=>(await D()).ConsolePanel.WrapperView.instance()}),c.ActionRegistration.registerActionExtension({actionId:"console.show",category:c.ActionRegistration.ActionCategory.CONSOLE,title:x(f.showConsole),loadActionDelegate:async()=>(await D()).ConsoleView.ActionDelegate.instance(),bindings:[{shortcut:"Ctrl+`",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"console.clear",category:c.ActionRegistration.ActionCategory.CONSOLE,title:x(f.clearConsole),iconClass:"clear",loadActionDelegate:async()=>(await D()).ConsoleView.ActionDelegate.instance(),contextTypes:()=>void 0===N?[]:(e=>[e.ConsoleView.ConsoleView])(N),bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),c.ActionRegistration.registerActionExtension({actionId:"console.clear.history",category:c.ActionRegistration.ActionCategory.CONSOLE,title:x(f.clearConsoleHistory),loadActionDelegate:async()=>(await D()).ConsoleView.ActionDelegate.instance()}),c.ActionRegistration.registerActionExtension({actionId:"console.create-pin",category:c.ActionRegistration.ActionCategory.CONSOLE,title:x(f.createLiveExpression),iconClass:"eye",loadActionDelegate:async()=>(await D()).ConsoleView.ActionDelegate.instance()}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,storageType:t.Settings.SettingStorageType.Synced,title:x(f.hideNetworkMessages),settingName:"hideNetworkMessages",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:x(f.hideNetworkMessages)},{value:!1,title:x(f.showNetworkMessages)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,storageType:t.Settings.SettingStorageType.Synced,title:x(f.selectedContextOnly),settingName:"selectedContextFilterEnabled",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:x(f.onlyShowMessagesFromTheCurrent)},{value:!1,title:x(f.showMessagesFromAllContexts)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,storageType:t.Settings.SettingStorageType.Synced,title:x(f.logXmlhttprequests),settingName:"monitoringXHREnabled",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,storageType:t.Settings.SettingStorageType.Synced,title:x(f.showTimestamps),settingName:"consoleTimestampsEnabled",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:x(f.showTimestamps)},{value:!1,title:x(f.hideTimestamps)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,title:x(f.autocompleteFromHistory),settingName:"consoleHistoryAutocomplete",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:x(f.autocompleteFromHistory)},{value:!1,title:x(f.doNotAutocompleteFromHistory)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,storageType:t.Settings.SettingStorageType.Synced,title:x(f.autocompleteOnEnter),settingName:"consoleAutocompleteOnEnter",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:x(f.autocompleteOnEnter)},{value:!1,title:x(f.doNotAutocompleteOnEnter)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,storageType:t.Settings.SettingStorageType.Synced,title:x(f.groupSimilarMessagesInConsole),settingName:"consoleGroupSimilar",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:x(f.groupSimilarMessagesInConsole)},{value:!1,title:x(f.doNotGroupSimilarMessagesIn)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,title:x(f.showCorsErrorsInConsole),settingName:"consoleShowsCorsErrors",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:x(f.showCorsErrorsInConsole)},{value:!1,title:x(f.doNotShowCorsErrorsIn)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,storageType:t.Settings.SettingStorageType.Synced,title:x(f.eagerEvaluation),settingName:"consoleEagerEval",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:x(f.eagerlyEvaluateConsolePromptText)},{value:!1,title:x(f.doNotEagerlyEvaluateConsole)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,storageType:t.Settings.SettingStorageType.Synced,title:x(f.evaluateTriggersUserActivation),settingName:"consoleUserActivationEval",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:x(f.treatEvaluationAsUserActivation)},{value:!1,title:x(f.doNotTreatEvaluationAsUser)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,storageType:t.Settings.SettingStorageType.Synced,title:x(f.expandConsoleTraceMessagesByDefault),settingName:"consoleTraceExpand",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,options:[{value:!0,title:x(f.expandConsoleTraceMessagesByDefault)},{value:!1,title:x(f.collapseConsoleTraceMessagesByDefault)}]}),t.Revealer.registerRevealer({contextTypes:()=>[t.Console.Console],loadRevealer:async()=>(await D()).ConsolePanel.ConsoleRevealer.instance(),destination:void 0});const L={coverage:"Coverage",showCoverage:"Show Coverage",instrumentCoverage:"Instrument coverage",stopInstrumentingCoverageAndShow:"Stop instrumenting coverage and show results",startInstrumentingCoverageAnd:"Start instrumenting coverage and reload page",reloadPage:"Reload page"},O=i.i18n.registerUIStrings("panels/coverage/coverage-meta.ts",L),P=i.i18n.getLazilyComputedLocalizedString.bind(void 0,O);let I,M;async function k(){return I||(I=await import("../../panels/coverage/coverage.js")),I}c.ViewManager.registerViewExtension({location:"drawer-view",id:"coverage",title:P(L.coverage),commandPrompt:P(L.showCoverage),persistence:"closeable",order:100,loadView:async()=>(await k()).CoverageView.CoverageView.instance()}),c.ActionRegistration.registerActionExtension({actionId:"coverage.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,loadActionDelegate:async()=>(await k()).CoverageView.ActionDelegate.instance(),category:c.ActionRegistration.ActionCategory.PERFORMANCE,options:[{value:!0,title:P(L.instrumentCoverage)},{value:!1,title:P(L.stopInstrumentingCoverageAndShow)}]}),c.ActionRegistration.registerActionExtension({actionId:"coverage.start-with-reload",iconClass:"refresh",loadActionDelegate:async()=>(await k()).CoverageView.ActionDelegate.instance(),category:c.ActionRegistration.ActionCategory.PERFORMANCE,title:P(L.startInstrumentingCoverageAnd)}),c.ActionRegistration.registerActionExtension({actionId:"coverage.reload",iconClass:"refresh",loadActionDelegate:async()=>(await k()).CoverageView.ActionDelegate.instance(),category:c.ActionRegistration.ActionCategory.PERFORMANCE,title:P(L.reloadPage)});const V={changes:"Changes",showChanges:"Show Changes"},F=i.i18n.registerUIStrings("panels/changes/changes-meta.ts",V),B=i.i18n.getLazilyComputedLocalizedString.bind(void 0,F);async function U(){return M||(M=await import("../../panels/changes/changes.js")),M}c.ViewManager.registerViewExtension({location:"drawer-view",id:"changes.changes",title:B(V.changes),commandPrompt:B(V.showChanges),persistence:"closeable",loadView:async()=>(await U()).ChangesView.ChangesView.instance()}),t.Revealer.registerRevealer({contextTypes:()=>[g.WorkspaceDiff.DiffUILocation],destination:t.Revealer.RevealerDestination.CHANGES_DRAWER,loadRevealer:async()=>(await U()).ChangesView.DiffUILocationRevealer.instance()});const G={memoryInspector:"Memory Inspector",showMemoryInspector:"Show Memory Inspector"},H=i.i18n.registerUIStrings("ui/components/linear_memory_inspector/linear_memory_inspector-meta.ts",G),_=i.i18n.getLazilyComputedLocalizedString.bind(void 0,H);let W;c.ViewManager.registerViewExtension({location:"drawer-view",id:"linear-memory-inspector",title:_(G.memoryInspector),commandPrompt:_(G.showMemoryInspector),order:100,persistence:"closeable",loadView:async()=>(await async function(){return W||(W=await import("../../ui/components/linear_memory_inspector/linear_memory_inspector.js")),W}()).LinearMemoryInspectorPane.Wrapper.instance()});const z={devices:"Devices",showDevices:"Show Devices"},j=i.i18n.registerUIStrings("panels/settings/emulation/emulation-meta.ts",z),q=i.i18n.getLazilyComputedLocalizedString.bind(void 0,j);let J;c.ViewManager.registerViewExtension({location:"settings-view",commandPrompt:q(z.showDevices),title:q(z.devices),order:30,loadView:async()=>(await async function(){return J||(J=await import("../../panels/settings/emulation/emulation.js")),J}()).DevicesSettingsTab.DevicesSettingsTab.instance(),id:"devices",settings:["standardEmulatedDeviceList","customEmulatedDeviceList"]});const K={shortcuts:"Shortcuts",preferences:"Preferences",experiments:"Experiments",ignoreList:"Ignore List",showShortcuts:"Show Shortcuts",showPreferences:"Show Preferences",showExperiments:"Show Experiments",showIgnoreList:"Show Ignore List",settings:"Settings",documentation:"Documentation"},Q=i.i18n.registerUIStrings("panels/settings/settings-meta.ts",K),Y=i.i18n.getLazilyComputedLocalizedString.bind(void 0,Q);let X;async function Z(){return X||(X=await import("../../panels/settings/settings.js")),X}c.ViewManager.registerViewExtension({location:"settings-view",id:"preferences",title:Y(K.preferences),commandPrompt:Y(K.showPreferences),order:0,loadView:async()=>(await Z()).SettingsScreen.GenericSettingsTab.instance()}),c.ViewManager.registerViewExtension({location:"settings-view",id:"experiments",title:Y(K.experiments),commandPrompt:Y(K.showExperiments),order:3,experiment:e.Runtime.ExperimentName.ALL,loadView:async()=>(await Z()).SettingsScreen.ExperimentsSettingsTab.instance()}),c.ViewManager.registerViewExtension({location:"settings-view",id:"blackbox",title:Y(K.ignoreList),commandPrompt:Y(K.showIgnoreList),order:4,loadView:async()=>(await Z()).FrameworkIgnoreListSettingsTab.FrameworkIgnoreListSettingsTab.instance()}),c.ViewManager.registerViewExtension({location:"settings-view",id:"keybinds",title:Y(K.shortcuts),commandPrompt:Y(K.showShortcuts),order:100,loadView:async()=>(await Z()).KeybindsSettingsTab.KeybindsSettingsTab.instance()}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.SETTINGS,actionId:"settings.show",title:Y(K.settings),loadActionDelegate:async()=>(await Z()).SettingsScreen.ActionDelegate.instance(),iconClass:"gear",bindings:[{shortcut:"F1",keybindSets:["devToolsDefault"]},{shortcut:"Shift+?"},{platform:"windows,linux",shortcut:"Ctrl+,",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+,",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.SETTINGS,actionId:"settings.documentation",title:Y(K.documentation),loadActionDelegate:async()=>(await Z()).SettingsScreen.ActionDelegate.instance()}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.SETTINGS,actionId:"settings.shortcuts",title:Y(K.shortcuts),loadActionDelegate:async()=>(await Z()).SettingsScreen.ActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+K Ctrl+S",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+K Meta+S",keybindSets:["vsCode"]}]}),c.ViewManager.registerLocationResolver({name:"settings-view",category:c.ViewManager.ViewLocationCategory.SETTINGS,loadResolver:async()=>(await Z()).SettingsScreen.SettingsScreen.instance()}),t.Revealer.registerRevealer({contextTypes:()=>[t.Settings.Setting,e.Runtime.Experiment],loadRevealer:async()=>(await Z()).SettingsScreen.Revealer.instance(),destination:void 0}),c.ContextMenu.registerItem({location:c.ContextMenu.ItemLocation.MAIN_MENU_FOOTER,actionId:"settings.shortcuts",order:void 0}),c.ContextMenu.registerItem({location:c.ContextMenu.ItemLocation.MAIN_MENU_HELP_DEFAULT,actionId:"settings.documentation",order:void 0});const $={protocolMonitor:"Protocol monitor",showProtocolMonitor:"Show Protocol monitor"},ee=i.i18n.registerUIStrings("panels/protocol_monitor/protocol_monitor-meta.ts",$),te=i.i18n.getLazilyComputedLocalizedString.bind(void 0,ee);let oe;c.ViewManager.registerViewExtension({location:"drawer-view",id:"protocol-monitor",title:te($.protocolMonitor),commandPrompt:te($.showProtocolMonitor),order:100,persistence:"closeable",loadView:async()=>(await async function(){return oe||(oe=await import("../../panels/protocol_monitor/protocol_monitor.js")),oe}()).ProtocolMonitor.ProtocolMonitorImpl.instance(),experiment:e.Runtime.ExperimentName.PROTOCOL_MONITOR});const ie={workspace:"Workspace",showWorkspace:"Show Workspace",enableLocalOverrides:"Enable Local Overrides",interception:"interception",override:"override",network:"network",rewrite:"rewrite",request:"request",enableOverrideNetworkRequests:"Enable override network requests",disableOverrideNetworkRequests:"Disable override network requests"},ne=i.i18n.registerUIStrings("models/persistence/persistence-meta.ts",ie),ae=i.i18n.getLazilyComputedLocalizedString.bind(void 0,ne);let se;async function re(){return se||(se=await import("../../models/persistence/persistence.js")),se}c.ViewManager.registerViewExtension({location:"settings-view",id:"workspace",title:ae(ie.workspace),commandPrompt:ae(ie.showWorkspace),order:1,loadView:async()=>(await re()).WorkspaceSettingsTab.WorkspaceSettingsTab.instance()}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.PERSISTENCE,title:ae(ie.enableLocalOverrides),settingName:"persistenceNetworkOverridesEnabled",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,tags:[ae(ie.interception),ae(ie.override),ae(ie.network),ae(ie.rewrite),ae(ie.request)],options:[{value:!0,title:ae(ie.enableOverrideNetworkRequests)},{value:!1,title:ae(ie.disableOverrideNetworkRequests)}]}),c.ContextMenu.registerProvider({contextTypes:()=>[s.UISourceCode.UISourceCode,n.Resource.Resource,n.NetworkRequest.NetworkRequest],loadProvider:async()=>(await re()).PersistenceActions.ContextMenuProvider.instance(),experiment:void 0});const le={preserveLog:"Preserve log",preserve:"preserve",clear:"clear",reset:"reset",preserveLogOnPageReload:"Preserve log on page reload / navigation",doNotPreserveLogOnPageReload:"Do not preserve log on page reload / navigation",recordNetworkLog:"Record network log"},ce=i.i18n.registerUIStrings("models/logs/logs-meta.ts",le),ge=i.i18n.getLazilyComputedLocalizedString.bind(void 0,ce);t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.NETWORK,title:ge(le.preserveLog),settingName:"network_log.preserve-log",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,tags:[ge(le.preserve),ge(le.clear),ge(le.reset)],options:[{value:!0,title:ge(le.preserveLogOnPageReload)},{value:!1,title:ge(le.doNotPreserveLogOnPageReload)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.NETWORK,title:ge(le.recordNetworkLog),settingName:"network_log.record-log",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0,storageType:t.Settings.SettingStorageType.Session});const de={focusDebuggee:"Focus debuggee",toggleDrawer:"Toggle drawer",nextPanel:"Next panel",previousPanel:"Previous panel",reloadDevtools:"Reload DevTools",restoreLastDockPosition:"Restore last dock position",zoomIn:"Zoom in",zoomOut:"Zoom out",resetZoomLevel:"Reset zoom level",searchInPanel:"Search in panel",cancelSearch:"Cancel search",findNextResult:"Find next result",findPreviousResult:"Find previous result",theme:"Theme:",switchToSystemPreferredColor:"Switch to system preferred color theme",systemPreference:"System preference",switchToLightTheme:"Switch to light theme",lightCapital:"Light",switchToDarkTheme:"Switch to dark theme",darkCapital:"Dark",darkLower:"dark",lightLower:"light",panelLayout:"Panel layout:",useHorizontalPanelLayout:"Use horizontal panel layout",horizontal:"horizontal",useVerticalPanelLayout:"Use vertical panel layout",vertical:"vertical",useAutomaticPanelLayout:"Use automatic panel layout",auto:"auto",colorFormat:"Color format:",setColorFormatAsAuthored:"Set color format as authored",asAuthored:"As authored",setColorFormatToHex:"Set color format to HEX",setColorFormatToRgb:"Set color format to RGB",setColorFormatToHsl:"Set color format to HSL",enableCtrlShortcutToSwitchPanels:"Enable Ctrl + 1-9 shortcut to switch panels",enableShortcutToSwitchPanels:"Enable ⌘ + 1-9 shortcut to switch panels",right:"Right",dockToRight:"Dock to right",bottom:"Bottom",dockToBottom:"Dock to bottom",left:"Left",dockToLeft:"Dock to left",undocked:"Undocked",undockIntoSeparateWindow:"Undock into separate window",devtoolsDefault:"DevTools (Default)",language:"Language:",browserLanguage:"Browser UI language",enableSync:"Enable settings sync",colorFormatSettingDisabled:"This setting is deprecated because it is incompatible with modern color spaces. To re-enable it, disable the corresponding experiment.",searchAsYouTypeSetting:"Search as you type",searchAsYouTypeCommand:"Enable search as you type",searchOnEnterCommand:"Disable search as you type (press Enter to search)"},ue=i.i18n.registerUIStrings("entrypoints/main/main-meta.ts",de),Se=i.i18n.getLazilyComputedLocalizedString.bind(void 0,ue);let pe,me;async function ye(){return pe||(pe=await import("../main/main.js")),pe}function we(e){return()=>i.i18n.getLocalizedLanguageRegion(e,i.DevToolsLocale.DevToolsLocale.instance())}c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.DRAWER,actionId:"inspector_main.focus-debuggee",loadActionDelegate:async()=>(await async function(){return me||(me=await import("../inspector_main/inspector_main.js")),me}()).InspectorMain.FocusDebuggeeActionDelegate.instance(),order:100,title:Se(de.focusDebuggee)}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.DRAWER,actionId:"main.toggle-drawer",loadActionDelegate:async()=>c.InspectorView.ActionDelegate.instance(),order:101,title:Se(de.toggleDrawer),bindings:[{shortcut:"Esc"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.next-tab",category:c.ActionRegistration.ActionCategory.GLOBAL,title:Se(de.nextPanel),loadActionDelegate:async()=>c.InspectorView.ActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+]"},{platform:"mac",shortcut:"Meta+]"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.previous-tab",category:c.ActionRegistration.ActionCategory.GLOBAL,title:Se(de.previousPanel),loadActionDelegate:async()=>c.InspectorView.ActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+["},{platform:"mac",shortcut:"Meta+["}]}),c.ActionRegistration.registerActionExtension({actionId:"main.debug-reload",category:c.ActionRegistration.ActionCategory.GLOBAL,title:Se(de.reloadDevtools),loadActionDelegate:async()=>(await ye()).MainImpl.ReloadActionDelegate.instance(),bindings:[{shortcut:"Alt+R"}]}),c.ActionRegistration.registerActionExtension({category:c.ActionRegistration.ActionCategory.GLOBAL,title:Se(de.restoreLastDockPosition),actionId:"main.toggle-dock",loadActionDelegate:async()=>c.DockController.ToggleDockActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+D"},{platform:"mac",shortcut:"Meta+Shift+D"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.zoom-in",category:c.ActionRegistration.ActionCategory.GLOBAL,title:Se(de.zoomIn),loadActionDelegate:async()=>(await ye()).MainImpl.ZoomActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Plus",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+Plus"},{platform:"windows,linux",shortcut:"Ctrl+NumpadPlus"},{platform:"windows,linux",shortcut:"Ctrl+Shift+NumpadPlus"},{platform:"mac",shortcut:"Meta+Plus",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+Plus"},{platform:"mac",shortcut:"Meta+NumpadPlus"},{platform:"mac",shortcut:"Meta+Shift+NumpadPlus"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.zoom-out",category:c.ActionRegistration.ActionCategory.GLOBAL,title:Se(de.zoomOut),loadActionDelegate:async()=>(await ye()).MainImpl.ZoomActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Minus",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+Minus"},{platform:"windows,linux",shortcut:"Ctrl+NumpadMinus"},{platform:"windows,linux",shortcut:"Ctrl+Shift+NumpadMinus"},{platform:"mac",shortcut:"Meta+Minus",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+Minus"},{platform:"mac",shortcut:"Meta+NumpadMinus"},{platform:"mac",shortcut:"Meta+Shift+NumpadMinus"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.zoom-reset",category:c.ActionRegistration.ActionCategory.GLOBAL,title:Se(de.resetZoomLevel),loadActionDelegate:async()=>(await ye()).MainImpl.ZoomActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+0"},{platform:"windows,linux",shortcut:"Ctrl+Numpad0"},{platform:"mac",shortcut:"Meta+Numpad0"},{platform:"mac",shortcut:"Meta+0"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.find",category:c.ActionRegistration.ActionCategory.GLOBAL,title:Se(de.searchInPanel),loadActionDelegate:async()=>(await ye()).MainImpl.SearchActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"F3"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.cancel",category:c.ActionRegistration.ActionCategory.GLOBAL,title:Se(de.cancelSearch),loadActionDelegate:async()=>(await ye()).MainImpl.SearchActionDelegate.instance(),order:10,bindings:[{shortcut:"Esc"}]}),c.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.find-next",category:c.ActionRegistration.ActionCategory.GLOBAL,title:Se(de.findNextResult),loadActionDelegate:async()=>(await ye()).MainImpl.SearchActionDelegate.instance(),bindings:[{platform:"mac",shortcut:"Meta+G",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+G"},{platform:"windows,linux",shortcut:"F3",keybindSets:["devToolsDefault","vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"main.search-in-panel.find-previous",category:c.ActionRegistration.ActionCategory.GLOBAL,title:Se(de.findPreviousResult),loadActionDelegate:async()=>(await ye()).MainImpl.SearchActionDelegate.instance(),bindings:[{platform:"mac",shortcut:"Meta+Shift+G",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+G"},{platform:"windows,linux",shortcut:"Shift+F3",keybindSets:["devToolsDefault","vsCode"]}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.APPEARANCE,storageType:t.Settings.SettingStorageType.Synced,title:Se(de.theme),settingName:"uiTheme",settingType:t.Settings.SettingType.ENUM,defaultValue:"systemPreferred",reloadRequired:!1,options:[{title:Se(de.switchToSystemPreferredColor),text:Se(de.systemPreference),value:"systemPreferred"},{title:Se(de.switchToLightTheme),text:Se(de.lightCapital),value:"default"},{title:Se(de.switchToDarkTheme),text:Se(de.darkCapital),value:"dark"}],tags:[Se(de.darkLower),Se(de.lightLower)]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.APPEARANCE,storageType:t.Settings.SettingStorageType.Synced,title:Se(de.panelLayout),settingName:"sidebarPosition",settingType:t.Settings.SettingType.ENUM,defaultValue:"auto",options:[{title:Se(de.useHorizontalPanelLayout),text:Se(de.horizontal),value:"bottom"},{title:Se(de.useVerticalPanelLayout),text:Se(de.vertical),value:"right"},{title:Se(de.useAutomaticPanelLayout),text:Se(de.auto),value:"auto"}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.APPEARANCE,storageType:t.Settings.SettingStorageType.Synced,title:Se(de.colorFormat),settingName:"colorFormat",settingType:t.Settings.SettingType.ENUM,defaultValue:"original",options:[{title:Se(de.setColorFormatAsAuthored),text:Se(de.asAuthored),value:"original"},{title:Se(de.setColorFormatToHex),text:"HEX: #dac0de",value:"hex",raw:!0},{title:Se(de.setColorFormatToRgb),text:"RGB: rgb(128 255 255)",value:"rgb",raw:!0},{title:Se(de.setColorFormatToHsl),text:"HSL: hsl(300deg 80% 90%)",value:"hsl",raw:!0}],deprecationNotice:{disabled:!0,warning:Se(de.colorFormatSettingDisabled),experiment:e.Runtime.ExperimentName.DISABLE_COLOR_FORMAT_SETTING}}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.APPEARANCE,storageType:t.Settings.SettingStorageType.Synced,settingName:"language",settingType:t.Settings.SettingType.ENUM,title:Se(de.language),defaultValue:"en-US",options:[{value:"browserLanguage",title:Se(de.browserLanguage),text:Se(de.browserLanguage)},...i.i18n.getAllSupportedDevToolsLocales().sort().map((e=>{return{value:t=e,title:we(t),text:we(t)};var t}))],reloadRequired:!0}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.APPEARANCE,storageType:t.Settings.SettingStorageType.Synced,title:Se(de.enableCtrlShortcutToSwitchPanels),titleMac:Se(de.enableShortcutToSwitchPanels),settingName:"shortcutPanelSwitch",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.GLOBAL,settingName:"currentDockState",settingType:t.Settings.SettingType.ENUM,defaultValue:"right",options:[{value:"right",text:Se(de.right),title:Se(de.dockToRight)},{value:"bottom",text:Se(de.bottom),title:Se(de.dockToBottom)},{value:"left",text:Se(de.left),title:Se(de.dockToLeft)},{value:"undocked",text:Se(de.undocked),title:Se(de.undockIntoSeparateWindow)}]}),t.Settings.registerSettingExtension({storageType:t.Settings.SettingStorageType.Synced,settingName:"activeKeybindSet",settingType:t.Settings.SettingType.ENUM,defaultValue:"devToolsDefault",options:[{value:"devToolsDefault",title:Se(de.devtoolsDefault),text:Se(de.devtoolsDefault)},{value:"vsCode",title:i.i18n.lockedLazyString("Visual Studio Code"),text:i.i18n.lockedLazyString("Visual Studio Code")}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SYNC,settingName:"sync_preferences",settingType:t.Settings.SettingType.BOOLEAN,title:Se(de.enableSync),defaultValue:!1,reloadRequired:!0}),t.Settings.registerSettingExtension({storageType:t.Settings.SettingStorageType.Synced,settingName:"userShortcuts",settingType:t.Settings.SettingType.ARRAY,defaultValue:[]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.GLOBAL,storageType:t.Settings.SettingStorageType.Local,title:Se(de.searchAsYouTypeSetting),settingName:"searchAsYouType",settingType:t.Settings.SettingType.BOOLEAN,order:3,defaultValue:!0,options:[{value:!0,title:Se(de.searchAsYouTypeCommand)},{value:!1,title:Se(de.searchOnEnterCommand)}]}),c.ViewManager.registerLocationResolver({name:"drawer-view",category:c.ViewManager.ViewLocationCategory.DRAWER,loadResolver:async()=>c.InspectorView.InspectorView.instance()}),c.ViewManager.registerLocationResolver({name:"drawer-sidebar",category:c.ViewManager.ViewLocationCategory.DRAWER_SIDEBAR,loadResolver:async()=>c.InspectorView.InspectorView.instance()}),c.ViewManager.registerLocationResolver({name:"panel",category:c.ViewManager.ViewLocationCategory.PANEL,loadResolver:async()=>c.InspectorView.InspectorView.instance()}),c.ContextMenu.registerProvider({contextTypes:()=>[s.UISourceCode.UISourceCode,n.Resource.Resource,n.NetworkRequest.NetworkRequest],loadProvider:async()=>d.Linkifier.ContentProviderContextMenuProvider.instance(),experiment:void 0}),c.ContextMenu.registerProvider({contextTypes:()=>[Node],loadProvider:async()=>c.XLink.ContextMenuProvider.instance(),experiment:void 0}),c.ContextMenu.registerProvider({contextTypes:()=>[Node],loadProvider:async()=>d.Linkifier.LinkContextMenuProvider.instance(),experiment:void 0}),c.Toolbar.registerToolbarItem({separator:!0,location:c.Toolbar.ToolbarItemLocation.MAIN_TOOLBAR_LEFT,order:100,showLabel:void 0,actionId:void 0,condition:void 0,loadItem:void 0}),c.Toolbar.registerToolbarItem({separator:!0,order:97,location:c.Toolbar.ToolbarItemLocation.MAIN_TOOLBAR_RIGHT,showLabel:void 0,actionId:void 0,condition:void 0,loadItem:void 0}),c.Toolbar.registerToolbarItem({loadItem:async()=>(await ye()).MainImpl.SettingsButtonProvider.instance(),order:99,location:c.Toolbar.ToolbarItemLocation.MAIN_TOOLBAR_RIGHT,showLabel:void 0,condition:void 0,separator:void 0,actionId:void 0}),c.Toolbar.registerToolbarItem({loadItem:async()=>(await ye()).MainImpl.MainMenuItem.instance(),order:100,location:c.Toolbar.ToolbarItemLocation.MAIN_TOOLBAR_RIGHT,showLabel:void 0,condition:void 0,separator:void 0,actionId:void 0}),c.Toolbar.registerToolbarItem({loadItem:async()=>c.DockController.CloseButtonProvider.instance(),order:101,location:c.Toolbar.ToolbarItemLocation.MAIN_TOOLBAR_RIGHT,showLabel:void 0,condition:void 0,separator:void 0,actionId:void 0}),t.AppProvider.registerAppProvider({loadAppProvider:async()=>(await ye()).SimpleApp.SimpleAppProvider.instance(),order:10,condition:void 0});const he={flamechartMouseWheelAction:"Flamechart mouse wheel action:",scroll:"Scroll",zoom:"Zoom",liveMemoryAllocationAnnotations:"Live memory allocation annotations",showLiveMemoryAllocation:"Show live memory allocation annotations",hideLiveMemoryAllocation:"Hide live memory allocation annotations",collectGarbage:"Collect garbage"},ve=i.i18n.registerUIStrings("ui/legacy/components/perf_ui/perf_ui-meta.ts",he),Ae=i.i18n.getLazilyComputedLocalizedString.bind(void 0,ve);let Ce;c.ActionRegistration.registerActionExtension({actionId:"components.collect-garbage",category:c.ActionRegistration.ActionCategory.PERFORMANCE,title:Ae(he.collectGarbage),iconClass:"bin",loadActionDelegate:async()=>(await async function(){return Ce||(Ce=await import("../../ui/legacy/components/perf_ui/perf_ui.js")),Ce}()).GCActionDelegate.GCActionDelegate.instance()}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.PERFORMANCE,storageType:t.Settings.SettingStorageType.Synced,title:Ae(he.flamechartMouseWheelAction),settingName:"flamechartMouseWheelAction",settingType:t.Settings.SettingType.ENUM,defaultValue:"zoom",options:[{title:Ae(he.scroll),text:Ae(he.scroll),value:"scroll"},{title:Ae(he.zoom),text:Ae(he.zoom),value:"zoom"}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.MEMORY,experiment:e.Runtime.ExperimentName.LIVE_HEAP_PROFILE,title:Ae(he.liveMemoryAllocationAnnotations),settingName:"memoryLiveHeapProfile",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:Ae(he.showLiveMemoryAllocation)},{value:!1,title:Ae(he.hideLiveMemoryAllocation)}]});const Ee={openFile:"Open file",runCommand:"Run command"},Te=i.i18n.registerUIStrings("ui/legacy/components/quick_open/quick_open-meta.ts",Ee),be=i.i18n.getLazilyComputedLocalizedString.bind(void 0,Te);let fe;async function Re(){return fe||(fe=await import("../../ui/legacy/components/quick_open/quick_open.js")),fe}c.ActionRegistration.registerActionExtension({actionId:"commandMenu.show",category:c.ActionRegistration.ActionCategory.GLOBAL,title:be(Ee.runCommand),loadActionDelegate:async()=>(await Re()).CommandMenu.ShowActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+P",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+P",keybindSets:["devToolsDefault","vsCode"]},{shortcut:"F1",keybindSets:["vsCode"]}]}),c.ActionRegistration.registerActionExtension({actionId:"quickOpen.show",category:c.ActionRegistration.ActionCategory.GLOBAL,title:be(Ee.openFile),loadActionDelegate:async()=>(await Re()).QuickOpen.ShowActionDelegate.instance(),order:100,bindings:[{platform:"mac",shortcut:"Meta+P",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+O",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+P",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+O",keybindSets:["devToolsDefault","vsCode"]}]}),c.ContextMenu.registerItem({location:c.ContextMenu.ItemLocation.MAIN_MENU_DEFAULT,actionId:"commandMenu.show",order:void 0}),c.ContextMenu.registerItem({location:c.ContextMenu.ItemLocation.MAIN_MENU_DEFAULT,actionId:"quickOpen.show",order:void 0});const xe={preserveLogUponNavigation:"Preserve log upon navigation",doNotPreserveLogUponNavigation:"Do not preserve log upon navigation",pauseOnExceptions:"Pause on exceptions",doNotPauseOnExceptions:"Do not pause on exceptions",disableJavascript:"Disable JavaScript",enableJavascript:"Enable JavaScript",disableAsyncStackTraces:"Disable async stack traces",doNotCaptureAsyncStackTraces:"Do not capture async stack traces",captureAsyncStackTraces:"Capture async stack traces",showRulersOnHover:"Show rulers on hover",doNotShowRulersOnHover:"Do not show rulers on hover",showAreaNames:"Show area names",showGridNamedAreas:"Show grid named areas",doNotShowGridNamedAreas:"Do not show grid named areas",showTrackSizes:"Show track sizes",showGridTrackSizes:"Show grid track sizes",doNotShowGridTrackSizes:"Do not show grid track sizes",extendGridLines:"Extend grid lines",doNotExtendGridLines:"Do not extend grid lines",showLineLabels:"Show line labels",hideLineLabels:"Hide line labels",showLineNumbers:"Show line numbers",showLineNames:"Show line names",showPaintFlashingRectangles:"Show paint flashing rectangles",hidePaintFlashingRectangles:"Hide paint flashing rectangles",showLayoutShiftRegions:"Show layout shift regions",hideLayoutShiftRegions:"Hide layout shift regions",highlightAdFrames:"Highlight ad frames",doNotHighlightAdFrames:"Do not highlight ad frames",showLayerBorders:"Show layer borders",hideLayerBorders:"Hide layer borders",showCoreWebVitalsOverlay:"Show Core Web Vitals overlay",hideCoreWebVitalsOverlay:"Hide Core Web Vitals overlay",showFramesPerSecondFpsMeter:"Show frames per second (FPS) meter",hideFramesPerSecondFpsMeter:"Hide frames per second (FPS) meter",showScrollPerformanceBottlenecks:"Show scroll performance bottlenecks",hideScrollPerformanceBottlenecks:"Hide scroll performance bottlenecks",emulateAFocusedPage:"Emulate a focused page",doNotEmulateAFocusedPage:"Do not emulate a focused page",doNotEmulateCssMediaType:"Do not emulate CSS media type",noEmulation:"No emulation",emulateCssPrintMediaType:"Emulate CSS print media type",print:"print",emulateCssScreenMediaType:"Emulate CSS screen media type",screen:"screen",query:"query",emulateCssMediaType:"Emulate CSS media type",doNotEmulateCss:"Do not emulate CSS {PH1}",emulateCss:"Emulate CSS {PH1}",emulateCssMediaFeature:"Emulate CSS media feature {PH1}",doNotEmulateAnyVisionDeficiency:"Do not emulate any vision deficiency",emulateBlurredVision:"Emulate blurred vision",emulateReducedContrast:"Emulate reduced contrast",blurredVision:"Blurred vision",reducedContrast:"Reduced contrast",emulateProtanopia:"Emulate protanopia (no red)",protanopia:"Protanopia (no red)",emulateDeuteranopia:"Emulate deuteranopia (no green)",deuteranopia:"Deuteranopia (no green)",emulateTritanopia:"Emulate tritanopia (no blue)",tritanopia:"Tritanopia (no blue)",emulateAchromatopsia:"Emulate achromatopsia (no color)",achromatopsia:"Achromatopsia (no color)",emulateVisionDeficiencies:"Emulate vision deficiencies",disableLocalFonts:"Disable local fonts",enableLocalFonts:"Enable local fonts",disableAvifFormat:"Disable `AVIF` format",enableAvifFormat:"Enable `AVIF` format",disableWebpFormat:"Disable `WebP` format",enableWebpFormat:"Enable `WebP` format",enableCustomFormatters:"Enable custom formatters",enableNetworkRequestBlocking:"Enable network request blocking",disableNetworkRequestBlocking:"Disable network request blocking",enableCache:"Enable cache",disableCache:"Disable cache (while DevTools is open)",emulateAutoDarkMode:"Emulate auto dark mode",enableRemoteFileLoading:"Allow `DevTools` to load resources, such as source maps, from remote file paths. Disabled by default for security reasons."},Ne=i.i18n.registerUIStrings("core/sdk/sdk-meta.ts",xe),De=i.i18n.getLazilyComputedLocalizedString.bind(void 0,Ne);t.Settings.registerSettingExtension({storageType:t.Settings.SettingStorageType.Synced,settingName:"skipStackFramesPattern",settingType:t.Settings.SettingType.REGEX,defaultValue:""}),t.Settings.registerSettingExtension({storageType:t.Settings.SettingStorageType.Synced,settingName:"skipContentScripts",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0}),t.Settings.registerSettingExtension({storageType:t.Settings.SettingStorageType.Synced,settingName:"automaticallyIgnoreListKnownThirdPartyScripts",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0}),t.Settings.registerSettingExtension({storageType:t.Settings.SettingStorageType.Synced,settingName:"enableIgnoreListing",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!0}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,storageType:t.Settings.SettingStorageType.Synced,title:De(xe.preserveLogUponNavigation),settingName:"preserveConsoleLog",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:De(xe.preserveLogUponNavigation)},{value:!1,title:De(xe.doNotPreserveLogUponNavigation)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.DEBUGGER,settingName:"pauseOnExceptionEnabled",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,options:[{value:!0,title:De(xe.pauseOnExceptions)},{value:!1,title:De(xe.doNotPauseOnExceptions)}]}),t.Settings.registerSettingExtension({settingName:"pauseOnCaughtException",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1}),t.Settings.registerSettingExtension({settingName:"pauseOnUncaughtException",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.DEBUGGER,title:De(xe.disableJavascript),settingName:"javaScriptDisabled",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,order:1,defaultValue:!1,options:[{value:!0,title:De(xe.disableJavascript)},{value:!1,title:De(xe.enableJavascript)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.DEBUGGER,title:De(xe.disableAsyncStackTraces),settingName:"disableAsyncStackTraces",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1,order:2,options:[{value:!0,title:De(xe.doNotCaptureAsyncStackTraces)},{value:!1,title:De(xe.captureAsyncStackTraces)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.DEBUGGER,settingName:"breakpointsActive",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,defaultValue:!0}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.ELEMENTS,storageType:t.Settings.SettingStorageType.Synced,title:De(xe.showRulersOnHover),settingName:"showMetricsRulers",settingType:t.Settings.SettingType.BOOLEAN,options:[{value:!0,title:De(xe.showRulersOnHover)},{value:!1,title:De(xe.doNotShowRulersOnHover)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.GRID,storageType:t.Settings.SettingStorageType.Synced,title:De(xe.showAreaNames),settingName:"showGridAreas",settingType:t.Settings.SettingType.BOOLEAN,options:[{value:!0,title:De(xe.showGridNamedAreas)},{value:!1,title:De(xe.doNotShowGridNamedAreas)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.GRID,storageType:t.Settings.SettingStorageType.Synced,title:De(xe.showTrackSizes),settingName:"showGridTrackSizes",settingType:t.Settings.SettingType.BOOLEAN,options:[{value:!0,title:De(xe.showGridTrackSizes)},{value:!1,title:De(xe.doNotShowGridTrackSizes)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.GRID,storageType:t.Settings.SettingStorageType.Synced,title:De(xe.extendGridLines),settingName:"extendGridLines",settingType:t.Settings.SettingType.BOOLEAN,options:[{value:!0,title:De(xe.extendGridLines)},{value:!1,title:De(xe.doNotExtendGridLines)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.GRID,storageType:t.Settings.SettingStorageType.Synced,title:De(xe.showLineLabels),settingName:"showGridLineLabels",settingType:t.Settings.SettingType.ENUM,options:[{title:De(xe.hideLineLabels),text:De(xe.hideLineLabels),value:"none"},{title:De(xe.showLineNumbers),text:De(xe.showLineNumbers),value:"lineNumbers"},{title:De(xe.showLineNames),text:De(xe.showLineNames),value:"lineNames"}],defaultValue:"lineNumbers"}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"showPaintRects",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,options:[{value:!0,title:De(xe.showPaintFlashingRectangles)},{value:!1,title:De(xe.hidePaintFlashingRectangles)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"showLayoutShiftRegions",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,options:[{value:!0,title:De(xe.showLayoutShiftRegions)},{value:!1,title:De(xe.hideLayoutShiftRegions)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"showAdHighlights",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,options:[{value:!0,title:De(xe.highlightAdFrames)},{value:!1,title:De(xe.doNotHighlightAdFrames)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"showDebugBorders",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,options:[{value:!0,title:De(xe.showLayerBorders)},{value:!1,title:De(xe.hideLayerBorders)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"showWebVitals",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,options:[{value:!0,title:De(xe.showCoreWebVitalsOverlay)},{value:!1,title:De(xe.hideCoreWebVitalsOverlay)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"showFPSCounter",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,options:[{value:!0,title:De(xe.showFramesPerSecondFpsMeter)},{value:!1,title:De(xe.hideFramesPerSecondFpsMeter)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"showScrollBottleneckRects",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,options:[{value:!0,title:De(xe.showScrollPerformanceBottlenecks)},{value:!1,title:De(xe.hideScrollPerformanceBottlenecks)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,title:De(xe.emulateAFocusedPage),settingName:"emulatePageFocus",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,defaultValue:!1,options:[{value:!0,title:De(xe.emulateAFocusedPage)},{value:!1,title:De(xe.doNotEmulateAFocusedPage)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"emulatedCSSMedia",settingType:t.Settings.SettingType.ENUM,storageType:t.Settings.SettingStorageType.Session,defaultValue:"",options:[{title:De(xe.doNotEmulateCssMediaType),text:De(xe.noEmulation),value:""},{title:De(xe.emulateCssPrintMediaType),text:De(xe.print),value:"print"},{title:De(xe.emulateCssScreenMediaType),text:De(xe.screen),value:"screen"}],tags:[De(xe.query)],title:De(xe.emulateCssMediaType)}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"emulatedCSSMediaFeaturePrefersColorScheme",settingType:t.Settings.SettingType.ENUM,storageType:t.Settings.SettingStorageType.Session,defaultValue:"",options:[{title:De(xe.doNotEmulateCss,{PH1:"prefers-color-scheme"}),text:De(xe.noEmulation),value:""},{title:De(xe.emulateCss,{PH1:"prefers-color-scheme: light"}),text:i.i18n.lockedLazyString("prefers-color-scheme: light"),value:"light"},{title:De(xe.emulateCss,{PH1:"prefers-color-scheme: dark"}),text:i.i18n.lockedLazyString("prefers-color-scheme: dark"),value:"dark"}],tags:[De(xe.query)],title:De(xe.emulateCssMediaFeature,{PH1:"prefers-color-scheme"})}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"emulatedCSSMediaFeatureForcedColors",settingType:t.Settings.SettingType.ENUM,storageType:t.Settings.SettingStorageType.Session,defaultValue:"",options:[{title:De(xe.doNotEmulateCss,{PH1:"forced-colors"}),text:De(xe.noEmulation),value:""},{title:De(xe.emulateCss,{PH1:"forced-colors: active"}),text:i.i18n.lockedLazyString("forced-colors: active"),value:"active"},{title:De(xe.emulateCss,{PH1:"forced-colors: none"}),text:i.i18n.lockedLazyString("forced-colors: none"),value:"none"}],tags:[De(xe.query)],title:De(xe.emulateCssMediaFeature,{PH1:"forced-colors"})}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"emulatedCSSMediaFeaturePrefersReducedMotion",settingType:t.Settings.SettingType.ENUM,storageType:t.Settings.SettingStorageType.Session,defaultValue:"",options:[{title:De(xe.doNotEmulateCss,{PH1:"prefers-reduced-motion"}),text:De(xe.noEmulation),value:""},{title:De(xe.emulateCss,{PH1:"prefers-reduced-motion: reduce"}),text:i.i18n.lockedLazyString("prefers-reduced-motion: reduce"),value:"reduce"}],tags:[De(xe.query)],title:De(xe.emulateCssMediaFeature,{PH1:"prefers-reduced-motion"})}),t.Settings.registerSettingExtension({settingName:"emulatedCSSMediaFeaturePrefersContrast",settingType:t.Settings.SettingType.ENUM,storageType:t.Settings.SettingStorageType.Session,defaultValue:"",options:[{title:De(xe.doNotEmulateCss,{PH1:"prefers-contrast"}),text:De(xe.noEmulation),value:""},{title:De(xe.emulateCss,{PH1:"prefers-contrast: more"}),text:i.i18n.lockedLazyString("prefers-contrast: more"),value:"more"},{title:De(xe.emulateCss,{PH1:"prefers-contrast: less"}),text:i.i18n.lockedLazyString("prefers-contrast: less"),value:"less"},{title:De(xe.emulateCss,{PH1:"prefers-contrast: custom"}),text:i.i18n.lockedLazyString("prefers-contrast: custom"),value:"custom"}],tags:[De(xe.query)],title:De(xe.emulateCssMediaFeature,{PH1:"prefers-contrast"})}),t.Settings.registerSettingExtension({settingName:"emulatedCSSMediaFeaturePrefersReducedData",settingType:t.Settings.SettingType.ENUM,storageType:t.Settings.SettingStorageType.Session,defaultValue:"",options:[{title:De(xe.doNotEmulateCss,{PH1:"prefers-reduced-data"}),text:De(xe.noEmulation),value:""},{title:De(xe.emulateCss,{PH1:"prefers-reduced-data: reduce"}),text:i.i18n.lockedLazyString("prefers-reduced-data: reduce"),value:"reduce"}],title:De(xe.emulateCssMediaFeature,{PH1:"prefers-reduced-data"})}),t.Settings.registerSettingExtension({settingName:"emulatedCSSMediaFeatureColorGamut",settingType:t.Settings.SettingType.ENUM,storageType:t.Settings.SettingStorageType.Session,defaultValue:"",options:[{title:De(xe.doNotEmulateCss,{PH1:"color-gamut"}),text:De(xe.noEmulation),value:""},{title:De(xe.emulateCss,{PH1:"color-gamut: srgb"}),text:i.i18n.lockedLazyString("color-gamut: srgb"),value:"srgb"},{title:De(xe.emulateCss,{PH1:"color-gamut: p3"}),text:i.i18n.lockedLazyString("color-gamut: p3"),value:"p3"},{title:De(xe.emulateCss,{PH1:"color-gamut: rec2020"}),text:i.i18n.lockedLazyString("color-gamut: rec2020"),value:"rec2020"}],title:De(xe.emulateCssMediaFeature,{PH1:"color-gamut"})}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"emulatedVisionDeficiency",settingType:t.Settings.SettingType.ENUM,storageType:t.Settings.SettingStorageType.Session,defaultValue:"none",options:[{title:De(xe.doNotEmulateAnyVisionDeficiency),text:De(xe.noEmulation),value:"none"},{title:De(xe.emulateBlurredVision),text:De(xe.blurredVision),value:"blurredVision"},{title:De(xe.emulateReducedContrast),text:De(xe.reducedContrast),value:"reducedContrast"},{title:De(xe.emulateProtanopia),text:De(xe.protanopia),value:"protanopia"},{title:De(xe.emulateDeuteranopia),text:De(xe.deuteranopia),value:"deuteranopia"},{title:De(xe.emulateTritanopia),text:De(xe.tritanopia),value:"tritanopia"},{title:De(xe.emulateAchromatopsia),text:De(xe.achromatopsia),value:"achromatopsia"}],tags:[De(xe.query)],title:De(xe.emulateVisionDeficiencies)}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"localFontsDisabled",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,options:[{value:!0,title:De(xe.disableLocalFonts)},{value:!1,title:De(xe.enableLocalFonts)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"avifFormatDisabled",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,options:[{value:!0,title:De(xe.disableAvifFormat)},{value:!1,title:De(xe.enableAvifFormat)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,settingName:"webpFormatDisabled",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,options:[{value:!0,title:De(xe.disableWebpFormat)},{value:!1,title:De(xe.enableWebpFormat)}],defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.CONSOLE,title:De(xe.enableCustomFormatters),settingName:"customFormatters",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.NETWORK,title:De(xe.enableNetworkRequestBlocking),settingName:"requestBlockingEnabled",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,defaultValue:!1,options:[{value:!0,title:De(xe.enableNetworkRequestBlocking)},{value:!1,title:De(xe.disableNetworkRequestBlocking)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.NETWORK,title:De(xe.disableCache),settingName:"cacheDisabled",settingType:t.Settings.SettingType.BOOLEAN,order:0,defaultValue:!1,userActionCondition:"hasOtherClients",options:[{value:!0,title:De(xe.disableCache)},{value:!1,title:De(xe.enableCache)}]}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.RENDERING,title:De(xe.emulateAutoDarkMode),settingName:"emulateAutoDarkMode",settingType:t.Settings.SettingType.BOOLEAN,storageType:t.Settings.SettingStorageType.Session,defaultValue:!1}),t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:De(xe.enableRemoteFileLoading),settingName:"network.enable-remote-file-loading",settingType:t.Settings.SettingType.BOOLEAN,defaultValue:!1});const Le={defaultIndentation:"Default indentation:",setIndentationToSpaces:"Set indentation to 2 spaces",Spaces:"2 spaces",setIndentationToFSpaces:"Set indentation to 4 spaces",fSpaces:"4 spaces",setIndentationToESpaces:"Set indentation to 8 spaces",eSpaces:"8 spaces",setIndentationToTabCharacter:"Set indentation to tab character",tabCharacter:"Tab character"},Oe=i.i18n.registerUIStrings("ui/legacy/components/source_frame/source_frame-meta.ts",Le),Pe=i.i18n.getLazilyComputedLocalizedString.bind(void 0,Oe);let Ie,Me;t.Settings.registerSettingExtension({category:t.Settings.SettingCategory.SOURCES,storageType:t.Settings.SettingStorageType.Synced,title:Pe(Le.defaultIndentation),settingName:"textEditorIndent",settingType:t.Settings.SettingType.ENUM,defaultValue:"    ",options:[{title:Pe(Le.setIndentationToSpaces),text:Pe(Le.Spaces),value:"  "},{title:Pe(Le.setIndentationToFSpaces),text:Pe(Le.fSpaces),value:"    "},{title:Pe(Le.setIndentationToESpaces),text:Pe(Le.eSpaces),value:"        "},{title:Pe(Le.setIndentationToTabCharacter),text:Pe(Le.tabCharacter),value:"\t"}]}),c.Toolbar.registerToolbarItem({loadItem:async()=>(await async function(){return Ie||(Ie=await import("../../panels/console_counters/console_counters.js")),Ie}()).WarningErrorCounter.WarningErrorCounter.instance(),order:1,location:c.Toolbar.ToolbarItemLocation.MAIN_TOOLBAR_RIGHT,showLabel:void 0,condition:void 0,separator:void 0,actionId:void 0}),c.UIUtils.registerRenderer({contextTypes:()=>[n.RemoteObject.RemoteObject],loadRenderer:async()=>(await async function(){return Me||(Me=await import("../../ui/legacy/components/object_ui/object_ui.js")),Me}()).ObjectPropertiesSection.Renderer.instance()});
