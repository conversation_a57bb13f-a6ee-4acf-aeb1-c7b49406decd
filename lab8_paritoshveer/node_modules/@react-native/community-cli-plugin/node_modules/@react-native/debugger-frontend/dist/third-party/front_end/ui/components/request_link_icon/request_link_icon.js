import*as e from"../../../core/root/root.js";import*as t from"../../../core/i18n/i18n.js";import*as i from"../../../core/common/common.js";import*as r from"../../../panels/network/forward/forward.js";import*as s from"../helpers/helpers.js";import*as o from"../icon_button/icon_button.js";import*as n from"../../lit-html/lit-html.js";import*as a from"../render_coordinator/render_coordinator.js";const l=new CSSStyleSheet;l.replaceSync(":host{display:inline-block;white-space:nowrap;color:inherit;font-size:inherit;font-family:inherit}devtools-icon{vertical-align:middle}.link{cursor:pointer}.link span{color:var(--color-link)}\n/*# sourceURL=requestLinkIcon.css */\n");const h={clickToShowRequestInTheNetwork:"Click to open the network panel and show request for URL: {url}",requestUnavailableInTheNetwork:"Request unavailable in the network panel, try reloading the inspected page",shortenedURL:"Shortened URL"},c=t.i18n.registerUIStrings("ui/components/request_link_icon/RequestLinkIcon.ts",h),d=t.i18n.getLocalizedString.bind(void 0,c),u=e=>(/[^/]+$/.exec(e)||/[^/]+\/$/.exec(e)||[""])[0],q=a.RenderCoordinator.RenderCoordinator.instance();class R extends HTMLElement{static litTagName=n.literal`devtools-request-link-icon`;#e=this.attachShadow({mode:"open"});#t;#i;#r;#s;#o=!1;#n;#a;#l;#h=i.Revealer.reveal;#c=Promise.resolve(void 0);set data(e){this.#t=e.linkToPreflight,this.#i=e.request,e.affectedRequest&&(this.#a={...e.affectedRequest}),this.#r=e.highlightHeader,this.#n=e.networkTab,this.#s=e.requestResolver,this.#o=e.displayURL??!1,this.#l=e.additionalOnClickAction,e.revealOverride&&(this.#h=e.revealOverride),!this.#i&&e.affectedRequest&&(this.#c=this.#d(e.affectedRequest.requestId)),this.#u()}connectedCallback(){this.#e.adoptedStyleSheets=[l]}#d(e){if(!this.#s)throw new Error("A `RequestResolver` must be provided if an `affectedRequest` is provided.");return this.#s.waitFor(e).then((e=>{this.#i=e})).catch((()=>{this.#i=null}))}get data(){return{linkToPreflight:this.#t,request:this.#i,affectedRequest:this.#a,highlightHeader:this.#r,networkTab:this.#n,requestResolver:this.#s,displayURL:this.#o,additionalOnClickAction:this.#l,revealOverride:this.#h!==i.Revealer.reveal?this.#h:void 0}}#q(){return this.#i?"--icon-link":"--icon-no-request"}iconData(){return{iconName:"arrow-up-down-circle",color:`var(${this.#q()})`,width:"16px",height:"16px"}}handleClick(t){if(0!==t.button)return;const i=this.#t?this.#i?.preflightRequest():this.#i;if(i){if(this.#r){const e=r.UIRequestLocation.UIRequestLocation.header(i,this.#r.section,this.#r.name);this.#h(e)}else{const t=e.Runtime.experiments.isEnabled(e.Runtime.ExperimentName.HEADER_OVERRIDES)?r.UIRequestLocation.UIRequestTabs.HeadersComponent:r.UIRequestLocation.UIRequestTabs.Headers,s=r.UIRequestLocation.UIRequestLocation.tab(i,this.#n??t);this.#h(s)}this.#l?.()}}#R(){return this.#i?d(h.clickToShowRequestInTheNetwork,{url:this.#i.url()}):d(h.requestUnavailableInTheNetwork)}#p(){return this.#i?this.#i.url():this.#a?.url}#m(){if(!this.#o)return n.nothing;const e=this.#p();if(!e)return n.nothing;const t=u(e);return n.html`<span aria-label="${d(h.shortenedURL)}" title="${e}">${t}</span>`}#u(){return q.write((()=>{n.render(n.html` ${n.Directives.until(this.#c.then((()=>this.#f())),this.#f())} `,this.#e,{host:this})}))}#f(){return n.html` <span class="${n.Directives.classMap({link:Boolean(this.#i)})}" tabindex="0" @click="${this.handleClick}"> <${o.Icon.Icon.litTagName} .data="${this.iconData()}" title="${this.#R()}"></${o.Icon.Icon.litTagName}> ${this.#m()} </span>`}}s.CustomElements.defineComponent("devtools-request-link-icon",R);var p=Object.freeze({__proto__:null,extractShortPath:u,RequestLinkIcon:R});export{p as RequestLinkIcon};
