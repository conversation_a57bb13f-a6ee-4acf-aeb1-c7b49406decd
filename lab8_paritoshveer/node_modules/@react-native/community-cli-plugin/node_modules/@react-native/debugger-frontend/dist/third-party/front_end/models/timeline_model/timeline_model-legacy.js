import*as e from"./timeline_model.js";self.TimelineModel=self.TimelineModel||{},TimelineModel=TimelineModel||{},TimelineModel.TimelineFrameModel=e.TimelineFrameModel.TimelineFrameModel,TimelineModel.TracingFrameLayerTree=e.TimelineFrameModel.TracingFrameLayerTree,TimelineModel.TimelineFrame=e.TimelineFrameModel.TimelineFrame,TimelineModel.LayerPaintEvent=e.TimelineFrameModel.LayerPaintEvent,TimelineModel.PendingFrame=e.TimelineFrameModel.PendingFrame,TimelineModel.TimelineJSProfileProcessor=e.TimelineJSProfile.TimelineJSProfileProcessor,TimelineModel.TimelineModel=e.TimelineModel.TimelineModelImpl,TimelineModel.TimelineModel.Track=e.TimelineModel.Track,TimelineModel.TimelineModel.TrackType=e.TimelineModel.TrackType,TimelineModel.TimelineModel.RecordType=e.TimelineModel.RecordType,TimelineModel.TimelineModel.PageFrame=e.TimelineModel.PageFrame,TimelineModel.TimelineModel.NetworkRequest=e.TimelineModel.NetworkRequest,TimelineModel.InvalidationTrackingEvent=e.TimelineModel.InvalidationTrackingEvent,TimelineModel.InvalidationTracker=e.TimelineModel.InvalidationTracker,TimelineModel.TimelineAsyncEventTracker=e.TimelineModel.TimelineAsyncEventTracker,TimelineModel.TimelineData=e.TimelineModel.EventOnTimelineData,TimelineModel.TimelineModelFilter=e.TimelineModelFilter.TimelineModelFilter,TimelineModel.TimelineVisibleEventsFilter=e.TimelineModelFilter.TimelineVisibleEventsFilter,TimelineModel.TimelineInvisibleEventsFilter=e.TimelineModelFilter.TimelineInvisibleEventsFilter,TimelineModel.ExclusiveNameFilter=e.TimelineModelFilter.ExclusiveNameFilter,TimelineModel.TimelineProfileTree={},TimelineModel.TimelineProfileTree.Node=e.TimelineProfileTree.Node,TimelineModel.TimelineProfileTree.TopDownNode=e.TimelineProfileTree.TopDownNode,TimelineModel.TimelineProfileTree.TopDownRootNode=e.TimelineProfileTree.TopDownRootNode,TimelineModel.TimelineProfileTree.BottomUpRootNode=e.TimelineProfileTree.BottomUpRootNode,TimelineModel.TimelineProfileTree.GroupNode=e.TimelineProfileTree.GroupNode,TimelineModel.TimelineProfileTree.BottomUpNode=e.TimelineProfileTree.BottomUpNode,TimelineModel.TimelineProfileTree.eventURL=e.TimelineProfileTree.eventURL,TimelineModel.TimelineProfileTree.eventStackFrame=e.TimelineProfileTree.eventStackFrame,TimelineModel.TracingLayerTree=e.TracingLayerTree.TracingLayerTree,TimelineModel.TracingLayer=e.TracingLayerTree.TracingLayer;
