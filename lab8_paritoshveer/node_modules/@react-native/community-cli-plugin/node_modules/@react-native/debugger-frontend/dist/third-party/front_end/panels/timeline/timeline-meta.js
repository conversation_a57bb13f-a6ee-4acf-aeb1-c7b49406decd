import*as e from"../../core/common/common.js";import*as t from"../../core/root/root.js";import*as i from"../../ui/legacy/legacy.js";import*as n from"../../core/i18n/i18n.js";const o={performance:"Performance",showPerformance:"Show Performance",javascriptProfiler:"JavaScript Profiler",showJavascriptProfiler:"Show JavaScript Profiler",record:"Record",stop:"Stop",startProfilingAndReloadPage:"Start profiling and reload page",saveProfile:"Save profile…",loadProfile:"Load profile…",previousFrame:"Previous frame",nextFrame:"Next frame",showRecentTimelineSessions:"Show recent timeline sessions",previousRecording:"Previous recording",nextRecording:"Next recording",hideChromeFrameInLayersView:"Hide `chrome` frame in Layers view",startStopRecording:"Start/stop recording"},a=n.i18n.registerUIStrings("panels/timeline/timeline-meta.ts",o),r=n.i18n.getLazilyComputedLocalizedString.bind(void 0,a);let l,s;async function c(){return l||(l=await import("./timeline.js")),l}async function g(){return s||(s=await import("../profiler/profiler.js")),s}function m(e){return void 0===l?[]:e(l)}i.ViewManager.registerViewExtension({location:"panel",id:"timeline",title:r(o.performance),commandPrompt:r(o.showPerformance),order:50,loadView:async()=>(await c()).TimelinePanel.TimelinePanel.instance()}),i.ViewManager.registerViewExtension({location:"panel",id:"js_profiler",title:r(o.javascriptProfiler),commandPrompt:r(o.showJavascriptProfiler),persistence:"closeable",order:65,experiment:t.Runtime.ExperimentName.JS_PROFILER_TEMP_ENABLE,loadView:async()=>(await g()).ProfilesPanel.JSProfilerPanel.instance()}),i.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:i.ActionRegistration.ActionCategory.PERFORMANCE,iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>m((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>(await c()).TimelinePanel.ActionDelegate.instance(),options:[{value:!0,title:r(o.record)},{value:!1,title:r(o.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>m((e=>[e.TimelinePanel.TimelinePanel])),category:i.ActionRegistration.ActionCategory.PERFORMANCE,title:r(o.startProfilingAndReloadPage),loadActionDelegate:async()=>(await c()).TimelinePanel.ActionDelegate.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]}),i.ActionRegistration.registerActionExtension({category:i.ActionRegistration.ActionCategory.PERFORMANCE,actionId:"timeline.save-to-file",contextTypes:()=>m((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>(await c()).TimelinePanel.ActionDelegate.instance(),title:r(o.saveProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+S"},{platform:"mac",shortcut:"Meta+S"}]}),i.ActionRegistration.registerActionExtension({category:i.ActionRegistration.ActionCategory.PERFORMANCE,actionId:"timeline.load-from-file",contextTypes:()=>m((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>(await c()).TimelinePanel.ActionDelegate.instance(),title:r(o.loadProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+O"},{platform:"mac",shortcut:"Meta+O"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-previous-frame",category:i.ActionRegistration.ActionCategory.PERFORMANCE,title:r(o.previousFrame),contextTypes:()=>m((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>(await c()).TimelinePanel.ActionDelegate.instance(),bindings:[{shortcut:"["}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-next-frame",category:i.ActionRegistration.ActionCategory.PERFORMANCE,title:r(o.nextFrame),contextTypes:()=>m((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>(await c()).TimelinePanel.ActionDelegate.instance(),bindings:[{shortcut:"]"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>(await c()).TimelinePanel.ActionDelegate.instance(),category:i.ActionRegistration.ActionCategory.PERFORMANCE,title:r(o.showRecentTimelineSessions),contextTypes:()=>m((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.previous-recording",category:i.ActionRegistration.ActionCategory.PERFORMANCE,loadActionDelegate:async()=>(await c()).TimelinePanel.ActionDelegate.instance(),title:r(o.previousRecording),contextTypes:()=>m((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Left"},{platform:"mac",shortcut:"Meta+Left"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.next-recording",category:i.ActionRegistration.ActionCategory.PERFORMANCE,loadActionDelegate:async()=>(await c()).TimelinePanel.ActionDelegate.instance(),title:r(o.nextRecording),contextTypes:()=>m((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Right"},{platform:"mac",shortcut:"Meta+Right"}]}),i.ActionRegistration.registerActionExtension({actionId:"profiler.js-toggle-recording",category:i.ActionRegistration.ActionCategory.JAVASCRIPT_PROFILER,title:r(o.startStopRecording),iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes(){return e=e=>[e.ProfilesPanel.JSProfilerPanel],void 0===s?[]:e(s);var e},loadActionDelegate:async()=>(await g()).ProfilesPanel.JSProfilerPanel.instance(),bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),e.Settings.registerSettingExtension({category:e.Settings.SettingCategory.PERFORMANCE,storageType:e.Settings.SettingStorageType.Synced,title:r(o.hideChromeFrameInLayersView),settingName:"frameViewerHideChromeWindow",settingType:e.Settings.SettingType.BOOLEAN,defaultValue:!1}),e.Linkifier.registerLinkifier({contextTypes:()=>m((e=>[e.CLSLinkifier.CLSRect])),loadLinkifier:async()=>(await c()).CLSLinkifier.Linkifier.instance()}),i.ContextMenu.registerItem({location:i.ContextMenu.ItemLocation.TIMELINE_MENU_OPEN,actionId:"timeline.load-from-file",order:10}),i.ContextMenu.registerItem({location:i.ContextMenu.ItemLocation.TIMELINE_MENU_OPEN,actionId:"timeline.save-to-file",order:15});
