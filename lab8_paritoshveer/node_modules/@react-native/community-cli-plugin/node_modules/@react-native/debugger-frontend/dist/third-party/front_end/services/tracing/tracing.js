import*as e from"../../core/sdk/sdk.js";var t=Object.freeze({__proto__:null,PerformanceTracing:class{#e=[];#t=null;#a;constructor(t,a){this.#t=t.model(e.TracingManager.TracingManager),this.#a=a}async start(){if(this.#e.length=0,!this.#t)throw new Error("No tracing manager");const e=["-*","blink.console","blink.user_timing","devtools.timeline","disabled-by-default-devtools.screenshot","disabled-by-default-devtools.timeline","disabled-by-default-devtools.timeline.invalidationTracking","disabled-by-default-devtools.timeline.frame","disabled-by-default-devtools.timeline.stack","disabled-by-default-v8.cpu_profiler","disabled-by-default-v8.cpu_profiler.hires","latencyInfo","loading","disabled-by-default-lighthouse","v8.execute","v8"].join(",");if(!await this.#t.start(this,e,""))throw new Error("Unable to start tracing.")}async stop(){return this.#t?.stop()}getTraceEvents(){return this.#e}traceEventsCollected(e){this.#e.push(...e)}tracingBufferUsage(e){this.#a.tracingBufferUsage(e)}eventsRetrievalProgress(e){this.#a.eventsRetrievalProgress(e)}tracingComplete(){this.#a.tracingComplete(this.#e)}},RawTraceEvents:class{events;constructor(e){this.events=e}getEvents(){return this.events}}});export{t as PerformanceTracing};
