{"version": 3, "names": ["DEFAULT_VERSION", "YARN_VERSION", "bumpYarnVersion", "silent", "root", "yarnVersion", "semver", "parse", "getYarnVersionIfAvailable", "setVersionArgs", "major", "minor", "executeCommand", "e", "logger", "debug", "doesDirectoryExist", "dir", "fs", "existsSync", "getConflictsForDirectory", "directory", "readdirSync", "setProjectDirectory", "replaceDirectory", "directoryExists", "DirectoryAlreadyExistsError", "deleteDirectory", "conflicts", "length", "warnMessage", "chalk", "bold", "conflict", "warn", "replace", "prompt", "type", "name", "message", "removeSync", "mkdirSync", "recursive", "process", "chdir", "error", "CLIError", "cwd", "getTemplateName", "Object", "keys", "JSON", "readFileSync", "path", "join", "dependencies", "setEmptyHashForCachedDependencies", "projectName", "cacheManager", "set", "createHash", "update", "digest", "createFromTemplate", "shouldBumpYarnVersion", "templateUri", "npm", "pm", "projectTitle", "<PERSON><PERSON><PERSON><PERSON>", "packageName", "installCocoaPods", "yarnConfigOptions", "env", "CI", "log", "banner", "didInstallPods", "String", "packageManager", "userAgentPM", "userAgentPackageManager", "removeProjectCache", "projectDirectory", "loader", "<PERSON><PERSON><PERSON><PERSON>", "text", "templateSourceDir", "mkdtempSync", "os", "tmpdir", "start", "installTemplatePackage", "succeed", "templateName", "templateConfig", "getTemplateConfig", "copyTemplate", "templateDir", "changePlaceholderInTemplate", "placeholder<PERSON><PERSON>", "placeholder<PERSON><PERSON><PERSON>", "titlePlaceholder", "postInitScript", "info", "executePostInitScript", "installDependencies", "platform", "installPodsValue", "installPods", "installCocoapods", "reset", "dim", "Error", "fail", "underline", "PackageManager", "installAll", "checkPackageManagerAvailability", "getBunVersionIfAvailable", "getNpmVersionIfAvailable", "createTemplateUri", "options", "version", "isTypescriptTemplate", "template", "platformName", "createProject", "title", "userAgent", "npm_config_user_agent", "startsWith", "initialize", "proj<PERSON><PERSON>", "validateProjectName", "TemplateAndVersionError", "directoryName", "relative", "projectFolder", "shouldCreateGitRepository", "isGitAvailable", "checkGitInstallation", "isFolderGitRepo", "checkIfFolderIsGitRepo", "skipGitInit", "createGitRepository", "printRunInstructions", "showPodsInstructions"], "sources": ["../../../src/commands/init/init.ts"], "sourcesContent": ["import os from 'os';\nimport path from 'path';\nimport fs, {readdirSync} from 'fs-extra';\nimport {validateProjectName} from './validate';\nimport chalk from 'chalk';\nimport printRunInstructions from './printRunInstructions';\nimport {\n  CLIError,\n  logger,\n  getLoader,\n  Loader,\n  cacheManager,\n  prompt,\n} from '@react-native-community/cli-tools';\nimport {installPods} from '@react-native-community/cli-platform-apple';\nimport {\n  installTemplatePackage,\n  getTemplateConfig,\n  copyTemplate,\n  executePostInitScript,\n} from './template';\nimport {changePlaceholderInTemplate} from './editTemplate';\nimport * as PackageManager from '../../tools/packageManager';\nimport banner from './banner';\nimport TemplateAndVersionError from './errors/TemplateAndVersionError';\nimport {getBunVersionIfAvailable} from '../../tools/bun';\nimport {getNpmVersionIfAvailable} from '../../tools/npm';\nimport {getYarnVersionIfAvailable} from '../../tools/yarn';\nimport {createHash} from 'crypto';\nimport {\n  createGitRepository,\n  checkGitInstallation,\n  checkIfFolderIsGitRepo,\n} from './git';\nimport semver from 'semver';\nimport {executeCommand} from '../../tools/executeCommand';\nimport DirectoryAlreadyExistsError from './errors/DirectoryAlreadyExistsError';\n\nconst DEFAULT_VERSION = 'latest';\n\ntype Options = {\n  template?: string;\n  npm?: boolean;\n  pm?: PackageManager.PackageManager;\n  directory?: string;\n  displayName?: string;\n  title?: string;\n  skipInstall?: boolean;\n  version?: string;\n  packageName?: string;\n  installPods?: string | boolean;\n  platformName?: string;\n  skipGitInit?: boolean;\n  replaceDirectory?: string | boolean;\n  yarnConfigOptions?: Record<string, string>;\n};\n\ninterface TemplateOptions {\n  projectName: string;\n  shouldBumpYarnVersion: boolean;\n  templateUri: string;\n  npm?: boolean;\n  pm?: PackageManager.PackageManager;\n  directory: string;\n  projectTitle?: string;\n  skipInstall?: boolean;\n  packageName?: string;\n  installCocoaPods?: string | boolean;\n  version?: string;\n  replaceDirectory?: string | boolean;\n  yarnConfigOptions?: Record<string, string>;\n}\n\ninterface TemplateReturnType {\n  didInstallPods?: boolean;\n  replaceDirectory?: string | boolean;\n}\n\n// Here we are defining explicit version of Yarn to be used in the new project because in some cases providing `3.x` don't work.\nconst YARN_VERSION = '3.6.4';\n\nconst bumpYarnVersion = async (silent: boolean, root: string) => {\n  try {\n    let yarnVersion = semver.parse(getYarnVersionIfAvailable());\n\n    if (yarnVersion) {\n      // `yarn set` is unsupported until 1.22, however it's a alias (yarnpkg/yarn/pull/7862) calling `policies set-version`.\n      const setVersionArgs =\n        yarnVersion.major > 1 && yarnVersion.minor >= 22\n          ? ['set', 'version', YARN_VERSION]\n          : ['policies', 'set-version', YARN_VERSION];\n\n      await executeCommand('yarn', setVersionArgs, {\n        root,\n        silent,\n      });\n\n      // React Native doesn't support PnP, so we need to set nodeLinker to node-modules. Read more here: https://github.com/react-native-community/cli/issues/27#issuecomment-1772626767\n      await executeCommand(\n        'yarn',\n        ['config', 'set', 'nodeLinker', 'node-modules'],\n        {root, silent},\n      );\n    }\n  } catch (e) {\n    logger.debug(e as string);\n  }\n};\n\nfunction doesDirectoryExist(dir: string) {\n  return fs.existsSync(dir);\n}\n\nfunction getConflictsForDirectory(directory: string) {\n  return readdirSync(directory);\n}\n\nasync function setProjectDirectory(\n  directory: string,\n  replaceDirectory: string,\n) {\n  const directoryExists = doesDirectoryExist(directory);\n\n  if (replaceDirectory === 'false' && directoryExists) {\n    throw new DirectoryAlreadyExistsError(directory);\n  }\n\n  let deleteDirectory = false;\n\n  if (replaceDirectory === 'true' && directoryExists) {\n    deleteDirectory = true;\n  } else if (directoryExists) {\n    const conflicts = getConflictsForDirectory(directory);\n\n    if (conflicts.length > 0) {\n      let warnMessage = `The directory ${chalk.bold(\n        directory,\n      )} contains files that will be overwritten:\\n`;\n\n      for (const conflict of conflicts) {\n        warnMessage += `   ${conflict}\\n`;\n      }\n\n      logger.warn(warnMessage);\n\n      const {replace} = await prompt({\n        type: 'confirm',\n        name: 'replace',\n        message: 'Do you want to replace existing files?',\n      });\n\n      deleteDirectory = replace;\n\n      if (!replace) {\n        throw new DirectoryAlreadyExistsError(directory);\n      }\n    }\n  }\n\n  try {\n    if (deleteDirectory) {\n      fs.removeSync(directory);\n    }\n\n    fs.mkdirSync(directory, {recursive: true});\n    process.chdir(directory);\n  } catch (error) {\n    throw new CLIError(\n      'Error occurred while trying to create project directory.',\n      error as Error,\n    );\n  }\n\n  return process.cwd();\n}\n\nfunction getTemplateName(cwd: string) {\n  // We use package manager to infer the name of the template module for us.\n  // That's why we get it from temporary package.json, where the name is the\n  // first and only dependency (hence 0).\n  const name = Object.keys(\n    JSON.parse(fs.readFileSync(path.join(cwd, './package.json'), 'utf8'))\n      .dependencies,\n  )[0];\n  return name;\n}\n\n//set cache to empty string to prevent installing cocoapods on freshly created project\nfunction setEmptyHashForCachedDependencies(projectName: string) {\n  cacheManager.set(\n    projectName,\n    'dependencies',\n    createHash('md5').update('').digest('hex'),\n  );\n}\n\nasync function createFromTemplate({\n  projectName,\n  shouldBumpYarnVersion,\n  templateUri,\n  npm,\n  pm,\n  directory,\n  projectTitle,\n  skipInstall,\n  packageName,\n  installCocoaPods,\n  replaceDirectory,\n  yarnConfigOptions,\n}: TemplateOptions): Promise<TemplateReturnType> {\n  logger.debug('Initializing new project');\n  // Only print out the banner if we're not in a CI\n  if (!process.env.CI) {\n    logger.log(banner);\n  }\n  let didInstallPods = String(installCocoaPods) === 'true';\n  let packageManager = pm;\n\n  if (pm) {\n    packageManager = pm;\n  } else {\n    const userAgentPM = userAgentPackageManager();\n    // if possible, use the package manager from the user agent. Otherwise fallback to default (yarn)\n    packageManager = userAgentPM || 'yarn';\n  }\n\n  if (npm) {\n    logger.warn(\n      'Flag --npm is deprecated and will be removed soon. In the future, please use --pm npm instead.',\n    );\n\n    packageManager = 'npm';\n  }\n\n  // if the project with the name already has cache, remove the cache to avoid problems with pods installation\n  cacheManager.removeProjectCache(projectName);\n\n  const projectDirectory = await setProjectDirectory(\n    directory,\n    String(replaceDirectory),\n  );\n\n  const loader = getLoader({text: 'Downloading template'});\n  const templateSourceDir = fs.mkdtempSync(\n    path.join(os.tmpdir(), 'rncli-init-template-'),\n  );\n\n  try {\n    loader.start();\n\n    await installTemplatePackage(\n      templateUri,\n      templateSourceDir,\n      packageManager,\n      yarnConfigOptions,\n    );\n\n    loader.succeed();\n    loader.start('Copying template');\n\n    const templateName = getTemplateName(templateSourceDir);\n    const templateConfig = getTemplateConfig(templateName, templateSourceDir);\n    await copyTemplate(\n      templateName,\n      templateConfig.templateDir,\n      templateSourceDir,\n    );\n\n    loader.succeed();\n    loader.start('Processing template');\n\n    await changePlaceholderInTemplate({\n      projectName,\n      projectTitle,\n      placeholderName: templateConfig.placeholderName,\n      placeholderTitle: templateConfig.titlePlaceholder,\n      packageName,\n    });\n\n    if (packageManager === 'yarn' && shouldBumpYarnVersion) {\n      await bumpYarnVersion(false, projectDirectory);\n    }\n\n    loader.succeed();\n    const {postInitScript} = templateConfig;\n    if (postInitScript) {\n      loader.info('Executing post init script ');\n      await executePostInitScript(\n        templateName,\n        postInitScript,\n        templateSourceDir,\n      );\n    }\n\n    if (!skipInstall) {\n      await installDependencies({\n        packageManager,\n        loader,\n        root: projectDirectory,\n      });\n\n      if (process.platform === 'darwin') {\n        const installPodsValue = String(installCocoaPods);\n\n        if (installPodsValue === 'true') {\n          didInstallPods = true;\n          await installPods(loader);\n          loader.succeed();\n          setEmptyHashForCachedDependencies(projectName);\n        } else if (installPodsValue === 'undefined') {\n          const {installCocoapods} = await prompt({\n            type: 'confirm',\n            name: 'installCocoapods',\n            message: `Do you want to install CocoaPods now? ${chalk.reset.dim(\n              'Only needed if you run your project in Xcode directly',\n            )}`,\n          });\n          didInstallPods = installCocoapods;\n\n          if (installCocoapods) {\n            await installPods(loader);\n            loader.succeed();\n            setEmptyHashForCachedDependencies(projectName);\n          }\n        }\n      }\n    } else {\n      didInstallPods = false;\n      loader.succeed('Dependencies installation skipped');\n    }\n  } catch (e) {\n    if (e instanceof Error) {\n      logger.error(\n        'Installing pods failed. This doesn\\'t affect project initialization and you can safely proceed. \\nHowever, you will need to install pods manually when running iOS, follow additional steps in \"Run instructions for iOS\" section.\\n',\n      );\n    }\n    loader.fail();\n    didInstallPods = false;\n  } finally {\n    fs.removeSync(templateSourceDir);\n  }\n\n  if (process.platform === 'darwin') {\n    logger.log('\\n');\n    logger.info(\n      `💡 To enable automatic CocoaPods installation when building for iOS you can create react-native.config.js with automaticPodsInstallation field. \\n${chalk.reset.dim(\n        `For more details, see ${chalk.underline(\n          'https://github.com/react-native-community/cli/blob/main/docs/projects.md#projectiosautomaticpodsinstallation',\n        )}`,\n      )}\n            `,\n    );\n  }\n\n  return {didInstallPods};\n}\n\nasync function installDependencies({\n  packageManager,\n  loader,\n  root,\n}: {\n  packageManager: PackageManager.PackageManager;\n  loader: Loader;\n  root: string;\n}) {\n  loader.start('Installing dependencies');\n\n  await PackageManager.installAll({\n    packageManager,\n    silent: true,\n    root,\n  });\n\n  loader.succeed();\n}\n\nfunction checkPackageManagerAvailability(\n  packageManager: PackageManager.PackageManager,\n) {\n  if (packageManager === 'bun') {\n    return getBunVersionIfAvailable();\n  } else if (packageManager === 'npm') {\n    return getNpmVersionIfAvailable();\n  } else if (packageManager === 'yarn') {\n    return getYarnVersionIfAvailable();\n  }\n\n  return false;\n}\n\nfunction createTemplateUri(options: Options, version: string): string {\n  const isTypescriptTemplate =\n    options.template === 'react-native-template-typescript';\n\n  // This allows to correctly retrieve template uri for out of tree platforms.\n  const platform = options.platformName || 'react-native';\n\n  if (isTypescriptTemplate) {\n    logger.warn(\n      \"Ignoring custom template: 'react-native-template-typescript'. Starting from React Native v0.71 TypeScript is used by default.\",\n    );\n    return platform;\n  }\n\n  return options.template || `${platform}@${version}`;\n}\n\nasync function createProject(\n  projectName: string,\n  directory: string,\n  version: string,\n  shouldBumpYarnVersion: boolean,\n  options: Options,\n): Promise<TemplateReturnType> {\n  const templateUri = createTemplateUri(options, version);\n\n  return createFromTemplate({\n    projectName,\n    shouldBumpYarnVersion,\n    templateUri,\n    npm: options.npm,\n    pm: options.pm,\n    directory,\n    projectTitle: options.title,\n    skipInstall: options.skipInstall,\n    packageName: options.packageName,\n    installCocoaPods: options.installPods,\n    version,\n    replaceDirectory: options.replaceDirectory,\n    yarnConfigOptions: options.yarnConfigOptions,\n  });\n}\n\nfunction userAgentPackageManager() {\n  const userAgent = process.env.npm_config_user_agent;\n\n  if (userAgent && userAgent.startsWith('bun')) {\n    return 'bun';\n  }\n\n  return null;\n}\n\nexport default (async function initialize(\n  [projectName]: Array<string>,\n  options: Options,\n) {\n  if (!projectName) {\n    const {projName} = await prompt({\n      type: 'text',\n      name: 'projName',\n      message: 'How would you like to name the app?',\n    });\n    projectName = projName;\n  }\n\n  validateProjectName(projectName);\n\n  if (!!options.template && !!options.version) {\n    throw new TemplateAndVersionError(options.template);\n  }\n\n  const root = process.cwd();\n  const version = options.version || DEFAULT_VERSION;\n\n  const directoryName = path.relative(root, options.directory || projectName);\n  const projectFolder = path.join(root, directoryName);\n\n  if (options.pm && !checkPackageManagerAvailability(options.pm)) {\n    logger.error(\n      'Seems like the package manager you want to use is not installed. Please install it or choose another package manager.',\n    );\n    return;\n  }\n\n  let shouldBumpYarnVersion = true;\n  let shouldCreateGitRepository = false;\n\n  const isGitAvailable = await checkGitInstallation();\n\n  if (isGitAvailable) {\n    const isFolderGitRepo = await checkIfFolderIsGitRepo(projectFolder);\n\n    if (isFolderGitRepo) {\n      shouldBumpYarnVersion = false;\n    } else {\n      shouldCreateGitRepository = true; // Initialize git repo after creating project\n    }\n  } else {\n    logger.warn(\n      'Git is not installed on your system. This might cause some features to work incorrectly.',\n    );\n  }\n\n  const {didInstallPods} = await createProject(\n    projectName,\n    directoryName,\n    version,\n    shouldBumpYarnVersion,\n    options,\n  );\n\n  if (shouldCreateGitRepository && !options.skipGitInit) {\n    await createGitRepository(projectFolder);\n  }\n\n  printRunInstructions(projectFolder, projectName, {\n    showPodsInstructions: !didInstallPods,\n  });\n});\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAKA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAA+E;AAAA;AAAA;AAE/E,MAAMA,eAAe,GAAG,QAAQ;AAwChC;AACA,MAAMC,YAAY,GAAG,OAAO;AAE5B,MAAMC,eAAe,GAAG,OAAOC,MAAe,EAAEC,IAAY,KAAK;EAC/D,IAAI;IACF,IAAIC,WAAW,GAAGC,iBAAM,CAACC,KAAK,CAAC,IAAAC,+BAAyB,GAAE,CAAC;IAE3D,IAAIH,WAAW,EAAE;MACf;MACA,MAAMI,cAAc,GAClBJ,WAAW,CAACK,KAAK,GAAG,CAAC,IAAIL,WAAW,CAACM,KAAK,IAAI,EAAE,GAC5C,CAAC,KAAK,EAAE,SAAS,EAAEV,YAAY,CAAC,GAChC,CAAC,UAAU,EAAE,aAAa,EAAEA,YAAY,CAAC;MAE/C,MAAM,IAAAW,8BAAc,EAAC,MAAM,EAAEH,cAAc,EAAE;QAC3CL,IAAI;QACJD;MACF,CAAC,CAAC;;MAEF;MACA,MAAM,IAAAS,8BAAc,EAClB,MAAM,EACN,CAAC,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,cAAc,CAAC,EAC/C;QAACR,IAAI;QAAED;MAAM,CAAC,CACf;IACH;EACF,CAAC,CAAC,OAAOU,CAAC,EAAE;IACVC,kBAAM,CAACC,KAAK,CAACF,CAAC,CAAW;EAC3B;AACF,CAAC;AAED,SAASG,kBAAkB,CAACC,GAAW,EAAE;EACvC,OAAOC,kBAAE,CAACC,UAAU,CAACF,GAAG,CAAC;AAC3B;AAEA,SAASG,wBAAwB,CAACC,SAAiB,EAAE;EACnD,OAAO,IAAAC,sBAAW,EAACD,SAAS,CAAC;AAC/B;AAEA,eAAeE,mBAAmB,CAChCF,SAAiB,EACjBG,gBAAwB,EACxB;EACA,MAAMC,eAAe,GAAGT,kBAAkB,CAACK,SAAS,CAAC;EAErD,IAAIG,gBAAgB,KAAK,OAAO,IAAIC,eAAe,EAAE;IACnD,MAAM,IAAIC,oCAA2B,CAACL,SAAS,CAAC;EAClD;EAEA,IAAIM,eAAe,GAAG,KAAK;EAE3B,IAAIH,gBAAgB,KAAK,MAAM,IAAIC,eAAe,EAAE;IAClDE,eAAe,GAAG,IAAI;EACxB,CAAC,MAAM,IAAIF,eAAe,EAAE;IAC1B,MAAMG,SAAS,GAAGR,wBAAwB,CAACC,SAAS,CAAC;IAErD,IAAIO,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;MACxB,IAAIC,WAAW,GAAI,iBAAgBC,gBAAK,CAACC,IAAI,CAC3CX,SAAS,CACT,6CAA4C;MAE9C,KAAK,MAAMY,QAAQ,IAAIL,SAAS,EAAE;QAChCE,WAAW,IAAK,MAAKG,QAAS,IAAG;MACnC;MAEAnB,kBAAM,CAACoB,IAAI,CAACJ,WAAW,CAAC;MAExB,MAAM;QAACK;MAAO,CAAC,GAAG,MAAM,IAAAC,kBAAM,EAAC;QAC7BC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,SAAS;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFZ,eAAe,GAAGQ,OAAO;MAEzB,IAAI,CAACA,OAAO,EAAE;QACZ,MAAM,IAAIT,oCAA2B,CAACL,SAAS,CAAC;MAClD;IACF;EACF;EAEA,IAAI;IACF,IAAIM,eAAe,EAAE;MACnBT,kBAAE,CAACsB,UAAU,CAACnB,SAAS,CAAC;IAC1B;IAEAH,kBAAE,CAACuB,SAAS,CAACpB,SAAS,EAAE;MAACqB,SAAS,EAAE;IAAI,CAAC,CAAC;IAC1CC,OAAO,CAACC,KAAK,CAACvB,SAAS,CAAC;EAC1B,CAAC,CAAC,OAAOwB,KAAK,EAAE;IACd,MAAM,KAAIC,oBAAQ,EAChB,0DAA0D,EAC1DD,KAAK,CACN;EACH;EAEA,OAAOF,OAAO,CAACI,GAAG,EAAE;AACtB;AAEA,SAASC,eAAe,CAACD,GAAW,EAAE;EACpC;EACA;EACA;EACA,MAAMT,IAAI,GAAGW,MAAM,CAACC,IAAI,CACtBC,IAAI,CAAC5C,KAAK,CAACW,kBAAE,CAACkC,YAAY,CAACC,eAAI,CAACC,IAAI,CAACP,GAAG,EAAE,gBAAgB,CAAC,EAAE,MAAM,CAAC,CAAC,CAClEQ,YAAY,CAChB,CAAC,CAAC,CAAC;EACJ,OAAOjB,IAAI;AACb;;AAEA;AACA,SAASkB,iCAAiC,CAACC,WAAmB,EAAE;EAC9DC,wBAAY,CAACC,GAAG,CACdF,WAAW,EACX,cAAc,EACd,IAAAG,oBAAU,EAAC,KAAK,CAAC,CAACC,MAAM,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,KAAK,CAAC,CAC3C;AACH;AAEA,eAAeC,kBAAkB,CAAC;EAChCN,WAAW;EACXO,qBAAqB;EACrBC,WAAW;EACXC,GAAG;EACHC,EAAE;EACF9C,SAAS;EACT+C,YAAY;EACZC,WAAW;EACXC,WAAW;EACXC,gBAAgB;EAChB/C,gBAAgB;EAChBgD;AACe,CAAC,EAA+B;EAC/C1D,kBAAM,CAACC,KAAK,CAAC,0BAA0B,CAAC;EACxC;EACA,IAAI,CAAC4B,OAAO,CAAC8B,GAAG,CAACC,EAAE,EAAE;IACnB5D,kBAAM,CAAC6D,GAAG,CAACC,eAAM,CAAC;EACpB;EACA,IAAIC,cAAc,GAAGC,MAAM,CAACP,gBAAgB,CAAC,KAAK,MAAM;EACxD,IAAIQ,cAAc,GAAGZ,EAAE;EAEvB,IAAIA,EAAE,EAAE;IACNY,cAAc,GAAGZ,EAAE;EACrB,CAAC,MAAM;IACL,MAAMa,WAAW,GAAGC,uBAAuB,EAAE;IAC7C;IACAF,cAAc,GAAGC,WAAW,IAAI,MAAM;EACxC;EAEA,IAAId,GAAG,EAAE;IACPpD,kBAAM,CAACoB,IAAI,CACT,gGAAgG,CACjG;IAED6C,cAAc,GAAG,KAAK;EACxB;;EAEA;EACArB,wBAAY,CAACwB,kBAAkB,CAACzB,WAAW,CAAC;EAE5C,MAAM0B,gBAAgB,GAAG,MAAM5D,mBAAmB,CAChDF,SAAS,EACTyD,MAAM,CAACtD,gBAAgB,CAAC,CACzB;EAED,MAAM4D,MAAM,GAAG,IAAAC,qBAAS,EAAC;IAACC,IAAI,EAAE;EAAsB,CAAC,CAAC;EACxD,MAAMC,iBAAiB,GAAGrE,kBAAE,CAACsE,WAAW,CACtCnC,eAAI,CAACC,IAAI,CAACmC,aAAE,CAACC,MAAM,EAAE,EAAE,sBAAsB,CAAC,CAC/C;EAED,IAAI;IACFN,MAAM,CAACO,KAAK,EAAE;IAEd,MAAM,IAAAC,gCAAsB,EAC1B3B,WAAW,EACXsB,iBAAiB,EACjBR,cAAc,EACdP,iBAAiB,CAClB;IAEDY,MAAM,CAACS,OAAO,EAAE;IAChBT,MAAM,CAACO,KAAK,CAAC,kBAAkB,CAAC;IAEhC,MAAMG,YAAY,GAAG9C,eAAe,CAACuC,iBAAiB,CAAC;IACvD,MAAMQ,cAAc,GAAG,IAAAC,2BAAiB,EAACF,YAAY,EAAEP,iBAAiB,CAAC;IACzE,MAAM,IAAAU,sBAAY,EAChBH,YAAY,EACZC,cAAc,CAACG,WAAW,EAC1BX,iBAAiB,CAClB;IAEDH,MAAM,CAACS,OAAO,EAAE;IAChBT,MAAM,CAACO,KAAK,CAAC,qBAAqB,CAAC;IAEnC,MAAM,IAAAQ,yCAA2B,EAAC;MAChC1C,WAAW;MACXW,YAAY;MACZgC,eAAe,EAAEL,cAAc,CAACK,eAAe;MAC/CC,gBAAgB,EAAEN,cAAc,CAACO,gBAAgB;MACjDhC;IACF,CAAC,CAAC;IAEF,IAAIS,cAAc,KAAK,MAAM,IAAIf,qBAAqB,EAAE;MACtD,MAAM9D,eAAe,CAAC,KAAK,EAAEiF,gBAAgB,CAAC;IAChD;IAEAC,MAAM,CAACS,OAAO,EAAE;IAChB,MAAM;MAACU;IAAc,CAAC,GAAGR,cAAc;IACvC,IAAIQ,cAAc,EAAE;MAClBnB,MAAM,CAACoB,IAAI,CAAC,6BAA6B,CAAC;MAC1C,MAAM,IAAAC,+BAAqB,EACzBX,YAAY,EACZS,cAAc,EACdhB,iBAAiB,CAClB;IACH;IAEA,IAAI,CAAClB,WAAW,EAAE;MAChB,MAAMqC,mBAAmB,CAAC;QACxB3B,cAAc;QACdK,MAAM;QACNhF,IAAI,EAAE+E;MACR,CAAC,CAAC;MAEF,IAAIxC,OAAO,CAACgE,QAAQ,KAAK,QAAQ,EAAE;QACjC,MAAMC,gBAAgB,GAAG9B,MAAM,CAACP,gBAAgB,CAAC;QAEjD,IAAIqC,gBAAgB,KAAK,MAAM,EAAE;UAC/B/B,cAAc,GAAG,IAAI;UACrB,MAAM,IAAAgC,+BAAW,EAACzB,MAAM,CAAC;UACzBA,MAAM,CAACS,OAAO,EAAE;UAChBrC,iCAAiC,CAACC,WAAW,CAAC;QAChD,CAAC,MAAM,IAAImD,gBAAgB,KAAK,WAAW,EAAE;UAC3C,MAAM;YAACE;UAAgB,CAAC,GAAG,MAAM,IAAA1E,kBAAM,EAAC;YACtCC,IAAI,EAAE,SAAS;YACfC,IAAI,EAAE,kBAAkB;YACxBC,OAAO,EAAG,yCAAwCR,gBAAK,CAACgF,KAAK,CAACC,GAAG,CAC/D,uDAAuD,CACvD;UACJ,CAAC,CAAC;UACFnC,cAAc,GAAGiC,gBAAgB;UAEjC,IAAIA,gBAAgB,EAAE;YACpB,MAAM,IAAAD,+BAAW,EAACzB,MAAM,CAAC;YACzBA,MAAM,CAACS,OAAO,EAAE;YAChBrC,iCAAiC,CAACC,WAAW,CAAC;UAChD;QACF;MACF;IACF,CAAC,MAAM;MACLoB,cAAc,GAAG,KAAK;MACtBO,MAAM,CAACS,OAAO,CAAC,mCAAmC,CAAC;IACrD;EACF,CAAC,CAAC,OAAOhF,CAAC,EAAE;IACV,IAAIA,CAAC,YAAYoG,KAAK,EAAE;MACtBnG,kBAAM,CAAC+B,KAAK,CACV,sOAAsO,CACvO;IACH;IACAuC,MAAM,CAAC8B,IAAI,EAAE;IACbrC,cAAc,GAAG,KAAK;EACxB,CAAC,SAAS;IACR3D,kBAAE,CAACsB,UAAU,CAAC+C,iBAAiB,CAAC;EAClC;EAEA,IAAI5C,OAAO,CAACgE,QAAQ,KAAK,QAAQ,EAAE;IACjC7F,kBAAM,CAAC6D,GAAG,CAAC,IAAI,CAAC;IAChB7D,kBAAM,CAAC0F,IAAI,CACR,qJAAoJzE,gBAAK,CAACgF,KAAK,CAACC,GAAG,CACjK,yBAAwBjF,gBAAK,CAACoF,SAAS,CACtC,8GAA8G,CAC9G,EAAC,CACH;AACR,aAAa,CACR;EACH;EAEA,OAAO;IAACtC;EAAc,CAAC;AACzB;AAEA,eAAe6B,mBAAmB,CAAC;EACjC3B,cAAc;EACdK,MAAM;EACNhF;AAKF,CAAC,EAAE;EACDgF,MAAM,CAACO,KAAK,CAAC,yBAAyB,CAAC;EAEvC,MAAMyB,cAAc,CAACC,UAAU,CAAC;IAC9BtC,cAAc;IACd5E,MAAM,EAAE,IAAI;IACZC;EACF,CAAC,CAAC;EAEFgF,MAAM,CAACS,OAAO,EAAE;AAClB;AAEA,SAASyB,+BAA+B,CACtCvC,cAA6C,EAC7C;EACA,IAAIA,cAAc,KAAK,KAAK,EAAE;IAC5B,OAAO,IAAAwC,6BAAwB,GAAE;EACnC,CAAC,MAAM,IAAIxC,cAAc,KAAK,KAAK,EAAE;IACnC,OAAO,IAAAyC,6BAAwB,GAAE;EACnC,CAAC,MAAM,IAAIzC,cAAc,KAAK,MAAM,EAAE;IACpC,OAAO,IAAAvE,+BAAyB,GAAE;EACpC;EAEA,OAAO,KAAK;AACd;AAEA,SAASiH,iBAAiB,CAACC,OAAgB,EAAEC,OAAe,EAAU;EACpE,MAAMC,oBAAoB,GACxBF,OAAO,CAACG,QAAQ,KAAK,kCAAkC;;EAEzD;EACA,MAAMlB,QAAQ,GAAGe,OAAO,CAACI,YAAY,IAAI,cAAc;EAEvD,IAAIF,oBAAoB,EAAE;IACxB9G,kBAAM,CAACoB,IAAI,CACT,+HAA+H,CAChI;IACD,OAAOyE,QAAQ;EACjB;EAEA,OAAOe,OAAO,CAACG,QAAQ,IAAK,GAAElB,QAAS,IAAGgB,OAAQ,EAAC;AACrD;AAEA,eAAeI,aAAa,CAC1BtE,WAAmB,EACnBpC,SAAiB,EACjBsG,OAAe,EACf3D,qBAA8B,EAC9B0D,OAAgB,EACa;EAC7B,MAAMzD,WAAW,GAAGwD,iBAAiB,CAACC,OAAO,EAAEC,OAAO,CAAC;EAEvD,OAAO5D,kBAAkB,CAAC;IACxBN,WAAW;IACXO,qBAAqB;IACrBC,WAAW;IACXC,GAAG,EAAEwD,OAAO,CAACxD,GAAG;IAChBC,EAAE,EAAEuD,OAAO,CAACvD,EAAE;IACd9C,SAAS;IACT+C,YAAY,EAAEsD,OAAO,CAACM,KAAK;IAC3B3D,WAAW,EAAEqD,OAAO,CAACrD,WAAW;IAChCC,WAAW,EAAEoD,OAAO,CAACpD,WAAW;IAChCC,gBAAgB,EAAEmD,OAAO,CAACb,WAAW;IACrCc,OAAO;IACPnG,gBAAgB,EAAEkG,OAAO,CAAClG,gBAAgB;IAC1CgD,iBAAiB,EAAEkD,OAAO,CAAClD;EAC7B,CAAC,CAAC;AACJ;AAEA,SAASS,uBAAuB,GAAG;EACjC,MAAMgD,SAAS,GAAGtF,OAAO,CAAC8B,GAAG,CAACyD,qBAAqB;EAEnD,IAAID,SAAS,IAAIA,SAAS,CAACE,UAAU,CAAC,KAAK,CAAC,EAAE;IAC5C,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb;AAAC,IAE8BC,UAAU,GAAzB,eAAeA,UAAU,CACvC,CAAC3E,WAAW,CAAgB,EAC5BiE,OAAgB,EAChB;EACA,IAAI,CAACjE,WAAW,EAAE;IAChB,MAAM;MAAC4E;IAAQ,CAAC,GAAG,MAAM,IAAAjG,kBAAM,EAAC;MAC9BC,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE,UAAU;MAChBC,OAAO,EAAE;IACX,CAAC,CAAC;IACFkB,WAAW,GAAG4E,QAAQ;EACxB;EAEA,IAAAC,6BAAmB,EAAC7E,WAAW,CAAC;EAEhC,IAAI,CAAC,CAACiE,OAAO,CAACG,QAAQ,IAAI,CAAC,CAACH,OAAO,CAACC,OAAO,EAAE;IAC3C,MAAM,IAAIY,gCAAuB,CAACb,OAAO,CAACG,QAAQ,CAAC;EACrD;EAEA,MAAMzH,IAAI,GAAGuC,OAAO,CAACI,GAAG,EAAE;EAC1B,MAAM4E,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI3H,eAAe;EAElD,MAAMwI,aAAa,GAAGnF,eAAI,CAACoF,QAAQ,CAACrI,IAAI,EAAEsH,OAAO,CAACrG,SAAS,IAAIoC,WAAW,CAAC;EAC3E,MAAMiF,aAAa,GAAGrF,eAAI,CAACC,IAAI,CAAClD,IAAI,EAAEoI,aAAa,CAAC;EAEpD,IAAId,OAAO,CAACvD,EAAE,IAAI,CAACmD,+BAA+B,CAACI,OAAO,CAACvD,EAAE,CAAC,EAAE;IAC9DrD,kBAAM,CAAC+B,KAAK,CACV,uHAAuH,CACxH;IACD;EACF;EAEA,IAAImB,qBAAqB,GAAG,IAAI;EAChC,IAAI2E,yBAAyB,GAAG,KAAK;EAErC,MAAMC,cAAc,GAAG,MAAM,IAAAC,yBAAoB,GAAE;EAEnD,IAAID,cAAc,EAAE;IAClB,MAAME,eAAe,GAAG,MAAM,IAAAC,2BAAsB,EAACL,aAAa,CAAC;IAEnE,IAAII,eAAe,EAAE;MACnB9E,qBAAqB,GAAG,KAAK;IAC/B,CAAC,MAAM;MACL2E,yBAAyB,GAAG,IAAI,CAAC,CAAC;IACpC;EACF,CAAC,MAAM;IACL7H,kBAAM,CAACoB,IAAI,CACT,0FAA0F,CAC3F;EACH;EAEA,MAAM;IAAC2C;EAAc,CAAC,GAAG,MAAMkD,aAAa,CAC1CtE,WAAW,EACX+E,aAAa,EACbb,OAAO,EACP3D,qBAAqB,EACrB0D,OAAO,CACR;EAED,IAAIiB,yBAAyB,IAAI,CAACjB,OAAO,CAACsB,WAAW,EAAE;IACrD,MAAM,IAAAC,wBAAmB,EAACP,aAAa,CAAC;EAC1C;EAEA,IAAAQ,6BAAoB,EAACR,aAAa,EAAEjF,WAAW,EAAE;IAC/C0F,oBAAoB,EAAE,CAACtE;EACzB,CAAC,CAAC;AACJ,CAAC;AAAA"}