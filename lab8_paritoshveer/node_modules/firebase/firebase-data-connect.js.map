{"version": 3, "file": "firebase-data-connect.js", "sources": ["../util/src/errors.ts", "../component/src/component.ts", "../logger/src/logger.ts", "../data-connect/src/core/version.ts", "../data-connect/src/core/AppCheckTokenProvider.ts", "../data-connect/src/core/error.ts", "../data-connect/src/logger.ts", "../data-connect/src/core/FirebaseAuthProvider.ts", "../data-connect/src/api/Reference.ts", "../data-connect/src/util/encoder.ts", "../data-connect/src/core/QueryManager.ts", "../data-connect/src/util/map.ts", "../data-connect/src/util/url.ts", "../data-connect/src/network/fetch.ts", "../data-connect/src/network/transport/rest.ts", "../data-connect/src/api/Mutation.ts", "../data-connect/src/api/DataConnect.ts", "../data-connect/src/api/query.ts", "../data-connect/src/util/validateArgs.ts", "../data-connect/src/api.browser.ts", "../data-connect/src/register.ts", "../data-connect/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\n\nexport type ErrorMap<ErrorCode extends string> = {\n  readonly [K in ErrorCode]: string;\n};\n\nconst ERROR_NAME = 'FirebaseError';\n\nexport interface StringLike {\n  toString(): string;\n}\n\nexport interface ErrorData {\n  [key: string]: unknown;\n}\n\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nexport class FirebaseError extends Error {\n  /** The custom name for all FirebaseErrors. */\n  readonly name: string = ERROR_NAME;\n\n  constructor(\n    /** The error code for this error. */\n    readonly code: string,\n    message: string,\n    /** Custom data for this error. */\n    public customData?: Record<string, unknown>\n  ) {\n    super(message);\n\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\n\nexport class ErrorFactory<\n  ErrorCode extends string,\n  ErrorParams extends { readonly [K in ErrorCode]?: ErrorData } = {}\n> {\n  constructor(\n    private readonly service: string,\n    private readonly serviceName: string,\n    private readonly errors: ErrorMap<ErrorCode>\n  ) {}\n\n  create<K extends ErrorCode>(\n    code: K,\n    ...data: K extends keyof ErrorParams ? [ErrorParams[K]] : []\n  ): FirebaseError {\n    const customData = (data[0] as ErrorData) || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n\n    return error;\n  }\n}\n\nfunction replaceTemplate(template: string, data: ErrorData): string {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\n\nconst PATTERN = /\\{\\$([^}]+)}/g;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** The semver (www.semver.org) version of the SDK. */\nexport let SDK_VERSION = '';\n\n/**\n * SDK_VERSION should be set before any database instance is created\n * @internal\n */\nexport function setSDKVersion(version: string): void {\n  SDK_VERSION = version;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AppCheckInternalComponentName,\n  AppCheckTokenListener,\n  AppCheckTokenResult,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport { Provider } from '@firebase/component';\n\n/**\n * @internal\n * Abstraction around AppChe<PERSON>'s token fetching capabilities.\n */\nexport class AppCheckTokenProvider {\n  private appCheck?: FirebaseAppCheckInternal;\n  constructor(\n    private appName_: string,\n    private appCheckProvider?: Provider<AppCheckInternalComponentName>\n  ) {\n    this.appCheck = appCheckProvider?.getImmediate({ optional: true });\n    if (!this.appCheck) {\n      void appCheckProvider\n        ?.get()\n        .then(appCheck => (this.appCheck = appCheck))\n        .catch();\n    }\n  }\n\n  getToken(forceRefresh?: boolean): Promise<AppCheckTokenResult> {\n    if (!this.appCheck) {\n      return new Promise<AppCheckTokenResult>((resolve, reject) => {\n        // Support delayed initialization of FirebaseAppCheck. This allows our\n        // customers to initialize the RTDB SDK before initializing Firebase\n        // AppCheck and ensures that all requests are authenticated if a token\n        // becomes available before the timoeout below expires.\n        setTimeout(() => {\n          if (this.appCheck) {\n            this.getToken(forceRefresh).then(resolve, reject);\n          } else {\n            resolve(null);\n          }\n        }, 0);\n      });\n    }\n    return this.appCheck.getToken(forceRefresh);\n  }\n\n  addTokenChangeListener(listener: AppCheckTokenListener): void {\n    void this.appCheckProvider\n      ?.get()\n      .then(appCheck => appCheck.addTokenListener(listener));\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\n\nexport type DataConnectErrorCode =\n  | 'other'\n  | 'already-initialized'\n  | 'not-initialized'\n  | 'not-supported'\n  | 'invalid-argument'\n  | 'partial-error'\n  | 'unauthorized';\n\nexport type Code = DataConnectErrorCode;\n\nexport const Code = {\n  OTHER: 'other' as DataConnectErrorCode,\n  ALREADY_INITIALIZED: 'already-initialized' as DataConnectErrorCode,\n  NOT_INITIALIZED: 'not-initialized' as DataConnect<PERSON><PERSON>r<PERSON>ode,\n  NOT_SUPPORTED: 'not-supported' as DataConnect<PERSON>rrorCode,\n  INVALID_ARGUMENT: 'invalid-argument' as DataConnectErrorCode,\n  PARTIAL_ERROR: 'partial-error' as DataConnectErrorCode,\n  UNAUTHORIZED: 'unauthorized' as DataConnectErrorCode\n};\n\n/** An error returned by a DataConnect operation. */\nexport class DataConnectError extends FirebaseError {\n  /** The stack of the error. */\n  readonly stack?: string;\n\n  /** @hideconstructor */\n  constructor(\n    /**\n     * The backend error code associated with this error.\n     */\n    readonly code: DataConnectErrorCode,\n    /**\n     * A custom error description.\n     */\n    readonly message: string\n  ) {\n    super(code, message);\n\n    // HACK: We write a toString property directly because Error is not a real\n    // class and so inheritance does not work correctly. We could alternatively\n    // do the same \"back-door inheritance\" trick that FirebaseError does.\n    this.toString = () => `${this.name}: [code=${this.code}]: ${this.message}`;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Logger, LogLevelString } from '@firebase/logger';\n\nimport { SDK_VERSION } from './core/version';\n\nconst logger = new Logger('@firebase/data-connect');\nexport function setLogLevel(logLevel: LogLevelString): void {\n  logger.setLogLevel(logLevel);\n}\nexport function logDebug(msg: string): void {\n  logger.debug(`DataConnect (${SDK_VERSION}): ${msg}`);\n}\n\nexport function logError(msg: string): void {\n  logger.error(`DataConnect (${SDK_VERSION}): ${msg}`);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseOptions } from '@firebase/app-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName,\n  FirebaseAuthTokenData\n} from '@firebase/auth-interop-types';\nimport { Provider } from '@firebase/component';\n\nimport { logDebug, logError } from '../logger';\n\n// @internal\nexport interface AuthTokenProvider {\n  getToken(forceRefresh: boolean): Promise<FirebaseAuthTokenData | null>;\n  addTokenChangeListener(listener: AuthTokenListener): void;\n}\nexport type AuthTokenListener = (token: string | null) => void;\n\n// @internal\nexport class FirebaseAuthProvider implements AuthTokenProvider {\n  private _auth: FirebaseAuthInternal;\n  constructor(\n    private _appName: string,\n    private _options: FirebaseOptions,\n    private _authProvider: Provider<FirebaseAuthInternalName>\n  ) {\n    this._auth = _authProvider.getImmediate({ optional: true })!;\n    if (!this._auth) {\n      _authProvider.onInit(auth => (this._auth = auth));\n    }\n  }\n  getToken(forceRefresh: boolean): Promise<FirebaseAuthTokenData | null> {\n    if (!this._auth) {\n      return new Promise((resolve, reject) => {\n        setTimeout(() => {\n          if (this._auth) {\n            this.getToken(forceRefresh).then(resolve, reject);\n          } else {\n            resolve(null);\n          }\n        }, 0);\n      });\n    }\n    return this._auth.getToken(forceRefresh).catch(error => {\n      if (error && error.code === 'auth/token-not-initialized') {\n        logDebug(\n          'Got auth/token-not-initialized error.  Treating as null token.'\n        );\n        return null;\n      } else {\n        logError(\n          'Error received when attempting to retrieve token: ' +\n            JSON.stringify(error)\n        );\n        return Promise.reject(error);\n      }\n    });\n  }\n  addTokenChangeListener(listener: AuthTokenListener): void {\n    this._auth?.addAuthTokenListener(listener);\n  }\n  removeTokenChangeListener(listener: (token: string | null) => void): void {\n    this._authProvider\n      .get()\n      .then(auth => auth.removeAuthTokenListener(listener))\n      .catch(err => logError(err));\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnect, DataConnectOptions } from './DataConnect';\nexport const QUERY_STR = 'query';\nexport const MUTATION_STR = 'mutation';\nexport type ReferenceType = typeof QUERY_STR | typeof MUTATION_STR;\n\nexport const SOURCE_SERVER = 'SERVER';\nexport const SOURCE_CACHE = 'CACHE';\nexport type DataSource = typeof SOURCE_CACHE | typeof SOURCE_SERVER;\n\nexport interface OpResult<Data> {\n  data: Data;\n  source: DataSource;\n  fetchTime: string;\n}\n\nexport interface OperationRef<_Data, Variables> {\n  name: string;\n  variables: Variables;\n  refType: ReferenceType;\n  dataConnect: DataConnect;\n}\n\nexport interface DataConnectResult<Data, Variables> extends OpResult<Data> {\n  ref: OperationRef<Data, Variables>;\n  // future metadata\n}\n\n/**\n * Serialized RefInfo as a result of `QueryResult.toJSON().refInfo`\n */\nexport interface RefInfo<Variables> {\n  name: string;\n  variables: Variables;\n  connectorConfig: DataConnectOptions;\n}\n/**\n * Serialized Ref as a result of `QueryResult.toJSON()`\n */\nexport interface SerializedRef<Data, Variables> extends OpResult<Data> {\n  refInfo: RefInfo<Variables>;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type HmacImpl = (obj: unknown) => string;\nexport let encoderImpl: HmacImpl;\nexport function setEncoder(encoder: HmacImpl): void {\n  encoderImpl = encoder;\n}\nsetEncoder(o => JSON.stringify(o));\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  DataConnectSubscription,\n  OnErrorSubscription,\n  OnResultSubscription,\n  QueryPromise,\n  QueryRef,\n  QueryResult\n} from '../api/query';\nimport {\n  OperationRef,\n  QUERY_STR,\n  OpResult,\n  SerializedRef,\n  SOURCE_SERVER,\n  DataSource,\n  SOURCE_CACHE\n} from '../api/Reference';\nimport { logDebug } from '../logger';\nimport { DataConnectTransport } from '../network';\nimport { encoderImpl } from '../util/encoder';\nimport { setIfNotExists } from '../util/map';\n\nimport { DataConnectError } from './error';\n\ninterface TrackedQuery<Data, Variables> {\n  ref: Omit<OperationRef<Data, Variables>, 'dataConnect'>;\n  subscriptions: Array<DataConnectSubscription<Data, Variables>>;\n  currentCache: OpResult<Data> | null;\n  lastError: DataConnectError | null;\n}\n\nfunction getRefSerializer<Data, Variables>(\n  queryRef: QueryRef<Data, Variables>,\n  data: Data,\n  source: DataSource\n) {\n  return function toJSON(): SerializedRef<Data, Variables> {\n    return {\n      data,\n      refInfo: {\n        name: queryRef.name,\n        variables: queryRef.variables,\n        connectorConfig: {\n          projectId: queryRef.dataConnect.app.options.projectId!,\n          ...queryRef.dataConnect.getSettings()\n        }\n      },\n      fetchTime: Date.now().toLocaleString(),\n      source\n    };\n  };\n}\n\nexport class QueryManager {\n  _queries: Map<string, TrackedQuery<unknown, unknown>>;\n  constructor(private transport: DataConnectTransport) {\n    this._queries = new Map();\n  }\n  track<Data, Variables>(\n    queryName: string,\n    variables: Variables,\n    initialCache?: OpResult<Data>\n  ): TrackedQuery<Data, Variables> {\n    const ref: TrackedQuery<Data, Variables>['ref'] = {\n      name: queryName,\n      variables,\n      refType: QUERY_STR\n    };\n    const key = encoderImpl(ref);\n    const newTrackedQuery: TrackedQuery<Data, Variables> = {\n      ref,\n      subscriptions: [],\n      currentCache: initialCache || null,\n      lastError: null\n    };\n    // @ts-ignore\n    setIfNotExists(this._queries, key, newTrackedQuery);\n    return this._queries.get(key) as TrackedQuery<Data, Variables>;\n  }\n  addSubscription<Data, Variables>(\n    queryRef: OperationRef<Data, Variables>,\n    onResultCallback: OnResultSubscription<Data, Variables>,\n    onErrorCallback?: OnErrorSubscription,\n    initialCache?: OpResult<Data>\n  ): () => void {\n    const key = encoderImpl({\n      name: queryRef.name,\n      variables: queryRef.variables,\n      refType: QUERY_STR\n    });\n    const trackedQuery = this._queries.get(key) as TrackedQuery<\n      Data,\n      Variables\n    >;\n    const subscription = {\n      userCallback: onResultCallback,\n      errCallback: onErrorCallback\n    };\n    const unsubscribe = (): void => {\n      const trackedQuery = this._queries.get(key)!;\n      trackedQuery.subscriptions = trackedQuery.subscriptions.filter(\n        sub => sub !== subscription\n      );\n    };\n    if (initialCache && trackedQuery.currentCache !== initialCache) {\n      logDebug('Initial cache found. Comparing dates.');\n      if (\n        !trackedQuery.currentCache ||\n        (trackedQuery.currentCache &&\n          compareDates(\n            trackedQuery.currentCache.fetchTime,\n            initialCache.fetchTime\n          ))\n      ) {\n        trackedQuery.currentCache = initialCache;\n      }\n    }\n    if (trackedQuery.currentCache !== null) {\n      const cachedData = trackedQuery.currentCache.data;\n      onResultCallback({\n        data: cachedData,\n        source: SOURCE_CACHE,\n        ref: queryRef as QueryRef<Data, Variables>,\n        toJSON: getRefSerializer(\n          queryRef as QueryRef<Data, Variables>,\n          trackedQuery.currentCache.data,\n          SOURCE_CACHE\n        ),\n        fetchTime: trackedQuery.currentCache.fetchTime\n      });\n      if (trackedQuery.lastError !== null && onErrorCallback) {\n        onErrorCallback(undefined);\n      }\n    }\n\n    trackedQuery.subscriptions.push({\n      userCallback: onResultCallback,\n      errCallback: onErrorCallback,\n      unsubscribe\n    });\n    if (!trackedQuery.currentCache) {\n      logDebug(\n        `No cache available for query ${\n          queryRef.name\n        } with variables ${JSON.stringify(\n          queryRef.variables\n        )}. Calling executeQuery.`\n      );\n      const promise = this.executeQuery(queryRef as QueryRef<Data, Variables>);\n      // We want to ignore the error and let subscriptions handle it\n      promise.then(undefined, err => {});\n    }\n    return unsubscribe;\n  }\n  executeQuery<Data, Variables>(\n    queryRef: QueryRef<Data, Variables>\n  ): QueryPromise<Data, Variables> {\n    const key = encoderImpl({\n      name: queryRef.name,\n      variables: queryRef.variables,\n      refType: QUERY_STR\n    });\n    const trackedQuery = this._queries.get(key)!;\n    const result = this.transport.invokeQuery<Data, Variables>(\n      queryRef.name,\n      queryRef.variables\n    );\n    const newR = result.then(\n      res => {\n        const fetchTime = new Date().toString();\n        const result: QueryResult<Data, Variables> = {\n          ...res,\n          source: SOURCE_SERVER,\n          ref: queryRef,\n          toJSON: getRefSerializer(queryRef, res.data, SOURCE_SERVER),\n          fetchTime\n        };\n        trackedQuery.subscriptions.forEach(subscription => {\n          subscription.userCallback(result);\n        });\n        trackedQuery.currentCache = {\n          data: res.data,\n          source: SOURCE_CACHE,\n          fetchTime\n        };\n        return result;\n      },\n      err => {\n        trackedQuery.lastError = err;\n        trackedQuery.subscriptions.forEach(subscription => {\n          if (subscription.errCallback) {\n            subscription.errCallback(err);\n          }\n        });\n        throw err;\n      }\n    );\n\n    return newR;\n  }\n  enableEmulator(host: string, port: number): void {\n    this.transport.useEmulator(host, port);\n  }\n}\nfunction compareDates(str1: string, str2: string): boolean {\n  const date1 = new Date(str1);\n  const date2 = new Date(str2);\n  return date1.getTime() < date2.getTime();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function setIfNotExists<T>(\n  map: Map<string, T>,\n  key: string,\n  val: T\n): void {\n  if (!map.has(key)) {\n    map.set(key, val);\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../api/DataConnect';\nimport { Code, DataConnectError } from '../core/error';\nimport { logError } from '../logger';\n\nexport function urlBuilder(\n  projectConfig: DataConnectOptions,\n  transportOptions: TransportOptions\n): string {\n  const { connector, location, projectId: project, service } = projectConfig;\n  const { host, sslEnabled, port } = transportOptions;\n  const protocol = sslEnabled ? 'https' : 'http';\n  const realHost = host || `firebasedataconnect.googleapis.com`;\n  let baseUrl = `${protocol}://${realHost}`;\n  if (typeof port === 'number') {\n    baseUrl += `:${port}`;\n  } else if (typeof port !== 'undefined') {\n    logError('Port type is of an invalid type');\n    throw new DataConnectError(\n      Code.INVALID_ARGUMENT,\n      'Incorrect type for port passed in!'\n    );\n  }\n  return `${baseUrl}/v1beta/projects/${project}/locations/${location}/services/${service}/connectors/${connector}`;\n}\nexport function addToken(url: string, apiKey?: string): string {\n  if (!apiKey) {\n    return url;\n  }\n  const newUrl = new URL(url);\n  newUrl.searchParams.append('key', apiKey);\n  return newUrl.toString();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Code, DataConnectError } from '../core/error';\nimport { SDK_VERSION } from '../core/version';\nimport { logDebug, logError } from '../logger';\n\nlet connectFetch: typeof fetch | null = globalThis.fetch;\nexport function initializeFetch(fetchImpl: typeof fetch): void {\n  connectFetch = fetchImpl;\n}\nfunction getGoogApiClientValue(_isUsingGen: boolean): string {\n  let str = 'gl-js/ fire/' + SDK_VERSION;\n  if (_isUsingGen) {\n    str += ' web/gen';\n  }\n  return str;\n}\nexport function dcFetch<T, U>(\n  url: string,\n  body: U,\n  { signal }: AbortController,\n  appId: string | null,\n  accessToken: string | null,\n  appCheckToken: string | null,\n  _isUsingGen: boolean\n): Promise<{ data: T; errors: Error[] }> {\n  if (!connectFetch) {\n    throw new DataConnectError(Code.OTHER, 'No Fetch Implementation detected!');\n  }\n  const headers: HeadersInit = {\n    'Content-Type': 'application/json',\n    'X-Goog-Api-Client': getGoogApiClientValue(_isUsingGen)\n  };\n  if (accessToken) {\n    headers['X-Firebase-Auth-Token'] = accessToken;\n  }\n  if (appId) {\n    headers['x-firebase-gmpid'] = appId;\n  }\n  if (appCheckToken) {\n    headers['X-Firebase-AppCheck'] = appCheckToken;\n  }\n  const bodyStr = JSON.stringify(body);\n  logDebug(`Making request out to ${url} with body: ${bodyStr}`);\n\n  return connectFetch(url, {\n    body: bodyStr,\n    method: 'POST',\n    headers,\n    signal\n  })\n    .catch(err => {\n      throw new DataConnectError(\n        Code.OTHER,\n        'Failed to fetch: ' + JSON.stringify(err)\n      );\n    })\n    .then(async response => {\n      let jsonResponse = null;\n      try {\n        jsonResponse = await response.json();\n      } catch (e) {\n        throw new DataConnectError(Code.OTHER, JSON.stringify(e));\n      }\n      const message = getMessage(jsonResponse);\n      if (response.status >= 400) {\n        logError(\n          'Error while performing request: ' + JSON.stringify(jsonResponse)\n        );\n        if (response.status === 401) {\n          throw new DataConnectError(Code.UNAUTHORIZED, message);\n        }\n        throw new DataConnectError(Code.OTHER, message);\n      }\n      return jsonResponse;\n    })\n    .then(res => {\n      if (res.errors && res.errors.length) {\n        const stringified = JSON.stringify(res.errors);\n        logError('DataConnect error while performing request: ' + stringified);\n        throw new DataConnectError(Code.OTHER, stringified);\n      }\n      return res as { data: T; errors: Error[] };\n    });\n}\ninterface MessageObject {\n  message?: string;\n}\nfunction getMessage(obj: MessageObject): string {\n  if ('message' in obj) {\n    return obj.message;\n  }\n  return JSON.stringify(obj);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectOptions, TransportOptions } from '../../api/DataConnect';\nimport { AppCheckTokenProvider } from '../../core/AppCheckTokenProvider';\nimport { DataConnectError, Code } from '../../core/error';\nimport { AuthTokenProvider } from '../../core/FirebaseAuthProvider';\nimport { logDebug } from '../../logger';\nimport { addToken, urlBuilder } from '../../util/url';\nimport { dcFetch } from '../fetch';\n\nimport { DataConnectTransport } from '.';\n\nexport class RESTTransport implements DataConnectTransport {\n  private _host = '';\n  private _port: number | undefined;\n  private _location = 'l';\n  private _connectorName = '';\n  private _secure = true;\n  private _project = 'p';\n  private _serviceName: string;\n  private _accessToken: string | null = null;\n  private _appCheckToken: string | null = null;\n  private _lastToken: string | null = null;\n  constructor(\n    options: DataConnectOptions,\n    private apiKey?: string | undefined,\n    private appId?: string,\n    private authProvider?: AuthTokenProvider | undefined,\n    private appCheckProvider?: AppCheckTokenProvider | undefined,\n    transportOptions?: TransportOptions | undefined,\n    private _isUsingGen = false\n  ) {\n    if (transportOptions) {\n      if (typeof transportOptions.port === 'number') {\n        this._port = transportOptions.port;\n      }\n      if (typeof transportOptions.sslEnabled !== 'undefined') {\n        this._secure = transportOptions.sslEnabled;\n      }\n      this._host = transportOptions.host;\n    }\n    const { location, projectId: project, connector, service } = options;\n    if (location) {\n      this._location = location;\n    }\n    if (project) {\n      this._project = project;\n    }\n    this._serviceName = service;\n    if (!connector) {\n      throw new DataConnectError(\n        Code.INVALID_ARGUMENT,\n        'Connector Name required!'\n      );\n    }\n    this._connectorName = connector;\n    this.authProvider?.addTokenChangeListener(token => {\n      logDebug(`New Token Available: ${token}`);\n      this._accessToken = token;\n    });\n    this.appCheckProvider?.addTokenChangeListener(result => {\n      const { token } = result;\n      logDebug(`New App Check Token Available: ${token}`);\n      this._appCheckToken = token;\n    });\n  }\n  get endpointUrl(): string {\n    return urlBuilder(\n      {\n        connector: this._connectorName,\n        location: this._location,\n        projectId: this._project,\n        service: this._serviceName\n      },\n      { host: this._host, sslEnabled: this._secure, port: this._port }\n    );\n  }\n  useEmulator(host: string, port?: number, isSecure?: boolean): void {\n    this._host = host;\n    if (typeof port === 'number') {\n      this._port = port;\n    }\n    if (typeof isSecure !== 'undefined') {\n      this._secure = isSecure;\n    }\n  }\n  onTokenChanged(newToken: string | null): void {\n    this._accessToken = newToken;\n  }\n\n  async getWithAuth(forceToken = false): Promise<string> {\n    let starterPromise: Promise<string | null> = new Promise(resolve =>\n      resolve(this._accessToken)\n    );\n    if (this.appCheckProvider) {\n      this._appCheckToken = (await this.appCheckProvider.getToken())?.token;\n    }\n    if (this.authProvider) {\n      starterPromise = this.authProvider\n        .getToken(/*forceToken=*/ forceToken)\n        .then(data => {\n          if (!data) {\n            return null;\n          }\n          this._accessToken = data.accessToken;\n          return this._accessToken;\n        });\n    } else {\n      starterPromise = new Promise(resolve => resolve(''));\n    }\n    return starterPromise;\n  }\n\n  _setLastToken(lastToken: string | null): void {\n    this._lastToken = lastToken;\n  }\n\n  withRetry<T>(\n    promiseFactory: () => Promise<{ data: T; errors: Error[] }>,\n    retry = false\n  ): Promise<{ data: T; errors: Error[] }> {\n    let isNewToken = false;\n    return this.getWithAuth(retry)\n      .then(res => {\n        isNewToken = this._lastToken !== res;\n        this._lastToken = res;\n        return res;\n      })\n      .then(promiseFactory)\n      .catch(err => {\n        // Only retry if the result is unauthorized and the last token isn't the same as the new one.\n        if (\n          'code' in err &&\n          err.code === Code.UNAUTHORIZED &&\n          !retry &&\n          isNewToken\n        ) {\n          logDebug('Retrying due to unauthorized');\n          return this.withRetry(promiseFactory, true);\n        }\n        throw err;\n      });\n  }\n\n  // TODO(mtewani): Update U to include shape of body defined in line 13.\n  invokeQuery: <T, U>(\n    queryName: string,\n    body?: U\n  ) => PromiseLike<{ data: T; errors: Error[] }> = <T, U = unknown>(\n    queryName: string,\n    body: U\n  ) => {\n    const abortController = new AbortController();\n    // TODO(mtewani): Update to proper value\n    const withAuth = this.withRetry(() =>\n      dcFetch<T, U>(\n        addToken(`${this.endpointUrl}:executeQuery`, this.apiKey),\n        {\n          name: `projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,\n          operationName: queryName,\n          variables: body\n        } as unknown as U, // TODO(mtewani): This is a patch, fix this.\n        abortController,\n        this.appId,\n        this._accessToken,\n        this._appCheckToken,\n        this._isUsingGen\n      )\n    );\n\n    return {\n      then: withAuth.then.bind(withAuth),\n      catch: withAuth.catch.bind(withAuth)\n    };\n  };\n  invokeMutation: <T, U>(\n    queryName: string,\n    body?: U\n  ) => PromiseLike<{ data: T; errors: Error[] }> = <T, U = unknown>(\n    mutationName: string,\n    body: U\n  ) => {\n    const abortController = new AbortController();\n    const taskResult = this.withRetry(() => {\n      return dcFetch<T, U>(\n        addToken(`${this.endpointUrl}:executeMutation`, this.apiKey),\n        {\n          name: `projects/${this._project}/locations/${this._location}/services/${this._serviceName}/connectors/${this._connectorName}`,\n          operationName: mutationName,\n          variables: body\n        } as unknown as U,\n        abortController,\n        this.appId,\n        this._accessToken,\n        this._appCheckToken,\n        this._isUsingGen\n      );\n    });\n\n    return {\n      then: taskResult.then.bind(taskResult),\n      // catch: taskResult.catch.bind(taskResult),\n      // finally: taskResult.finally.bind(taskResult),\n      cancel: () => abortController.abort()\n    };\n  };\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectTransport } from '../network/transport';\n\nimport { DataConnect } from './DataConnect';\nimport {\n  DataConnectResult,\n  MUTATION_STR,\n  OperationRef,\n  SOURCE_SERVER\n} from './Reference';\n\nexport interface MutationRef<Data, Variables>\n  extends OperationRef<Data, Variables> {\n  refType: typeof MUTATION_STR;\n}\n\n/**\n * Creates a `MutationRef`\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n */\nexport function mutationRef<Data>(\n  dcInstance: DataConnect,\n  mutationName: string\n): MutationRef<Data, undefined>;\n/**\n *\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n * @param variables variables to send with mutation\n */\nexport function mutationRef<Data, Variables>(\n  dcInstance: DataConnect,\n  mutationName: string,\n  variables: Variables\n): MutationRef<Data, Variables>;\n/**\n *\n * @param dcInstance Data Connect instance\n * @param mutationName name of mutation\n * @param variables variables to send with mutation\n * @returns `MutationRef`\n */\nexport function mutationRef<Data, Variables>(\n  dcInstance: DataConnect,\n  mutationName: string,\n  variables?: Variables\n): MutationRef<Data, Variables> {\n  dcInstance.setInitialized();\n  const ref: MutationRef<Data, Variables> = {\n    dataConnect: dcInstance,\n    name: mutationName,\n    refType: MUTATION_STR,\n    variables: variables as Variables\n  };\n  return ref;\n}\n\n/**\n * @internal\n */\nexport class MutationManager {\n  private _inflight: Array<PromiseLike<unknown>> = [];\n  constructor(private _transport: DataConnectTransport) {}\n  executeMutation<Data, Variables>(\n    mutationRef: MutationRef<Data, Variables>\n  ): MutationPromise<Data, Variables> {\n    const result = this._transport.invokeMutation<Data, Variables>(\n      mutationRef.name,\n      mutationRef.variables\n    );\n    const withRefPromise = result.then(res => {\n      const obj: MutationResult<Data, Variables> = {\n        ...res, // Double check that the result is result.data, not just result\n        source: SOURCE_SERVER,\n        ref: mutationRef,\n        fetchTime: Date.now().toLocaleString()\n      };\n      return obj;\n    });\n    this._inflight.push(result);\n    const removePromise = (): Array<PromiseLike<unknown>> =>\n      (this._inflight = this._inflight.filter(promise => promise !== result));\n    result.then(removePromise, removePromise);\n    return withRefPromise;\n  }\n}\n\n/**\n * Mutation Result from `executeMutation`\n */\nexport interface MutationResult<Data, Variables>\n  extends DataConnectResult<Data, Variables> {\n  ref: MutationRef<Data, Variables>;\n}\n/**\n * Mutation return value from `executeMutation`\n */\nexport interface MutationPromise<Data, Variables>\n  extends PromiseLike<MutationResult<Data, Variables>> {\n  // reserved for special actions like cancellation\n}\n\n/**\n * Execute Mutation\n * @param mutationRef mutation to execute\n * @returns `MutationRef`\n */\nexport function executeMutation<Data, Variables>(\n  mutationRef: MutationRef<Data, Variables>\n): MutationPromise<Data, Variables> {\n  return mutationRef.dataConnect._mutationManager.executeMutation(mutationRef);\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  _getProvider,\n  _removeServiceInstance,\n  getApp\n} from '@firebase/app';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { Provider } from '@firebase/component';\n\nimport { AppCheckTokenProvider } from '../core/AppCheckTokenProvider';\nimport { Code, DataConnectError } from '../core/error';\nimport {\n  AuthTokenProvider,\n  FirebaseAuthProvider\n} from '../core/FirebaseAuthProvider';\nimport { QueryManager } from '../core/QueryManager';\nimport { logDebug, logError } from '../logger';\nimport { DataConnectTransport, TransportClass } from '../network';\nimport { RESTTransport } from '../network/transport/rest';\n\nimport { MutationManager } from './Mutation';\n\n/**\n * Connector Config for calling Data Connect backend.\n */\nexport interface ConnectorConfig {\n  location: string;\n  connector: string;\n  service: string;\n}\n\n/**\n * Options to connect to emulator\n */\nexport interface TransportOptions {\n  host: string;\n  sslEnabled?: boolean;\n  port?: number;\n}\n\nconst FIREBASE_DATA_CONNECT_EMULATOR_HOST_VAR =\n  'FIREBASE_DATA_CONNECT_EMULATOR_HOST';\n\n/**\n *\n * @param fullHost\n * @returns TransportOptions\n * @internal\n */\nexport function parseOptions(fullHost: string): TransportOptions {\n  const [protocol, hostName] = fullHost.split('://');\n  const isSecure = protocol === 'https';\n  const [host, portAsString] = hostName.split(':');\n  const port = Number(portAsString);\n  return { host, port, sslEnabled: isSecure };\n}\n/**\n * DataConnectOptions including project id\n */\nexport interface DataConnectOptions extends ConnectorConfig {\n  projectId: string;\n}\n\n/**\n * Class representing Firebase Data Connect\n */\nexport class DataConnect {\n  _queryManager!: QueryManager;\n  _mutationManager!: MutationManager;\n  isEmulator = false;\n  _initialized = false;\n  private _transport!: DataConnectTransport;\n  private _transportClass: TransportClass | undefined;\n  private _transportOptions?: TransportOptions;\n  private _authTokenProvider?: AuthTokenProvider;\n  _isUsingGeneratedSdk: boolean = false;\n  private _appCheckTokenProvider?: AppCheckTokenProvider;\n  // @internal\n  constructor(\n    public readonly app: FirebaseApp,\n    // TODO(mtewani): Replace with _dataConnectOptions in the future\n    private readonly dataConnectOptions: DataConnectOptions,\n    private readonly _authProvider: Provider<FirebaseAuthInternalName>,\n    private readonly _appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    if (typeof process !== 'undefined' && process.env) {\n      const host = process.env[FIREBASE_DATA_CONNECT_EMULATOR_HOST_VAR];\n      if (host) {\n        logDebug('Found custom host. Using emulator');\n        this.isEmulator = true;\n        this._transportOptions = parseOptions(host);\n      }\n    }\n  }\n  // @internal\n  _useGeneratedSdk(): void {\n    if (!this._isUsingGeneratedSdk) {\n      this._isUsingGeneratedSdk = true;\n    }\n  }\n  _delete(): Promise<void> {\n    _removeServiceInstance(\n      this.app,\n      'data-connect',\n      JSON.stringify(this.getSettings())\n    );\n    return Promise.resolve();\n  }\n\n  // @internal\n  getSettings(): ConnectorConfig {\n    const copy = JSON.parse(JSON.stringify(this.dataConnectOptions));\n    delete copy.projectId;\n    return copy;\n  }\n\n  // @internal\n  setInitialized(): void {\n    if (this._initialized) {\n      return;\n    }\n    if (this._transportClass === undefined) {\n      logDebug('transportClass not provided. Defaulting to RESTTransport.');\n      this._transportClass = RESTTransport;\n    }\n\n    if (this._authProvider) {\n      this._authTokenProvider = new FirebaseAuthProvider(\n        this.app.name,\n        this.app.options,\n        this._authProvider\n      );\n    }\n    if (this._appCheckProvider) {\n      this._appCheckTokenProvider = new AppCheckTokenProvider(\n        this.app.name,\n        this._appCheckProvider\n      );\n    }\n\n    this._initialized = true;\n    this._transport = new this._transportClass(\n      this.dataConnectOptions,\n      this.app.options.apiKey,\n      this.app.options.appId,\n      this._authTokenProvider,\n      this._appCheckTokenProvider,\n      undefined,\n      this._isUsingGeneratedSdk\n    );\n    if (this._transportOptions) {\n      this._transport.useEmulator(\n        this._transportOptions.host,\n        this._transportOptions.port,\n        this._transportOptions.sslEnabled\n      );\n    }\n    this._queryManager = new QueryManager(this._transport);\n    this._mutationManager = new MutationManager(this._transport);\n  }\n\n  // @internal\n  enableEmulator(transportOptions: TransportOptions): void {\n    if (this._initialized) {\n      logError('enableEmulator called after initialization');\n      throw new DataConnectError(\n        Code.ALREADY_INITIALIZED,\n        'DataConnect instance already initialized!'\n      );\n    }\n    this._transportOptions = transportOptions;\n    this.isEmulator = true;\n  }\n}\n\n/**\n * Connect to the DataConnect Emulator\n * @param dc Data Connect instance\n * @param host host of emulator server\n * @param port port of emulator server\n * @param sslEnabled use https\n */\nexport function connectDataConnectEmulator(\n  dc: DataConnect,\n  host: string,\n  port?: number,\n  sslEnabled = false\n): void {\n  dc.enableEmulator({ host, port, sslEnabled });\n}\n\n/**\n * Initialize DataConnect instance\n * @param options ConnectorConfig\n */\nexport function getDataConnect(options: ConnectorConfig): DataConnect;\n/**\n * Initialize DataConnect instance\n * @param app FirebaseApp to initialize to.\n * @param options ConnectorConfig\n */\nexport function getDataConnect(\n  app: FirebaseApp,\n  options: ConnectorConfig\n): DataConnect;\nexport function getDataConnect(\n  appOrOptions: FirebaseApp | ConnectorConfig,\n  optionalOptions?: ConnectorConfig\n): DataConnect {\n  let app: FirebaseApp;\n  let dcOptions: ConnectorConfig;\n  if ('location' in appOrOptions) {\n    dcOptions = appOrOptions;\n    app = getApp();\n  } else {\n    dcOptions = optionalOptions!;\n    app = appOrOptions;\n  }\n\n  if (!app || Object.keys(app).length === 0) {\n    app = getApp();\n  }\n  const provider = _getProvider(app, 'data-connect');\n  const identifier = JSON.stringify(dcOptions);\n  if (provider.isInitialized(identifier)) {\n    const dcInstance = provider.getImmediate({ identifier });\n    const options = provider.getOptions(identifier);\n    const optionsValid = Object.keys(options).length > 0;\n    if (optionsValid) {\n      logDebug('Re-using cached instance');\n      return dcInstance;\n    }\n  }\n  validateDCOptions(dcOptions);\n\n  logDebug('Creating new DataConnect instance');\n  // Initialize with options.\n  return provider.initialize({\n    instanceIdentifier: identifier,\n    options: dcOptions\n  });\n}\n\n/**\n *\n * @param dcOptions\n * @returns {void}\n * @internal\n */\nexport function validateDCOptions(dcOptions: ConnectorConfig): boolean {\n  const fields = ['connector', 'location', 'service'];\n  if (!dcOptions) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'DC Option Required');\n  }\n  fields.forEach(field => {\n    if (dcOptions[field] === null || dcOptions[field] === undefined) {\n      throw new DataConnectError(Code.INVALID_ARGUMENT, `${field} Required`);\n    }\n  });\n  return true;\n}\n\n/**\n * Delete DataConnect instance\n * @param dataConnect DataConnect instance\n * @returns\n */\nexport function terminate(dataConnect: DataConnect): Promise<void> {\n  return dataConnect._delete();\n  // TODO(mtewani): Stop pending tasks\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DataConnectError } from '../core/error';\n\nimport { DataConnect, getDataConnect } from './DataConnect';\nimport {\n  OperationRef,\n  QUERY_STR,\n  DataConnectResult,\n  SerializedRef\n} from './Reference';\n\n/**\n * Signature for `OnResultSubscription` for `subscribe`\n */\nexport type OnResultSubscription<Data, Variables> = (\n  res: QueryResult<Data, Variables>\n) => void;\n/**\n * Signature for `OnErrorSubscription` for `subscribe`\n */\nexport type OnErrorSubscription = (err?: DataConnectError) => void;\n/**\n * Signature for unsubscribe from `subscribe`\n */\nexport type QueryUnsubscribe = () => void;\n/**\n * Representation of user provided subscription options.\n */\nexport interface DataConnectSubscription<Data, Variables> {\n  userCallback: OnResultSubscription<Data, Variables>;\n  errCallback?: (e?: DataConnectError) => void;\n  unsubscribe: () => void;\n}\n\n/**\n * QueryRef object\n */\nexport interface QueryRef<Data, Variables>\n  extends OperationRef<Data, Variables> {\n  refType: typeof QUERY_STR;\n}\n/**\n * Result of `executeQuery`\n */\nexport interface QueryResult<Data, Variables>\n  extends DataConnectResult<Data, Variables> {\n  ref: QueryRef<Data, Variables>;\n  toJSON: () => SerializedRef<Data, Variables>;\n}\n/**\n * Promise returned from `executeQuery`\n */\nexport interface QueryPromise<Data, Variables>\n  extends PromiseLike<QueryResult<Data, Variables>> {\n  // reserved for special actions like cancellation\n}\n\n/**\n * Execute Query\n * @param queryRef query to execute.\n * @returns `QueryPromise`\n */\nexport function executeQuery<Data, Variables>(\n  queryRef: QueryRef<Data, Variables>\n): QueryPromise<Data, Variables> {\n  return queryRef.dataConnect._queryManager.executeQuery(queryRef);\n}\n\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @returns `QueryRef`\n */\nexport function queryRef<Data>(\n  dcInstance: DataConnect,\n  queryName: string\n): QueryRef<Data, undefined>;\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @param variables Variables to execute with\n * @returns `QueryRef`\n */\nexport function queryRef<Data, Variables>(\n  dcInstance: DataConnect,\n  queryName: string,\n  variables: Variables\n): QueryRef<Data, Variables>;\n/**\n * Execute Query\n * @param dcInstance Data Connect instance to use.\n * @param queryName Query to execute\n * @param variables Variables to execute with\n * @param initialCache initial cache to use for client hydration\n * @returns `QueryRef`\n */\nexport function queryRef<Data, Variables>(\n  dcInstance: DataConnect,\n  queryName: string,\n  variables?: Variables,\n  initialCache?: QueryResult<Data, Variables>\n): QueryRef<Data, Variables> {\n  dcInstance.setInitialized();\n  dcInstance._queryManager.track(queryName, variables, initialCache);\n  return {\n    dataConnect: dcInstance,\n    refType: QUERY_STR,\n    name: queryName,\n    variables: variables as Variables\n  };\n}\n/**\n * Converts serialized ref to query ref\n * @param serializedRef ref to convert to `QueryRef`\n * @returns `QueryRef`\n */\nexport function toQueryRef<Data, Variables>(\n  serializedRef: SerializedRef<Data, Variables>\n): QueryRef<Data, Variables> {\n  const {\n    refInfo: { name, variables, connectorConfig }\n  } = serializedRef;\n  return queryRef(getDataConnect(connectorConfig), name, variables);\n}\n/**\n * `OnCompleteSubscription`\n */\nexport type OnCompleteSubscription = () => void;\n/**\n * Representation of full observer options in `subscribe`\n */\nexport interface SubscriptionOptions<Data, Variables> {\n  onNext?: OnResultSubscription<Data, Variables>;\n  onErr?: OnErrorSubscription;\n  onComplete?: OnCompleteSubscription;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ConnectorConfig,\n  DataConnect,\n  getDataConnect\n} from '../api/DataConnect';\nimport { Code, DataConnectError } from '../core/error';\ninterface ParsedArgs<Variables> {\n  dc: DataConnect;\n  vars: Variables;\n}\n\n/**\n * The generated SDK will allow the user to pass in either the variable or the data connect instance with the variable,\n * and this function validates the variables and returns back the DataConnect instance and variables based on the arguments passed in.\n * @param connectorConfig\n * @param dcOrVars\n * @param vars\n * @param validateVars\n * @returns {DataConnect} and {Variables} instance\n * @internal\n */\nexport function validateArgs<Variables extends object>(\n  connectorConfig: ConnectorConfig,\n  dcOrVars?: DataConnect | Variables,\n  vars?: Variables,\n  validateVars?: boolean\n): ParsedArgs<Variables> {\n  let dcInstance: DataConnect;\n  let realVars: Variables;\n  if (dcOrVars && 'enableEmulator' in dcOrVars) {\n    dcInstance = dcOrVars as DataConnect;\n    realVars = vars;\n  } else {\n    dcInstance = getDataConnect(connectorConfig);\n    realVars = dcOrVars as Variables;\n  }\n  if (!dcInstance || (!realVars && validateVars)) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'Variables required.');\n  }\n  return { dc: dcInstance, vars: realVars };\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  OnCompleteSubscription,\n  OnErrorSubscription,\n  OnResultSubscription,\n  QueryRef,\n  QueryUnsubscribe,\n  SubscriptionOptions,\n  toQueryRef\n} from './api/query';\nimport { OpResult, SerializedRef } from './api/Reference';\nimport { DataConnectError, Code } from './core/error';\n\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param observer observer object to use for subscribing.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  observer: SubscriptionOptions<Data, Variables>\n): QueryUnsubscribe;\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param onNext Callback to call when result comes back.\n * @param onError Callback to call when error gets thrown.\n * @param onComplete Called when subscription completes.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  onNext: OnResultSubscription<Data, Variables>,\n  onError?: OnErrorSubscription,\n  onComplete?: OnCompleteSubscription\n): QueryUnsubscribe;\n/**\n * Subscribe to a `QueryRef`\n * @param queryRefOrSerializedResult query ref or serialized result.\n * @param observerOrOnNext observer object or next function.\n * @param onError Callback to call when error gets thrown.\n * @param onComplete Called when subscription completes.\n * @returns `SubscriptionOptions`\n */\nexport function subscribe<Data, Variables>(\n  queryRefOrSerializedResult:\n    | QueryRef<Data, Variables>\n    | SerializedRef<Data, Variables>,\n  observerOrOnNext:\n    | SubscriptionOptions<Data, Variables>\n    | OnResultSubscription<Data, Variables>,\n  onError?: OnErrorSubscription,\n  onComplete?: OnCompleteSubscription\n): QueryUnsubscribe {\n  let ref: QueryRef<Data, Variables>;\n  let initialCache: OpResult<Data> | undefined;\n  if ('refInfo' in queryRefOrSerializedResult) {\n    const serializedRef: SerializedRef<Data, Variables> =\n      queryRefOrSerializedResult;\n    const { data, source, fetchTime } = serializedRef;\n    initialCache = {\n      data,\n      source,\n      fetchTime\n    };\n    ref = toQueryRef(serializedRef);\n  } else {\n    ref = queryRefOrSerializedResult;\n  }\n  let onResult: OnResultSubscription<Data, Variables> | undefined = undefined;\n  if (typeof observerOrOnNext === 'function') {\n    onResult = observerOrOnNext;\n  } else {\n    onResult = observerOrOnNext.onNext;\n    onError = observerOrOnNext.onErr;\n    onComplete = observerOrOnNext.onComplete;\n  }\n  if (!onResult) {\n    throw new DataConnectError(Code.INVALID_ARGUMENT, 'Must provide onNext');\n  }\n  return ref.dataConnect._queryManager.addSubscription(\n    ref,\n    onResult,\n    onError,\n    initialCache\n  );\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  _registerComponent,\n  registerVersion,\n  SDK_VERSION\n} from '@firebase/app';\nimport { Component, ComponentType } from '@firebase/component';\n\nimport { name, version } from '../package.json';\nimport { setSDKVersion } from '../src/core/version';\n\nimport { DataConnect, ConnectorConfig } from './api/DataConnect';\nimport { Code, DataConnectError } from './core/error';\n\nexport function registerDataConnect(variant?: string): void {\n  setSDKVersion(SDK_VERSION);\n  _registerComponent(\n    new Component(\n      'data-connect',\n      (container, { instanceIdentifier: settings, options }) => {\n        const app = container.getProvider('app').getImmediate()!;\n        const authProvider = container.getProvider('auth-internal');\n        const appCheckProvider = container.getProvider('app-check-internal');\n        let newOpts = options as ConnectorConfig;\n        if (settings) {\n          newOpts = JSON.parse(settings);\n        }\n        if (!app.options.projectId) {\n          throw new DataConnectError(\n            Code.INVALID_ARGUMENT,\n            'Project ID must be provided. Did you pass in a proper projectId to initializeApp?'\n          );\n        }\n        return new DataConnect(\n          app,\n          { ...newOpts, projectId: app.options.projectId! },\n          authProvider,\n          appCheckProvider\n        );\n      },\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * Firebase Data Connect\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { DataConnect } from './api/DataConnect';\nimport { registerDataConnect } from './register';\n\nexport * from './api';\nexport * from './api.browser';\n\nregisterDataConnect();\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'data-connect': DataConnect;\n  }\n}\n"], "names": ["FirebaseError", "Error", "constructor", "code", "message", "customData", "super", "this", "name", "Object", "setPrototypeOf", "prototype", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "replace", "PATTERN", "_", "key", "value", "String", "fullMessage", "Component", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "LogLevel", "levelStringToEnum", "debug", "DEBUG", "verbose", "VERBOSE", "info", "INFO", "warn", "WARN", "error", "ERROR", "silent", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "console", "SDK_VERSION", "AppCheckTokenProvider", "appName_", "appCheckProvider", "appCheck", "getImmediate", "optional", "get", "then", "catch", "getToken", "forceRefresh", "Promise", "resolve", "reject", "setTimeout", "addTokenChangeListener", "listener", "_a", "addTokenListener", "Code", "DataConnectError", "toString", "logger", "<PERSON><PERSON>", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "log", "logDebug", "msg", "logError", "FirebaseAuthProvider", "_appName", "_options", "_authProvider", "_auth", "onInit", "auth", "JSON", "stringify", "addAuthTokenListener", "removeTokenChangeListener", "removeAuthTokenListener", "err", "QUERY_STR", "MUTATION_STR", "SOURCE_SERVER", "SOURCE_CACHE", "encoderImpl", "getRefSerializer", "queryRef", "source", "toJSON", "refInfo", "variables", "connectorConfig", "projectId", "dataConnect", "app", "options", "getSettings", "fetchTime", "toLocaleString", "<PERSON><PERSON><PERSON><PERSON>", "encoder", "o", "QueryManager", "transport", "_queries", "Map", "track", "queryName", "initialCache", "ref", "refType", "newTrackedQuery", "subscriptions", "currentCache", "lastError", "setIfNotExists", "map", "has", "set", "addSubscription", "onResultCallback", "onError<PERSON>allback", "<PERSON><PERSON><PERSON><PERSON>", "subscription", "userCallback", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unsubscribe", "filter", "sub", "compareDates", "str1", "str2", "date1", "date2", "getTime", "undefined", "push", "execute<PERSON>uery", "invoke<PERSON><PERSON>y", "res", "result", "assign", "for<PERSON>ach", "enableEmulator", "host", "port", "useEmulator", "addToken", "url", "<PERSON><PERSON><PERSON><PERSON>", "newUrl", "URL", "searchParams", "append", "connectFetch", "globalThis", "fetch", "getGoogApiClientValue", "_isUsingGen", "str", "dcFetch", "body", "signal", "appId", "accessToken", "appCheckToken", "headers", "bodyStr", "async", "response", "jsonResponse", "json", "e", "getMessage", "obj", "status", "length", "stringified", "RESTTransport", "authProvider", "transportOptions", "_host", "_location", "_connectorName", "_secure", "_project", "_accessToken", "_appCheckToken", "_lastToken", "abortController", "AbortController", "<PERSON><PERSON><PERSON>", "withRetry", "endpointUrl", "_serviceName", "operationName", "bind", "invokeMutation", "mutationName", "taskResult", "cancel", "abort", "_port", "sslEnabled", "location", "project", "connector", "token", "_b", "urlBuilder", "projectConfig", "baseUrl", "isSecure", "onTokenChanged", "newToken", "forceToken", "<PERSON><PERSON><PERSON><PERSON>", "_setLastToken", "lastToken", "promiseFactory", "retry", "isNewToken", "getWithAuth", "mutationRef", "dcInstance", "setInitialized", "MutationManager", "_transport", "_inflight", "executeMutation", "withRefPromise", "removePromise", "promise", "_mutationManager", "parseOptions", "fullHost", "protocol", "hostName", "split", "portAsString", "Number", "DataConnect", "dataConnectOptions", "_appCheckProvider", "isEmulator", "_initialized", "_isUsingGeneratedSdk", "process", "env", "_transportOptions", "_useGeneratedSdk", "_delete", "_removeServiceInstance", "copy", "parse", "_transportClass", "_authTokenProvider", "_appCheckTokenProvider", "_queryManager", "connectDataConnectEmulator", "dc", "getDataConnect", "appOrOptions", "optionalOptions", "dcOptions", "getApp", "keys", "provider", "_get<PERSON><PERSON><PERSON>", "identifier", "isInitialized", "getOptions", "validateDCOptions", "initialize", "instanceIdentifier", "field", "terminate", "toQueryRef", "serializedRef", "validateArgs", "dcOrVars", "vars", "validateVars", "realVars", "subscribe", "queryRefOrSerializedResult", "observerOrOnNext", "onError", "onComplete", "onResult", "onNext", "onErr", "registerDataConnect", "variant", "setSDKVersion", "version", "_registerComponent", "container", "settings", "get<PERSON><PERSON><PERSON>", "newOpts", "registerVersion"], "mappings": "8IAyEM,MAAOA,sBAAsBC,MAIjCC,YAEWC,EACTC,EAEOC,GAEPC,MAAMF,GALGG,KAAIJ,KAAJA,EAGFI,KAAUF,WAAVA,EAPAE,KAAIC,KAdI,gBA2BfC,OAAOC,eAAeH,KAAMP,cAAcW,WAItCV,MAAMW,mBACRX,MAAMW,kBAAkBL,KAAMM,aAAaF,UAAUG,SAK9C,MAAAD,aAIXX,YACmBa,EACAC,EACAC,GAFAV,KAAOQ,QAAPA,EACAR,KAAWS,YAAXA,EACAT,KAAMU,OAANA,EAGnBH,OACEX,KACGe,GAEH,MAAMb,EAAca,EAAK,IAAoB,GACvCC,EAAW,GAAGZ,KAAKQ,WAAWZ,IAC9BiB,EAAWb,KAAKU,OAAOd,GAEvBC,EAAUgB,EAUpB,SAASC,gBAAgBD,EAAkBF,GACzC,OAAOE,EAASE,QAAQC,GAAS,CAACC,EAAGC,KACnC,MAAMC,EAAQR,EAAKO,GACnB,OAAgB,MAATC,EAAgBC,OAAOD,GAAS,IAAID,SAbhBJ,CAAgBD,EAAUf,GAAc,QAE7DuB,EAAc,GAAGrB,KAAKS,gBAAgBZ,MAAYe,MAIxD,OAFc,IAAInB,cAAcmB,EAAUS,EAAavB,IAa3D,MAAMkB,EAAU,gBCzGH,MAAAM,UAiBX3B,YACWM,EACAsB,EACAC,GAFAxB,KAAIC,KAAJA,EACAD,KAAeuB,gBAAfA,EACAvB,KAAIwB,KAAJA,EAnBXxB,KAAiByB,mBAAG,EAIpBzB,KAAY0B,aAAe,GAE3B1B,KAAA2B,kBAA2C,OAE3C3B,KAAiB4B,kBAAwC,KAczDC,qBAAqBC,GAEnB,OADA9B,KAAK2B,kBAAoBG,EAClB9B,KAGT+B,qBAAqBN,GAEnB,OADAzB,KAAKyB,kBAAoBA,EAClBzB,KAGTgC,gBAAgBC,GAEd,OADAjC,KAAK0B,aAAeO,EACbjC,KAGTkC,2BAA2BC,GAEzB,OADAnC,KAAK4B,kBAAoBO,EAClBnC,UCdCoC,GAAZ,SAAYA,GACVA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,QAAA,GAAA,UACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,KAAA,GAAA,OACAA,EAAAA,EAAA,MAAA,GAAA,QACAA,EAAAA,EAAA,OAAA,GAAA,SANF,CAAYA,IAAAA,EAOX,KAED,MAAMC,EAA2D,CAC/DC,MAASF,EAASG,MAClBC,QAAWJ,EAASK,QACpBC,KAAQN,EAASO,KACjBC,KAAQR,EAASS,KACjBC,MAASV,EAASW,MAClBC,OAAUZ,EAASa,QAMfC,EAA4Bd,EAASO,KAmBrCQ,EAAgB,CACpB,CAACf,EAASG,OAAQ,MAClB,CAACH,EAASK,SAAU,MACpB,CAACL,EAASO,MAAO,OACjB,CAACP,EAASS,MAAO,OACjB,CAACT,EAASW,OAAQ,SAQdK,kBAAgC,CAACC,EAAUC,KAAYC,KAC3D,GAAID,EAAUD,EAASG,SACrB,OAEF,MAAMC,GAAM,IAAIC,MAAOC,cACjBC,EAAST,EAAcG,GAC7B,IAAIM,EAMF,MAAM,IAAIlE,MACR,8DAA8D4D,MANhEO,QAAQD,GACN,IAAIH,OAASJ,EAASpD,WACnBsD,qCClGF,IAAIO,EAAc,GCWZ,MAAAC,sBAEXpE,YACUqE,EACAC,GADAjE,KAAQgE,SAARA,EACAhE,KAAgBiE,iBAAhBA,EAERjE,KAAKkE,SAAWD,MAAAA,OAAA,EAAAA,EAAkBE,aAAa,CAAEC,UAAU,IACtDpE,KAAKkE,UACHD,MAAAA,GAAAA,EACDI,MACDC,MAAKJ,GAAalE,KAAKkE,SAAWA,IAClCK,QAIPC,SAASC,GACP,OAAKzE,KAAKkE,SAeHlE,KAAKkE,SAASM,SAASC,GAdrB,IAAIC,SAA6B,CAACC,EAASC,KAKhDC,YAAW,KACL7E,KAAKkE,SACPlE,KAAKwE,SAASC,GAAcH,KAAKK,EAASC,GAE1CD,EAAQ,QAET,MAMTG,uBAAuBC,SAEjB,QADCC,EAAAhF,KAAKiE,wBACN,IAAAe,GAAAA,EAAAX,MACDC,MAAKJ,GAAYA,EAASe,iBAAiBF,MCpC3C,MAAMG,EACJ,QADIA,EAEU,sBAFVA,EAKO,mBALPA,EAOG,eAIV,MAAOC,yBAAyB1F,cAKpCE,YAIWC,EAIAC,GAETE,MAAMH,EAAMC,GANHG,KAAIJ,KAAJA,EAIAI,KAAOH,QAAPA,EAOTG,KAAKoF,SAAW,IAAM,GAAGpF,KAAKC,eAAeD,KAAKJ,UAAUI,KAAKH,WCzCrE,MAAMwF,EAAS,IJyGF,MAAAC,OAOX3F,YAAmBM,GAAAD,KAAIC,KAAJA,EAUXD,KAASuF,UAAGrC,EAsBZlD,KAAWwF,YAAepC,kBAc1BpD,KAAeyF,gBAAsB,KAlCzCjC,eACF,OAAOxD,KAAKuF,UAGV/B,aAASkC,GACX,KAAMA,KAAOtD,GACX,MAAM,IAAIuD,UAAU,kBAAkBD,+BAExC1F,KAAKuF,UAAYG,EAInBE,YAAYF,GACV1F,KAAKuF,UAA2B,iBAARG,EAAmBrD,EAAkBqD,GAAOA,EAQlEG,iBACF,OAAO7F,KAAKwF,YAEVK,eAAWH,GACb,GAAmB,mBAARA,EACT,MAAM,IAAIC,UAAU,qDAEtB3F,KAAKwF,YAAcE,EAOjBI,qBACF,OAAO9F,KAAKyF,gBAEVK,mBAAeJ,GACjB1F,KAAKyF,gBAAkBC,EAOzBpD,SAASiB,GACPvD,KAAKyF,iBAAmBzF,KAAKyF,gBAAgBzF,KAAMoC,EAASG,SAAUgB,GACtEvD,KAAKwF,YAAYxF,KAAMoC,EAASG,SAAUgB,GAE5CwC,OAAOxC,GACLvD,KAAKyF,iBACHzF,KAAKyF,gBAAgBzF,KAAMoC,EAASK,WAAYc,GAClDvD,KAAKwF,YAAYxF,KAAMoC,EAASK,WAAYc,GAE9Cb,QAAQa,GACNvD,KAAKyF,iBAAmBzF,KAAKyF,gBAAgBzF,KAAMoC,EAASO,QAASY,GACrEvD,KAAKwF,YAAYxF,KAAMoC,EAASO,QAASY,GAE3CX,QAAQW,GACNvD,KAAKyF,iBAAmBzF,KAAKyF,gBAAgBzF,KAAMoC,EAASS,QAASU,GACrEvD,KAAKwF,YAAYxF,KAAMoC,EAASS,QAASU,GAE3CT,SAASS,GACPvD,KAAKyF,iBAAmBzF,KAAKyF,gBAAgBzF,KAAMoC,EAASW,SAAUQ,GACtEvD,KAAKwF,YAAYxF,KAAMoC,EAASW,SAAUQ,KI7LpB,0BACpB,SAAUqC,YAAYpC,GAC1B6B,EAAOO,YAAYpC,GAEf,SAAUwC,SAASC,GACvBZ,EAAO/C,MAAM,gBAAgBwB,OAAiBmC,KAG1C,SAAUC,SAASD,GACvBZ,EAAOvC,MAAM,gBAAgBgB,OAAiBmC,KCMnC,MAAAE,qBAEXxG,YACUyG,EACAC,EACAC,GAFAtG,KAAQoG,SAARA,EACApG,KAAQqG,SAARA,EACArG,KAAasG,cAAbA,EAERtG,KAAKuG,MAAQD,EAAcnC,aAAa,CAAEC,UAAU,IAC/CpE,KAAKuG,OACRD,EAAcE,QAAOC,GAASzG,KAAKuG,MAAQE,IAG/CjC,SAASC,GACP,OAAKzE,KAAKuG,MAWHvG,KAAKuG,MAAM/B,SAASC,GAAcF,OAAMzB,GACzCA,GAAwB,+BAAfA,EAAMlD,MACjBoG,SACE,kEAEK,OAEPE,SACE,qDACEQ,KAAKC,UAAU7D,IAEZ4B,QAAQE,OAAO9B,MArBjB,IAAI4B,SAAQ,CAACC,EAASC,KAC3BC,YAAW,KACL7E,KAAKuG,MACPvG,KAAKwE,SAASC,GAAcH,KAAKK,EAASC,GAE1CD,EAAQ,QAET,MAkBTG,uBAAuBC,SACX,QAAVC,EAAAhF,KAAKuG,aAAK,IAAAvB,GAAAA,EAAE4B,qBAAqB7B,GAEnC8B,0BAA0B9B,GACxB/E,KAAKsG,cACFjC,MACAC,MAAKmC,GAAQA,EAAKK,wBAAwB/B,KAC1CR,OAAMwC,GAAOb,SAASa,MC/DhB,MAAAC,EAAY,QACZC,EAAe,WAGfC,EAAgB,SAChBC,EAAe,QCLrB,IAAIC,EC8BX,SAASC,iBACPC,EACA3G,EACA4G,GAEA,OAAO,SAASC,SACd,MAAO,CACL7G,KAAAA,EACA8G,QAAS,CACPxH,KAAMqH,EAASrH,KACfyH,UAAWJ,EAASI,UACpBC,+BACEC,UAAWN,EAASO,YAAYC,IAAIC,QAAQH,WACzCN,EAASO,YAAYG,gBAG5BC,UAAWvE,KAAKD,MAAMyE,iBACtBX,OAAAA,KD9CA,SAAUY,WAAWC,GACzBhB,EAAcgB,EAEhBD,EAAWE,GAAK3B,KAAKC,UAAU0B,KCgDlB,MAAAC,aAEX3I,YAAoB4I,GAAAvI,KAASuI,UAATA,EAClBvI,KAAKwI,SAAW,IAAIC,IAEtBC,MACEC,EACAjB,EACAkB,GAEA,MAAMC,EAA4C,CAChD5I,KAAM0I,EACNjB,UAAAA,EACAoB,QFjEmB,SEmEf5H,EAAMkG,EAAYyB,GAClBE,EAAiD,CACrDF,IAAAA,EACAG,cAAe,GACfC,aAAcL,GAAgB,KAC9BM,UAAW,MAIb,OC7EY,SAAAC,eACdC,EACAlI,EACAwE,GAEK0D,EAAIC,IAAInI,IACXkI,EAAIE,IAAIpI,EAAKwE,GDsEbyD,CAAenJ,KAAKwI,SAAUtH,EAAK6H,GAC5B/I,KAAKwI,SAASnE,IAAInD,GAE3BqI,gBACEjC,EACAkC,EACAC,EACAb,GAEA,MAAM1H,EAAMkG,EAAY,CACtBnH,KAAMqH,EAASrH,KACfyH,UAAWJ,EAASI,UACpBoB,QFvFmB,UEyFfY,EAAe1J,KAAKwI,SAASnE,IAAInD,GAIjCyI,EAAe,CACnBC,aAAcJ,EACdK,YAAaJ,GAETK,YAAc,KAClB,MAAMJ,EAAe1J,KAAKwI,SAASnE,IAAInD,GACvCwI,EAAaV,cAAgBU,EAAaV,cAAce,QACtDC,GAAOA,IAAQL,KAgBnB,GAbIf,GAAgBc,EAAaT,eAAiBL,IAChD5C,SAAS,2CAEN0D,EAAaT,cACbS,EAAaT,cAgGtB,SAASgB,aAAaC,EAAcC,GAClC,MAAMC,EAAQ,IAAI1G,KAAKwG,GACjBG,EAAQ,IAAI3G,KAAKyG,GACvB,OAAOC,EAAME,UAAYD,EAAMC,UAlGvBL,CACEP,EAAaT,aAAahB,UAC1BW,EAAaX,cAGjByB,EAAaT,aAAeL,IAGE,OAA9Bc,EAAaT,aAAuB,CAEtCO,EAAiB,CACf7I,KAFiB+I,EAAaT,aAAatI,KAG3C4G,OFnHoB,QEoHpBsB,IAAKvB,EACLE,OAAQH,iBACNC,EACAoC,EAAaT,aAAatI,KFvHR,SE0HpBsH,UAAWyB,EAAaT,aAAahB,YAER,OAA3ByB,EAAaR,WAAsBO,GACrCA,OAAgBc,GASpB,GALAb,EAAaV,cAAcwB,KAAK,CAC9BZ,aAAcJ,EACdK,YAAaJ,EACbK,YAAAA,eAEGJ,EAAaT,aAAc,CAC9BjD,SACE,gCACEsB,EAASrH,uBACQyG,KAAKC,UACtBW,EAASI,qCAGG1H,KAAKyK,aAAanD,GAE1BhD,UAAKiG,GAAWxD,QAE1B,OAAO+C,YAETW,aACEnD,GAEA,MAAMpG,EAAMkG,EAAY,CACtBnH,KAAMqH,EAASrH,KACfyH,UAAWJ,EAASI,UACpBoB,QF/JmB,UEiKfY,EAAe1J,KAAKwI,SAASnE,IAAInD,GAoCvC,OAnCelB,KAAKuI,UAAUmC,YAC5BpD,EAASrH,KACTqH,EAASI,WAESpD,MAClBqG,IACE,MAAM1C,GAAY,IAAIvE,MAAO0B,WACvBwF,EACD1K,OAAA2K,OAAA3K,OAAA2K,OAAA,GAAAF,GACH,CAAApD,OFvKmB,SEwKnBsB,IAAKvB,EACLE,OAAQH,iBAAiBC,EAAUqD,EAAIhK,KFzKpB,UE0KnBsH,UAAAA,IAUF,OARAyB,EAAaV,cAAc8B,SAAQnB,IACjCA,EAAaC,aAAagB,MAE5BlB,EAAaT,aAAe,CAC1BtI,KAAMgK,EAAIhK,KACV4G,OFhLkB,QEiLlBU,UAAAA,GAEK2C,KAET7D,IAOE,MANA2C,EAAaR,UAAYnC,EACzB2C,EAAaV,cAAc8B,SAAQnB,IAC7BA,EAAaE,aACfF,EAAaE,YAAY9C,MAGvBA,KAMZgE,eAAeC,EAAcC,GAC3BjL,KAAKuI,UAAU2C,YAAYF,EAAMC,IEjLrB,SAAAE,SAASC,EAAaC,GACpC,IAAKA,EACH,OAAOD,EAET,MAAME,EAAS,IAAIC,IAAIH,GAEvB,OADAE,EAAOE,aAAaC,OAAO,MAAOJ,GAC3BC,EAAOlG,WC1BhB,IAAIsG,EAAoCC,WAAWC,MAInD,SAASC,sBAAsBC,GAC7B,IAAIC,EAAM,eAAiBjI,EAI3B,OAHIgI,IACFC,GAAO,YAEFA,EAEO,SAAAC,QACdZ,EACAa,GACAC,OAAEA,GACFC,EACAC,EACAC,EACAP,GAEA,IAAKJ,EACH,MAAM,IAAIvG,iBAAiBD,EAAY,qCAEzC,MAAMoH,EAAuB,CAC3B,eAAgB,mBAChB,oBAAqBT,sBAAsBC,IAEzCM,IACFE,EAAQ,yBAA2BF,GAEjCD,IACFG,EAAQ,oBAAsBH,GAE5BE,IACFC,EAAQ,uBAAyBD,GAEnC,MAAME,EAAU7F,KAAKC,UAAUsF,GAG/B,OAFAjG,SAAS,yBAAyBoF,gBAAkBmB,KAE7Cb,EAAaN,EAAK,CACvBa,KAAMM,EACN3I,OAAQ,OACR0I,QAAAA,EACAJ,OAAAA,IAEC3H,OAAMwC,IACL,MAAM,IAAI5B,iBACRD,EACA,oBAAsBwB,KAAKC,UAAUI,OAGxCzC,MAAKkI,MAAMC,IACV,IAAIC,EAAe,KACnB,IACEA,QAAqBD,EAASE,OAC9B,MAAOC,GACP,MAAM,IAAIzH,iBAAiBD,EAAYwB,KAAKC,UAAUiG,IAExD,MAAM/M,EAwBZ,SAASgN,WAAWC,GAClB,GAAI,YAAaA,EACf,OAAOA,EAAIjN,QAEb,OAAO6G,KAAKC,UAAUmG,GA5BFD,CAAWH,GAC3B,GAAID,EAASM,QAAU,IAAK,CAI1B,GAHA7G,SACE,mCAAqCQ,KAAKC,UAAU+F,IAE9B,MAApBD,EAASM,OACX,MAAM,IAAI5H,iBAAiBD,EAAmBrF,GAEhD,MAAM,IAAIsF,iBAAiBD,EAAYrF,GAEzC,OAAO6M,KAERpI,MAAKqG,IACJ,GAAIA,EAAIjK,QAAUiK,EAAIjK,OAAOsM,OAAQ,CACnC,MAAMC,EAAcvG,KAAKC,UAAUgE,EAAIjK,QAEvC,MADAwF,SAAS,+CAAiD+G,GACpD,IAAI9H,iBAAiBD,EAAY+H,GAEzC,OAAOtC,KCtEA,MAAAuC,cAWXvN,YACEoI,EACQsD,EACAc,EACAgB,EACAlJ,EACRmJ,EACQtB,GAAc,WALd9L,KAAMqL,OAANA,EACArL,KAAKmM,MAALA,EACAnM,KAAYmN,aAAZA,EACAnN,KAAgBiE,iBAAhBA,EAEAjE,KAAW8L,YAAXA,EAjBF9L,KAAKqN,MAAG,GAERrN,KAASsN,UAAG,IACZtN,KAAcuN,eAAG,GACjBvN,KAAOwN,SAAG,EACVxN,KAAQyN,SAAG,IAEXzN,KAAY0N,aAAkB,KAC9B1N,KAAc2N,eAAkB,KAChC3N,KAAU4N,WAAkB,KA2HpC5N,KAAA0K,YAGiD,CAC/C/B,EACAsD,KAEA,MAAM4B,EAAkB,IAAIC,gBAEtBC,EAAW/N,KAAKgO,WAAU,IAC9BhC,QACEb,SAAS,GAAGnL,KAAKiO,2BAA4BjO,KAAKqL,QAClD,CACEpL,KAAM,YAAYD,KAAKyN,sBAAsBzN,KAAKsN,sBAAsBtN,KAAKkO,2BAA2BlO,KAAKuN,iBAC7GY,cAAexF,EACfjB,UAAWuE,GAEb4B,EACA7N,KAAKmM,MACLnM,KAAK0N,aACL1N,KAAK2N,eACL3N,KAAK8L,eAIT,MAAO,CACLxH,KAAMyJ,EAASzJ,KAAK8J,KAAKL,GACzBxJ,MAAOwJ,EAASxJ,MAAM6J,KAAKL,KAG/B/N,KAAAqO,eAGiD,CAC/CC,EACArC,KAEA,MAAM4B,EAAkB,IAAIC,gBACtBS,EAAavO,KAAKgO,WAAU,IACzBhC,QACLb,SAAS,GAAGnL,KAAKiO,8BAA+BjO,KAAKqL,QACrD,CACEpL,KAAM,YAAYD,KAAKyN,sBAAsBzN,KAAKsN,sBAAsBtN,KAAKkO,2BAA2BlO,KAAKuN,iBAC7GY,cAAeG,EACf5G,UAAWuE,GAEb4B,EACA7N,KAAKmM,MACLnM,KAAK0N,aACL1N,KAAK2N,eACL3N,KAAK8L,eAIT,MAAO,CACLxH,KAAMiK,EAAWjK,KAAK8J,KAAKG,GAG3BC,OAAQ,IAAMX,EAAgBY,UA3K5BrB,IACmC,iBAA1BA,EAAiBnC,OAC1BjL,KAAK0O,MAAQtB,EAAiBnC,WAEW,IAAhCmC,EAAiBuB,aAC1B3O,KAAKwN,QAAUJ,EAAiBuB,YAElC3O,KAAKqN,MAAQD,EAAiBpC,MAEhC,MAAM4D,SAAEA,EAAUhH,UAAWiH,EAAOC,UAAEA,EAAStO,QAAEA,GAAYuH,EAQ7D,GAPI6G,IACF5O,KAAKsN,UAAYsB,GAEfC,IACF7O,KAAKyN,SAAWoB,GAElB7O,KAAKkO,aAAe1N,GACfsO,EACH,MAAM,IAAI3J,iBACRD,EACA,4BAGJlF,KAAKuN,eAAiBuB,EACL,QAAjB9J,EAAAhF,KAAKmN,oBAAY,IAAAnI,GAAAA,EAAEF,wBAAuBiK,IACxC/I,SAAS,wBAAwB+I,KACjC/O,KAAK0N,aAAeqB,KAED,QAArBC,EAAAhP,KAAKiE,wBAAgB,IAAA+K,GAAAA,EAAElK,wBAAuB8F,IAC5C,MAAMmE,MAAEA,GAAUnE,EAClB5E,SAAS,kCAAkC+I,KAC3C/O,KAAK2N,eAAiBoB,KAGtBd,kBACF,OF7DY,SAAAgB,WACdC,EACA9B,GAEA,MAAM0B,UAAEA,EAASF,SAAEA,EAAUhH,UAAWiH,EAAOrO,QAAEA,GAAY0O,GACvDlE,KAAEA,EAAI2D,WAAEA,EAAU1D,KAAEA,GAASmC,EAGnC,IAAI+B,EAAU,GAFGR,EAAa,QAAU,YACvB3D,GAAQ,uCAEzB,GAAoB,iBAATC,EACTkE,GAAW,IAAIlE,SACV,QAAoB,IAATA,EAEhB,MADA/E,SAAS,mCACH,IAAIf,iBACRD,EACA,sCAGJ,MAAO,GAAGiK,qBAA2BN,eAAqBD,cAAqBpO,gBAAsBsO,IE2C5FG,CACL,CACEH,UAAW9O,KAAKuN,eAChBqB,SAAU5O,KAAKsN,UACf1F,UAAW5H,KAAKyN,SAChBjN,QAASR,KAAKkO,cAEhB,CAAElD,KAAMhL,KAAKqN,MAAOsB,WAAY3O,KAAKwN,QAASvC,KAAMjL,KAAK0O,QAG7DxD,YAAYF,EAAcC,EAAemE,GACvCpP,KAAKqN,MAAQrC,EACO,iBAATC,IACTjL,KAAK0O,MAAQzD,QAES,IAAbmE,IACTpP,KAAKwN,QAAU4B,GAGnBC,eAAeC,GACbtP,KAAK0N,aAAe4B,EAGtB9C,kBAAkB+C,GAAa,SAC7B,IAAIC,EAAyC,IAAI9K,SAAQC,GACvDA,EAAQ3E,KAAK0N,gBAkBf,OAhBI1N,KAAKiE,mBACPjE,KAAK2N,eAAyD,QAAxC3I,QAAOhF,KAAKiE,iBAAiBO,kBAAW,IAAAQ,OAAA,EAAAA,EAAE+J,OAGhES,EADExP,KAAKmN,aACUnN,KAAKmN,aACnB3I,SAAyB+K,GACzBjL,MAAK3D,GACCA,GAGLX,KAAK0N,aAAe/M,EAAKyL,YAClBpM,KAAK0N,cAHH,OAMI,IAAIhJ,SAAQC,GAAWA,EAAQ,MAE3C6K,EAGTC,cAAcC,GACZ1P,KAAK4N,WAAa8B,EAGpB1B,UACE2B,EACAC,GAAQ,GAER,IAAIC,GAAa,EACjB,OAAO7P,KAAK8P,YAAYF,GACrBtL,MAAKqG,IACJkF,EAAa7P,KAAK4N,aAAejD,EACjC3K,KAAK4N,WAAajD,EACXA,KAERrG,KAAKqL,GACLpL,OAAMwC,IAEL,GACE,SAAUA,GACVA,EAAInH,OAASsF,IACZ0K,GACDC,EAGA,OADA7J,SAAS,gCACFhG,KAAKgO,UAAU2B,GAAgB,GAExC,MAAM5I,MChGE,SAAAgJ,YACdC,EACA1B,EACA5G,GAEAsI,EAAWC,iBAOX,MAN0C,CACxCpI,YAAamI,EACb/P,KAAMqO,EACNxF,QPjDwB,WOkDxBpB,UAAWA,GAQF,MAAAwI,gBAEXvQ,YAAoBwQ,GAAAnQ,KAAUmQ,WAAVA,EADZnQ,KAASoQ,UAAgC,GAEjDC,gBACEN,GAEA,MAAMnF,EAAS5K,KAAKmQ,WAAW9B,eAC7B0B,EAAY9P,KACZ8P,EAAYrI,WAER4I,EAAiB1F,EAAOtG,MAAKqG,GAE5BzK,OAAA2K,OAAA3K,OAAA2K,OAAA,GAAAF,GAAG,CACNpD,OPpEqB,SOqErBsB,IAAKkH,EACL9H,UAAWvE,KAAKD,MAAMyE,qBAI1BlI,KAAKoQ,UAAU5F,KAAKI,GACpB,MAAM2F,cAAgB,IACnBvQ,KAAKoQ,UAAYpQ,KAAKoQ,UAAUrG,QAAOyG,GAAWA,IAAY5F,IAEjE,OADAA,EAAOtG,KAAKiM,cAAeA,eACpBD,GAwBL,SAAUD,gBACdN,GAEA,OAAOA,EAAYlI,YAAY4I,iBAAiBJ,gBAAgBN,GC5D5D,SAAUW,aAAaC,GAC3B,MAAOC,EAAUC,GAAYF,EAASG,MAAM,OACtC1B,EAAwB,UAAbwB,GACV5F,EAAM+F,GAAgBF,EAASC,MAAM,KAE5C,MAAO,CAAE9F,KAAAA,EAAMC,KADF+F,OAAOD,GACCpC,WAAYS,GAYtB,MAAA6B,YAYXtR,YACkBmI,EAECoJ,EACA5K,EACA6K,GAEjB,GANgBnR,KAAG8H,IAAHA,EAEC9H,KAAkBkR,mBAAlBA,EACAlR,KAAasG,cAAbA,EACAtG,KAAiBmR,kBAAjBA,EAdnBnR,KAAUoR,YAAG,EACbpR,KAAYqR,cAAG,EAKfrR,KAAoBsR,sBAAY,EAUP,oBAAZC,SAA2BA,QAAQC,IAAK,CACjD,MAAMxG,EAAOuG,QAAQC,IAA2C,oCAC5DxG,IACFhF,SAAS,qCACThG,KAAKoR,YAAa,EAClBpR,KAAKyR,kBAAoBf,aAAa1F,KAK5C0G,mBACO1R,KAAKsR,uBACRtR,KAAKsR,sBAAuB,GAGhCK,UAME,OALAC,EACE5R,KAAK8H,IACL,eACApB,KAAKC,UAAU3G,KAAKgI,gBAEftD,QAAQC,UAIjBqD,cACE,MAAM6J,EAAOnL,KAAKoL,MAAMpL,KAAKC,UAAU3G,KAAKkR,qBAE5C,cADOW,EAAKjK,UACLiK,EAIT5B,iBACMjQ,KAAKqR,oBAGoB9G,IAAzBvK,KAAK+R,kBACP/L,SAAS,6DACThG,KAAK+R,gBAAkB7E,eAGrBlN,KAAKsG,gBACPtG,KAAKgS,mBAAqB,IAAI7L,qBAC5BnG,KAAK8H,IAAI7H,KACTD,KAAK8H,IAAIC,QACT/H,KAAKsG,gBAGLtG,KAAKmR,oBACPnR,KAAKiS,uBAAyB,IAAIlO,sBAChC/D,KAAK8H,IAAI7H,KACTD,KAAKmR,oBAITnR,KAAKqR,cAAe,EACpBrR,KAAKmQ,WAAa,IAAInQ,KAAK+R,gBACzB/R,KAAKkR,mBACLlR,KAAK8H,IAAIC,QAAQsD,OACjBrL,KAAK8H,IAAIC,QAAQoE,MACjBnM,KAAKgS,mBACLhS,KAAKiS,4BACL1H,EACAvK,KAAKsR,sBAEHtR,KAAKyR,mBACPzR,KAAKmQ,WAAWjF,YACdlL,KAAKyR,kBAAkBzG,KACvBhL,KAAKyR,kBAAkBxG,KACvBjL,KAAKyR,kBAAkB9C,YAG3B3O,KAAKkS,cAAgB,IAAI5J,aAAatI,KAAKmQ,YAC3CnQ,KAAKyQ,iBAAmB,IAAIP,gBAAgBlQ,KAAKmQ,aAInDpF,eAAeqC,GACb,GAAIpN,KAAKqR,aAEP,MADAnL,SAAS,8CACH,IAAIf,iBACRD,EACA,6CAGJlF,KAAKyR,kBAAoBrE,EACzBpN,KAAKoR,YAAa,GAWhB,SAAUe,2BACdC,EACApH,EACAC,EACA0D,GAAa,GAEbyD,EAAGrH,eAAe,CAAEC,KAAAA,EAAMC,KAAAA,EAAM0D,WAAAA,IAiBlB,SAAA0D,eACdC,EACAC,GAEA,IAAIzK,EACA0K,EACA,aAAcF,GAChBE,EAAYF,EACZxK,EAAM2K,MAEND,EAAYD,EACZzK,EAAMwK,GAGHxK,GAAmC,IAA5B5H,OAAOwS,KAAK5K,GAAKkF,SAC3BlF,EAAM2K,KAER,MAAME,EAAWC,aAAa9K,EAAK,gBAC7B+K,EAAanM,KAAKC,UAAU6L,GAClC,GAAIG,EAASG,cAAcD,GAAa,CACtC,MAAM7C,EAAa2C,EAASxO,aAAa,CAAE0O,WAAAA,IACrC9K,EAAU4K,EAASI,WAAWF,GAEpC,GADqB3S,OAAOwS,KAAK3K,GAASiF,OAAS,EAGjD,OADAhH,SAAS,4BACFgK,EAOX,OAJAgD,kBAAkBR,GAElBxM,SAAS,qCAEF2M,EAASM,WAAW,CACzBC,mBAAoBL,EACpB9K,QAASyK,IAUP,SAAUQ,kBAAkBR,GAEhC,IAAKA,EACH,MAAM,IAAIrN,iBAAiBD,EAAuB,sBAOpD,MATe,CAAC,YAAa,WAAY,WAIlC4F,SAAQqI,IACb,GAAyB,OAArBX,EAAUW,SAAwC5I,IAArBiI,EAAUW,GACzC,MAAM,IAAIhO,iBAAiBD,EAAuB,GAAGiO,kBAGlD,EAQH,SAAUC,UAAUvL,GACxB,OAAOA,EAAY8J,UChNf,SAAUlH,aACdnD,GAEA,OAAOA,EAASO,YAAYqK,cAAczH,aAAanD,GAiCnD,SAAUA,SACd0I,EACArH,EACAjB,EACAkB,GAIA,OAFAoH,EAAWC,iBACXD,EAAWkC,cAAcxJ,MAAMC,EAAWjB,EAAWkB,GAC9C,CACLf,YAAamI,EACblH,QT1GqB,QS2GrB7I,KAAM0I,EACNjB,UAAWA,GAQT,SAAU2L,WACdC,GAEA,MACE7L,SAASxH,KAAEA,EAAIyH,UAAEA,EAASC,gBAAEA,IAC1B2L,EACJ,OAAOhM,SAAS+K,eAAe1K,GAAkB1H,EAAMyH,GCtGnD,SAAU6L,aACd5L,EACA6L,EACAC,EACAC,GAEA,IAAI1D,EACA2D,EAQJ,GAPIH,GAAY,mBAAoBA,GAClCxD,EAAawD,EACbG,EAAWF,IAEXzD,EAAaqC,eAAe1K,GAC5BgM,EAAWH,IAERxD,IAAgB2D,GAAYD,EAC/B,MAAM,IAAIvO,iBAAiBD,EAAuB,uBAEpD,MAAO,CAAEkN,GAAIpC,EAAYyD,KAAME,GCS3B,SAAUC,UACdC,EAGAC,EAGAC,EACAC,GAEA,IAAInL,EACAD,EAcAqL,EAbJ,GAAI,YAAaJ,EAA4B,CAC3C,MAAMP,EACJO,GACIlT,KAAEA,EAAI4G,OAAEA,EAAMU,UAAEA,GAAcqL,EACpC1K,EAAe,CACbjI,KAAAA,EACA4G,OAAAA,EACAU,UAAAA,GAEFY,EAAMwK,WAAWC,QAEjBzK,EAAMgL,EAUR,GAPgC,mBAArBC,EACTG,EAAWH,GAEXG,EAAWH,EAAiBI,OAC5BH,EAAUD,EAAiBK,MACdL,EAAiBE,aAE3BC,EACH,MAAM,IAAI9O,iBAAiBD,EAAuB,uBAEpD,OAAO2D,EAAIhB,YAAYqK,cAAc3I,gBACnCV,EACAoL,EACAF,EACAnL,IC3EE,SAAUwL,oBAAoBC,IjBN9B,SAAUC,cAAcC,GAC5BzQ,EAAcyQ,EiBMdD,CAAcxQ,GACd0Q,EACE,IAAIlT,UACF,gBACA,CAACmT,GAAavB,mBAAoBwB,EAAU3M,QAAAA,MAC1C,MAAMD,EAAM2M,EAAUE,YAAY,OAAOxQ,eACnCgJ,EAAesH,EAAUE,YAAY,iBACrC1Q,EAAmBwQ,EAAUE,YAAY,sBAC/C,IAAIC,EAAU7M,EAId,GAHI2M,IACFE,EAAUlO,KAAKoL,MAAM4C,KAElB5M,EAAIC,QAAQH,UACf,MAAM,IAAIzC,iBACRD,EACA,qFAGJ,OAAO,IAAI+L,YACTnJ,EACK5H,OAAA2K,OAAA3K,OAAA2K,OAAA,GAAA+J,GAAO,CAAEhN,UAAWE,EAAIC,QAAQH,YACrCuF,EACAlJ,eAIJlC,sBAAqB,IAEzB8S,EAAgB5U,UAAeoU,GAE/BQ,EAAgB5U,UAAe,WCjCjCmU", "preExistingComment": "firebase-data-connect.js.map"}