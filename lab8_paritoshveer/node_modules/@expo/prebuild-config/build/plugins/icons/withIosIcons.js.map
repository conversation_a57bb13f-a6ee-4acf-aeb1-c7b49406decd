{"version": 3, "file": "withIosIcons.js", "names": ["_configPlugins", "data", "require", "_imageUtils", "fs", "_interopRequireWildcard", "_path", "_AssetContents", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "getProjectName", "IOSConfig", "XcodeUtils", "IMAGE_CACHE_NAME", "IMAGESET_PATH", "withIosIcons", "config", "withDangerousMod", "setIconsAsync", "modRequest", "projectRoot", "exports", "getIcons", "ios", "icon", "WarningAggregator", "addWarningIOS", "iosNamedProjectRoot", "getIosNamedProjectPath", "ensureDir", "join", "imagesJson", "generateUniversalIconAsync", "cache<PERSON>ey", "platform", "writeContentsJsonAsync", "images", "projectName", "getAppleIconName", "size", "scale", "filename", "source", "generateImageAsync", "cacheType", "src", "name", "width", "height", "removeTransparency", "resizeMode", "backgroundColor", "createSquareAsync", "assetPath", "writeFile", "idiom"], "sources": ["../../../src/plugins/icons/withIosIcons.ts"], "sourcesContent": ["import { ConfigPlugin, IOSConfig, WarningAggregator, withDangerousMod } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\nimport { createSquareAsync, generateImageAsync } from '@expo/image-utils';\nimport * as fs from 'fs-extra';\nimport { join } from 'path';\n\nimport { ContentsJson, writeContentsJsonAsync } from './AssetContents';\n\nconst { getProjectName } = IOSConfig.XcodeUtils;\n\nconst IMAGE_CACHE_NAME = 'icons';\nconst IMAGESET_PATH = 'Images.xcassets/AppIcon.appiconset';\n\nexport const withIosIcons: ConfigPlugin = (config) => {\n  return withDangerousMod(config, [\n    'ios',\n    async (config) => {\n      await setIconsAsync(config, config.modRequest.projectRoot);\n      return config;\n    },\n  ]);\n};\n\nexport function getIcons(config: Pick<ExpoConfig, 'icon' | 'ios'>): string | null {\n  // No support for empty strings.\n  return config.ios?.icon || config.icon || null;\n}\n\nexport async function setIconsAsync(config: ExpoConfig, projectRoot: string) {\n  const icon = getIcons(config);\n  if (!icon) {\n    WarningAggregator.addWarningIOS('icon', 'No icon is defined in the Expo config.');\n  }\n  // Something like projectRoot/ios/MyApp/\n  const iosNamedProjectRoot = getIosNamedProjectPath(projectRoot);\n\n  // Ensure the Images.xcassets/AppIcon.appiconset path exists\n  await fs.ensureDir(join(iosNamedProjectRoot, IMAGESET_PATH));\n\n  // Store the image JSON data for assigning via the Contents.json\n  const imagesJson: ContentsJson['images'] = await generateUniversalIconAsync(projectRoot, {\n    icon,\n    cacheKey: 'universal-icon',\n    iosNamedProjectRoot,\n    platform: 'ios',\n  });\n\n  // Finally, write the Config.json\n  await writeContentsJsonAsync(join(iosNamedProjectRoot, IMAGESET_PATH), { images: imagesJson });\n}\n\n/**\n * Return the project's named iOS path: ios/MyProject/\n *\n * @param projectRoot Expo project root path.\n */\nfunction getIosNamedProjectPath(projectRoot: string): string {\n  const projectName = getProjectName(projectRoot);\n  return join(projectRoot, 'ios', projectName);\n}\n\nfunction getAppleIconName(size: number, scale: number): string {\n  return `App-Icon-${size}x${size}@${scale}x.png`;\n}\n\nexport async function generateUniversalIconAsync(\n  projectRoot: string,\n  {\n    icon,\n    cacheKey,\n    iosNamedProjectRoot,\n    platform,\n  }: {\n    platform: 'watchos' | 'ios';\n    icon?: string | null;\n    iosNamedProjectRoot: string;\n    cacheKey: string;\n  }\n): Promise<ContentsJson['images']> {\n  const size = 1024;\n  const filename = getAppleIconName(size, 1);\n\n  let source: Buffer;\n\n  if (icon) {\n    // Using this method will cache the images in `.expo` based on the properties used to generate them.\n    // this method also supports remote URLs and using the global sharp instance.\n    source = (\n      await generateImageAsync(\n        { projectRoot, cacheType: IMAGE_CACHE_NAME + cacheKey },\n        {\n          src: icon,\n          name: filename,\n          width: size,\n          height: size,\n          removeTransparency: true,\n          // The icon should be square, but if it's not then it will be cropped.\n          resizeMode: 'cover',\n          // Force the background color to solid white to prevent any transparency.\n          // TODO: Maybe use a more adaptive option based on the icon color?\n          backgroundColor: '#ffffff',\n        }\n      )\n    ).source;\n  } else {\n    // Create a white square image if no icon exists to mitigate the chance of a submission failure to the app store.\n    source = await createSquareAsync({ size });\n  }\n  // Write image buffer to the file system.\n  const assetPath = join(iosNamedProjectRoot, IMAGESET_PATH, filename);\n  await fs.writeFile(assetPath, source);\n\n  return [\n    {\n      filename: getAppleIconName(size, 1),\n      idiom: 'universal',\n      platform,\n      size: `${size}x${size}`,\n    },\n  ];\n}\n"], "mappings": ";;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,GAAA;EAAA,MAAAH,IAAA,GAAAI,uBAAA,CAAAH,OAAA;EAAAE,EAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAM,eAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,cAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuE,SAAAO,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEvE,MAAM;EAAEY;AAAe,CAAC,GAAGC,0BAAS,CAACC,UAAU;AAE/C,MAAMC,gBAAgB,GAAG,OAAO;AAChC,MAAMC,aAAa,GAAG,oCAAoC;AAEnD,MAAMC,YAA0B,GAAIC,MAAM,IAAK;EACpD,OAAO,IAAAC,iCAAgB,EAACD,MAAM,EAAE,CAC9B,KAAK,EACL,MAAOA,MAAM,IAAK;IAChB,MAAME,aAAa,CAACF,MAAM,EAAEA,MAAM,CAACG,UAAU,CAACC,WAAW,CAAC;IAC1D,OAAOJ,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAN,YAAA,GAAAA,YAAA;AAEK,SAASO,QAAQA,CAACN,MAAwC,EAAiB;EAChF;EACA,OAAOA,MAAM,CAACO,GAAG,EAAEC,IAAI,IAAIR,MAAM,CAACQ,IAAI,IAAI,IAAI;AAChD;AAEO,eAAeN,aAAaA,CAACF,MAAkB,EAAEI,WAAmB,EAAE;EAC3E,MAAMI,IAAI,GAAGF,QAAQ,CAACN,MAAM,CAAC;EAC7B,IAAI,CAACQ,IAAI,EAAE;IACTC,kCAAiB,CAACC,aAAa,CAAC,MAAM,EAAE,wCAAwC,CAAC;EACnF;EACA;EACA,MAAMC,mBAAmB,GAAGC,sBAAsB,CAACR,WAAW,CAAC;;EAE/D;EACA,MAAMnC,EAAE,CAAD,CAAC,CAAC4C,SAAS,CAAC,IAAAC,YAAI,EAACH,mBAAmB,EAAEb,aAAa,CAAC,CAAC;;EAE5D;EACA,MAAMiB,UAAkC,GAAG,MAAMC,0BAA0B,CAACZ,WAAW,EAAE;IACvFI,IAAI;IACJS,QAAQ,EAAE,gBAAgB;IAC1BN,mBAAmB;IACnBO,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,IAAAC,uCAAsB,EAAC,IAAAL,YAAI,EAACH,mBAAmB,EAAEb,aAAa,CAAC,EAAE;IAAEsB,MAAM,EAAEL;EAAW,CAAC,CAAC;AAChG;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASH,sBAAsBA,CAACR,WAAmB,EAAU;EAC3D,MAAMiB,WAAW,GAAG3B,cAAc,CAACU,WAAW,CAAC;EAC/C,OAAO,IAAAU,YAAI,EAACV,WAAW,EAAE,KAAK,EAAEiB,WAAW,CAAC;AAC9C;AAEA,SAASC,gBAAgBA,CAACC,IAAY,EAAEC,KAAa,EAAU;EAC7D,OAAO,YAAYD,IAAI,IAAIA,IAAI,IAAIC,KAAK,OAAO;AACjD;AAEO,eAAeR,0BAA0BA,CAC9CZ,WAAmB,EACnB;EACEI,IAAI;EACJS,QAAQ;EACRN,mBAAmB;EACnBO;AAMF,CAAC,EACgC;EACjC,MAAMK,IAAI,GAAG,IAAI;EACjB,MAAME,QAAQ,GAAGH,gBAAgB,CAACC,IAAI,EAAE,CAAC,CAAC;EAE1C,IAAIG,MAAc;EAElB,IAAIlB,IAAI,EAAE;IACR;IACA;IACAkB,MAAM,GAAG,CACP,MAAM,IAAAC,gCAAkB,EACtB;MAAEvB,WAAW;MAAEwB,SAAS,EAAE/B,gBAAgB,GAAGoB;IAAS,CAAC,EACvD;MACEY,GAAG,EAAErB,IAAI;MACTsB,IAAI,EAAEL,QAAQ;MACdM,KAAK,EAAER,IAAI;MACXS,MAAM,EAAET,IAAI;MACZU,kBAAkB,EAAE,IAAI;MACxB;MACAC,UAAU,EAAE,OAAO;MACnB;MACA;MACAC,eAAe,EAAE;IACnB,CACF,CAAC,EACDT,MAAM;EACV,CAAC,MAAM;IACL;IACAA,MAAM,GAAG,MAAM,IAAAU,+BAAiB,EAAC;MAAEb;IAAK,CAAC,CAAC;EAC5C;EACA;EACA,MAAMc,SAAS,GAAG,IAAAvB,YAAI,EAACH,mBAAmB,EAAEb,aAAa,EAAE2B,QAAQ,CAAC;EACpE,MAAMxD,EAAE,CAAD,CAAC,CAACqE,SAAS,CAACD,SAAS,EAAEX,MAAM,CAAC;EAErC,OAAO,CACL;IACED,QAAQ,EAAEH,gBAAgB,CAACC,IAAI,EAAE,CAAC,CAAC;IACnCgB,KAAK,EAAE,WAAW;IAClBrB,QAAQ;IACRK,IAAI,EAAE,GAAGA,IAAI,IAAIA,IAAI;EACvB,CAAC,CACF;AACH", "ignoreList": []}