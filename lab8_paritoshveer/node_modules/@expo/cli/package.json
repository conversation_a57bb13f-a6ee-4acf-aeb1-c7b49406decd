{"name": "@expo/cli", "version": "0.18.31", "description": "The Expo CLI", "main": "build/bin/cli", "bin": {"expo-internal": "build/bin/cli"}, "files": ["static", "build"], "scripts": {"build": "taskr", "prepare": "taskr release", "clean": "expo-module clean", "lint": "expo-module lint", "typecheck": "expo-module typecheck", "test": "expo-module test", "test:e2e": "jest --config e2e/jest.config.js", "test:playwright": "playwright test", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module", "generate-graphql-code": "graphql-codegen --config graphql-codegen.yml"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/cli"}, "keywords": ["expo"], "author": "Expo", "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/cli", "dependencies": {"@babel/runtime": "^7.20.0", "@expo/code-signing-certificates": "0.0.5", "@expo/config": "~9.0.0-beta.0", "@expo/config-plugins": "~8.0.8", "@expo/devcert": "^1.0.0", "@expo/env": "~0.3.0", "@expo/image-utils": "^0.5.0", "@expo/json-file": "^8.3.0", "@expo/metro-config": "0.18.11", "@expo/osascript": "^2.0.31", "@expo/package-manager": "^1.5.0", "@expo/plist": "^0.1.0", "@expo/prebuild-config": "7.0.9", "@expo/rudder-sdk-node": "1.1.1", "@expo/spawn-async": "^1.7.2", "@expo/xcpretty": "^4.3.0", "@react-native/dev-middleware": "0.74.85", "@urql/core": "2.3.6", "@urql/exchange-retry": "0.3.0", "accepts": "^1.3.8", "arg": "5.0.2", "better-opn": "~3.0.2", "bplist-creator": "0.0.7", "bplist-parser": "^0.3.1", "cacache": "^18.0.2", "chalk": "^4.0.0", "ci-info": "^3.3.0", "connect": "^3.7.0", "debug": "^4.3.4", "env-editor": "^0.4.1", "fast-glob": "^3.3.2", "find-yarn-workspace-root": "~2.0.0", "form-data": "^3.0.1", "freeport-async": "2.0.0", "fs-extra": "~8.1.0", "getenv": "^1.0.0", "glob": "^7.1.7", "graphql": "15.8.0", "graphql-tag": "^2.10.1", "https-proxy-agent": "^5.0.1", "internal-ip": "4.3.0", "is-docker": "^2.0.0", "is-wsl": "^2.1.1", "js-yaml": "^3.13.1", "json-schema-deref-sync": "^0.13.0", "lodash.debounce": "^4.0.8", "md5hex": "^1.0.0", "minimatch": "^3.0.4", "node-fetch": "^2.6.7", "node-forge": "^1.3.1", "npm-package-arg": "^7.0.0", "open": "^8.3.0", "ora": "3.4.0", "picomatch": "^3.0.1", "pretty-bytes": "5.6.0", "progress": "2.0.3", "prompts": "^2.3.2", "qrcode-terminal": "0.11.0", "require-from-string": "^2.0.2", "requireg": "^0.2.2", "resolve": "^1.22.2", "resolve-from": "^5.0.0", "resolve.exports": "^2.0.2", "semver": "^7.6.0", "send": "^0.18.0", "slugify": "^1.3.4", "source-map-support": "~0.5.21", "stacktrace-parser": "^0.1.10", "structured-headers": "^0.4.1", "tar": "^6.0.5", "temp-dir": "^2.0.0", "tempy": "^0.7.1", "terminal-link": "^2.1.1", "text-table": "^0.2.0", "url-join": "4.0.0", "wrap-ansi": "^7.0.0", "ws": "^8.12.1"}, "taskr": {"requires": ["./taskfile-swc.js"]}, "devDependencies": {"@expo/multipart-body-parser": "^1.0.0", "@expo/ngrok": "4.1.3", "@expo/server": "^0.4.0", "@expo/webpack-config": "^0.17.4", "@graphql-codegen/cli": "2.16.3", "@graphql-codegen/typescript": "2.8.7", "@graphql-codegen/typescript-operations": "2.5.12", "@playwright/test": "^1.40.1", "@swc/core": "~1.2.249", "@taskr/clear": "1.1.0", "@taskr/esnext": "1.1.0", "@taskr/watch": "1.1.0", "@types/accepts": "^1.3.5", "@types/cacache": "^17.0.2", "@types/connect": "^3.4.33", "@types/debug": "^4.1.7", "@types/execa": "^0.9.0", "@types/form-data": "^2.2.0", "@types/getenv": "^1.0.0", "@types/js-yaml": "^3.12.2", "@types/klaw-sync": "^6.0.0", "@types/lodash.debounce": "^4.0.9", "@types/npm-package-arg": "^6.1.0", "@types/picomatch": "^2.3.3", "@types/progress": "^2.0.5", "@types/prompts": "^2.0.6", "@types/resolve": "^1.20.2", "@types/semver": "^7.5.8", "@types/send": "^0.17.1", "@types/tar": "^6.1.1", "@types/text-table": "^0.2.1", "@types/url-join": "^4.0.0", "@types/webpack": "~4.41.32", "@types/webpack-dev-server": "3.11.0", "@types/wrap-ansi": "^8.0.1", "@types/ws": "^8.5.4", "devtools-protocol": "^0.0.1113120", "expo-atlas": "^0.3.0", "expo-module-scripts": "^3.0.0", "find-process": "^1.4.7", "jest-runner-tsd": "^6.0.0", "klaw-sync": "^6.0.0", "memfs": "^3.2.0", "nock": "~13.2.2", "node-html-parser": "^6.1.5", "nullthrows": "^1.1.1", "playwright": "^1.40.1", "taskr": "1.1.0", "tree-kill": "^1.2.2", "tsd": "^0.28.1"}, "gitHead": "672a1e49651614f047f5f104fa2bb65ac7fb050e"}