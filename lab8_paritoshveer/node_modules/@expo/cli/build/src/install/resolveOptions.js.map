{"version": 3, "sources": ["../../../src/install/resolveOptions.ts"], "sourcesContent": ["import { NodePackageManagerForProject } from '@expo/package-manager';\n\nimport { CommandError } from '../utils/errors';\nimport { assertUnexpectedVariadicFlags, parseVariadicArguments } from '../utils/variadic';\n\nexport type Options = Pick<NodePackageManagerForProject, 'npm' | 'pnpm' | 'yarn' | 'bun'> & {\n  /** Check which packages need to be updated, does not install any provided packages. */\n  check?: boolean;\n  /** Should the dependencies be fixed automatically. */\n  fix?: boolean;\n  /** Should disable install output, used for commands like `prebuild` that run install internally. */\n  silent?: boolean;\n  /** Should be installed as dev dependencies */\n  dev?: boolean;\n};\n\nfunction resolveOptions(options: Options): Options {\n  if (options.fix && options.check) {\n    throw new CommandError('BAD_ARGS', 'Specify at most one of: --check, --fix');\n  }\n  if ([options.npm, options.pnpm, options.yarn, options.bun].filter(Boolean).length > 1) {\n    throw new CommandError('BAD_ARGS', 'Specify at most one of: --npm, --pnpm, --yarn, --bun');\n  }\n  return {\n    ...options,\n  };\n}\n\nexport async function resolveArgsAsync(\n  argv: string[]\n): Promise<{ variadic: string[]; options: Options; extras: string[] }> {\n  const { variadic, extras, flags } = parseVariadicArguments(argv);\n\n  assertUnexpectedVariadicFlags(\n    ['--check', '--fix', '--npm', '--pnpm', '--yarn', '--bun'],\n    { variadic, extras, flags },\n    'npx expo install'\n  );\n\n  return {\n    // Variadic arguments like `npx expo install react react-dom` -> ['react', 'react-dom']\n    variadic,\n    options: resolveOptions({\n      fix: !!flags['--fix'],\n      check: !!flags['--check'],\n      yarn: !!flags['--yarn'],\n      npm: !!flags['--npm'],\n      pnpm: !!flags['--pnpm'],\n      bun: !!flags['--bun'],\n    }),\n    extras,\n  };\n}\n"], "names": ["resolveArgsAsync", "resolveOptions", "options", "fix", "check", "CommandError", "npm", "pnpm", "yarn", "bun", "filter", "Boolean", "length", "argv", "variadic", "extras", "flags", "parseVariadicArguments", "assertUnexpectedVariadicFlags"], "mappings": "AAAA;;;;+BA4BsBA,kBAAgB;;aAAhBA,gBAAgB;;wBA1BT,iBAAiB;0BACwB,mBAAmB;AAazF,SAASC,cAAc,CAACC,OAAgB,EAAW;IACjD,IAAIA,OAAO,CAACC,GAAG,IAAID,OAAO,CAACE,KAAK,EAAE;QAChC,MAAM,IAAIC,OAAY,aAAA,CAAC,UAAU,EAAE,wCAAwC,CAAC,CAAC;IAC/E,CAAC;IACD,IAAI;QAACH,OAAO,CAACI,GAAG;QAAEJ,OAAO,CAACK,IAAI;QAAEL,OAAO,CAACM,IAAI;QAAEN,OAAO,CAACO,GAAG;KAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;QACrF,MAAM,IAAIP,OAAY,aAAA,CAAC,UAAU,EAAE,sDAAsD,CAAC,CAAC;IAC7F,CAAC;IACD,OAAO;QACL,GAAGH,OAAO;KACX,CAAC;AACJ,CAAC;AAEM,eAAeF,gBAAgB,CACpCa,IAAc,EACuD;IACrE,MAAM,EAAEC,QAAQ,CAAA,EAAEC,MAAM,CAAA,EAAEC,KAAK,CAAA,EAAE,GAAGC,IAAAA,SAAsB,uBAAA,EAACJ,IAAI,CAAC,AAAC;IAEjEK,IAAAA,SAA6B,8BAAA,EAC3B;QAAC,SAAS;QAAE,OAAO;QAAE,OAAO;QAAE,QAAQ;QAAE,QAAQ;QAAE,OAAO;KAAC,EAC1D;QAAEJ,QAAQ;QAAEC,MAAM;QAAEC,KAAK;KAAE,EAC3B,kBAAkB,CACnB,CAAC;IAEF,OAAO;QACL,uFAAuF;QACvFF,QAAQ;QACRZ,OAAO,EAAED,cAAc,CAAC;YACtBE,GAAG,EAAE,CAAC,CAACa,KAAK,CAAC,OAAO,CAAC;YACrBZ,KAAK,EAAE,CAAC,CAACY,KAAK,CAAC,SAAS,CAAC;YACzBR,IAAI,EAAE,CAAC,CAACQ,KAAK,CAAC,QAAQ,CAAC;YACvBV,GAAG,EAAE,CAAC,CAACU,KAAK,CAAC,OAAO,CAAC;YACrBT,IAAI,EAAE,CAAC,CAACS,KAAK,CAAC,QAAQ,CAAC;YACvBP,GAAG,EAAE,CAAC,CAACO,KAAK,CAAC,OAAO,CAAC;SACtB,CAAC;QACFD,MAAM;KACP,CAAC;AACJ,CAAC"}