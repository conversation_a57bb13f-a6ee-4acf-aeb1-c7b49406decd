{"version": 3, "sources": ["../../../src/register/registerAsync.ts"], "sourcesContent": ["import { env } from '../utils/env';\nimport { CommandError } from '../utils/errors';\nimport { isInteractive } from '../utils/interactive';\nimport { learnMore } from '../utils/link';\nimport { openBrowserAsync } from '../utils/open';\nimport { ora } from '../utils/ora';\n\nexport async function registerAsync() {\n  if (!isInteractive()) {\n    throw new CommandError(\n      'NON_INTERACTIVE',\n      `Cannot register an account in CI. Use the EXPO_TOKEN environment variable to authenticate in CI (${learnMore(\n        'https://docs.expo.dev/accounts/programmatic-access/'\n      )})`\n    );\n  } else if (env.EXPO_OFFLINE) {\n    throw new CommandError('OFFLINE', `Cannot register an account in offline-mode`);\n  }\n\n  const registrationUrl = `https://expo.dev/signup`;\n  const failedMessage = `Unable to open a web browser. Register an account at: ${registrationUrl}`;\n  const spinner = ora(`Opening ${registrationUrl}`).start();\n  try {\n    const opened = await openBrowserAsync(registrationUrl);\n\n    if (opened) {\n      spinner.succeed(`Opened ${registrationUrl}`);\n    } else {\n      spinner.fail(failedMessage);\n    }\n  } catch (error) {\n    spinner.fail(failedMessage);\n    throw error;\n  }\n}\n"], "names": ["registerAsync", "isInteractive", "CommandError", "learnMore", "env", "EXPO_OFFLINE", "registrationUrl", "failedMessage", "spinner", "ora", "start", "opened", "openBrowserAsync", "succeed", "fail", "error"], "mappings": "AAAA;;;;+BAOs<PERSON>,eAAa;;aAAbA,aAAa;;qBAPf,cAAc;wBACL,iBAAiB;6BAChB,sBAAsB;sBAC1B,eAAe;sBACR,eAAe;qBAC5B,cAAc;AAE3B,eAAeA,aAAa,GAAG;IACpC,IAAI,CAACC,IAAAA,YAAa,cAAA,GAAE,EAAE;QACpB,MAAM,IAAIC,OAAY,aAAA,CACpB,iBAAiB,EACjB,CAAC,iGAAiG,EAAEC,IAAAA,KAAS,UAAA,EAC3G,qDAAqD,CACtD,CAAC,CAAC,CAAC,CACL,CAAC;IACJ,OAAO,IAAIC,IAAG,IAAA,CAACC,YAAY,EAAE;QAC3B,MAAM,IAAIH,OAAY,aAAA,CAAC,SAAS,EAAE,CAAC,0CAA0C,CAAC,CAAC,CAAC;IAClF,CAAC;IAED,MAAMI,eAAe,GAAG,CAAC,uBAAuB,CAAC,AAAC;IAClD,MAAMC,aAAa,GAAG,CAAC,sDAAsD,EAAED,eAAe,CAAC,CAAC,AAAC;IACjG,MAAME,OAAO,GAAGC,IAAAA,IAAG,IAAA,EAAC,CAAC,QAAQ,EAAEH,eAAe,CAAC,CAAC,CAAC,CAACI,KAAK,EAAE,AAAC;IAC1D,IAAI;QACF,MAAMC,MAAM,GAAG,MAAMC,IAAAA,KAAgB,iBAAA,EAACN,eAAe,CAAC,AAAC;QAEvD,IAAIK,MAAM,EAAE;YACVH,OAAO,CAACK,OAAO,CAAC,CAAC,OAAO,EAAEP,eAAe,CAAC,CAAC,CAAC,CAAC;QAC/C,OAAO;YACLE,OAAO,CAACM,IAAI,CAACP,aAAa,CAAC,CAAC;QAC9B,CAAC;IACH,EAAE,OAAOQ,KAAK,EAAE;QACdP,OAAO,CAACM,IAAI,CAACP,aAAa,CAAC,CAAC;QAC5B,MAAMQ,KAAK,CAAC;IACd,CAAC;AACH,CAAC"}