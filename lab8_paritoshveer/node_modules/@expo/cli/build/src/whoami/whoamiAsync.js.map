{"version": 3, "sources": ["../../../src/whoami/whoamiAsync.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { getActorDisplayName, getUserAsync } from '../api/user/user';\nimport * as Log from '../log';\n\nexport async function whoamiAsync() {\n  const user = await getUserAsync();\n  if (user) {\n    Log.exit(chalk.green(getActorDisplayName(user)), 0);\n  } else {\n    Log.exit('Not logged in');\n  }\n}\n"], "names": ["<PERSON>ami<PERSON><PERSON>", "user", "getUserAsync", "Log", "exit", "chalk", "green", "getActorDisplayName"], "mappings": "AAAA;;;;+BAKs<PERSON>,aAAW;;aAAXA,WAAW;;;8DALf,OAAO;;;;;;sBAEyB,kBAAkB;2DAC/C,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,eAAeA,WAAW,GAAG;IAClC,MAAMC,IAAI,GAAG,MAAMC,IAAAA,KAAY,aAAA,GAAE,AAAC;IAClC,IAAID,IAAI,EAAE;QACRE,IAAG,CAACC,IAAI,CAACC,MAAK,EAAA,QAAA,CAACC,KAAK,CAACC,IAAAA,KAAmB,oBAAA,EAACN,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,OAAO;QACLE,IAAG,CAACC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC5B,CAAC;AACH,CAAC"}