{"version": 3, "sources": ["../../../src/export/writeContents.ts"], "sourcesContent": ["import { Asset } from './saveAssets';\n\nexport function createAssetMap({ assets }: { assets: Asset[] }) {\n  // Convert the assets array to a k/v pair where the asset hash is the key and the asset is the value.\n  return Object.fromEntries(assets.map((asset) => [asset.hash, asset]));\n}\n\nexport function createSourceMapDebugHtml({ fileNames }: { fileNames: string[] }) {\n  // Make a debug html so user can debug their bundles\n  return `\n      ${fileNames\n        .filter((value) => value != null)\n        .map((fileName) => `<script src=\"${fileName}\"></script>`)\n        .join('\\n      ')}\n      Open up this file in Chrome. In the JavaScript developer console, navigate to the Source tab.\n      You can see a red colored folder containing the original source code from your bundle.\n      `;\n}\n"], "names": ["createAssetMap", "createSourceMapDebugHtml", "assets", "Object", "fromEntries", "map", "asset", "hash", "fileNames", "filter", "value", "fileName", "join"], "mappings": "AAAA;;;;;;;;;;;IAEgBA,cAAc,MAAdA,cAAc;IAKdC,wBAAwB,MAAxBA,wBAAwB;;AALjC,SAASD,cAAc,CAAC,EAAEE,MAAM,CAAA,EAAuB,EAAE;IAC9D,qGAAqG;IACrG,OAAOC,MAAM,CAACC,WAAW,CAACF,MAAM,CAACG,GAAG,CAAC,CAACC,KAAK,GAAK;YAACA,KAAK,CAACC,IAAI;YAAED,KAAK;SAAC,CAAC,CAAC,CAAC;AACxE,CAAC;AAEM,SAASL,wBAAwB,CAAC,EAAEO,SAAS,CAAA,EAA2B,EAAE;IAC/E,oDAAoD;IACpD,OAAO,CAAC;MACJ,EAAEA,SAAS,CACRC,MAAM,CAAC,CAACC,KAAK,GAAKA,KAAK,IAAI,IAAI,CAAC,CAChCL,GAAG,CAAC,CAACM,QAAQ,GAAK,CAAC,aAAa,EAAEA,QAAQ,CAAC,WAAW,CAAC,CAAC,CACxDC,IAAI,CAAC,UAAU,CAAC,CAAC;;;MAGpB,CAAC,CAAC;AACR,CAAC"}