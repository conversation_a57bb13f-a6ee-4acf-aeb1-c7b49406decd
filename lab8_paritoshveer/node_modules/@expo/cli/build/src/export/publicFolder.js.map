{"version": 3, "sources": ["../../../src/export/publicFolder.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport { copyAsync } from '../utils/dir';\nimport { env } from '../utils/env';\n\nconst debug = require('debug')('expo:public-folder') as typeof console.log;\n\n/** @returns the file system path for a user-defined file in the public folder. */\nexport function getUserDefinedFile(projectRoot: string, possiblePaths: string[]): string | null {\n  const publicPath = path.join(projectRoot, env.EXPO_PUBLIC_FOLDER);\n\n  for (const possiblePath of possiblePaths) {\n    const fullPath = path.join(publicPath, possiblePath);\n    if (fs.existsSync(fullPath)) {\n      debug(`Found user-defined public file: ` + possiblePath);\n      return fullPath;\n    }\n  }\n\n  return null;\n}\n\n/**\n * Copy the contents of the public folder into the output folder.\n * This enables users to add static files like `favicon.ico` or `serve.json`.\n *\n * The contents of this folder are completely universal since they refer to\n * static network requests which fall outside the scope of React Native's magic\n * platform resolution patterns.\n */\nexport async function copyPublicFolderAsync(publicFolder: string, outputFolder: string) {\n  if (fs.existsSync(publicFolder)) {\n    await copyAsync(publicFolder, outputFolder);\n  }\n}\n"], "names": ["getUserDefinedFile", "copyPublicFolderAsync", "debug", "require", "projectRoot", "possiblePaths", "publicPath", "path", "join", "env", "EXPO_PUBLIC_FOLDER", "<PERSON><PERSON><PERSON>", "fullPath", "fs", "existsSync", "publicFolder", "outputFolder", "copyAsync"], "mappings": "AAAA;;;;;;;;;;;IASgBA,kBAAkB,MAAlBA,kBAAkB;IAsBZC,qBAAqB,MAArBA,qBAAqB;;;8DA/B5B,IAAI;;;;;;;8DACF,MAAM;;;;;;qBAEG,cAAc;qBACpB,cAAc;;;;;;AAElC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,AAAsB,AAAC;AAGpE,SAASH,kBAAkB,CAACI,WAAmB,EAAEC,aAAuB,EAAiB;IAC9F,MAAMC,UAAU,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACJ,WAAW,EAAEK,IAAG,IAAA,CAACC,kBAAkB,CAAC,AAAC;IAElE,KAAK,MAAMC,YAAY,IAAIN,aAAa,CAAE;QACxC,MAAMO,QAAQ,GAAGL,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,UAAU,EAAEK,YAAY,CAAC,AAAC;QACrD,IAAIE,GAAE,EAAA,QAAA,CAACC,UAAU,CAACF,QAAQ,CAAC,EAAE;YAC3BV,KAAK,CAAC,CAAC,gCAAgC,CAAC,GAAGS,YAAY,CAAC,CAAC;YACzD,OAAOC,QAAQ,CAAC;QAClB,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAUM,eAAeX,qBAAqB,CAACc,YAAoB,EAAEC,YAAoB,EAAE;IACtF,IAAIH,GAAE,EAAA,QAAA,CAACC,UAAU,CAACC,YAAY,CAAC,EAAE;QAC/B,MAAME,IAAAA,IAAS,UAAA,EAACF,YAAY,EAAEC,YAAY,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC"}