{"version": 3, "sources": ["../../../src/export/resolveOptions.ts"], "sourcesContent": ["import { ExpoConfig, getConfig, Platform } from '@expo/config';\n\nimport { getPlatformBundlers, PlatformBundlers } from '../start/server/platformBundlers';\nimport { CommandError } from '../utils/errors';\n\nexport type Options = {\n  outputDir: string;\n  platforms: Platform[];\n  maxWorkers?: number;\n  dev: boolean;\n  clear: boolean;\n  minify: boolean;\n  bytecode: boolean;\n  dumpAssetmap: boolean;\n  sourceMaps: boolean;\n};\n\n/** Returns an array of platforms based on the input platform identifier and runtime constraints. */\nexport function resolvePlatformOption(\n  exp: ExpoConfig,\n  platformBundlers: PlatformBundlers,\n  platform: string[] = ['all']\n): Platform[] {\n  const platformsAvailable: Partial<PlatformBundlers> = Object.fromEntries(\n    Object.entries(platformBundlers).filter(\n      ([platform, bundler]) => bundler === 'metro' && exp.platforms?.includes(platform as Platform)\n    )\n  );\n\n  if (!Object.keys(platformsAvailable).length) {\n    throw new CommandError(\n      `No platforms are configured to use the Metro bundler in the project Expo config.`\n    );\n  }\n\n  const assertPlatformBundler = (platform: Platform): Platform => {\n    if (!platformsAvailable[platform]) {\n      if (!exp.platforms?.includes(platform) && platform === 'web') {\n        // Pass through so the more robust error message is shown.\n        return platform;\n      }\n      throw new CommandError(\n        'BAD_ARGS',\n        `Platform \"${platform}\" is not configured to use the Metro bundler in the project Expo config, or is missing from the supported platforms in the platforms array: [${exp.platforms?.join(\n          ', '\n        )}].`\n      );\n    }\n\n    return platform;\n  };\n\n  const knownPlatforms = ['android', 'ios', 'web'] as Platform[];\n  const assertPlatformIsKnown = (platform: string): Platform => {\n    if (!knownPlatforms.includes(platform as Platform)) {\n      throw new CommandError(\n        `Unsupported platform \"${platform}\". Options are: ${knownPlatforms.join(',')},all`\n      );\n    }\n\n    return platform as Platform;\n  };\n\n  return (\n    platform\n      // Expand `all` to all available platforms.\n      .map((platform) => (platform === 'all' ? Object.keys(platformsAvailable) : platform))\n      .flat()\n      // Remove duplicated platforms\n      .filter((platform, index, list) => list.indexOf(platform) === index)\n      // Assert platforms are valid\n      .map((platform) => assertPlatformIsKnown(platform))\n      .map((platform) => assertPlatformBundler(platform))\n  );\n}\n\nexport async function resolveOptionsAsync(projectRoot: string, args: any): Promise<Options> {\n  const { exp } = getConfig(projectRoot, { skipPlugins: true, skipSDKVersionRequirement: true });\n  const platformBundlers = getPlatformBundlers(projectRoot, exp);\n\n  return {\n    platforms: resolvePlatformOption(exp, platformBundlers, args['--platform']),\n    outputDir: args['--output-dir'] ?? 'dist',\n    minify: !args['--no-minify'],\n    bytecode: !args['--no-bytecode'],\n    clear: !!args['--clear'],\n    dev: !!args['--dev'],\n    maxWorkers: args['--max-workers'],\n    dumpAssetmap: !!args['--dump-assetmap'],\n    sourceMaps: !!args['--source-maps'],\n  };\n}\n"], "names": ["resolvePlatformOption", "resolveOptionsAsync", "exp", "platformBundlers", "platform", "platformsAvailable", "Object", "fromEntries", "entries", "filter", "bundler", "platforms", "includes", "keys", "length", "CommandError", "assertPlatformBundler", "join", "knownPlatforms", "assertPlatformIsKnown", "map", "flat", "index", "list", "indexOf", "projectRoot", "args", "getConfig", "skip<PERSON>lug<PERSON>", "skipSDKVersionRequirement", "getPlatformBundlers", "outputDir", "minify", "bytecode", "clear", "dev", "maxWorkers", "dumpAssetmap", "sourceMaps"], "mappings": "AAAA;;;;;;;;;;;IAkBgBA,qBAAqB,MAArBA,qBAAqB;IA0DfC,mBAAmB,MAAnBA,mBAAmB;;;yBA5EO,cAAc;;;;;;kCAER,kCAAkC;wBAC3D,iBAAiB;AAevC,SAASD,qBAAqB,CACnCE,GAAe,EACfC,gBAAkC,EAClCC,QAAkB,GAAG;IAAC,KAAK;CAAC,EAChB;IACZ,MAAMC,kBAAkB,GAA8BC,MAAM,CAACC,WAAW,CACtED,MAAM,CAACE,OAAO,CAACL,gBAAgB,CAAC,CAACM,MAAM,CACrC,CAAC,CAACL,QAAQ,EAAEM,OAAO,CAAC;YAA4BR,GAAa;QAApCQ,OAAAA,OAAO,KAAK,OAAO,KAAIR,CAAAA,GAAa,GAAbA,GAAG,CAACS,SAAS,SAAU,GAAvBT,KAAAA,CAAuB,GAAvBA,GAAa,CAAEU,QAAQ,CAACR,QAAQ,CAAa,CAAA,CAAA;KAAA,CAC9F,CACF,AAAC;IAEF,IAAI,CAACE,MAAM,CAACO,IAAI,CAACR,kBAAkB,CAAC,CAACS,MAAM,EAAE;QAC3C,MAAM,IAAIC,OAAY,aAAA,CACpB,CAAC,gFAAgF,CAAC,CACnF,CAAC;IACJ,CAAC;IAED,MAAMC,qBAAqB,GAAG,CAACZ,QAAkB,GAAe;QAC9D,IAAI,CAACC,kBAAkB,CAACD,QAAQ,CAAC,EAAE;gBAC5BF,GAAa,EAMqJA,IAAa;YANpL,IAAI,EAACA,CAAAA,GAAa,GAAbA,GAAG,CAACS,SAAS,SAAU,GAAvBT,KAAAA,CAAuB,GAAvBA,GAAa,CAAEU,QAAQ,CAACR,QAAQ,CAAC,CAAA,IAAIA,QAAQ,KAAK,KAAK,EAAE;gBAC5D,0DAA0D;gBAC1D,OAAOA,QAAQ,CAAC;YAClB,CAAC;YACD,MAAM,IAAIW,OAAY,aAAA,CACpB,UAAU,EACV,CAAC,UAAU,EAAEX,QAAQ,CAAC,6IAA6I,EAAEF,CAAAA,IAAa,GAAbA,GAAG,CAACS,SAAS,SAAM,GAAnBT,KAAAA,CAAmB,GAAnBA,IAAa,CAAEe,IAAI,CACtL,IAAI,CACL,CAAC,EAAE,CAAC,CACN,CAAC;QACJ,CAAC;QAED,OAAOb,QAAQ,CAAC;IAClB,CAAC,AAAC;IAEF,MAAMc,cAAc,GAAG;QAAC,SAAS;QAAE,KAAK;QAAE,KAAK;KAAC,AAAc,AAAC;IAC/D,MAAMC,qBAAqB,GAAG,CAACf,QAAgB,GAAe;QAC5D,IAAI,CAACc,cAAc,CAACN,QAAQ,CAACR,QAAQ,CAAa,EAAE;YAClD,MAAM,IAAIW,OAAY,aAAA,CACpB,CAAC,sBAAsB,EAAEX,QAAQ,CAAC,gBAAgB,EAAEc,cAAc,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CACnF,CAAC;QACJ,CAAC;QAED,OAAOb,QAAQ,CAAa;IAC9B,CAAC,AAAC;IAEF,OACEA,QAAQ,AACN,2CAA2C;KAC1CgB,GAAG,CAAC,CAAChB,QAAQ,GAAMA,QAAQ,KAAK,KAAK,GAAGE,MAAM,CAACO,IAAI,CAACR,kBAAkB,CAAC,GAAGD,QAAQ,AAAC,CAAC,CACpFiB,IAAI,EAAE,AACP,8BAA8B;KAC7BZ,MAAM,CAAC,CAACL,QAAQ,EAAEkB,KAAK,EAAEC,IAAI,GAAKA,IAAI,CAACC,OAAO,CAACpB,QAAQ,CAAC,KAAKkB,KAAK,CAAC,AACpE,6BAA6B;KAC5BF,GAAG,CAAC,CAAChB,QAAQ,GAAKe,qBAAqB,CAACf,QAAQ,CAAC,CAAC,CAClDgB,GAAG,CAAC,CAAChB,QAAQ,GAAKY,qBAAqB,CAACZ,QAAQ,CAAC,CAAC,CACrD;AACJ,CAAC;AAEM,eAAeH,mBAAmB,CAACwB,WAAmB,EAAEC,IAAS,EAAoB;IAC1F,MAAM,EAAExB,GAAG,CAAA,EAAE,GAAGyB,IAAAA,OAAS,EAAA,UAAA,EAACF,WAAW,EAAE;QAAEG,WAAW,EAAE,IAAI;QAAEC,yBAAyB,EAAE,IAAI;KAAE,CAAC,AAAC;IAC/F,MAAM1B,gBAAgB,GAAG2B,IAAAA,iBAAmB,oBAAA,EAACL,WAAW,EAAEvB,GAAG,CAAC,AAAC;QAIlDwB,GAAoB;IAFjC,OAAO;QACLf,SAAS,EAAEX,qBAAqB,CAACE,GAAG,EAAEC,gBAAgB,EAAEuB,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3EK,SAAS,EAAEL,CAAAA,GAAoB,GAApBA,IAAI,CAAC,cAAc,CAAC,YAApBA,GAAoB,GAAI,MAAM;QACzCM,MAAM,EAAE,CAACN,IAAI,CAAC,aAAa,CAAC;QAC5BO,QAAQ,EAAE,CAACP,IAAI,CAAC,eAAe,CAAC;QAChCQ,KAAK,EAAE,CAAC,CAACR,IAAI,CAAC,SAAS,CAAC;QACxBS,GAAG,EAAE,CAAC,CAACT,IAAI,CAAC,OAAO,CAAC;QACpBU,UAAU,EAAEV,IAAI,CAAC,eAAe,CAAC;QACjCW,YAAY,EAAE,CAAC,CAACX,IAAI,CAAC,iBAAiB,CAAC;QACvCY,UAAU,EAAE,CAAC,CAACZ,IAAI,CAAC,eAAe,CAAC;KACpC,CAAC;AACJ,CAAC"}