{"version": 3, "sources": ["../../../src/export/saveAssets.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport Metro from 'metro';\nimport path from 'path';\nimport prettyBytes from 'pretty-bytes';\n\nimport { Log } from '../log';\n\nexport type BundleOptions = {\n  entryPoint: string;\n  platform: 'android' | 'ios' | 'web';\n  dev?: boolean;\n  minify?: boolean;\n  bytecode: boolean;\n  sourceMapUrl?: string;\n  sourcemaps?: boolean;\n};\n\nexport type BundleAssetWithFileHashes = Metro.AssetData & {\n  fileHashes: string[]; // added by the hashAssets asset plugin\n};\n\nexport type BundleOutput = {\n  artifacts: SerialAsset[];\n  assets: readonly BundleAssetWithFileHashes[];\n};\n\nexport type ManifestAsset = { fileHashes: string[]; files: string[]; hash: string };\n\nexport type Asset = ManifestAsset | BundleAssetWithFileHashes;\n\nexport type ExportAssetDescriptor = {\n  contents: string | Buffer;\n  originFilename?: string;\n  /** An identifier for grouping together variations of the same asset. */\n  assetId?: string;\n  /** Expo Router route path for formatting the HTML output. */\n  routeId?: string;\n  /** A key for grouping together output files by server- or client-side. */\n  targetDomain?: 'server' | 'client';\n};\n\nexport type ExportAssetMap = Map<string, ExportAssetDescriptor>;\n\nexport async function persistMetroFilesAsync(files: ExportAssetMap, outputDir: string) {\n  if (!files.size) {\n    return;\n  }\n  fs.mkdirSync(path.join(outputDir), { recursive: true });\n\n  // Test fixtures:\n  // Log.log(\n  //   JSON.stringify(\n  //     Object.fromEntries([...files.entries()].map(([k, v]) => [k, { ...v, contents: '' }]))\n  //   )\n  // );\n\n  const assetEntries: [string, ExportAssetDescriptor][] = [];\n  const routeEntries: [string, ExportAssetDescriptor][] = [];\n  const remainingEntries: [string, ExportAssetDescriptor][] = [];\n\n  let hasServerOutput = false;\n  for (const asset of files.entries()) {\n    hasServerOutput = hasServerOutput || asset[1].targetDomain === 'server';\n    if (asset[1].assetId) assetEntries.push(asset);\n    else if (asset[1].routeId != null) routeEntries.push(asset);\n    else remainingEntries.push(asset);\n  }\n\n  const groups = groupBy(assetEntries, ([, { assetId }]) => assetId!);\n\n  const contentSize = (contents: string | Buffer) => {\n    const length =\n      typeof contents === 'string' ? Buffer.byteLength(contents, 'utf8') : contents.length;\n    return length;\n  };\n\n  const sizeStr = (contents: string | Buffer) => {\n    const length = contentSize(contents);\n    const size = chalk.gray`(${prettyBytes(length)})`;\n    return size;\n  };\n\n  if (routeEntries.length) {\n    const plural = routeEntries.length === 1 ? '' : 's';\n\n    Log.log('');\n    Log.log(chalk.bold`Exporting ${routeEntries.length} static route${plural}:`);\n\n    for (const [, assets] of routeEntries.sort((a, b) => a[0].length - b[0].length)) {\n      const id = assets.routeId!;\n      Log.log('/' + (id === '' ? chalk.gray(' (index)') : id), sizeStr(assets.contents));\n    }\n  }\n\n  const assetGroups = [...groups.entries()].sort((a, b) => a[0].localeCompare(b[0])) as [\n    string,\n    [string, ExportAssetDescriptor][],\n  ][];\n\n  if (assetGroups.length) {\n    const totalAssets = assetGroups.reduce((sum, [, assets]) => sum + assets.length, 0);\n    const plural = totalAssets === 1 ? '' : 's';\n\n    Log.log('');\n    Log.log(chalk.bold`Exporting ${totalAssets} asset${plural}:`);\n\n    for (const [assetId, assets] of assetGroups) {\n      const averageContentSize =\n        assets.reduce((sum, [, { contents }]) => sum + contentSize(contents), 0) / assets.length;\n      Log.log(\n        assetId,\n        chalk.gray(\n          `(${[\n            assets.length > 1 ? `${assets.length} variations` : '',\n            `${prettyBytes(averageContentSize)}`,\n          ]\n            .filter(Boolean)\n            .join(' | ')})`\n        )\n      );\n    }\n  }\n\n  const bundles: Map<string, [string, ExportAssetDescriptor][]> = new Map();\n  const other: [string, ExportAssetDescriptor][] = [];\n\n  remainingEntries.forEach(([filepath, asset]) => {\n    if (!filepath.match(/_expo\\/static\\//)) {\n      other.push([filepath, asset]);\n    } else {\n      const platform = filepath.match(/_expo\\/static\\/js\\/([^/]+)\\//)?.[1] ?? 'web';\n      if (!bundles.has(platform)) bundles.set(platform, []);\n\n      bundles.get(platform)!.push([filepath, asset]);\n    }\n  });\n\n  [...bundles.entries()].forEach(([platform, assets]) => {\n    Log.log('');\n    const plural = assets.length === 1 ? '' : 's';\n    Log.log(chalk.bold`Exporting ${assets.length} bundle${plural} for ${platform}:`);\n\n    const allAssets = assets.sort((a, b) => a[0].localeCompare(b[0]));\n    while (allAssets.length) {\n      const [filePath, asset] = allAssets.shift()!;\n      Log.log(filePath, sizeStr(asset.contents));\n      if (filePath.match(/\\.(js|hbc)$/)) {\n        // Get source map\n        const sourceMapIndex = allAssets.findIndex(([fp]) => fp === filePath + '.map');\n        if (sourceMapIndex !== -1) {\n          const [sourceMapFilePath, sourceMapAsset] = allAssets.splice(sourceMapIndex, 1)[0];\n          Log.log(chalk.gray(sourceMapFilePath), sizeStr(sourceMapAsset.contents));\n        }\n      }\n    }\n  });\n\n  if (other.length) {\n    Log.log('');\n    const plural = other.length === 1 ? '' : 's';\n    Log.log(chalk.bold`Exporting ${other.length} file${plural}:`);\n\n    for (const [filePath, asset] of other.sort((a, b) => a[0].localeCompare(b[0]))) {\n      Log.log(filePath, sizeStr(asset.contents));\n    }\n  }\n\n  // Decouple logging from writing for better performance.\n\n  await Promise.all(\n    [...files.entries()]\n      .sort(([a], [b]) => a.localeCompare(b))\n      .map(async ([file, { contents, targetDomain }]) => {\n        // NOTE: Only use `targetDomain` if we have at least one server asset\n        const domain = (hasServerOutput && targetDomain) || '';\n        const outputPath = path.join(outputDir, domain, file);\n        await fs.promises.mkdir(path.dirname(outputPath), { recursive: true });\n        await fs.promises.writeFile(outputPath, contents);\n      })\n  );\n\n  Log.log('');\n}\n\nfunction groupBy<T>(array: T[], key: (item: T) => string): Map<string, T[]> {\n  const map = new Map<string, T[]>();\n  array.forEach((item) => {\n    const group = key(item);\n    const list = map.get(group) ?? [];\n    list.push(item);\n    map.set(group, list);\n  });\n  return map;\n}\n\n// TODO: Move source map modification to the serializer\nexport function getFilesFromSerialAssets(\n  resources: SerialAsset[],\n  {\n    includeSourceMaps,\n    files = new Map(),\n    platform,\n  }: {\n    includeSourceMaps: boolean;\n    files?: ExportAssetMap;\n    platform?: string;\n  }\n) {\n  resources.forEach((resource) => {\n    files.set(resource.filename, {\n      contents: resource.source,\n      originFilename: resource.originFilename,\n      targetDomain: platform === 'web' ? 'client' : undefined,\n    });\n  });\n\n  return files;\n}\n"], "names": ["persistMetroFilesAsync", "getFilesFromSerialAssets", "files", "outputDir", "size", "fs", "mkdirSync", "path", "join", "recursive", "assetEntries", "routeEntries", "remainingEntries", "hasServerOutput", "asset", "entries", "targetDomain", "assetId", "push", "routeId", "groups", "groupBy", "contentSize", "contents", "length", "<PERSON><PERSON><PERSON>", "byteLength", "sizeStr", "chalk", "gray", "prettyBytes", "plural", "Log", "log", "bold", "assets", "sort", "a", "b", "id", "assetGroups", "localeCompare", "totalAssets", "reduce", "sum", "averageContentSize", "filter", "Boolean", "bundles", "Map", "other", "for<PERSON>ach", "filepath", "match", "platform", "has", "set", "get", "allAssets", "filePath", "shift", "sourceMapIndex", "findIndex", "fp", "sourceMapFilePath", "sourceMapAsset", "splice", "Promise", "all", "map", "file", "domain", "outputPath", "promises", "mkdir", "dirname", "writeFile", "array", "key", "item", "group", "list", "resources", "includeSourceMaps", "resource", "filename", "source", "originFilename", "undefined"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IA6CsBA,sBAAsB,MAAtBA,sBAAsB;IAyJ5BC,wBAAwB,MAAxBA,wBAAwB;;;8DArMtB,OAAO;;;;;;;8DACV,IAAI;;;;;;;8DAEF,MAAM;;;;;;;8DACC,cAAc;;;;;;qBAElB,QAAQ;;;;;;AAsCrB,eAAeD,sBAAsB,CAACE,KAAqB,EAAEC,SAAiB,EAAE;IACrF,IAAI,CAACD,KAAK,CAACE,IAAI,EAAE;QACf,OAAO;IACT,CAAC;IACDC,GAAE,EAAA,QAAA,CAACC,SAAS,CAACC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACL,SAAS,CAAC,EAAE;QAAEM,SAAS,EAAE,IAAI;KAAE,CAAC,CAAC;IAExD,iBAAiB;IACjB,WAAW;IACX,oBAAoB;IACpB,4FAA4F;IAC5F,MAAM;IACN,KAAK;IAEL,MAAMC,YAAY,GAAsC,EAAE,AAAC;IAC3D,MAAMC,YAAY,GAAsC,EAAE,AAAC;IAC3D,MAAMC,gBAAgB,GAAsC,EAAE,AAAC;IAE/D,IAAIC,eAAe,GAAG,KAAK,AAAC;IAC5B,KAAK,MAAMC,KAAK,IAAIZ,KAAK,CAACa,OAAO,EAAE,CAAE;QACnCF,eAAe,GAAGA,eAAe,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACE,YAAY,KAAK,QAAQ,CAAC;QACxE,IAAIF,KAAK,CAAC,CAAC,CAAC,CAACG,OAAO,EAAEP,YAAY,CAACQ,IAAI,CAACJ,KAAK,CAAC,CAAC;aAC1C,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACK,OAAO,IAAI,IAAI,EAAER,YAAY,CAACO,IAAI,CAACJ,KAAK,CAAC,CAAC;aACvDF,gBAAgB,CAACM,IAAI,CAACJ,KAAK,CAAC,CAAC;IACpC,CAAC;IAED,MAAMM,MAAM,GAAGC,OAAO,CAACX,YAAY,EAAE,CAAC,GAAG,EAAEO,OAAO,CAAA,EAAE,CAAC,GAAKA,OAAO,AAAC,CAAC,AAAC;IAEpE,MAAMK,WAAW,GAAG,CAACC,QAAyB,GAAK;QACjD,MAAMC,MAAM,GACV,OAAOD,QAAQ,KAAK,QAAQ,GAAGE,MAAM,CAACC,UAAU,CAACH,QAAQ,EAAE,MAAM,CAAC,GAAGA,QAAQ,CAACC,MAAM,AAAC;QACvF,OAAOA,MAAM,CAAC;IAChB,CAAC,AAAC;IAEF,MAAMG,OAAO,GAAG,CAACJ,QAAyB,GAAK;QAC7C,MAAMC,MAAM,GAAGF,WAAW,CAACC,QAAQ,CAAC,AAAC;QACrC,MAAMnB,IAAI,GAAGwB,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,CAAC,EAAEC,IAAAA,YAAW,EAAA,QAAA,EAACN,MAAM,CAAC,CAAC,CAAC,CAAC,AAAC;QAClD,OAAOpB,IAAI,CAAC;IACd,CAAC,AAAC;IAEF,IAAIO,YAAY,CAACa,MAAM,EAAE;QACvB,MAAMO,MAAM,GAAGpB,YAAY,CAACa,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,AAAC;QAEpDQ,IAAG,IAAA,CAACC,GAAG,CAAC,EAAE,CAAC,CAAC;QACZD,IAAG,IAAA,CAACC,GAAG,CAACL,MAAK,EAAA,QAAA,CAACM,IAAI,CAAC,UAAU,EAAEvB,YAAY,CAACa,MAAM,CAAC,aAAa,EAAEO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7E,KAAK,MAAM,GAAGI,MAAM,CAAC,IAAIxB,YAAY,CAACyB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKD,CAAC,CAAC,CAAC,CAAC,CAACb,MAAM,GAAGc,CAAC,CAAC,CAAC,CAAC,CAACd,MAAM,CAAC,CAAE;YAC/E,MAAMe,EAAE,GAAGJ,MAAM,CAAChB,OAAO,AAAC,AAAC;YAC3Ba,IAAG,IAAA,CAACC,GAAG,CAAC,GAAG,GAAG,CAACM,EAAE,KAAK,EAAE,GAAGX,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,UAAU,CAAC,GAAGU,EAAE,CAAC,EAAEZ,OAAO,CAACQ,MAAM,CAACZ,QAAQ,CAAC,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAED,MAAMiB,WAAW,GAAG;WAAIpB,MAAM,CAACL,OAAO,EAAE;KAAC,CAACqB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKD,CAAC,CAAC,CAAC,CAAC,CAACI,aAAa,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAG/E,AAAC;IAEJ,IAAIE,WAAW,CAAChB,MAAM,EAAE;QACtB,MAAMkB,WAAW,GAAGF,WAAW,CAACG,MAAM,CAAC,CAACC,GAAG,EAAE,GAAGT,MAAM,CAAC,GAAKS,GAAG,GAAGT,MAAM,CAACX,MAAM,EAAE,CAAC,CAAC,AAAC;QACpF,MAAMO,OAAM,GAAGW,WAAW,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,AAAC;QAE5CV,IAAG,IAAA,CAACC,GAAG,CAAC,EAAE,CAAC,CAAC;QACZD,IAAG,IAAA,CAACC,GAAG,CAACL,MAAK,EAAA,QAAA,CAACM,IAAI,CAAC,UAAU,EAAEQ,WAAW,CAAC,MAAM,EAAEX,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9D,KAAK,MAAM,CAACd,OAAO,EAAEkB,OAAM,CAAC,IAAIK,WAAW,CAAE;YAC3C,MAAMK,kBAAkB,GACtBV,OAAM,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAE,GAAG,EAAErB,QAAQ,CAAA,EAAE,CAAC,GAAKqB,GAAG,GAAGtB,WAAW,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAGY,OAAM,CAACX,MAAM,AAAC;YAC3FQ,IAAG,IAAA,CAACC,GAAG,CACLhB,OAAO,EACPW,MAAK,EAAA,QAAA,CAACC,IAAI,CACR,CAAC,CAAC,EAAE;gBACFM,OAAM,CAACX,MAAM,GAAG,CAAC,GAAG,CAAC,EAAEW,OAAM,CAACX,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE;gBACtD,CAAC,EAAEM,IAAAA,YAAW,EAAA,QAAA,EAACe,kBAAkB,CAAC,CAAC,CAAC;aACrC,CACEC,MAAM,CAACC,OAAO,CAAC,CACfvC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAClB,CACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED,MAAMwC,OAAO,GAAmD,IAAIC,GAAG,EAAE,AAAC;IAC1E,MAAMC,KAAK,GAAsC,EAAE,AAAC;IAEpDtC,gBAAgB,CAACuC,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEtC,KAAK,CAAC,GAAK;QAC9C,IAAI,CAACsC,QAAQ,CAACC,KAAK,mBAAmB,EAAE;YACtCH,KAAK,CAAChC,IAAI,CAAC;gBAACkC,QAAQ;gBAAEtC,KAAK;aAAC,CAAC,CAAC;QAChC,OAAO;gBACYsC,GAA8C;gBAA9CA,IAAmD;YAApE,MAAME,QAAQ,GAAGF,CAAAA,IAAmD,GAAnDA,CAAAA,GAA8C,GAA9CA,QAAQ,CAACC,KAAK,gCAAgC,SAAK,GAAnDD,KAAAA,CAAmD,GAAnDA,GAA8C,AAAE,CAAC,CAAC,CAAC,YAAnDA,IAAmD,GAAI,KAAK,AAAC;YAC9E,IAAI,CAACJ,OAAO,CAACO,GAAG,CAACD,QAAQ,CAAC,EAAEN,OAAO,CAACQ,GAAG,CAACF,QAAQ,EAAE,EAAE,CAAC,CAAC;YAEtDN,OAAO,CAACS,GAAG,CAACH,QAAQ,CAAC,CAAEpC,IAAI,CAAC;gBAACkC,QAAQ;gBAAEtC,KAAK;aAAC,CAAC,CAAC;QACjD,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;WAAIkC,OAAO,CAACjC,OAAO,EAAE;KAAC,CAACoC,OAAO,CAAC,CAAC,CAACG,QAAQ,EAAEnB,MAAM,CAAC,GAAK;QACrDH,IAAG,IAAA,CAACC,GAAG,CAAC,EAAE,CAAC,CAAC;QACZ,MAAMF,MAAM,GAAGI,MAAM,CAACX,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,AAAC;QAC9CQ,IAAG,IAAA,CAACC,GAAG,CAACL,MAAK,EAAA,QAAA,CAACM,IAAI,CAAC,UAAU,EAAEC,MAAM,CAACX,MAAM,CAAC,OAAO,EAAEO,MAAM,CAAC,KAAK,EAAEuB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjF,MAAMI,SAAS,GAAGvB,MAAM,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKD,CAAC,CAAC,CAAC,CAAC,CAACI,aAAa,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC;QAClE,MAAOoB,SAAS,CAAClC,MAAM,CAAE;YACvB,MAAM,CAACmC,QAAQ,EAAE7C,KAAK,CAAC,GAAG4C,SAAS,CAACE,KAAK,EAAE,AAAC,AAAC;YAC7C5B,IAAG,IAAA,CAACC,GAAG,CAAC0B,QAAQ,EAAEhC,OAAO,CAACb,KAAK,CAACS,QAAQ,CAAC,CAAC,CAAC;YAC3C,IAAIoC,QAAQ,CAACN,KAAK,eAAe,EAAE;gBACjC,iBAAiB;gBACjB,MAAMQ,cAAc,GAAGH,SAAS,CAACI,SAAS,CAAC,CAAC,CAACC,EAAE,CAAC,GAAKA,EAAE,KAAKJ,QAAQ,GAAG,MAAM,CAAC,AAAC;gBAC/E,IAAIE,cAAc,KAAK,CAAC,CAAC,EAAE;oBACzB,MAAM,CAACG,iBAAiB,EAAEC,cAAc,CAAC,GAAGP,SAAS,CAACQ,MAAM,CAACL,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC;oBACnF7B,IAAG,IAAA,CAACC,GAAG,CAACL,MAAK,EAAA,QAAA,CAACC,IAAI,CAACmC,iBAAiB,CAAC,EAAErC,OAAO,CAACsC,cAAc,CAAC1C,QAAQ,CAAC,CAAC,CAAC;gBAC3E,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI2B,KAAK,CAAC1B,MAAM,EAAE;QAChBQ,IAAG,IAAA,CAACC,GAAG,CAAC,EAAE,CAAC,CAAC;QACZ,MAAMF,OAAM,GAAGmB,KAAK,CAAC1B,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,AAAC;QAC7CQ,IAAG,IAAA,CAACC,GAAG,CAACL,MAAK,EAAA,QAAA,CAACM,IAAI,CAAC,UAAU,EAAEgB,KAAK,CAAC1B,MAAM,CAAC,KAAK,EAAEO,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9D,KAAK,MAAM,CAAC4B,QAAQ,EAAE7C,MAAK,CAAC,IAAIoC,KAAK,CAACd,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKD,CAAC,CAAC,CAAC,CAAC,CAACI,aAAa,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE;YAC9EN,IAAG,IAAA,CAACC,GAAG,CAAC0B,QAAQ,EAAEhC,OAAO,CAACb,MAAK,CAACS,QAAQ,CAAC,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED,wDAAwD;IAExD,MAAM4C,OAAO,CAACC,GAAG,CACf;WAAIlE,KAAK,CAACa,OAAO,EAAE;KAAC,CACjBqB,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,GAAKD,CAAC,CAACI,aAAa,CAACH,CAAC,CAAC,CAAC,CACtC+B,GAAG,CAAC,OAAO,CAACC,IAAI,EAAE,EAAE/C,QAAQ,CAAA,EAAEP,YAAY,CAAA,EAAE,CAAC,GAAK;QACjD,qEAAqE;QACrE,MAAMuD,MAAM,GAAG,AAAC1D,eAAe,IAAIG,YAAY,IAAK,EAAE,AAAC;QACvD,MAAMwD,UAAU,GAAGjE,KAAI,EAAA,QAAA,CAACC,IAAI,CAACL,SAAS,EAAEoE,MAAM,EAAED,IAAI,CAAC,AAAC;QACtD,MAAMjE,GAAE,EAAA,QAAA,CAACoE,QAAQ,CAACC,KAAK,CAACnE,KAAI,EAAA,QAAA,CAACoE,OAAO,CAACH,UAAU,CAAC,EAAE;YAAE/D,SAAS,EAAE,IAAI;SAAE,CAAC,CAAC;QACvE,MAAMJ,GAAE,EAAA,QAAA,CAACoE,QAAQ,CAACG,SAAS,CAACJ,UAAU,EAAEjD,QAAQ,CAAC,CAAC;IACpD,CAAC,CAAC,CACL,CAAC;IAEFS,IAAG,IAAA,CAACC,GAAG,CAAC,EAAE,CAAC,CAAC;AACd,CAAC;AAED,SAASZ,OAAO,CAAIwD,KAAU,EAAEC,GAAwB,EAAoB;IAC1E,MAAMT,GAAG,GAAG,IAAIpB,GAAG,EAAe,AAAC;IACnC4B,KAAK,CAAC1B,OAAO,CAAC,CAAC4B,IAAI,GAAK;QACtB,MAAMC,KAAK,GAAGF,GAAG,CAACC,IAAI,CAAC,AAAC;YACXV,GAAc;QAA3B,MAAMY,IAAI,GAAGZ,CAAAA,GAAc,GAAdA,GAAG,CAACZ,GAAG,CAACuB,KAAK,CAAC,YAAdX,GAAc,GAAI,EAAE,AAAC;QAClCY,IAAI,CAAC/D,IAAI,CAAC6D,IAAI,CAAC,CAAC;QAChBV,GAAG,CAACb,GAAG,CAACwB,KAAK,EAAEC,IAAI,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC;IACH,OAAOZ,GAAG,CAAC;AACb,CAAC;AAGM,SAASpE,wBAAwB,CACtCiF,SAAwB,EACxB,EACEC,iBAAiB,CAAA,EACjBjF,KAAK,EAAG,IAAI+C,GAAG,EAAE,CAAA,EACjBK,QAAQ,CAAA,EAKT,EACD;IACA4B,SAAS,CAAC/B,OAAO,CAAC,CAACiC,QAAQ,GAAK;QAC9BlF,KAAK,CAACsD,GAAG,CAAC4B,QAAQ,CAACC,QAAQ,EAAE;YAC3B9D,QAAQ,EAAE6D,QAAQ,CAACE,MAAM;YACzBC,cAAc,EAAEH,QAAQ,CAACG,cAAc;YACvCvE,YAAY,EAAEsC,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAGkC,SAAS;SACxD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAOtF,KAAK,CAAC;AACf,CAAC"}