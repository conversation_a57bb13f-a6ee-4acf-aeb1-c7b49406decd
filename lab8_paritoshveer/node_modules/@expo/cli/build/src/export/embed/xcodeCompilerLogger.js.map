{"version": 3, "sources": ["../../../../src/export/embed/xcodeCompilerLogger.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport fs from 'fs';\nimport path from 'path';\n\nfunction isPossiblyUnableToResolveError(\n  error: any\n): error is { message: string; originModulePath: string; targetModuleName: string } {\n  return (\n    'message' in error &&\n    typeof error.message === 'string' &&\n    'originModulePath' in error &&\n    typeof error.originModulePath === 'string' &&\n    'targetModuleName' in error &&\n    typeof error.targetModuleName === 'string'\n  );\n}\nfunction isPossiblyTransformError(\n  error: any\n): error is { message: string; filename: string; lineNumber: number; column?: number } {\n  return (\n    'message' in error &&\n    typeof error.message === 'string' &&\n    'filename' in error &&\n    typeof error.filename === 'string' &&\n    'lineNumber' in error &&\n    typeof error.lineNumber === 'number'\n  );\n}\n\nexport function getXcodeCompilerErrorMessage(\n  projectRoot: string,\n  error: Error | any\n): string | null {\n  const makeFilepathAbsolute = (filepath: string) =>\n    filepath.startsWith('/') ? filepath : path.join(projectRoot, filepath);\n\n  if ('message' in error) {\n    // Metro's `UnableToResolveError`\n    if (isPossiblyUnableToResolveError(error)) {\n      const loc = getLineNumberForStringInFile(error.originModulePath, error.targetModuleName);\n      return makeXcodeCompilerLog('error', error.message, {\n        fileName: error.originModulePath,\n        lineNumber: loc?.lineNumber,\n        column: loc?.column,\n      });\n    } else if (isPossiblyTransformError(error)) {\n      return makeXcodeCompilerLog('error', error.message, {\n        // Metro generally returns the filename as relative from the project root.\n        fileName: makeFilepathAbsolute(error.filename),\n        lineNumber: error.lineNumber,\n        column: error.column,\n      });\n      // TODO: ResourceNotFoundError, GraphNotFoundError, RevisionNotFoundError, AmbiguousModuleResolutionError\n    } else {\n      // Unknown error\n      return makeXcodeCompilerLog('error', error.message);\n    }\n  }\n\n  return null;\n}\n\nexport function logMetroErrorInXcode(projectRoot: string, error: Error) {\n  const message = getXcodeCompilerErrorMessage(projectRoot, error);\n  if (message != null) {\n    console.error(message);\n  }\n}\n\n// https://developer.apple.com/documentation/xcode/running-custom-scripts-during-a-build#Access-script-related-files-from-environment-variables\nexport function isExecutingFromXcodebuild() {\n  return !!process.env.BUILT_PRODUCTS_DIR;\n}\n\nfunction makeXcodeCompilerLog(\n  type: 'error' | 'fatal error' | 'warning' | 'note',\n  message: string,\n  {\n    fileName,\n    lineNumber,\n    column,\n  }: {\n    /** Absolute file path to link to in Xcode. */\n    fileName?: string;\n    lineNumber?: number;\n    column?: number;\n  } = {}\n) {\n  // TODO: Figure out how to support multi-line logs.\n  const firstLine = message.split('\\n')[0];\n  if (fileName && !fileName?.includes(':')) {\n    return `${fileName}:${lineNumber || 0}:${\n      column != null ? column + ':' : ''\n    } ${type}: ${firstLine}`;\n  }\n  return `${type}: ${firstLine}`;\n}\n\n// TODO: Metro doesn't expose this info even though it knows it.\nfunction getLineNumberForStringInFile(originModulePath: string, targetModuleName: string) {\n  let file;\n  try {\n    file = fs.readFileSync(originModulePath, 'utf8');\n  } catch (error: any) {\n    if (error.code === 'ENOENT' || error.code === 'EISDIR') {\n      // We're probably dealing with a virtualised file system where\n      // `this.originModulePath` doesn't actually exist on disk.\n      // We can't show a code frame, but there's no need to let this I/O\n      // error shadow the original module resolution error.\n      return null;\n    }\n    throw error;\n  }\n  const lines = file.split('\\n');\n  let lineNumber = 0;\n  let column = -1;\n  for (let line = 0; line < lines.length; line++) {\n    const columnLocation = lines[line].lastIndexOf(targetModuleName);\n    if (columnLocation >= 0) {\n      lineNumber = line;\n      column = columnLocation;\n      break;\n    }\n  }\n  return { lineNumber, column };\n}\n"], "names": ["getXcodeCompilerErrorMessage", "logMetroErrorInXcode", "isExecutingFromXcodebuild", "isPossiblyUnableToResolveError", "error", "message", "originModulePath", "targetModuleName", "isPossiblyTransformError", "filename", "lineNumber", "projectRoot", "makeFilepathAbsolute", "filepath", "startsWith", "path", "join", "loc", "getLineNumberForStringInFile", "makeXcodeCompilerLog", "fileName", "column", "console", "process", "env", "BUILT_PRODUCTS_DIR", "type", "firstLine", "split", "includes", "file", "fs", "readFileSync", "code", "lines", "line", "length", "columnLocation", "lastIndexOf"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IA4BgBA,4BAA4B,MAA5BA,4BAA4B;IAiC5BC,oBAAoB,MAApBA,oBAAoB;IAQpBC,yBAAyB,MAAzBA,yBAAyB;;;8DArE1B,IAAI;;;;;;;8DACF,MAAM;;;;;;;;;;;AAEvB,SAASC,8BAA8B,CACrCC,KAAU,EACwE;IAClF,OACE,SAAS,IAAIA,KAAK,IAClB,OAAOA,KAAK,CAACC,OAAO,KAAK,QAAQ,IACjC,kBAAkB,IAAID,KAAK,IAC3B,OAAOA,KAAK,CAACE,gBAAgB,KAAK,QAAQ,IAC1C,kBAAkB,IAAIF,KAAK,IAC3B,OAAOA,KAAK,CAACG,gBAAgB,KAAK,QAAQ,CAC1C;AACJ,CAAC;AACD,SAASC,wBAAwB,CAC/BJ,KAAU,EAC2E;IACrF,OACE,SAAS,IAAIA,KAAK,IAClB,OAAOA,KAAK,CAACC,OAAO,KAAK,QAAQ,IACjC,UAAU,IAAID,KAAK,IACnB,OAAOA,KAAK,CAACK,QAAQ,KAAK,QAAQ,IAClC,YAAY,IAAIL,KAAK,IACrB,OAAOA,KAAK,CAACM,UAAU,KAAK,QAAQ,CACpC;AACJ,CAAC;AAEM,SAASV,4BAA4B,CAC1CW,WAAmB,EACnBP,KAAkB,EACH;IACf,MAAMQ,oBAAoB,GAAG,CAACC,QAAgB,GAC5CA,QAAQ,CAACC,UAAU,CAAC,GAAG,CAAC,GAAGD,QAAQ,GAAGE,KAAI,EAAA,QAAA,CAACC,IAAI,CAACL,WAAW,EAAEE,QAAQ,CAAC,AAAC;IAEzE,IAAI,SAAS,IAAIT,KAAK,EAAE;QACtB,iCAAiC;QACjC,IAAID,8BAA8B,CAACC,KAAK,CAAC,EAAE;YACzC,MAAMa,GAAG,GAAGC,4BAA4B,CAACd,KAAK,CAACE,gBAAgB,EAAEF,KAAK,CAACG,gBAAgB,CAAC,AAAC;YACzF,OAAOY,oBAAoB,CAAC,OAAO,EAAEf,KAAK,CAACC,OAAO,EAAE;gBAClDe,QAAQ,EAAEhB,KAAK,CAACE,gBAAgB;gBAChCI,UAAU,EAAEO,GAAG,QAAY,GAAfA,KAAAA,CAAe,GAAfA,GAAG,CAAEP,UAAU;gBAC3BW,MAAM,EAAEJ,GAAG,QAAQ,GAAXA,KAAAA,CAAW,GAAXA,GAAG,CAAEI,MAAM;aACpB,CAAC,CAAC;QACL,OAAO,IAAIb,wBAAwB,CAACJ,KAAK,CAAC,EAAE;YAC1C,OAAOe,oBAAoB,CAAC,OAAO,EAAEf,KAAK,CAACC,OAAO,EAAE;gBAClD,0EAA0E;gBAC1Ee,QAAQ,EAAER,oBAAoB,CAACR,KAAK,CAACK,QAAQ,CAAC;gBAC9CC,UAAU,EAAEN,KAAK,CAACM,UAAU;gBAC5BW,MAAM,EAAEjB,KAAK,CAACiB,MAAM;aACrB,CAAC,CAAC;QACH,yGAAyG;QAC3G,OAAO;YACL,gBAAgB;YAChB,OAAOF,oBAAoB,CAAC,OAAO,EAAEf,KAAK,CAACC,OAAO,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,SAASJ,oBAAoB,CAACU,WAAmB,EAAEP,KAAY,EAAE;IACtE,MAAMC,OAAO,GAAGL,4BAA4B,CAACW,WAAW,EAAEP,KAAK,CAAC,AAAC;IACjE,IAAIC,OAAO,IAAI,IAAI,EAAE;QACnBiB,OAAO,CAAClB,KAAK,CAACC,OAAO,CAAC,CAAC;IACzB,CAAC;AACH,CAAC;AAGM,SAASH,yBAAyB,GAAG;IAC1C,OAAO,CAAC,CAACqB,OAAO,CAACC,GAAG,CAACC,kBAAkB,CAAC;AAC1C,CAAC;AAED,SAASN,oBAAoB,CAC3BO,IAAkD,EAClDrB,OAAe,EACf,EACEe,QAAQ,CAAA,EACRV,UAAU,CAAA,EACVW,MAAM,CAAA,EAMP,GAAG,EAAE,EACN;IACA,mDAAmD;IACnD,MAAMM,SAAS,GAAGtB,OAAO,CAACuB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,AAAC;IACzC,IAAIR,QAAQ,IAAI,EAACA,QAAQ,QAAU,GAAlBA,KAAAA,CAAkB,GAAlBA,QAAQ,CAAES,QAAQ,CAAC,GAAG,CAAC,CAAA,EAAE;QACxC,OAAO,CAAC,EAAET,QAAQ,CAAC,CAAC,EAAEV,UAAU,IAAI,CAAC,CAAC,CAAC,EACrCW,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAG,GAAG,GAAG,EAAE,CACnC,CAAC,EAAEK,IAAI,CAAC,EAAE,EAAEC,SAAS,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,OAAO,CAAC,EAAED,IAAI,CAAC,EAAE,EAAEC,SAAS,CAAC,CAAC,CAAC;AACjC,CAAC;AAED,gEAAgE;AAChE,SAAST,4BAA4B,CAACZ,gBAAwB,EAAEC,gBAAwB,EAAE;IACxF,IAAIuB,IAAI,AAAC;IACT,IAAI;QACFA,IAAI,GAAGC,GAAE,EAAA,QAAA,CAACC,YAAY,CAAC1B,gBAAgB,EAAE,MAAM,CAAC,CAAC;IACnD,EAAE,OAAOF,KAAK,EAAO;QACnB,IAAIA,KAAK,CAAC6B,IAAI,KAAK,QAAQ,IAAI7B,KAAK,CAAC6B,IAAI,KAAK,QAAQ,EAAE;YACtD,8DAA8D;YAC9D,0DAA0D;YAC1D,kEAAkE;YAClE,qDAAqD;YACrD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM7B,KAAK,CAAC;IACd,CAAC;IACD,MAAM8B,KAAK,GAAGJ,IAAI,CAACF,KAAK,CAAC,IAAI,CAAC,AAAC;IAC/B,IAAIlB,UAAU,GAAG,CAAC,AAAC;IACnB,IAAIW,MAAM,GAAG,CAAC,CAAC,AAAC;IAChB,IAAK,IAAIc,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,KAAK,CAACE,MAAM,EAAED,IAAI,EAAE,CAAE;QAC9C,MAAME,cAAc,GAAGH,KAAK,CAACC,IAAI,CAAC,CAACG,WAAW,CAAC/B,gBAAgB,CAAC,AAAC;QACjE,IAAI8B,cAAc,IAAI,CAAC,EAAE;YACvB3B,UAAU,GAAGyB,IAAI,CAAC;YAClBd,MAAM,GAAGgB,cAAc,CAAC;YACxB,MAAM;QACR,CAAC;IACH,CAAC;IACD,OAAO;QAAE3B,UAAU;QAAEW,MAAM;KAAE,CAAC;AAChC,CAAC"}