{"version": 3, "sources": ["../../../../src/export/embed/resolveOptions.ts"], "sourcesContent": ["import arg from 'arg';\nimport path from 'path';\n\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { resolveCustomBooleanArgsAsync } from '../../utils/resolveArgs';\n\nexport interface Options {\n  assetsDest?: string;\n  assetCatalogDest?: string;\n  entryFile: string;\n  resetCache: boolean;\n  transformer?: string;\n  minify?: boolean;\n  config?: string;\n  platform: string;\n  dev: boolean;\n  bundleOutput: string;\n  bundleEncoding?: string;\n  maxWorkers?: number;\n  sourcemapOutput?: string;\n  sourcemapSourcesRoot?: string;\n  sourcemapUseAbsolutePath: boolean;\n  verbose: boolean;\n  unstableTransformProfile?: string;\n}\n\nfunction assertIsBoolean(val: any): asserts val is boolean {\n  if (typeof val !== 'boolean') {\n    throw new CommandError(`Expected boolean, got ${typeof val}`);\n  }\n}\n\nexport function resolveOptions(\n  args: arg.Result<arg.Spec>,\n  parsed: Awaited<ReturnType<typeof resolveCustomBooleanArgsAsync>>\n): Options {\n  const dev = parsed.args['--dev'] ?? true;\n  assertIsBoolean(dev);\n\n  const minify = parsed.args['--minify'] ?? !dev;\n  assertIsBoolean(minify);\n\n  const entryFile = args['--entry-file'];\n  if (!entryFile) {\n    throw new CommandError(`Missing required argument: --entry-file`);\n  }\n  const bundleOutput = args['--bundle-output'];\n  if (!bundleOutput) {\n    throw new CommandError(`Missing required argument: --bundle-output`);\n  }\n  return {\n    entryFile,\n    assetCatalogDest: args['--asset-catalog-dest'],\n    platform: args['--platform'] ?? 'ios',\n    transformer: args['--transformer'],\n    // TODO: Support `--dev false`\n    //   dev: false,\n    bundleOutput,\n    bundleEncoding: args['--bundle-encoding'] ?? 'utf8',\n    maxWorkers: args['--max-workers'],\n    sourcemapOutput: args['--sourcemap-output'],\n    sourcemapSourcesRoot: args['--sourcemap-sources-root'],\n    sourcemapUseAbsolutePath: !!parsed.args['--sourcemap-use-absolute-path'],\n    assetsDest: args['--assets-dest'],\n    unstableTransformProfile: args['--unstable-transform-profile'],\n    resetCache: !!parsed.args['--reset-cache'],\n    verbose: args['--verbose'] ?? env.EXPO_DEBUG,\n    config: args['--config'] ? path.resolve(args['--config']) : undefined,\n    dev,\n    minify,\n  };\n}\n"], "names": ["resolveOptions", "assertIsBoolean", "val", "CommandError", "args", "parsed", "dev", "minify", "entryFile", "bundleOutput", "assetCatalogDest", "platform", "transformer", "bundleEncoding", "maxWorkers", "sourcemapOutput", "sourcemapSourcesRoot", "sourcemapUseAbsolutePath", "assetsDest", "unstableTransformProfile", "resetCache", "verbose", "env", "EXPO_DEBUG", "config", "path", "resolve", "undefined"], "mappings": "AAAA;;;;+BAiCgBA,gBAAc;;aAAdA,cAAc;;;8DAhCb,MAAM;;;;;;qBAEH,iBAAiB;wBACR,oBAAoB;;;;;;AAuBjD,SAASC,eAAe,CAACC,GAAQ,EAA0B;IACzD,IAAI,OAAOA,GAAG,KAAK,SAAS,EAAE;QAC5B,MAAM,IAAIC,OAAY,aAAA,CAAC,CAAC,sBAAsB,EAAE,OAAOD,GAAG,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;AACH,CAAC;AAEM,SAASF,cAAc,CAC5BI,IAA0B,EAC1BC,MAAiE,EACxD;QACGA,GAAoB;IAAhC,MAAMC,GAAG,GAAGD,CAAAA,GAAoB,GAApBA,MAAM,CAACD,IAAI,CAAC,OAAO,CAAC,YAApBC,GAAoB,GAAI,IAAI,AAAC;IACzCJ,eAAe,CAACK,GAAG,CAAC,CAAC;QAEND,IAAuB;IAAtC,MAAME,MAAM,GAAGF,CAAAA,IAAuB,GAAvBA,MAAM,CAACD,IAAI,CAAC,UAAU,CAAC,YAAvBC,IAAuB,GAAI,CAACC,GAAG,AAAC;IAC/CL,eAAe,CAACM,MAAM,CAAC,CAAC;IAExB,MAAMC,SAAS,GAAGJ,IAAI,CAAC,cAAc,CAAC,AAAC;IACvC,IAAI,CAACI,SAAS,EAAE;QACd,MAAM,IAAIL,OAAY,aAAA,CAAC,CAAC,uCAAuC,CAAC,CAAC,CAAC;IACpE,CAAC;IACD,MAAMM,YAAY,GAAGL,IAAI,CAAC,iBAAiB,CAAC,AAAC;IAC7C,IAAI,CAACK,YAAY,EAAE;QACjB,MAAM,IAAIN,OAAY,aAAA,CAAC,CAAC,0CAA0C,CAAC,CAAC,CAAC;IACvE,CAAC;QAIWC,IAAkB,EAKZA,IAAyB,EAQhCA,IAAiB;IAhB5B,OAAO;QACLI,SAAS;QACTE,gBAAgB,EAAEN,IAAI,CAAC,sBAAsB,CAAC;QAC9CO,QAAQ,EAAEP,CAAAA,IAAkB,GAAlBA,IAAI,CAAC,YAAY,CAAC,YAAlBA,IAAkB,GAAI,KAAK;QACrCQ,WAAW,EAAER,IAAI,CAAC,eAAe,CAAC;QAClC,8BAA8B;QAC9B,gBAAgB;QAChBK,YAAY;QACZI,cAAc,EAAET,CAAAA,IAAyB,GAAzBA,IAAI,CAAC,mBAAmB,CAAC,YAAzBA,IAAyB,GAAI,MAAM;QACnDU,UAAU,EAAEV,IAAI,CAAC,eAAe,CAAC;QACjCW,eAAe,EAAEX,IAAI,CAAC,oBAAoB,CAAC;QAC3CY,oBAAoB,EAAEZ,IAAI,CAAC,0BAA0B,CAAC;QACtDa,wBAAwB,EAAE,CAAC,CAACZ,MAAM,CAACD,IAAI,CAAC,+BAA+B,CAAC;QACxEc,UAAU,EAAEd,IAAI,CAAC,eAAe,CAAC;QACjCe,wBAAwB,EAAEf,IAAI,CAAC,8BAA8B,CAAC;QAC9DgB,UAAU,EAAE,CAAC,CAACf,MAAM,CAACD,IAAI,CAAC,eAAe,CAAC;QAC1CiB,OAAO,EAAEjB,CAAAA,IAAiB,GAAjBA,IAAI,CAAC,WAAW,CAAC,YAAjBA,IAAiB,GAAIkB,IAAG,IAAA,CAACC,UAAU;QAC5CC,MAAM,EAAEpB,IAAI,CAAC,UAAU,CAAC,GAAGqB,KAAI,EAAA,QAAA,CAACC,OAAO,CAACtB,IAAI,CAAC,UAAU,CAAC,CAAC,GAAGuB,SAAS;QACrErB,GAAG;QACHC,MAAM;KACP,CAAC;AACJ,CAAC"}