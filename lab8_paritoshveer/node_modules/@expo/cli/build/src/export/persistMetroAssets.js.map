{"version": 3, "sources": ["../../../src/export/persistMetroAssets.ts"], "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on the community asset persisting for Metro but with base path and web support:\n * https://github.com/facebook/react-native/blob/d6e0bc714ad4d215ede4949d3c4f44af6dea5dd3/packages/community-cli-plugin/src/commands/bundle/saveAssets.js#L1\n */\nimport fs from 'fs';\nimport type { AssetData } from 'metro';\nimport path from 'path';\n\nimport { getAssetLocalPath } from './metroAssetLocalPath';\nimport { ExportAssetMap } from './saveAssets';\nimport { Log } from '../log';\n\nfunction cleanAssetCatalog(catalogDir: string): void {\n  const files = fs.readdirSync(catalogDir).filter((file) => file.endsWith('.imageset'));\n  for (const file of files) {\n    fs.rmSync(path.join(catalogDir, file));\n  }\n}\n\nexport async function persistMetroAssetsAsync(\n  assets: readonly AssetData[],\n  {\n    platform,\n    outputDirectory,\n    baseUrl,\n    iosAssetCatalogDirectory,\n    files,\n  }: {\n    platform: string;\n    outputDirectory: string;\n    baseUrl?: string;\n    iosAssetCatalogDirectory?: string;\n    files?: ExportAssetMap;\n  }\n) {\n  if (outputDirectory == null) {\n    Log.warn('Assets destination folder is not set, skipping...');\n    return;\n  }\n\n  let assetsToCopy: AssetData[] = [];\n\n  // TODO: Use `files` as below to defer writing files\n  if (platform === 'ios' && iosAssetCatalogDirectory != null) {\n    // Use iOS Asset Catalog for images. This will allow Apple app thinning to\n    // remove unused scales from the optimized bundle.\n    const catalogDir = path.join(iosAssetCatalogDirectory, 'RNAssets.xcassets');\n    if (!fs.existsSync(catalogDir)) {\n      Log.error(\n        `Could not find asset catalog 'RNAssets.xcassets' in ${iosAssetCatalogDirectory}. Make sure to create it if it does not exist.`\n      );\n      return;\n    }\n\n    Log.log('Adding images to asset catalog', catalogDir);\n    cleanAssetCatalog(catalogDir);\n    for (const asset of assets) {\n      if (isCatalogAsset(asset)) {\n        const imageSet = getImageSet(\n          catalogDir,\n          asset,\n          filterPlatformAssetScales(platform, asset.scales)\n        );\n        writeImageSet(imageSet);\n      } else {\n        assetsToCopy.push(asset);\n      }\n    }\n    Log.log('Done adding images to asset catalog');\n  } else {\n    assetsToCopy = [...assets];\n  }\n\n  const batches: Record<string, string> = {};\n\n  async function write(src: string, dest: string) {\n    if (files) {\n      const data = await fs.promises.readFile(src);\n      files.set(dest, {\n        contents: data,\n        targetDomain: platform === 'web' ? 'client' : undefined,\n      });\n    } else {\n      batches[src] = path.join(outputDirectory, dest);\n    }\n  }\n\n  for (const asset of assetsToCopy) {\n    const validScales = new Set(filterPlatformAssetScales(platform, asset.scales));\n    for (let idx = 0; idx < asset.scales.length; idx++) {\n      const scale = asset.scales[idx];\n      if (validScales.has(scale)) {\n        await write(asset.files[idx], getAssetLocalPath(asset, { platform, scale, baseUrl }));\n      }\n    }\n  }\n\n  if (!files) {\n    await copyInBatchesAsync(batches);\n  }\n}\n\nfunction writeImageSet(imageSet: ImageSet): void {\n  fs.mkdirSync(imageSet.baseUrl, { recursive: true });\n\n  for (const file of imageSet.files) {\n    const dest = path.join(imageSet.baseUrl, file.name);\n    fs.copyFileSync(file.src, dest);\n  }\n\n  fs.writeFileSync(\n    path.join(imageSet.baseUrl, 'Contents.json'),\n    JSON.stringify({\n      images: imageSet.files.map((file) => ({\n        filename: file.name,\n        idiom: 'universal',\n        scale: `${file.scale}x`,\n      })),\n      info: {\n        author: 'expo',\n        version: 1,\n      },\n    })\n  );\n}\n\nfunction isCatalogAsset(asset: Pick<AssetData, 'type'>): boolean {\n  return asset.type === 'png' || asset.type === 'jpg' || asset.type === 'jpeg';\n}\n\ntype ImageSet = {\n  baseUrl: string;\n  files: { name: string; src: string; scale: number }[];\n};\n\nfunction getImageSet(\n  catalogDir: string,\n  asset: Pick<AssetData, 'httpServerLocation' | 'name' | 'type' | 'files'>,\n  scales: number[]\n): ImageSet {\n  const fileName = getResourceIdentifier(asset);\n  return {\n    baseUrl: path.join(catalogDir, `${fileName}.imageset`),\n    files: scales.map((scale, idx) => {\n      const suffix = scale === 1 ? '' : `@${scale}x`;\n      return {\n        name: `${fileName + suffix}.${asset.type}`,\n        scale,\n        src: asset.files[idx],\n      };\n    }),\n  };\n}\n\nexport function copyInBatchesAsync(filesToCopy: Record<string, string>) {\n  const queue = Object.keys(filesToCopy);\n  if (queue.length === 0) {\n    return;\n  }\n\n  Log.log(`Copying ${queue.length} asset files`);\n  return new Promise<void>((resolve, reject) => {\n    const copyNext = (error?: NodeJS.ErrnoException) => {\n      if (error) {\n        return reject(error);\n      }\n      if (queue.length) {\n        // queue.length === 0 is checked in previous branch, so this is string\n        const src = queue.shift() as string;\n        const dest = filesToCopy[src];\n        copy(src, dest, copyNext);\n      } else {\n        resolve();\n      }\n    };\n    copyNext();\n  });\n}\n\nfunction copy(src: string, dest: string, callback: (error: NodeJS.ErrnoException) => void): void {\n  fs.mkdir(path.dirname(dest), { recursive: true }, (err?) => {\n    if (err) {\n      callback(err);\n      return;\n    }\n    fs.createReadStream(src).pipe(fs.createWriteStream(dest)).on('finish', callback);\n  });\n}\n\nconst ALLOWED_SCALES: { [key: string]: number[] } = {\n  ios: [1, 2, 3],\n};\n\nexport function filterPlatformAssetScales(platform: string, scales: number[]): number[] {\n  const whitelist: number[] = ALLOWED_SCALES[platform];\n  if (!whitelist) {\n    return scales;\n  }\n  const result = scales.filter((scale) => whitelist.includes(scale));\n  if (!result.length && scales.length) {\n    // No matching scale found, but there are some available. Ideally we don't\n    // want to be in this situation and should throw, but for now as a fallback\n    // let's just use the closest larger image\n    const maxScale = whitelist[whitelist.length - 1];\n    for (const scale of scales) {\n      if (scale > maxScale) {\n        result.push(scale);\n        break;\n      }\n    }\n\n    // There is no larger scales available, use the largest we have\n    if (!result.length) {\n      result.push(scales[scales.length - 1]);\n    }\n  }\n  return result;\n}\n\nfunction getResourceIdentifier(asset: Pick<AssetData, 'httpServerLocation' | 'name'>): string {\n  const folderPath = getBaseUrl(asset);\n  return `${folderPath}/${asset.name}`\n    .toLowerCase()\n    .replace(/\\//g, '_') // Encode folder structure in file name\n    .replace(/([^a-z0-9_])/g, '') // Remove illegal chars\n    .replace(/^assets_/, ''); // Remove \"assets_\" prefix\n}\n\nfunction getBaseUrl(asset: Pick<AssetData, 'httpServerLocation'>): string {\n  let baseUrl = asset.httpServerLocation;\n  if (baseUrl[0] === '/') {\n    baseUrl = baseUrl.substring(1);\n  }\n  return baseUrl;\n}\n"], "names": ["persistMetroAssetsAsync", "copyInBatchesAsync", "filterPlatformAssetScales", "cleanAssetCatalog", "catalogDir", "files", "fs", "readdirSync", "filter", "file", "endsWith", "rmSync", "path", "join", "assets", "platform", "outputDirectory", "baseUrl", "iosAssetCatalogDirectory", "Log", "warn", "assetsToCopy", "existsSync", "error", "log", "asset", "isCatalogAsset", "imageSet", "getImageSet", "scales", "writeImageSet", "push", "batches", "write", "src", "dest", "data", "promises", "readFile", "set", "contents", "targetDomain", "undefined", "validScales", "Set", "idx", "length", "scale", "has", "getAssetLocalPath", "mkdirSync", "recursive", "name", "copyFileSync", "writeFileSync", "JSON", "stringify", "images", "map", "filename", "idiom", "info", "author", "version", "type", "fileName", "getResourceIdentifier", "suffix", "filesToCopy", "queue", "Object", "keys", "Promise", "resolve", "reject", "copyNext", "shift", "copy", "callback", "mkdir", "dirname", "err", "createReadStream", "pipe", "createWriteStream", "on", "ALLOWED_SCALES", "ios", "whitelist", "result", "includes", "maxScale", "folderPath", "getBaseUrl", "toLowerCase", "replace", "httpServerLocation", "substring"], "mappings": "AAAA;;;;;;;;;CASC,GACD;;;;;;;;;;;IAesBA,uBAAuB,MAAvBA,uBAAuB;IAuI7BC,kBAAkB,MAAlBA,kBAAkB;IAuClBC,yBAAyB,MAAzBA,yBAAyB;;;8DA7L1B,IAAI;;;;;;;8DAEF,MAAM;;;;;;qCAEW,uBAAuB;qBAErC,QAAQ;;;;;;AAE5B,SAASC,iBAAiB,CAACC,UAAkB,EAAQ;IACnD,MAAMC,KAAK,GAAGC,GAAE,EAAA,QAAA,CAACC,WAAW,CAACH,UAAU,CAAC,CAACI,MAAM,CAAC,CAACC,IAAI,GAAKA,IAAI,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC,AAAC;IACtF,KAAK,MAAMD,IAAI,IAAIJ,KAAK,CAAE;QACxBC,GAAE,EAAA,QAAA,CAACK,MAAM,CAACC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACT,UAAU,EAAEK,IAAI,CAAC,CAAC,CAAC;IACzC,CAAC;AACH,CAAC;AAEM,eAAeT,uBAAuB,CAC3Cc,MAA4B,EAC5B,EACEC,QAAQ,CAAA,EACRC,eAAe,CAAA,EACfC,OAAO,CAAA,EACPC,wBAAwB,CAAA,EACxBb,KAAK,CAAA,EAON,EACD;IACA,IAAIW,eAAe,IAAI,IAAI,EAAE;QAC3BG,IAAG,IAAA,CAACC,IAAI,CAAC,mDAAmD,CAAC,CAAC;QAC9D,OAAO;IACT,CAAC;IAED,IAAIC,YAAY,GAAgB,EAAE,AAAC;IAEnC,oDAAoD;IACpD,IAAIN,QAAQ,KAAK,KAAK,IAAIG,wBAAwB,IAAI,IAAI,EAAE;QAC1D,0EAA0E;QAC1E,kDAAkD;QAClD,MAAMd,UAAU,GAAGQ,KAAI,EAAA,QAAA,CAACC,IAAI,CAACK,wBAAwB,EAAE,mBAAmB,CAAC,AAAC;QAC5E,IAAI,CAACZ,GAAE,EAAA,QAAA,CAACgB,UAAU,CAAClB,UAAU,CAAC,EAAE;YAC9Be,IAAG,IAAA,CAACI,KAAK,CACP,CAAC,oDAAoD,EAAEL,wBAAwB,CAAC,8CAA8C,CAAC,CAChI,CAAC;YACF,OAAO;QACT,CAAC;QAEDC,IAAG,IAAA,CAACK,GAAG,CAAC,gCAAgC,EAAEpB,UAAU,CAAC,CAAC;QACtDD,iBAAiB,CAACC,UAAU,CAAC,CAAC;QAC9B,KAAK,MAAMqB,KAAK,IAAIX,MAAM,CAAE;YAC1B,IAAIY,cAAc,CAACD,KAAK,CAAC,EAAE;gBACzB,MAAME,QAAQ,GAAGC,WAAW,CAC1BxB,UAAU,EACVqB,KAAK,EACLvB,yBAAyB,CAACa,QAAQ,EAAEU,KAAK,CAACI,MAAM,CAAC,CAClD,AAAC;gBACFC,aAAa,CAACH,QAAQ,CAAC,CAAC;YAC1B,OAAO;gBACLN,YAAY,CAACU,IAAI,CAACN,KAAK,CAAC,CAAC;YAC3B,CAAC;QACH,CAAC;QACDN,IAAG,IAAA,CAACK,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACjD,OAAO;QACLH,YAAY,GAAG;eAAIP,MAAM;SAAC,CAAC;IAC7B,CAAC;IAED,MAAMkB,OAAO,GAA2B,EAAE,AAAC;IAE3C,eAAeC,KAAK,CAACC,GAAW,EAAEC,IAAY,EAAE;QAC9C,IAAI9B,KAAK,EAAE;YACT,MAAM+B,IAAI,GAAG,MAAM9B,GAAE,EAAA,QAAA,CAAC+B,QAAQ,CAACC,QAAQ,CAACJ,GAAG,CAAC,AAAC;YAC7C7B,KAAK,CAACkC,GAAG,CAACJ,IAAI,EAAE;gBACdK,QAAQ,EAAEJ,IAAI;gBACdK,YAAY,EAAE1B,QAAQ,KAAK,KAAK,GAAG,QAAQ,GAAG2B,SAAS;aACxD,CAAC,CAAC;QACL,OAAO;YACLV,OAAO,CAACE,GAAG,CAAC,GAAGtB,KAAI,EAAA,QAAA,CAACC,IAAI,CAACG,eAAe,EAAEmB,IAAI,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,KAAK,MAAMV,MAAK,IAAIJ,YAAY,CAAE;QAChC,MAAMsB,WAAW,GAAG,IAAIC,GAAG,CAAC1C,yBAAyB,CAACa,QAAQ,EAAEU,MAAK,CAACI,MAAM,CAAC,CAAC,AAAC;QAC/E,IAAK,IAAIgB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGpB,MAAK,CAACI,MAAM,CAACiB,MAAM,EAAED,GAAG,EAAE,CAAE;YAClD,MAAME,KAAK,GAAGtB,MAAK,CAACI,MAAM,CAACgB,GAAG,CAAC,AAAC;YAChC,IAAIF,WAAW,CAACK,GAAG,CAACD,KAAK,CAAC,EAAE;gBAC1B,MAAMd,KAAK,CAACR,MAAK,CAACpB,KAAK,CAACwC,GAAG,CAAC,EAAEI,IAAAA,oBAAiB,kBAAA,EAACxB,MAAK,EAAE;oBAAEV,QAAQ;oBAAEgC,KAAK;oBAAE9B,OAAO;iBAAE,CAAC,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,CAACZ,KAAK,EAAE;QACV,MAAMJ,kBAAkB,CAAC+B,OAAO,CAAC,CAAC;IACpC,CAAC;AACH,CAAC;AAED,SAASF,aAAa,CAACH,QAAkB,EAAQ;IAC/CrB,GAAE,EAAA,QAAA,CAAC4C,SAAS,CAACvB,QAAQ,CAACV,OAAO,EAAE;QAAEkC,SAAS,EAAE,IAAI;KAAE,CAAC,CAAC;IAEpD,KAAK,MAAM1C,IAAI,IAAIkB,QAAQ,CAACtB,KAAK,CAAE;QACjC,MAAM8B,IAAI,GAAGvB,KAAI,EAAA,QAAA,CAACC,IAAI,CAACc,QAAQ,CAACV,OAAO,EAAER,IAAI,CAAC2C,IAAI,CAAC,AAAC;QACpD9C,GAAE,EAAA,QAAA,CAAC+C,YAAY,CAAC5C,IAAI,CAACyB,GAAG,EAAEC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED7B,GAAE,EAAA,QAAA,CAACgD,aAAa,CACd1C,KAAI,EAAA,QAAA,CAACC,IAAI,CAACc,QAAQ,CAACV,OAAO,EAAE,eAAe,CAAC,EAC5CsC,IAAI,CAACC,SAAS,CAAC;QACbC,MAAM,EAAE9B,QAAQ,CAACtB,KAAK,CAACqD,GAAG,CAAC,CAACjD,IAAI,GAAK,CAAC;gBACpCkD,QAAQ,EAAElD,IAAI,CAAC2C,IAAI;gBACnBQ,KAAK,EAAE,WAAW;gBAClBb,KAAK,EAAE,CAAC,EAAEtC,IAAI,CAACsC,KAAK,CAAC,CAAC,CAAC;aACxB,CAAC,CAAC;QACHc,IAAI,EAAE;YACJC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,CAAC;SACX;KACF,CAAC,CACH,CAAC;AACJ,CAAC;AAED,SAASrC,cAAc,CAACD,KAA8B,EAAW;IAC/D,OAAOA,KAAK,CAACuC,IAAI,KAAK,KAAK,IAAIvC,KAAK,CAACuC,IAAI,KAAK,KAAK,IAAIvC,KAAK,CAACuC,IAAI,KAAK,MAAM,CAAC;AAC/E,CAAC;AAOD,SAASpC,WAAW,CAClBxB,UAAkB,EAClBqB,KAAwE,EACxEI,MAAgB,EACN;IACV,MAAMoC,QAAQ,GAAGC,qBAAqB,CAACzC,KAAK,CAAC,AAAC;IAC9C,OAAO;QACLR,OAAO,EAAEL,KAAI,EAAA,QAAA,CAACC,IAAI,CAACT,UAAU,EAAE,CAAC,EAAE6D,QAAQ,CAAC,SAAS,CAAC,CAAC;QACtD5D,KAAK,EAAEwB,MAAM,CAAC6B,GAAG,CAAC,CAACX,KAAK,EAAEF,GAAG,GAAK;YAChC,MAAMsB,MAAM,GAAGpB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,AAAC;YAC/C,OAAO;gBACLK,IAAI,EAAE,CAAC,EAAEa,QAAQ,GAAGE,MAAM,CAAC,CAAC,EAAE1C,KAAK,CAACuC,IAAI,CAAC,CAAC;gBAC1CjB,KAAK;gBACLb,GAAG,EAAET,KAAK,CAACpB,KAAK,CAACwC,GAAG,CAAC;aACtB,CAAC;QACJ,CAAC,CAAC;KACH,CAAC;AACJ,CAAC;AAEM,SAAS5C,kBAAkB,CAACmE,WAAmC,EAAE;IACtE,MAAMC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACH,WAAW,CAAC,AAAC;IACvC,IAAIC,KAAK,CAACvB,MAAM,KAAK,CAAC,EAAE;QACtB,OAAO;IACT,CAAC;IAED3B,IAAG,IAAA,CAACK,GAAG,CAAC,CAAC,QAAQ,EAAE6C,KAAK,CAACvB,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;IAC/C,OAAO,IAAI0B,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,GAAK;QAC5C,MAAMC,QAAQ,GAAG,CAACpD,KAA6B,GAAK;YAClD,IAAIA,KAAK,EAAE;gBACT,OAAOmD,MAAM,CAACnD,KAAK,CAAC,CAAC;YACvB,CAAC;YACD,IAAI8C,KAAK,CAACvB,MAAM,EAAE;gBAChB,sEAAsE;gBACtE,MAAMZ,GAAG,GAAGmC,KAAK,CAACO,KAAK,EAAE,AAAU,AAAC;gBACpC,MAAMzC,IAAI,GAAGiC,WAAW,CAAClC,GAAG,CAAC,AAAC;gBAC9B2C,IAAI,CAAC3C,GAAG,EAAEC,IAAI,EAAEwC,QAAQ,CAAC,CAAC;YAC5B,OAAO;gBACLF,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC,AAAC;QACFE,QAAQ,EAAE,CAAC;IACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAASE,IAAI,CAAC3C,GAAW,EAAEC,IAAY,EAAE2C,QAAgD,EAAQ;IAC/FxE,GAAE,EAAA,QAAA,CAACyE,KAAK,CAACnE,KAAI,EAAA,QAAA,CAACoE,OAAO,CAAC7C,IAAI,CAAC,EAAE;QAAEgB,SAAS,EAAE,IAAI;KAAE,EAAE,CAAC8B,GAAG,GAAM;QAC1D,IAAIA,GAAG,EAAE;YACPH,QAAQ,CAACG,GAAG,CAAC,CAAC;YACd,OAAO;QACT,CAAC;QACD3E,GAAE,EAAA,QAAA,CAAC4E,gBAAgB,CAAChD,GAAG,CAAC,CAACiD,IAAI,CAAC7E,GAAE,EAAA,QAAA,CAAC8E,iBAAiB,CAACjD,IAAI,CAAC,CAAC,CAACkD,EAAE,CAAC,QAAQ,EAAEP,QAAQ,CAAC,CAAC;IACnF,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAMQ,cAAc,GAAgC;IAClDC,GAAG,EAAE;AAAC,SAAC;AAAE,SAAC;AAAE,SAAC;KAAC;CACf,AAAC;AAEK,SAASrF,yBAAyB,CAACa,QAAgB,EAAEc,MAAgB,EAAY;IACtF,MAAM2D,SAAS,GAAaF,cAAc,CAACvE,QAAQ,CAAC,AAAC;IACrD,IAAI,CAACyE,SAAS,EAAE;QACd,OAAO3D,MAAM,CAAC;IAChB,CAAC;IACD,MAAM4D,MAAM,GAAG5D,MAAM,CAACrB,MAAM,CAAC,CAACuC,KAAK,GAAKyC,SAAS,CAACE,QAAQ,CAAC3C,KAAK,CAAC,CAAC,AAAC;IACnE,IAAI,CAAC0C,MAAM,CAAC3C,MAAM,IAAIjB,MAAM,CAACiB,MAAM,EAAE;QACnC,0EAA0E;QAC1E,2EAA2E;QAC3E,0CAA0C;QAC1C,MAAM6C,QAAQ,GAAGH,SAAS,CAACA,SAAS,CAAC1C,MAAM,GAAG,CAAC,CAAC,AAAC;QACjD,KAAK,MAAMC,KAAK,IAAIlB,MAAM,CAAE;YAC1B,IAAIkB,KAAK,GAAG4C,QAAQ,EAAE;gBACpBF,MAAM,CAAC1D,IAAI,CAACgB,KAAK,CAAC,CAAC;gBACnB,MAAM;YACR,CAAC;QACH,CAAC;QAED,+DAA+D;QAC/D,IAAI,CAAC0C,MAAM,CAAC3C,MAAM,EAAE;YAClB2C,MAAM,CAAC1D,IAAI,CAACF,MAAM,CAACA,MAAM,CAACiB,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IACD,OAAO2C,MAAM,CAAC;AAChB,CAAC;AAED,SAASvB,qBAAqB,CAACzC,KAAqD,EAAU;IAC5F,MAAMmE,UAAU,GAAGC,UAAU,CAACpE,KAAK,CAAC,AAAC;IACrC,OAAO,CAAC,EAAEmE,UAAU,CAAC,CAAC,EAAEnE,KAAK,CAAC2B,IAAI,CAAC,CAAC,CACjC0C,WAAW,EAAE,CACbC,OAAO,QAAQ,GAAG,CAAC,CAAC,uCAAuC;KAC3DA,OAAO,kBAAkB,EAAE,CAAC,CAAC,uBAAuB;KACpDA,OAAO,aAAa,EAAE,CAAC,CAAC,CAAC,0BAA0B;AACxD,CAAC;AAED,SAASF,UAAU,CAACpE,KAA4C,EAAU;IACxE,IAAIR,OAAO,GAAGQ,KAAK,CAACuE,kBAAkB,AAAC;IACvC,IAAI/E,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACtBA,OAAO,GAAGA,OAAO,CAACgF,SAAS,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IACD,OAAOhF,OAAO,CAAC;AACjB,CAAC"}