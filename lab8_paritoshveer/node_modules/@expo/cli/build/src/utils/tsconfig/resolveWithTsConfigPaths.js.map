{"version": 3, "sources": ["../../../../src/utils/tsconfig/resolveWithTsConfigPaths.ts"], "sourcesContent": ["import { Resolution } from 'metro-resolver';\nimport path from 'path';\n\nimport { matchTsConfigPathAlias } from './matchTsConfigPathAlias';\n\ntype Paths = { [match: string]: string[] };\n\nconst debug = require('debug')('expo:metro:tsconfig-paths') as typeof console.log;\n\nconst isAbsolute = process.platform === 'win32' ? path.win32.isAbsolute : path.posix.isAbsolute;\n\nexport function resolveWithTsConfigPaths(\n  config: { paths: Paths; baseUrl: string; hasBaseUrl: boolean },\n  request: {\n    /** Import request */\n    moduleName: string;\n    /** Originating file path */\n    originModulePath: string;\n  },\n  resolve: (moduleName: string) => Resolution | null\n): Resolution | null {\n  const aliases = Object.keys(config.paths);\n\n  if (\n    // If no aliases are added bail out\n    (!aliases.length && !config.hasBaseUrl) ||\n    // Library authors cannot utilize this feature in userspace.\n    /node_modules/.test(request.originModulePath) ||\n    // Absolute paths are not supported\n    isAbsolute(request.moduleName) ||\n    // Relative paths are not supported\n    /^\\.\\.?($|[\\\\/])/.test(request.moduleName)\n  ) {\n    return null;\n  }\n\n  const matched = matchTsConfigPathAlias(aliases, request.moduleName);\n  if (matched) {\n    for (const alias of config.paths[matched.text]) {\n      const nextModuleName = matched.star ? alias.replace('*', matched.star) : alias;\n\n      if (/\\.d\\.ts$/.test(nextModuleName)) continue;\n\n      const possibleResult = path.join(config.baseUrl, nextModuleName);\n\n      const result = resolve(possibleResult);\n      if (result) {\n        debug(`${request.moduleName} -> ${possibleResult}`);\n        return result;\n      }\n    }\n  } else {\n    // Only resolve against baseUrl if no `paths` groups were matched.\n    // Base URL is resolved after paths, and before node_modules.\n    if (config.hasBaseUrl) {\n      const possibleResult = path.join(config.baseUrl, request.moduleName);\n      const result = resolve(possibleResult);\n      if (result) {\n        debug(`baseUrl: ${request.moduleName} -> ${possibleResult}`);\n        return result;\n      }\n    }\n  }\n\n  return null;\n}\n"], "names": ["resolveWithTsConfigPaths", "debug", "require", "isAbsolute", "process", "platform", "path", "win32", "posix", "config", "request", "resolve", "aliases", "Object", "keys", "paths", "length", "hasBaseUrl", "test", "originModulePath", "moduleName", "matched", "matchTsConfigPathAlias", "alias", "text", "nextModuleName", "star", "replace", "possibleResult", "join", "baseUrl", "result"], "mappings": "AAAA;;;;+BAWgBA,0BAAwB;;aAAxBA,wBAAwB;;;8DAVvB,MAAM;;;;;;wCAEgB,0BAA0B;;;;;;AAIjE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,2BAA2B,CAAC,AAAsB,AAAC;AAElF,MAAMC,UAAU,GAAGC,OAAO,CAACC,QAAQ,KAAK,OAAO,GAAGC,KAAI,EAAA,QAAA,CAACC,KAAK,CAACJ,UAAU,GAAGG,KAAI,EAAA,QAAA,CAACE,KAAK,CAACL,UAAU,AAAC;AAEzF,SAASH,wBAAwB,CACtCS,MAA8D,EAC9DC,OAKC,EACDC,OAAkD,EAC/B;IACnB,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACL,MAAM,CAACM,KAAK,CAAC,AAAC;IAE1C,IACE,mCAAmC;IACnC,CAAC,CAACH,OAAO,CAACI,MAAM,IAAI,CAACP,MAAM,CAACQ,UAAU,CAAC,IACvC,4DAA4D;IAC5D,eAAeC,IAAI,CAACR,OAAO,CAACS,gBAAgB,CAAC,IAC7C,mCAAmC;IACnChB,UAAU,CAACO,OAAO,CAACU,UAAU,CAAC,IAC9B,mCAAmC;IACnC,kBAAkBF,IAAI,CAACR,OAAO,CAACU,UAAU,CAAC,EAC1C;QACA,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAMC,OAAO,GAAGC,IAAAA,uBAAsB,uBAAA,EAACV,OAAO,EAAEF,OAAO,CAACU,UAAU,CAAC,AAAC;IACpE,IAAIC,OAAO,EAAE;QACX,KAAK,MAAME,KAAK,IAAId,MAAM,CAACM,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,CAAE;YAC9C,MAAMC,cAAc,GAAGJ,OAAO,CAACK,IAAI,GAAGH,KAAK,CAACI,OAAO,CAAC,GAAG,EAAEN,OAAO,CAACK,IAAI,CAAC,GAAGH,KAAK,AAAC;YAE/E,IAAI,WAAWL,IAAI,CAACO,cAAc,CAAC,EAAE,SAAS;YAE9C,MAAMG,cAAc,GAAGtB,KAAI,EAAA,QAAA,CAACuB,IAAI,CAACpB,MAAM,CAACqB,OAAO,EAAEL,cAAc,CAAC,AAAC;YAEjE,MAAMM,MAAM,GAAGpB,OAAO,CAACiB,cAAc,CAAC,AAAC;YACvC,IAAIG,MAAM,EAAE;gBACV9B,KAAK,CAAC,CAAC,EAAES,OAAO,CAACU,UAAU,CAAC,IAAI,EAAEQ,cAAc,CAAC,CAAC,CAAC,CAAC;gBACpD,OAAOG,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;IACH,OAAO;QACL,kEAAkE;QAClE,6DAA6D;QAC7D,IAAItB,MAAM,CAACQ,UAAU,EAAE;YACrB,MAAMW,eAAc,GAAGtB,KAAI,EAAA,QAAA,CAACuB,IAAI,CAACpB,MAAM,CAACqB,OAAO,EAAEpB,OAAO,CAACU,UAAU,CAAC,AAAC;YACrE,MAAMW,OAAM,GAAGpB,OAAO,CAACiB,eAAc,CAAC,AAAC;YACvC,IAAIG,OAAM,EAAE;gBACV9B,KAAK,CAAC,CAAC,SAAS,EAAES,OAAO,CAACU,UAAU,CAAC,IAAI,EAAEQ,eAAc,CAAC,CAAC,CAAC,CAAC;gBAC7D,OAAOG,OAAM,CAAC;YAChB,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}