{"version": 3, "sources": ["../../../src/utils/modifyConfigPlugins.ts"], "sourcesContent": ["import { ExpoConfig, modifyConfigAsync } from '@expo/config';\n\nimport { warnAboutConfigAndThrow } from './modifyConfigAsync';\nimport * as Log from '../log';\n\nexport async function attemptAddingPluginsAsync(\n  projectRoot: string,\n  exp: Pick<ExpoConfig, 'plugins'>,\n  plugins: string[]\n): Promise<void> {\n  if (!plugins.length) return;\n\n  const edits = {\n    plugins: [...new Set((exp.plugins || []).concat(plugins))],\n  };\n  const modification = await modifyConfigAsync(projectRoot, edits, {\n    skipSDKVersionRequirement: true,\n    skipPlugins: true,\n  });\n  if (modification.type === 'success') {\n    Log.log(`\\u203A Added config plugin${plugins.length === 1 ? '' : 's'}: ${plugins.join(', ')}`);\n  } else {\n    const exactEdits = {\n      plugins,\n    };\n    warnAboutConfigAndThrow(modification.type, modification.message!, exactEdits);\n  }\n}\n"], "names": ["attemptAddingPluginsAsync", "projectRoot", "exp", "plugins", "length", "edits", "Set", "concat", "modification", "modifyConfigAsync", "skipSDKVersionRequirement", "skip<PERSON>lug<PERSON>", "type", "Log", "log", "join", "exactEdits", "warnAboutConfigAndThrow", "message"], "mappings": "AAAA;;;;+BAKs<PERSON>,2BAAyB;;aAAzBA,yBAAyB;;;yBALD,cAAc;;;;;;mCAEpB,qBAAqB;2DACxC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,eAAeA,yBAAyB,CAC7CC,WAAmB,EACnBC,GAAgC,EAChCC,OAAiB,EACF;IACf,IAAI,CAACA,OAAO,CAACC,MAAM,EAAE,OAAO;IAE5B,MAAMC,KAAK,GAAG;QACZF,OAAO,EAAE;eAAI,IAAIG,GAAG,CAAC,CAACJ,GAAG,CAACC,OAAO,IAAI,EAAE,CAAC,CAACI,MAAM,CAACJ,OAAO,CAAC,CAAC;SAAC;KAC3D,AAAC;IACF,MAAMK,YAAY,GAAG,MAAMC,IAAAA,OAAiB,EAAA,kBAAA,EAACR,WAAW,EAAEI,KAAK,EAAE;QAC/DK,yBAAyB,EAAE,IAAI;QAC/BC,WAAW,EAAE,IAAI;KAClB,CAAC,AAAC;IACH,IAAIH,YAAY,CAACI,IAAI,KAAK,SAAS,EAAE;QACnCC,IAAG,CAACC,GAAG,CAAC,CAAC,0BAA0B,EAAEX,OAAO,CAACC,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,EAAED,OAAO,CAACY,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACjG,OAAO;QACL,MAAMC,UAAU,GAAG;YACjBb,OAAO;SACR,AAAC;QACFc,IAAAA,kBAAuB,wBAAA,EAACT,YAAY,CAACI,IAAI,EAAEJ,YAAY,CAACU,OAAO,EAAGF,UAAU,CAAC,CAAC;IAChF,CAAC;AACH,CAAC"}