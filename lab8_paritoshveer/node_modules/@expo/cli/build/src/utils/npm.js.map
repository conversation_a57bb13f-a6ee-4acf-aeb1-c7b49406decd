{"version": 3, "sources": ["../../../src/utils/npm.ts"], "sourcesContent": ["import { J<PERSON><PERSON>Value } from '@expo/json-file';\nimport spawnAsync from '@expo/spawn-async';\nimport assert from 'assert';\nimport crypto from 'crypto';\nimport fs from 'fs';\nimport slugify from 'slugify';\nimport { PassThrough, Stream } from 'stream';\nimport tar from 'tar';\nimport { promisify } from 'util';\n\nimport { createEntryResolver } from './createFileTransform';\nimport { ensureDirectoryAsync } from './dir';\nimport { CommandError } from './errors';\nimport { createCachedFetch } from '../api/rest/client';\n\nconst debug = require('debug')('expo:utils:npm') as typeof console.log;\n\nconst cachedFetch = createCachedFetch({\n  cacheDirectory: 'template-cache',\n  // Time to live. How long (in ms) responses remain cached before being automatically ejected. If undefined, responses are never automatically ejected from the cache.\n  // ttl: 1000,\n});\n\nexport function sanitizeNpmPackageName(name: string): string {\n  // https://github.com/npm/validate-npm-package-name/#naming-rules\n  return (\n    applyKnownNpmPackageNameRules(name) ||\n    applyKnownNpmPackageNameRules(slugify(name)) ||\n    // If nothing is left use 'app' like we do in Xcode projects.\n    'app'\n  );\n}\n\nfunction applyKnownNpmPackageNameRules(name: string): string | null {\n  // https://github.com/npm/validate-npm-package-name/#naming-rules\n\n  // package name cannot start with '.' or '_'.\n  while (/^(\\.|_)/.test(name)) {\n    name = name.substring(1);\n  }\n\n  name = name.toLowerCase().replace(/[^a-zA-Z._\\-/@]/g, '');\n\n  return (\n    name\n      // .replace(/![a-z0-9-._~]+/g, '')\n      // Remove special characters\n      .normalize('NFD')\n      .replace(/[\\u0300-\\u036f]/g, '') || null\n  );\n}\n\nexport async function npmViewAsync(...props: string[]): Promise<JSONValue> {\n  const cmd = ['view', ...props, '--json'];\n  const results = (await spawnAsync('npm', cmd)).stdout?.trim();\n  const cmdString = `npm ${cmd.join(' ')}`;\n  debug('Run:', cmdString);\n  if (!results) {\n    return null;\n  }\n  try {\n    return JSON.parse(results);\n  } catch (error: any) {\n    throw new Error(\n      `Could not parse JSON returned from \"${cmdString}\".\\n\\n${results}\\n\\nError: ${error.message}`\n    );\n  }\n}\n\n/** Given a package name like `expo` or `expo@beta`, return the registry URL if it exists. */\nexport async function getNpmUrlAsync(packageName: string): Promise<string> {\n  const results = await npmViewAsync(packageName, 'dist');\n\n  assert(results, `Could not get npm url for package \"${packageName}\"`);\n\n  // Fully qualified url returns an object.\n  // Example:\n  // 𝝠 npm view expo-template-bare-minimum@sdk-33 dist --json\n  if (typeof results === 'object' && !Array.isArray(results)) {\n    return results.tarball as string;\n  }\n\n  // When the tag is arbitrary, the tarball is an array, return the last value as it's the most recent.\n  // Example:\n  // 𝝠 npm view expo-template-bare-minimum@33 dist --json\n  if (Array.isArray(results)) {\n    const lastResult = results[results.length - 1];\n\n    if (lastResult && typeof lastResult === 'object' && !Array.isArray(lastResult)) {\n      return lastResult.tarball as string;\n    }\n  }\n\n  throw new CommandError(\n    'Expected results of `npm view ...` to be an array or string. Instead found: ' + results\n  );\n}\n\n// @ts-ignore\nconst pipeline = promisify(Stream.pipeline);\n\nexport async function downloadAndExtractNpmModuleAsync(\n  npmName: string,\n  props: ExtractProps\n): Promise<string> {\n  const url = await getNpmUrlAsync(npmName);\n\n  debug('Fetch from URL:', url);\n  return await extractNpmTarballFromUrlAsync(url, props);\n}\n\nexport async function extractLocalNpmTarballAsync(\n  tarFilePath: string,\n  props: ExtractProps\n): Promise<string> {\n  const readStream = fs.createReadStream(tarFilePath);\n  return await extractNpmTarballAsync(readStream, props);\n}\n\nexport type ExtractProps = {\n  name: string;\n  cwd: string;\n  strip?: number;\n  fileList?: string[];\n  /** The checksum algorithm to use when verifying the tarball. */\n  checksumAlgorithm?: string;\n  /** An optional filter to selectively extract specific paths */\n  filter?: tar.ExtractOptions['filter'];\n};\n\nasync function createUrlStreamAsync(url: string) {\n  const response = await cachedFetch(url);\n  if (!response.ok) {\n    throw new Error(`Unexpected response: ${response.statusText}. From url: ${url}`);\n  }\n\n  return response.body;\n}\n\nexport async function extractNpmTarballFromUrlAsync(\n  url: string,\n  props: ExtractProps\n): Promise<string> {\n  return await extractNpmTarballAsync(await createUrlStreamAsync(url), props);\n}\n\n/**\n * Extracts a tarball stream to a directory and returns the checksum of the tarball.\n */\nexport async function extractNpmTarballAsync(\n  stream: NodeJS.ReadableStream,\n  props: ExtractProps\n): Promise<string> {\n  const { cwd, strip, name, fileList = [], filter } = props;\n\n  await ensureDirectoryAsync(cwd);\n\n  const hash = crypto.createHash(props.checksumAlgorithm ?? 'md5');\n  const transformStream = new PassThrough();\n  transformStream.on('data', (chunk) => {\n    hash.update(chunk);\n  });\n\n  await pipeline(\n    stream,\n    transformStream,\n    tar.extract(\n      {\n        cwd,\n        filter,\n        onentry: createEntryResolver(name),\n        strip: strip ?? 1,\n      },\n      fileList\n    )\n  );\n\n  return hash.digest('hex');\n}\n"], "names": ["sanitizeNpmPackageName", "npmViewAsync", "getNpmUrlAsync", "downloadAndExtractNpmModuleAsync", "extractLocalNpmTarballAsync", "extractNpmTarballFromUrlAsync", "extractNpmTarballAsync", "debug", "require", "cachedFetch", "createCachedFetch", "cacheDirectory", "name", "applyKnownNpmPackageNameRules", "slugify", "test", "substring", "toLowerCase", "replace", "normalize", "props", "cmd", "results", "spawnAsync", "stdout", "trim", "cmdString", "join", "JSON", "parse", "error", "Error", "message", "packageName", "assert", "Array", "isArray", "tarball", "lastResult", "length", "CommandError", "pipeline", "promisify", "Stream", "npmName", "url", "tar<PERSON><PERSON><PERSON><PERSON>", "readStream", "fs", "createReadStream", "createUrlStreamAsync", "response", "ok", "statusText", "body", "stream", "cwd", "strip", "fileList", "filter", "ensureDirectoryAsync", "hash", "crypto", "createHash", "checksumAlgorithm", "transformStream", "PassThrough", "on", "chunk", "update", "tar", "extract", "onentry", "createEntryResolver", "digest"], "mappings": "AAAA;;;;;;;;;;;IAuBgBA,sBAAsB,MAAtBA,sBAAsB;IA6BhBC,YAAY,MAAZA,YAAY;IAkBZC,cAAc,MAAdA,cAAc;IA+BdC,gCAAgC,MAAhCA,gCAAgC;IAUhCC,2BAA2B,MAA3BA,2BAA2B;IA4B3BC,6BAA6B,MAA7BA,6BAA6B;IAU7BC,sBAAsB,MAAtBA,sBAAsB;;;8DApJrB,mBAAmB;;;;;;;8DACvB,QAAQ;;;;;;;8DACR,QAAQ;;;;;;;8DACZ,IAAI;;;;;;;8DACC,SAAS;;;;;;;yBACO,QAAQ;;;;;;;8DAC5B,KAAK;;;;;;;yBACK,MAAM;;;;;;qCAEI,uBAAuB;qBACtB,OAAO;wBACf,UAAU;wBACL,oBAAoB;;;;;;AAEtD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,AAAsB,AAAC;AAEvE,MAAMC,WAAW,GAAGC,IAAAA,OAAiB,kBAAA,EAAC;IACpCC,cAAc,EAAE,gBAAgB;CAGjC,CAAC,AAAC;AAEI,SAASX,sBAAsB,CAACY,IAAY,EAAU;IAC3D,iEAAiE;IACjE,OACEC,6BAA6B,CAACD,IAAI,CAAC,IACnCC,6BAA6B,CAACC,IAAAA,QAAO,EAAA,QAAA,EAACF,IAAI,CAAC,CAAC,IAC5C,6DAA6D;IAC7D,KAAK,CACL;AACJ,CAAC;AAED,SAASC,6BAA6B,CAACD,IAAY,EAAiB;IAClE,iEAAiE;IAEjE,6CAA6C;IAC7C,MAAO,UAAUG,IAAI,CAACH,IAAI,CAAC,CAAE;QAC3BA,IAAI,GAAGA,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAEDJ,IAAI,GAAGA,IAAI,CAACK,WAAW,EAAE,CAACC,OAAO,qBAAqB,EAAE,CAAC,CAAC;IAE1D,OACEN,IAAI,AACF,kCAAkC;IAClC,4BAA4B;KAC3BO,SAAS,CAAC,KAAK,CAAC,CAChBD,OAAO,qBAAqB,EAAE,CAAC,IAAI,IAAI,CAC1C;AACJ,CAAC;AAEM,eAAejB,YAAY,CAAC,GAAGmB,KAAK,AAAU,EAAsB;QAEzD,GAAqC;IADrD,MAAMC,GAAG,GAAG;QAAC,MAAM;WAAKD,KAAK;QAAE,QAAQ;KAAC,AAAC;IACzC,MAAME,OAAO,GAAG,CAAA,GAAqC,GAArC,CAAC,MAAMC,IAAAA,WAAU,EAAA,QAAA,EAAC,KAAK,EAAEF,GAAG,CAAC,CAAC,CAACG,MAAM,SAAM,GAA3C,KAAA,CAA2C,GAA3C,GAAqC,CAAEC,IAAI,EAAE,AAAC;IAC9D,MAAMC,SAAS,GAAG,CAAC,IAAI,EAAEL,GAAG,CAACM,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,AAAC;IACzCpB,KAAK,CAAC,MAAM,EAAEmB,SAAS,CAAC,CAAC;IACzB,IAAI,CAACJ,OAAO,EAAE;QACZ,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI;QACF,OAAOM,IAAI,CAACC,KAAK,CAACP,OAAO,CAAC,CAAC;IAC7B,EAAE,OAAOQ,KAAK,EAAO;QACnB,MAAM,IAAIC,KAAK,CACb,CAAC,oCAAoC,EAAEL,SAAS,CAAC,MAAM,EAAEJ,OAAO,CAAC,WAAW,EAAEQ,KAAK,CAACE,OAAO,CAAC,CAAC,CAC9F,CAAC;IACJ,CAAC;AACH,CAAC;AAGM,eAAe9B,cAAc,CAAC+B,WAAmB,EAAmB;IACzE,MAAMX,OAAO,GAAG,MAAMrB,YAAY,CAACgC,WAAW,EAAE,MAAM,CAAC,AAAC;IAExDC,IAAAA,OAAM,EAAA,QAAA,EAACZ,OAAO,EAAE,CAAC,mCAAmC,EAAEW,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtE,yCAAyC;IACzC,WAAW;IACX,2DAA2D;IAC3D,IAAI,OAAOX,OAAO,KAAK,QAAQ,IAAI,CAACa,KAAK,CAACC,OAAO,CAACd,OAAO,CAAC,EAAE;QAC1D,OAAOA,OAAO,CAACe,OAAO,CAAW;IACnC,CAAC;IAED,qGAAqG;IACrG,WAAW;IACX,uDAAuD;IACvD,IAAIF,KAAK,CAACC,OAAO,CAACd,OAAO,CAAC,EAAE;QAC1B,MAAMgB,UAAU,GAAGhB,OAAO,CAACA,OAAO,CAACiB,MAAM,GAAG,CAAC,CAAC,AAAC;QAE/C,IAAID,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,IAAI,CAACH,KAAK,CAACC,OAAO,CAACE,UAAU,CAAC,EAAE;YAC9E,OAAOA,UAAU,CAACD,OAAO,CAAW;QACtC,CAAC;IACH,CAAC;IAED,MAAM,IAAIG,OAAY,aAAA,CACpB,8EAA8E,GAAGlB,OAAO,CACzF,CAAC;AACJ,CAAC;AAED,aAAa;AACb,MAAMmB,QAAQ,GAAGC,IAAAA,KAAS,EAAA,UAAA,EAACC,OAAM,EAAA,OAAA,CAACF,QAAQ,CAAC,AAAC;AAErC,eAAetC,gCAAgC,CACpDyC,OAAe,EACfxB,KAAmB,EACF;IACjB,MAAMyB,GAAG,GAAG,MAAM3C,cAAc,CAAC0C,OAAO,CAAC,AAAC;IAE1CrC,KAAK,CAAC,iBAAiB,EAAEsC,GAAG,CAAC,CAAC;IAC9B,OAAO,MAAMxC,6BAA6B,CAACwC,GAAG,EAAEzB,KAAK,CAAC,CAAC;AACzD,CAAC;AAEM,eAAehB,2BAA2B,CAC/C0C,WAAmB,EACnB1B,KAAmB,EACF;IACjB,MAAM2B,UAAU,GAAGC,GAAE,EAAA,QAAA,CAACC,gBAAgB,CAACH,WAAW,CAAC,AAAC;IACpD,OAAO,MAAMxC,sBAAsB,CAACyC,UAAU,EAAE3B,KAAK,CAAC,CAAC;AACzD,CAAC;AAaD,eAAe8B,oBAAoB,CAACL,GAAW,EAAE;IAC/C,MAAMM,QAAQ,GAAG,MAAM1C,WAAW,CAACoC,GAAG,CAAC,AAAC;IACxC,IAAI,CAACM,QAAQ,CAACC,EAAE,EAAE;QAChB,MAAM,IAAIrB,KAAK,CAAC,CAAC,qBAAqB,EAAEoB,QAAQ,CAACE,UAAU,CAAC,YAAY,EAAER,GAAG,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC;IAED,OAAOM,QAAQ,CAACG,IAAI,CAAC;AACvB,CAAC;AAEM,eAAejD,6BAA6B,CACjDwC,GAAW,EACXzB,KAAmB,EACF;IACjB,OAAO,MAAMd,sBAAsB,CAAC,MAAM4C,oBAAoB,CAACL,GAAG,CAAC,EAAEzB,KAAK,CAAC,CAAC;AAC9E,CAAC;AAKM,eAAed,sBAAsB,CAC1CiD,MAA6B,EAC7BnC,KAAmB,EACF;IACjB,MAAM,EAAEoC,GAAG,CAAA,EAAEC,KAAK,CAAA,EAAE7C,IAAI,CAAA,EAAE8C,QAAQ,EAAG,EAAE,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAGvC,KAAK,AAAC;IAE1D,MAAMwC,IAAAA,IAAoB,qBAAA,EAACJ,GAAG,CAAC,CAAC;QAEDpC,kBAAuB;IAAtD,MAAMyC,IAAI,GAAGC,OAAM,EAAA,QAAA,CAACC,UAAU,CAAC3C,CAAAA,kBAAuB,GAAvBA,KAAK,CAAC4C,iBAAiB,YAAvB5C,kBAAuB,GAAI,KAAK,CAAC,AAAC;IACjE,MAAM6C,eAAe,GAAG,IAAIC,CAAAA,OAAW,EAAA,CAAA,YAAA,EAAE,AAAC;IAC1CD,eAAe,CAACE,EAAE,CAAC,MAAM,EAAE,CAACC,KAAK,GAAK;QACpCP,IAAI,CAACQ,MAAM,CAACD,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC;IAEH,MAAM3B,QAAQ,CACZc,MAAM,EACNU,eAAe,EACfK,IAAG,EAAA,QAAA,CAACC,OAAO,CACT;QACEf,GAAG;QACHG,MAAM;QACNa,OAAO,EAAEC,IAAAA,oBAAmB,oBAAA,EAAC7D,IAAI,CAAC;QAClC6C,KAAK,EAAEA,KAAK,WAALA,KAAK,GAAI,CAAC;KAClB,EACDC,QAAQ,CACT,CACF,CAAC;IAEF,OAAOG,IAAI,CAACa,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC"}