{"version": 3, "sources": ["../../../../src/utils/telemetry/types.ts"], "sourcesContent": ["import { type Actor } from '../../api/user/user';\n\nexport type TelemetryEvent =\n  | 'action'\n  | 'Open Url on Device'\n  | 'Start Project'\n  | 'Serve Manifest'\n  | 'Serve Expo Updates Manifest'\n  | 'dev client start command'\n  | 'dev client run command'\n  | 'metro config'\n  | 'metro debug';\n\nexport type TelemetryProperties = Record<string, any>;\n\nexport type TelemetryRecord = { event: TelemetryEvent; properties?: TelemetryProperties };\n\nexport type TelemetryRecordWithDate = TelemetryRecord & {\n  originalTimestamp: Date;\n};\n\nexport interface TelemetryClient {\n  /** Determine if the telemetry client is identified */\n  readonly isIdentified: boolean;\n  /** Identify the current actor */\n  identify(actor?: Actor): Promise<void>;\n  /** Record a (custom) event */\n  record(record: TelemetryRecord): Promise<void>;\n  /** Clear the record queue and send all recorded events */\n  flush(): Promise<void>;\n}\n"], "names": [], "mappings": "AAAA"}