{"version": 3, "sources": ["../../../../src/utils/telemetry/RudderClient.ts"], "sourcesContent": ["import RudderAnalytics from '@expo/rudder-sdk-node';\n\nimport { getContext } from './getContext';\nimport type { TelemetryClient, TelemetryRecord, TelemetryRecordWithDate } from './types';\nimport UserSettings from '../../api/user/UserSettings';\nimport { type Actor, getActorDisplayName, getUserAsync } from '../../api/user/user';\nimport { env } from '../env';\n\nconst debug = require('debug')('expo:telemetry:rudderClient') as typeof console.log;\n\nexport class RudderClient implements TelemetryClient {\n  /** The RudderStack SDK instance */\n  private rudderstack: RudderAnalytics;\n  /** The known identity of the user */\n  private identity: { userId: string; anonymousId: string } | undefined;\n  /** The promise to initially identify the user */\n  private initialIdentify: Promise<any> | undefined;\n\n  constructor(\n    sdk?: RudderAnalytics,\n    private mode: 'attached' | 'detached' = 'attached'\n  ) {\n    if (!sdk) {\n      sdk = new RudderAnalytics(\n        env.EXPO_STAGING || env.EXPO_LOCAL\n          ? '24TKICqYKilXM480mA7ktgVDdea'\n          : '24TKR7CQAaGgIrLTgu3Fp4OdOkI', // expo unified\n        'https://cdp.expo.dev/v1/batch',\n        {\n          flushInterval: 300,\n        }\n      );\n    }\n\n    this.rudderstack = sdk;\n  }\n\n  /**\n   * Wait until the initial identification is complete.\n   * This may be called multiple times, from `.record()`, but only calls `getUserAsync` once.\n   * Note, this method won't retry after the initial identification returns `undefined`.\n   */\n  private async waitUntilInitialIdentification() {\n    if (!this.identity && !this.initialIdentify) {\n      // This method has a side-effect that calls `.identify()` internally\n      this.initialIdentify = getUserAsync();\n    }\n\n    if (!this.identity && this.initialIdentify) {\n      await this.initialIdentify;\n    }\n  }\n\n  get isIdentified() {\n    return !!this.identity;\n  }\n\n  async identify(actor?: Actor) {\n    if (!actor) return;\n\n    debug('Actor received');\n\n    const userId = actor.id;\n    const anonymousId = await UserSettings.getAnonymousIdentifierAsync();\n\n    if (this.identity?.userId === userId && this.identity?.anonymousId === anonymousId) {\n      return;\n    }\n\n    this.identity = { userId, anonymousId };\n    this.rudderstack.identify({\n      userId,\n      anonymousId,\n      traits: {\n        username: getActorDisplayName(actor),\n        user_id: actor.id,\n        user_type: actor.__typename,\n      },\n    });\n  }\n\n  async record(record: TelemetryRecord | TelemetryRecordWithDate) {\n    if (!this.identity) {\n      await this.waitUntilInitialIdentification();\n    }\n\n    if (this.identity) {\n      debug('Event received: %s', record.event);\n\n      const originalTimestamp =\n        'originalTimestamp' in record ? record.originalTimestamp : undefined;\n\n      await this.rudderstack.track({\n        event: record.event,\n        originalTimestamp,\n        properties: record.properties,\n        ...this.identity,\n        context: {\n          ...getContext(),\n          client: { mode: this.mode },\n        },\n      });\n    }\n  }\n\n  async flush() {\n    await this.rudderstack.flush();\n  }\n}\n"], "names": ["RudderClient", "debug", "require", "constructor", "sdk", "mode", "RudderAnalytics", "env", "EXPO_STAGING", "EXPO_LOCAL", "flushInterval", "rudderstack", "waitUntilInitialIdentification", "identity", "initialIdentify", "getUserAsync", "isIdentified", "identify", "actor", "userId", "id", "anonymousId", "UserSettings", "getAnonymousIdentifierAsync", "traits", "username", "getActorDisplayName", "user_id", "user_type", "__typename", "record", "event", "originalTimestamp", "undefined", "track", "properties", "context", "getContext", "client", "flush"], "mappings": "AAAA;;;;+BAUaA,cAAY;;aAAZA,YAAY;;;8DAVG,uBAAuB;;;;;;4BAExB,cAAc;mEAEhB,6BAA6B;sBACQ,qBAAqB;qBAC/D,QAAQ;;;;;;AAE5B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,6BAA6B,CAAC,AAAsB,AAAC;AAE7E,MAAMF,YAAY;IAQvBG,YACEC,GAAqB,EACbC,IAA6B,GAAG,UAAU,CAClD;QADQA,YAAAA,IAA6B,CAAA;QAErC,IAAI,CAACD,GAAG,EAAE;YACRA,GAAG,GAAG,IAAIE,CAAAA,cAAe,EAAA,CAAA,QAAA,CACvBC,IAAG,IAAA,CAACC,YAAY,IAAID,IAAG,IAAA,CAACE,UAAU,GAC9B,6BAA6B,GAC7B,6BAA6B,EACjC,+BAA+B,EAC/B;gBACEC,aAAa,EAAE,GAAG;aACnB,CACF,CAAC;QACJ,CAAC;QAED,IAAI,CAACC,WAAW,GAAGP,GAAG,CAAC;IACzB;IAEA;;;;GAIC,SACaQ,8BAA8B,GAAG;QAC7C,IAAI,CAAC,IAAI,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;YAC3C,oEAAoE;YACpE,IAAI,CAACA,eAAe,GAAGC,IAAAA,KAAY,aAAA,GAAE,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,IAAI,CAACF,QAAQ,IAAI,IAAI,CAACC,eAAe,EAAE;YAC1C,MAAM,IAAI,CAACA,eAAe,CAAC;QAC7B,CAAC;IACH;QAEIE,YAAY,GAAG;QACjB,OAAO,CAAC,CAAC,IAAI,CAACH,QAAQ,CAAC;IACzB;UAEMI,QAAQ,CAACC,KAAa,EAAE;YAQxB,GAAa,EAAuB,IAAa;QAPrD,IAAI,CAACA,KAAK,EAAE,OAAO;QAEnBjB,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAExB,MAAMkB,MAAM,GAAGD,KAAK,CAACE,EAAE,AAAC;QACxB,MAAMC,WAAW,GAAG,MAAMC,aAAY,QAAA,CAACC,2BAA2B,EAAE,AAAC;QAErE,IAAI,CAAA,CAAA,GAAa,GAAb,IAAI,CAACV,QAAQ,SAAQ,GAArB,KAAA,CAAqB,GAArB,GAAa,CAAEM,MAAM,CAAA,KAAKA,MAAM,IAAI,CAAA,CAAA,IAAa,GAAb,IAAI,CAACN,QAAQ,SAAa,GAA1B,KAAA,CAA0B,GAA1B,IAAa,CAAEQ,WAAW,CAAA,KAAKA,WAAW,EAAE;YAClF,OAAO;QACT,CAAC;QAED,IAAI,CAACR,QAAQ,GAAG;YAAEM,MAAM;YAAEE,WAAW;SAAE,CAAC;QACxC,IAAI,CAACV,WAAW,CAACM,QAAQ,CAAC;YACxBE,MAAM;YACNE,WAAW;YACXG,MAAM,EAAE;gBACNC,QAAQ,EAAEC,IAAAA,KAAmB,oBAAA,EAACR,KAAK,CAAC;gBACpCS,OAAO,EAAET,KAAK,CAACE,EAAE;gBACjBQ,SAAS,EAAEV,KAAK,CAACW,UAAU;aAC5B;SACF,CAAC,CAAC;IACL;UAEMC,MAAM,CAACA,MAAiD,EAAE;QAC9D,IAAI,CAAC,IAAI,CAACjB,QAAQ,EAAE;YAClB,MAAM,IAAI,CAACD,8BAA8B,EAAE,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAACC,QAAQ,EAAE;YACjBZ,KAAK,CAAC,oBAAoB,EAAE6B,MAAM,CAACC,KAAK,CAAC,CAAC;YAE1C,MAAMC,iBAAiB,GACrB,mBAAmB,IAAIF,MAAM,GAAGA,MAAM,CAACE,iBAAiB,GAAGC,SAAS,AAAC;YAEvE,MAAM,IAAI,CAACtB,WAAW,CAACuB,KAAK,CAAC;gBAC3BH,KAAK,EAAED,MAAM,CAACC,KAAK;gBACnBC,iBAAiB;gBACjBG,UAAU,EAAEL,MAAM,CAACK,UAAU;gBAC7B,GAAG,IAAI,CAACtB,QAAQ;gBAChBuB,OAAO,EAAE;oBACP,GAAGC,IAAAA,WAAU,WAAA,GAAE;oBACfC,MAAM,EAAE;wBAAEjC,IAAI,EAAE,IAAI,CAACA,IAAI;qBAAE;iBAC5B;aACF,CAAC,CAAC;QACL,CAAC;IACH;UAEMkC,KAAK,GAAG;QACZ,MAAM,IAAI,CAAC5B,WAAW,CAAC4B,KAAK,EAAE,CAAC;IACjC;CACD"}