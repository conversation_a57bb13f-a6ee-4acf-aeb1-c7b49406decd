{"version": 3, "sources": ["../../../src/utils/prompts.ts"], "sourcesContent": ["import assert from 'assert';\nimport prompts, { Choice, Options, PromptObject } from 'prompts';\n\nimport { AbortCommandError, CommandError } from './errors';\nimport { isInteractive } from './interactive';\n\nconst debug = require('debug')('expo:utils:prompts') as typeof console.log;\n\nexport type Question<V extends string = string> = PromptObject<V> & {\n  optionsPerPage?: number;\n};\n\nexport interface ExpoChoice<T> extends Choice {\n  value: T;\n}\n\ntype PromptOptions = { nonInteractiveHelp?: string } & Options;\n\nexport type NamelessQuestion = Omit<Question<'value'>, 'name' | 'type'>;\n\ntype InteractionOptions = { pause: boolean; canEscape?: boolean };\n\ntype InteractionCallback = (options: InteractionOptions) => void;\n\n/** Interaction observers for detecting when keystroke tracking should pause/resume. */\nconst listeners: InteractionCallback[] = [];\n\nexport default async function prompt(\n  questions: Question | Question[],\n  { nonInteractiveHelp, ...options }: PromptOptions = {}\n) {\n  questions = Array.isArray(questions) ? questions : [questions];\n  if (!isInteractive() && questions.length !== 0) {\n    let message = `Input is required, but 'npx expo' is in non-interactive mode.\\n`;\n    if (nonInteractiveHelp) {\n      message += nonInteractiveHelp;\n    } else {\n      const question = questions[0];\n      const questionMessage =\n        typeof question.message === 'function'\n          ? question.message(undefined, {}, question)\n          : question.message;\n\n      message += `Required input:\\n${(questionMessage || '').trim().replace(/^/gm, '> ')}`;\n    }\n    throw new CommandError('NON_INTERACTIVE', message);\n  }\n\n  pauseInteractions();\n  try {\n    const results = await prompts(questions, {\n      onCancel() {\n        throw new AbortCommandError();\n      },\n      ...options,\n    });\n\n    return results;\n  } finally {\n    resumeInteractions();\n  }\n}\n\n/**\n * Create a standard yes/no confirmation that can be cancelled.\n *\n * @param questions\n * @param options\n */\nexport async function confirmAsync(\n  questions: NamelessQuestion,\n  options?: PromptOptions\n): Promise<boolean> {\n  const { value } = await prompt(\n    {\n      initial: true,\n      ...questions,\n      name: 'value',\n      type: 'confirm',\n    },\n    options\n  );\n  return value ?? null;\n}\n\n/** Select an option from a list of options. */\nexport async function selectAsync<T>(\n  message: string,\n  choices: ExpoChoice<T>[],\n  options?: PromptOptions\n): Promise<T> {\n  const { value } = await prompt(\n    {\n      message,\n      choices,\n      name: 'value',\n      type: 'select',\n    },\n    options\n  );\n  return value ?? null;\n}\n\nexport const promptAsync = prompt;\n\n/** Used to pause/resume interaction observers while prompting (made for TerminalUI). */\nexport function addInteractionListener(callback: InteractionCallback) {\n  listeners.push(callback);\n}\n\nexport function removeInteractionListener(callback: InteractionCallback) {\n  const listenerIndex = listeners.findIndex((_callback) => _callback === callback);\n  assert(\n    listenerIndex >= 0,\n    'removeInteractionListener(): cannot remove an unregistered event listener.'\n  );\n  listeners.splice(listenerIndex, 1);\n}\n\n/** Notify all listeners that keypress observations must pause. */\nexport function pauseInteractions(options: Omit<InteractionOptions, 'pause'> = {}) {\n  debug('Interaction observers paused');\n  for (const listener of listeners) {\n    listener({ pause: true, ...options });\n  }\n}\n\n/** Notify all listeners that keypress observations can start.. */\nexport function resumeInteractions(options: Omit<InteractionOptions, 'pause'> = {}) {\n  debug('Interaction observers resumed');\n  for (const listener of listeners) {\n    listener({ pause: false, ...options });\n  }\n}\n\nexport function createSelectionFilter(): (input: any, choices: Choice[]) => Promise<any> {\n  function escapeRegex(string: string) {\n    return string.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n  }\n\n  return async (input: any, choices: Choice[]) => {\n    try {\n      const regex = new RegExp(escapeRegex(input), 'i');\n      return choices.filter((choice: any) => regex.test(choice.title));\n    } catch (error: any) {\n      debug('Error filtering choices', error);\n      return [];\n    }\n  };\n}\n"], "names": ["prompt", "<PERSON><PERSON><PERSON>", "selectAsync", "promptAsync", "addInteractionListener", "removeInteractionListener", "pauseInteractions", "resumeInteractions", "createSelectionFilter", "debug", "require", "listeners", "questions", "nonInteractiveHelp", "options", "Array", "isArray", "isInteractive", "length", "message", "question", "questionMessage", "undefined", "trim", "replace", "CommandError", "results", "prompts", "onCancel", "AbortCommandError", "value", "initial", "name", "type", "choices", "callback", "push", "listenerIndex", "findIndex", "_callback", "assert", "splice", "listener", "pause", "escapeRegex", "string", "input", "regex", "RegExp", "filter", "choice", "test", "title", "error"], "mappings": "AAAA;;;;;;;;;;;IA2BA,OAkCC,MAlC6BA,MAAM;IA0CdC,YAAY,MAAZA,YAAY;IAiBZC,WAAW,MAAXA,WAAW;IAiBpBC,WAAW,MAAXA,WAAW;IAGRC,sBAAsB,MAAtBA,sBAAsB;IAItBC,yBAAyB,MAAzBA,yBAAyB;IAUzBC,iBAAiB,MAAjBA,iBAAiB;IAQjBC,kBAAkB,MAAlBA,kBAAkB;IAOlBC,qBAAqB,MAArBA,qBAAqB;;;8DAvIlB,QAAQ;;;;;;;8DAC4B,SAAS;;;;;;wBAEhB,UAAU;6BAC5B,eAAe;;;;;;AAE7C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oBAAoB,CAAC,AAAsB,AAAC;AAkB3E,qFAAqF,GACrF,MAAMC,SAAS,GAA0B,EAAE,AAAC;AAE7B,eAAeX,MAAM,CAClCY,SAAgC,EAChC,EAAEC,kBAAkB,CAAA,EAAE,GAAGC,OAAO,EAAiB,GAAG,EAAE,EACtD;IACAF,SAAS,GAAGG,KAAK,CAACC,OAAO,CAACJ,SAAS,CAAC,GAAGA,SAAS,GAAG;QAACA,SAAS;KAAC,CAAC;IAC/D,IAAI,CAACK,IAAAA,YAAa,cAAA,GAAE,IAAIL,SAAS,CAACM,MAAM,KAAK,CAAC,EAAE;QAC9C,IAAIC,OAAO,GAAG,CAAC,+DAA+D,CAAC,AAAC;QAChF,IAAIN,kBAAkB,EAAE;YACtBM,OAAO,IAAIN,kBAAkB,CAAC;QAChC,OAAO;YACL,MAAMO,QAAQ,GAAGR,SAAS,CAAC,CAAC,CAAC,AAAC;YAC9B,MAAMS,eAAe,GACnB,OAAOD,QAAQ,CAACD,OAAO,KAAK,UAAU,GAClCC,QAAQ,CAACD,OAAO,CAACG,SAAS,EAAE,EAAE,EAAEF,QAAQ,CAAC,GACzCA,QAAQ,CAACD,OAAO,AAAC;YAEvBA,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAACE,eAAe,IAAI,EAAE,CAAC,CAACE,IAAI,EAAE,CAACC,OAAO,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC;QACvF,CAAC;QACD,MAAM,IAAIC,OAAY,aAAA,CAAC,iBAAiB,EAAEN,OAAO,CAAC,CAAC;IACrD,CAAC;IAEDb,iBAAiB,EAAE,CAAC;IACpB,IAAI;QACF,MAAMoB,OAAO,GAAG,MAAMC,IAAAA,QAAO,EAAA,QAAA,EAACf,SAAS,EAAE;YACvCgB,QAAQ,IAAG;gBACT,MAAM,IAAIC,OAAiB,kBAAA,EAAE,CAAC;YAChC,CAAC;YACD,GAAGf,OAAO;SACX,CAAC,AAAC;QAEH,OAAOY,OAAO,CAAC;IACjB,SAAU;QACRnB,kBAAkB,EAAE,CAAC;IACvB,CAAC;AACH,CAAC;AAQM,eAAeN,YAAY,CAChCW,SAA2B,EAC3BE,OAAuB,EACL;IAClB,MAAM,EAAEgB,KAAK,CAAA,EAAE,GAAG,MAAM9B,MAAM,CAC5B;QACE+B,OAAO,EAAE,IAAI;QACb,GAAGnB,SAAS;QACZoB,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,SAAS;KAChB,EACDnB,OAAO,CACR,AAAC;IACF,OAAOgB,KAAK,WAALA,KAAK,GAAI,IAAI,CAAC;AACvB,CAAC;AAGM,eAAe5B,WAAW,CAC/BiB,OAAe,EACfe,OAAwB,EACxBpB,OAAuB,EACX;IACZ,MAAM,EAAEgB,KAAK,CAAA,EAAE,GAAG,MAAM9B,MAAM,CAC5B;QACEmB,OAAO;QACPe,OAAO;QACPF,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE,QAAQ;KACf,EACDnB,OAAO,CACR,AAAC;IACF,OAAOgB,KAAK,WAALA,KAAK,GAAI,IAAI,CAAC;AACvB,CAAC;AAEM,MAAM3B,WAAW,GAAGH,MAAM,AAAC;AAG3B,SAASI,sBAAsB,CAAC+B,QAA6B,EAAE;IACpExB,SAAS,CAACyB,IAAI,CAACD,QAAQ,CAAC,CAAC;AAC3B,CAAC;AAEM,SAAS9B,yBAAyB,CAAC8B,QAA6B,EAAE;IACvE,MAAME,aAAa,GAAG1B,SAAS,CAAC2B,SAAS,CAAC,CAACC,SAAS,GAAKA,SAAS,KAAKJ,QAAQ,CAAC,AAAC;IACjFK,IAAAA,OAAM,EAAA,QAAA,EACJH,aAAa,IAAI,CAAC,EAClB,4EAA4E,CAC7E,CAAC;IACF1B,SAAS,CAAC8B,MAAM,CAACJ,aAAa,EAAE,CAAC,CAAC,CAAC;AACrC,CAAC;AAGM,SAAS/B,iBAAiB,CAACQ,OAA0C,GAAG,EAAE,EAAE;IACjFL,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACtC,KAAK,MAAMiC,QAAQ,IAAI/B,SAAS,CAAE;QAChC+B,QAAQ,CAAC;YAAEC,KAAK,EAAE,IAAI;YAAE,GAAG7B,OAAO;SAAE,CAAC,CAAC;IACxC,CAAC;AACH,CAAC;AAGM,SAASP,kBAAkB,CAACO,OAA0C,GAAG,EAAE,EAAE;IAClFL,KAAK,CAAC,+BAA+B,CAAC,CAAC;IACvC,KAAK,MAAMiC,QAAQ,IAAI/B,SAAS,CAAE;QAChC+B,QAAQ,CAAC;YAAEC,KAAK,EAAE,KAAK;YAAE,GAAG7B,OAAO;SAAE,CAAC,CAAC;IACzC,CAAC;AACH,CAAC;AAEM,SAASN,qBAAqB,GAAoD;IACvF,SAASoC,WAAW,CAACC,MAAc,EAAE;QACnC,OAAOA,MAAM,CAACrB,OAAO,0BAA0B,MAAM,CAAC,CAAC;IACzD,CAAC;IAED,OAAO,OAAOsB,KAAU,EAAEZ,OAAiB,GAAK;QAC9C,IAAI;YACF,MAAMa,KAAK,GAAG,IAAIC,MAAM,CAACJ,WAAW,CAACE,KAAK,CAAC,EAAE,GAAG,CAAC,AAAC;YAClD,OAAOZ,OAAO,CAACe,MAAM,CAAC,CAACC,MAAW,GAAKH,KAAK,CAACI,IAAI,CAACD,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;QACnE,EAAE,OAAOC,KAAK,EAAO;YACnB5C,KAAK,CAAC,yBAAyB,EAAE4C,KAAK,CAAC,CAAC;YACxC,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC,CAAC;AACJ,CAAC"}