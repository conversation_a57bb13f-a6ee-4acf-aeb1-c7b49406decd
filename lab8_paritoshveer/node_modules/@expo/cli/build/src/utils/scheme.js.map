{"version": 3, "sources": ["../../../src/utils/scheme.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport { AndroidConfig, IOSConfig } from '@expo/config-plugins';\nimport { getInfoPlistPathFromPbxproj } from '@expo/config-plugins/build/ios/utils/getInfoPlistPath';\nimport plist from '@expo/plist';\nimport fs from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { intersecting } from './array';\nimport * as Log from '../log';\nimport {\n  hasRequiredAndroidFilesAsync,\n  hasRequiredIOSFilesAsync,\n} from '../prebuild/clearNativeFolder';\n\nconst debug = require('debug')('expo:utils:scheme') as typeof console.log;\n\n// sort longest to ensure uniqueness.\n// this might be undesirable as it causes the QR code to be longer.\nfunction sortLongest(obj: string[]): string[] {\n  return obj.sort((a, b) => b.length - a.length);\n}\n\n/**\n * Resolve the scheme for the dev client using two methods:\n *   - filter on known Expo schemes, starting with `exp+`, avoiding 3rd party schemes.\n *   - filter on longest to ensure uniqueness.\n */\nfunction resolveExpoOrLongestScheme(schemes: string[]): string[] {\n  const expoOnlySchemes = schemes.filter((scheme) => scheme.startsWith('exp+'));\n  return expoOnlySchemes.length > 0 ? sortLongest(expoOnlySchemes) : sortLongest(schemes);\n}\n\n// TODO: Revisit and test after run code is merged.\nexport async function getSchemesForIosAsync(projectRoot: string): Promise<string[]> {\n  try {\n    const infoPlistBuildProperty = getInfoPlistPathFromPbxproj(projectRoot);\n    debug(`ios application Info.plist path:`, infoPlistBuildProperty);\n    if (infoPlistBuildProperty) {\n      const configPath = path.join(projectRoot, 'ios', infoPlistBuildProperty);\n      const rawPlist = fs.readFileSync(configPath, 'utf8');\n      const plistObject = plist.parse(rawPlist);\n      const schemes = IOSConfig.Scheme.getSchemesFromPlist(plistObject);\n      debug(`ios application schemes:`, schemes);\n      return resolveExpoOrLongestScheme(schemes);\n    }\n  } catch (error) {\n    debug(`expected error collecting ios application schemes for the main target:`, error);\n  }\n  // No ios folder or some other error\n  return [];\n}\n\n// TODO: Revisit and test after run code is merged.\nexport async function getSchemesForAndroidAsync(projectRoot: string): Promise<string[]> {\n  try {\n    const configPath = await AndroidConfig.Paths.getAndroidManifestAsync(projectRoot);\n    const manifest = await AndroidConfig.Manifest.readAndroidManifestAsync(configPath);\n    const schemes = await AndroidConfig.Scheme.getSchemesFromManifest(manifest);\n    debug(`android application schemes:`, schemes);\n    return resolveExpoOrLongestScheme(schemes);\n  } catch (error) {\n    debug(`expected error collecting android application schemes for the main activity:`, error);\n    // No android folder or some other error\n    return [];\n  }\n}\n\n// TODO: Revisit and test after run code is merged.\nasync function getManagedDevClientSchemeAsync(projectRoot: string): Promise<string | null> {\n  const { exp } = getConfig(projectRoot);\n  try {\n    const getDefaultScheme = require(resolveFrom(projectRoot, 'expo-dev-client/getDefaultScheme'));\n    const scheme = getDefaultScheme(exp);\n    return scheme;\n  } catch {\n    Log.warn(\n      '\\nDevelopment build: Unable to get the default URI scheme for the project. Please make sure the expo-dev-client package is installed.'\n    );\n    return null;\n  }\n}\n\n// TODO: Revisit and test after run code is merged.\nexport async function getOptionalDevClientSchemeAsync(\n  projectRoot: string\n): Promise<{ scheme: string | null; resolution: 'config' | 'shared' | 'android' | 'ios' }> {\n  const [hasIos, hasAndroid] = await Promise.all([\n    hasRequiredIOSFilesAsync(projectRoot),\n    hasRequiredAndroidFilesAsync(projectRoot),\n  ]);\n\n  const [ios, android] = await Promise.all([\n    getSchemesForIosAsync(projectRoot),\n    getSchemesForAndroidAsync(projectRoot),\n  ]);\n\n  // Allow managed projects\n  if (!hasIos && !hasAndroid) {\n    return { scheme: await getManagedDevClientSchemeAsync(projectRoot), resolution: 'config' };\n  }\n\n  // Allow for only one native project to exist.\n  if (!hasIos) {\n    return { scheme: android[0], resolution: 'android' };\n  } else if (!hasAndroid) {\n    return { scheme: ios[0], resolution: 'ios' };\n  } else {\n    return { scheme: intersecting(ios, android)[0], resolution: 'shared' };\n  }\n}\n"], "names": ["getSchemesForIosAsync", "getSchemesForAndroidAsync", "getOptionalDevClientSchemeAsync", "debug", "require", "sortLongest", "obj", "sort", "a", "b", "length", "resolveExpoOrLongestScheme", "schemes", "expoOnlySchemes", "filter", "scheme", "startsWith", "projectRoot", "infoPlistBuildProperty", "getInfoPlistPathFromPbxproj", "config<PERSON><PERSON>", "path", "join", "rawPlist", "fs", "readFileSync", "plistObject", "plist", "parse", "IOSConfig", "Scheme", "getSchemesFromPlist", "error", "AndroidConfig", "Paths", "getAndroidManifestAsync", "manifest", "Manifest", "readAndroidManifestAsync", "getSchemesFromManifest", "getManagedDevClientSchemeAsync", "exp", "getConfig", "getDefaultScheme", "resolveFrom", "Log", "warn", "hasIos", "hasAndroid", "Promise", "all", "hasRequiredIOSFilesAsync", "hasRequiredAndroidFilesAsync", "ios", "android", "resolution", "intersecting"], "mappings": "AAAA;;;;;;;;;;;IAkCsBA,qBAAqB,MAArBA,qBAAqB;IAoBrBC,yBAAyB,MAAzBA,yBAAyB;IA8BzBC,+BAA+B,MAA/BA,+BAA+B;;;yBApF3B,cAAc;;;;;;;yBACC,sBAAsB;;;;;;;yBACnB,uDAAuD;;;;;;;8DACjF,aAAa;;;;;;;8DAChB,IAAI;;;;;;;8DACF,MAAM;;;;;;;8DACC,cAAc;;;;;;uBAET,SAAS;2DACjB,QAAQ;mCAItB,+BAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAC,AAAsB,AAAC;AAE1E,qCAAqC;AACrC,mEAAmE;AACnE,SAASC,WAAW,CAACC,GAAa,EAAY;IAC5C,OAAOA,GAAG,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKA,CAAC,CAACC,MAAM,GAAGF,CAAC,CAACE,MAAM,CAAC,CAAC;AACjD,CAAC;AAED;;;;CAIC,GACD,SAASC,0BAA0B,CAACC,OAAiB,EAAY;IAC/D,MAAMC,eAAe,GAAGD,OAAO,CAACE,MAAM,CAAC,CAACC,MAAM,GAAKA,MAAM,CAACC,UAAU,CAAC,MAAM,CAAC,CAAC,AAAC;IAC9E,OAAOH,eAAe,CAACH,MAAM,GAAG,CAAC,GAAGL,WAAW,CAACQ,eAAe,CAAC,GAAGR,WAAW,CAACO,OAAO,CAAC,CAAC;AAC1F,CAAC;AAGM,eAAeZ,qBAAqB,CAACiB,WAAmB,EAAqB;IAClF,IAAI;QACF,MAAMC,sBAAsB,GAAGC,IAAAA,iBAA2B,EAAA,4BAAA,EAACF,WAAW,CAAC,AAAC;QACxEd,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAEe,sBAAsB,CAAC,CAAC;QAClE,IAAIA,sBAAsB,EAAE;YAC1B,MAAME,UAAU,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACL,WAAW,EAAE,KAAK,EAAEC,sBAAsB,CAAC,AAAC;YACzE,MAAMK,QAAQ,GAAGC,GAAE,EAAA,QAAA,CAACC,YAAY,CAACL,UAAU,EAAE,MAAM,CAAC,AAAC;YACrD,MAAMM,WAAW,GAAGC,MAAK,EAAA,QAAA,CAACC,KAAK,CAACL,QAAQ,CAAC,AAAC;YAC1C,MAAMX,OAAO,GAAGiB,cAAS,EAAA,UAAA,CAACC,MAAM,CAACC,mBAAmB,CAACL,WAAW,CAAC,AAAC;YAClEvB,KAAK,CAAC,CAAC,wBAAwB,CAAC,EAAES,OAAO,CAAC,CAAC;YAC3C,OAAOD,0BAA0B,CAACC,OAAO,CAAC,CAAC;QAC7C,CAAC;IACH,EAAE,OAAOoB,KAAK,EAAE;QACd7B,KAAK,CAAC,CAAC,sEAAsE,CAAC,EAAE6B,KAAK,CAAC,CAAC;IACzF,CAAC;IACD,oCAAoC;IACpC,OAAO,EAAE,CAAC;AACZ,CAAC;AAGM,eAAe/B,yBAAyB,CAACgB,WAAmB,EAAqB;IACtF,IAAI;QACF,MAAMG,UAAU,GAAG,MAAMa,cAAa,EAAA,cAAA,CAACC,KAAK,CAACC,uBAAuB,CAAClB,WAAW,CAAC,AAAC;QAClF,MAAMmB,QAAQ,GAAG,MAAMH,cAAa,EAAA,cAAA,CAACI,QAAQ,CAACC,wBAAwB,CAAClB,UAAU,CAAC,AAAC;QACnF,MAAMR,OAAO,GAAG,MAAMqB,cAAa,EAAA,cAAA,CAACH,MAAM,CAACS,sBAAsB,CAACH,QAAQ,CAAC,AAAC;QAC5EjC,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAES,OAAO,CAAC,CAAC;QAC/C,OAAOD,0BAA0B,CAACC,OAAO,CAAC,CAAC;IAC7C,EAAE,OAAOoB,KAAK,EAAE;QACd7B,KAAK,CAAC,CAAC,4EAA4E,CAAC,EAAE6B,KAAK,CAAC,CAAC;QAC7F,wCAAwC;QACxC,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED,mDAAmD;AACnD,eAAeQ,8BAA8B,CAACvB,WAAmB,EAA0B;IACzF,MAAM,EAAEwB,GAAG,CAAA,EAAE,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAACzB,WAAW,CAAC,AAAC;IACvC,IAAI;QACF,MAAM0B,gBAAgB,GAAGvC,OAAO,CAACwC,IAAAA,YAAW,EAAA,QAAA,EAAC3B,WAAW,EAAE,kCAAkC,CAAC,CAAC,AAAC;QAC/F,MAAMF,MAAM,GAAG4B,gBAAgB,CAACF,GAAG,CAAC,AAAC;QACrC,OAAO1B,MAAM,CAAC;IAChB,EAAE,OAAM;QACN8B,IAAG,CAACC,IAAI,CACN,uIAAuI,CACxI,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAGM,eAAe5C,+BAA+B,CACnDe,WAAmB,EACsE;IACzF,MAAM,CAAC8B,MAAM,EAAEC,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC;QAC7CC,IAAAA,kBAAwB,yBAAA,EAAClC,WAAW,CAAC;QACrCmC,IAAAA,kBAA4B,6BAAA,EAACnC,WAAW,CAAC;KAC1C,CAAC,AAAC;IAEH,MAAM,CAACoC,GAAG,EAAEC,OAAO,CAAC,GAAG,MAAML,OAAO,CAACC,GAAG,CAAC;QACvClD,qBAAqB,CAACiB,WAAW,CAAC;QAClChB,yBAAyB,CAACgB,WAAW,CAAC;KACvC,CAAC,AAAC;IAEH,yBAAyB;IACzB,IAAI,CAAC8B,MAAM,IAAI,CAACC,UAAU,EAAE;QAC1B,OAAO;YAAEjC,MAAM,EAAE,MAAMyB,8BAA8B,CAACvB,WAAW,CAAC;YAAEsC,UAAU,EAAE,QAAQ;SAAE,CAAC;IAC7F,CAAC;IAED,8CAA8C;IAC9C,IAAI,CAACR,MAAM,EAAE;QACX,OAAO;YAAEhC,MAAM,EAAEuC,OAAO,CAAC,CAAC,CAAC;YAAEC,UAAU,EAAE,SAAS;SAAE,CAAC;IACvD,OAAO,IAAI,CAACP,UAAU,EAAE;QACtB,OAAO;YAAEjC,MAAM,EAAEsC,GAAG,CAAC,CAAC,CAAC;YAAEE,UAAU,EAAE,KAAK;SAAE,CAAC;IAC/C,OAAO;QACL,OAAO;YAAExC,MAAM,EAAEyC,IAAAA,MAAY,aAAA,EAACH,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC;YAAEC,UAAU,EAAE,QAAQ;SAAE,CAAC;IACzE,CAAC;AACH,CAAC"}