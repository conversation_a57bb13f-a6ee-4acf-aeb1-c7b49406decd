{"version": 3, "sources": ["../../../src/utils/progress.ts"], "sourcesContent": ["import ProgressBar from 'progress';\n\nlet currentProgress: ProgressBar | null = null;\n\nexport function setProgressBar(bar: ProgressBar | null): void {\n  currentProgress = bar;\n}\n\nexport function getProgressBar(): ProgressBar | null {\n  return currentProgress;\n}\n\nexport function createProgressBar(barFormat: string, options: ProgressBar.ProgressBarOptions) {\n  if (process.stderr.clearLine == null) {\n    return null;\n  }\n\n  const bar = new ProgressBar(barFormat, options);\n\n  const logReal = console.log;\n  const infoReal = console.info;\n  const warnReal = console.warn;\n  const errorReal = console.error;\n\n  const wrapNativeLogs = (): void => {\n    // @ts-expect-error\n    console.log = (...args: any) => bar.interrupt(...args);\n    // @ts-expect-error\n    console.info = (...args: any) => bar.interrupt(...args);\n    // @ts-expect-error\n    console.warn = (...args: any) => bar.interrupt(...args);\n    // @ts-expect-error\n    console.error = (...args: any) => bar.interrupt(...args);\n  };\n\n  const resetNativeLogs = (): void => {\n    console.log = logReal;\n    console.info = infoReal;\n    console.warn = warnReal;\n    console.error = errorReal;\n  };\n\n  const originalTerminate = bar.terminate.bind(bar);\n  bar.terminate = () => {\n    resetNativeLogs();\n    setProgressBar(null);\n    originalTerminate();\n  };\n\n  wrapNativeLogs();\n  setProgressBar(bar);\n  return bar;\n}\n"], "names": ["setProgressBar", "getProgressBar", "createProgressBar", "currentProgress", "bar", "barFormat", "options", "process", "stderr", "clearLine", "ProgressBar", "logReal", "console", "log", "infoReal", "info", "warnReal", "warn", "errorReal", "error", "wrapNativeLogs", "args", "interrupt", "resetNativeLogs", "originalTerminate", "terminate", "bind"], "mappings": "AAAA;;;;;;;;;;;IAIgBA,cAAc,MAAdA,cAAc;IAIdC,cAAc,MAAdA,cAAc;IAIdC,iBAAiB,MAAjBA,iBAAiB;;;8DAZT,UAAU;;;;;;;;;;;AAElC,IAAIC,eAAe,GAAuB,IAAI,AAAC;AAExC,SAASH,cAAc,CAACI,GAAuB,EAAQ;IAC5DD,eAAe,GAAGC,GAAG,CAAC;AACxB,CAAC;AAEM,SAASH,cAAc,GAAuB;IACnD,OAAOE,eAAe,CAAC;AACzB,CAAC;AAEM,SAASD,iBAAiB,CAACG,SAAiB,EAAEC,OAAuC,EAAE;IAC5F,IAAIC,OAAO,CAACC,MAAM,CAACC,SAAS,IAAI,IAAI,EAAE;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAML,GAAG,GAAG,IAAIM,CAAAA,SAAW,EAAA,CAAA,QAAA,CAACL,SAAS,EAAEC,OAAO,CAAC,AAAC;IAEhD,MAAMK,OAAO,GAAGC,OAAO,CAACC,GAAG,AAAC;IAC5B,MAAMC,QAAQ,GAAGF,OAAO,CAACG,IAAI,AAAC;IAC9B,MAAMC,QAAQ,GAAGJ,OAAO,CAACK,IAAI,AAAC;IAC9B,MAAMC,SAAS,GAAGN,OAAO,CAACO,KAAK,AAAC;IAEhC,MAAMC,cAAc,GAAG,IAAY;QACjC,mBAAmB;QACnBR,OAAO,CAACC,GAAG,GAAG,CAAC,GAAGQ,IAAI,AAAK,GAAKjB,GAAG,CAACkB,SAAS,IAAID,IAAI,CAAC,CAAC;QACvD,mBAAmB;QACnBT,OAAO,CAACG,IAAI,GAAG,CAAC,GAAGM,IAAI,AAAK,GAAKjB,GAAG,CAACkB,SAAS,IAAID,IAAI,CAAC,CAAC;QACxD,mBAAmB;QACnBT,OAAO,CAACK,IAAI,GAAG,CAAC,GAAGI,IAAI,AAAK,GAAKjB,GAAG,CAACkB,SAAS,IAAID,IAAI,CAAC,CAAC;QACxD,mBAAmB;QACnBT,OAAO,CAACO,KAAK,GAAG,CAAC,GAAGE,IAAI,AAAK,GAAKjB,GAAG,CAACkB,SAAS,IAAID,IAAI,CAAC,CAAC;IAC3D,CAAC,AAAC;IAEF,MAAME,eAAe,GAAG,IAAY;QAClCX,OAAO,CAACC,GAAG,GAAGF,OAAO,CAAC;QACtBC,OAAO,CAACG,IAAI,GAAGD,QAAQ,CAAC;QACxBF,OAAO,CAACK,IAAI,GAAGD,QAAQ,CAAC;QACxBJ,OAAO,CAACO,KAAK,GAAGD,SAAS,CAAC;IAC5B,CAAC,AAAC;IAEF,MAAMM,iBAAiB,GAAGpB,GAAG,CAACqB,SAAS,CAACC,IAAI,CAACtB,GAAG,CAAC,AAAC;IAClDA,GAAG,CAACqB,SAAS,GAAG,IAAM;QACpBF,eAAe,EAAE,CAAC;QAClBvB,cAAc,CAAC,IAAI,CAAC,CAAC;QACrBwB,iBAAiB,EAAE,CAAC;IACtB,CAAC,CAAC;IAEFJ,cAAc,EAAE,CAAC;IACjBpB,cAAc,CAACI,GAAG,CAAC,CAAC;IACpB,OAAOA,GAAG,CAAC;AACb,CAAC"}