{"version": 3, "sources": ["../../../src/utils/profile.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport { env } from './env';\nimport * as Log from '../log';\n\n/**\n * Wrap a method and profile the time it takes to execute the method using `EXPO_PROFILE`.\n * Works best with named functions (i.e. not arrow functions).\n *\n * @param fn function to profile.\n * @param functionName optional name of the function to display in the profile output.\n */\nexport function profile<IArgs extends any[], T extends (...args: IArgs) => any>(\n  fn: T,\n  functionName: string = fn.name\n): T {\n  if (!env.EXPO_PROFILE) {\n    return fn;\n  }\n\n  const name = chalk.dim(`⏱  [profile] ${functionName ?? 'unknown'}`);\n\n  return ((...args: IArgs) => {\n    // Start the timer.\n    Log.time(name);\n\n    // Invoke the method.\n    const results = fn(...args);\n\n    // If non-promise then return as-is.\n    if (!(results instanceof Promise)) {\n      Log.timeEnd(name);\n      return results;\n    }\n\n    // Otherwise await to profile after the promise resolves.\n    return new Promise<Awaited<ReturnType<T>>>((resolve, reject) => {\n      results.then(\n        (results) => {\n          resolve(results);\n          Log.timeEnd(name);\n        },\n        (reason) => {\n          reject(reason);\n          Log.timeEnd(name);\n        }\n      );\n    });\n  }) as T;\n}\n"], "names": ["profile", "fn", "functionName", "name", "env", "EXPO_PROFILE", "chalk", "dim", "args", "Log", "time", "results", "Promise", "timeEnd", "resolve", "reject", "then", "reason"], "mappings": "AAAA;;;;+BAYg<PERSON>,SAAO;;aAAPA,OAAO;;;8DAZL,OAAO;;;;;;qBAEL,OAAO;2DACN,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB,SAASA,OAAO,CACrBC,EAAK,EACLC,YAAoB,GAAGD,EAAE,CAACE,IAAI,EAC3B;IACH,IAAI,CAACC,IAAG,IAAA,CAACC,YAAY,EAAE;QACrB,OAAOJ,EAAE,CAAC;IACZ,CAAC;IAED,MAAME,IAAI,GAAGG,MAAK,EAAA,QAAA,CAACC,GAAG,CAAC,CAAC,aAAa,EAAEL,YAAY,WAAZA,YAAY,GAAI,SAAS,CAAC,CAAC,CAAC,AAAC;IAEpE,OAAQ,CAAC,GAAGM,IAAI,AAAO,GAAK;QAC1B,mBAAmB;QACnBC,IAAG,CAACC,IAAI,CAACP,IAAI,CAAC,CAAC;QAEf,qBAAqB;QACrB,MAAMQ,OAAO,GAAGV,EAAE,IAAIO,IAAI,CAAC,AAAC;QAE5B,oCAAoC;QACpC,IAAI,CAAC,CAACG,OAAO,YAAYC,OAAO,CAAC,EAAE;YACjCH,IAAG,CAACI,OAAO,CAACV,IAAI,CAAC,CAAC;YAClB,OAAOQ,OAAO,CAAC;QACjB,CAAC;QAED,yDAAyD;QACzD,OAAO,IAAIC,OAAO,CAAyB,CAACE,OAAO,EAAEC,MAAM,GAAK;YAC9DJ,OAAO,CAACK,IAAI,CACV,CAACL,OAAO,GAAK;gBACXG,OAAO,CAACH,OAAO,CAAC,CAAC;gBACjBF,IAAG,CAACI,OAAO,CAACV,IAAI,CAAC,CAAC;YACpB,CAAC,EACD,CAACc,MAAM,GAAK;gBACVF,MAAM,CAACE,MAAM,CAAC,CAAC;gBACfR,IAAG,CAACI,OAAO,CAACV,IAAI,CAAC,CAAC;YACpB,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAO;AACV,CAAC"}