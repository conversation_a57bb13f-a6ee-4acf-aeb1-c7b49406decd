{"version": 3, "sources": ["../../../src/utils/tar.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport tar from 'tar';\n\nimport * as Log from '../log';\n\nconst debug = require('debug')('expo:utils:tar') as typeof console.log;\n\n/** Extract a tar using built-in tools if available and falling back on Node.js. */\nexport async function extractAsync(input: string, output: string): Promise<void> {\n  try {\n    if (process.platform !== 'win32') {\n      debug(`Extracting ${input} to ${output}`);\n      await spawnAsync('tar', ['-xf', input, '-C', output], {\n        stdio: 'inherit',\n      });\n      return;\n    }\n  } catch (error: any) {\n    Log.warn(\n      `Failed to extract tar using native tools, falling back on JS tar module. ${error.message}`\n    );\n  }\n  debug(`Extracting ${input} to ${output} using JS tar module`);\n  // tar node module has previously had problems with big files, and seems to\n  // be slower, so only use it as a backup.\n  await tar.extract({ file: input, cwd: output });\n}\n"], "names": ["extractAsync", "debug", "require", "input", "output", "process", "platform", "spawnAsync", "stdio", "error", "Log", "warn", "message", "tar", "extract", "file", "cwd"], "mappings": "AAAA;;;;+BAQsBA,cAAY;;aAAZA,YAAY;;;8DARX,mBAAmB;;;;;;;8DAC1B,KAAK;;;;;;2DAEA,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,AAAsB,AAAC;AAGhE,eAAeF,YAAY,CAACG,KAAa,EAAEC,MAAc,EAAiB;IAC/E,IAAI;QACF,IAAIC,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;YAChCL,KAAK,CAAC,CAAC,WAAW,EAAEE,KAAK,CAAC,IAAI,EAAEC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAMG,IAAAA,WAAU,EAAA,QAAA,EAAC,KAAK,EAAE;gBAAC,KAAK;gBAAEJ,KAAK;gBAAE,IAAI;gBAAEC,MAAM;aAAC,EAAE;gBACpDI,KAAK,EAAE,SAAS;aACjB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;IACH,EAAE,OAAOC,KAAK,EAAO;QACnBC,IAAG,CAACC,IAAI,CACN,CAAC,yEAAyE,EAAEF,KAAK,CAACG,OAAO,CAAC,CAAC,CAC5F,CAAC;IACJ,CAAC;IACDX,KAAK,CAAC,CAAC,WAAW,EAAEE,KAAK,CAAC,IAAI,EAAEC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAC9D,2EAA2E;IAC3E,yCAAyC;IACzC,MAAMS,IAAG,EAAA,QAAA,CAACC,OAAO,CAAC;QAAEC,IAAI,EAAEZ,KAAK;QAAEa,GAAG,EAAEZ,MAAM;KAAE,CAAC,CAAC;AAClD,CAAC"}