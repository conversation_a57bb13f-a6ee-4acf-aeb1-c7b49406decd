{"version": 3, "sources": ["../../../src/utils/plist.ts"], "sourcesContent": ["import plist from '@expo/plist';\nimport binaryPlist from 'bplist-parser';\nimport fs from 'fs/promises';\n\nimport { CommandError } from './errors';\nimport * as Log from '../log';\n\nconst CHAR_CHEVRON_OPEN = 60;\nconst CHAR_B_LOWER = 98;\n// .mobileprovision\n// const CHAR_ZERO = 30;\n\nexport async function parsePlistAsync(plistPath: string) {\n  Log.debug(`Parse plist: ${plistPath}`);\n\n  return parsePlistBuffer(await fs.readFile(plistPath));\n}\n\nexport function parsePlistBuffer(contents: Buffer) {\n  if (contents[0] === CHAR_CHEVRON_OPEN) {\n    const info = plist.parse(contents.toString());\n    if (Array.isArray(info)) return info[0];\n    return info;\n  } else if (contents[0] === CHAR_B_LOWER) {\n    // @ts-expect-error\n    const info = binaryPlist.parseBuffer(contents);\n    if (Array.isArray(info)) return info[0];\n    return info;\n  } else {\n    throw new CommandError(\n      'PLIST',\n      `Cannot parse plist of type byte (0x${contents[0].toString(16)})`\n    );\n  }\n}\n"], "names": ["parsePlistAsync", "parsePlist<PERSON><PERSON><PERSON>", "CHAR_CHEVRON_OPEN", "CHAR_B_LOWER", "plist<PERSON><PERSON>", "Log", "debug", "fs", "readFile", "contents", "info", "plist", "parse", "toString", "Array", "isArray", "binaryPlist", "parse<PERSON><PERSON>er", "CommandError"], "mappings": "AAAA;;;;;;;;;;;IAYsBA,eAAe,MAAfA,eAAe;IAMrBC,gBAAgB,MAAhBA,gBAAgB;;;8DAlBd,aAAa;;;;;;;8DACP,eAAe;;;;;;;8DACxB,aAAa;;;;;;wBAEC,UAAU;2DAClB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7B,MAAMC,iBAAiB,GAAG,EAAE,AAAC;AAC7B,MAAMC,YAAY,GAAG,EAAE,AAAC;AAIjB,eAAeH,eAAe,CAACI,SAAiB,EAAE;IACvDC,IAAG,CAACC,KAAK,CAAC,CAAC,aAAa,EAAEF,SAAS,CAAC,CAAC,CAAC,CAAC;IAEvC,OAAOH,gBAAgB,CAAC,MAAMM,SAAE,EAAA,QAAA,CAACC,QAAQ,CAACJ,SAAS,CAAC,CAAC,CAAC;AACxD,CAAC;AAEM,SAASH,gBAAgB,CAACQ,QAAgB,EAAE;IACjD,IAAIA,QAAQ,CAAC,CAAC,CAAC,KAAKP,iBAAiB,EAAE;QACrC,MAAMQ,IAAI,GAAGC,MAAK,EAAA,QAAA,CAACC,KAAK,CAACH,QAAQ,CAACI,QAAQ,EAAE,CAAC,AAAC;QAC9C,IAAIC,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE,OAAOA,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,OAAOA,IAAI,CAAC;IACd,OAAO,IAAID,QAAQ,CAAC,CAAC,CAAC,KAAKN,YAAY,EAAE;QACvC,mBAAmB;QACnB,MAAMO,KAAI,GAAGM,aAAW,EAAA,QAAA,CAACC,WAAW,CAACR,QAAQ,CAAC,AAAC;QAC/C,IAAIK,KAAK,CAACC,OAAO,CAACL,KAAI,CAAC,EAAE,OAAOA,KAAI,CAAC,CAAC,CAAC,CAAC;QACxC,OAAOA,KAAI,CAAC;IACd,OAAO;QACL,MAAM,IAAIQ,OAAY,aAAA,CACpB,OAAO,EACP,CAAC,mCAAmC,EAAET,QAAQ,CAAC,CAAC,CAAC,CAACI,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAClE,CAAC;IACJ,CAAC;AACH,CAAC"}