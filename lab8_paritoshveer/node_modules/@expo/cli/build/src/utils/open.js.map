{"version": 3, "sources": ["../../../src/utils/open.ts"], "sourcesContent": ["import betterOpenBrowserAsync from 'better-opn';\n\n/**\n * Due to a bug in `open`, which is used as fallback on Windows, we need to ensure `process.env.SYSTEMROOT` is set.\n * This environment variable is set by Windows on `SystemRoot`, causing `open` to execute a command with an \"unknown\" drive letter.\n *\n * @see https://github.com/sindresorhus/open/issues/205\n */\nexport async function openBrowserAsync(\n  target: string,\n  options?: any\n): Promise<import('child_process').ChildProcess | false> {\n  if (process.platform !== 'win32') {\n    return await betterOpenBrowserAsync(target, options);\n  }\n\n  const oldSystemRoot = process.env.SYSTEMROOT;\n  try {\n    process.env.SYSTEMROOT = process.env.SYSTEMROOT ?? process.env.SystemRoot;\n    return await betterOpenBrowserAsync(target, options);\n  } finally {\n    process.env.SYSTEMROOT = oldSystemRoot;\n  }\n}\n"], "names": ["openBrowserAsync", "target", "options", "process", "platform", "betterOpenBrowserAsync", "oldSystemRoot", "env", "SYSTEMROOT", "SystemRoot"], "mappings": "AAAA;;;;+BAQsBA,kBAAgB;;aAAhBA,gBAAgB;;;8DARH,YAAY;;;;;;;;;;;AAQxC,eAAeA,gBAAgB,CACpCC,MAAc,EACdC,OAAa,EAC0C;IACvD,IAAIC,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;QAChC,OAAO,MAAMC,IAAAA,UAAsB,EAAA,QAAA,EAACJ,MAAM,EAAEC,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,MAAMI,aAAa,GAAGH,OAAO,CAACI,GAAG,CAACC,UAAU,AAAC;IAC7C,IAAI;YACuBL,WAAsB;QAA/CA,OAAO,CAACI,GAAG,CAACC,UAAU,GAAGL,CAAAA,WAAsB,GAAtBA,OAAO,CAACI,GAAG,CAACC,UAAU,YAAtBL,WAAsB,GAAIA,OAAO,CAACI,GAAG,CAACE,UAAU,CAAC;QAC1E,OAAO,MAAMJ,IAAAA,UAAsB,EAAA,QAAA,EAACJ,MAAM,EAAEC,OAAO,CAAC,CAAC;IACvD,SAAU;QACRC,OAAO,CAACI,GAAG,CAACC,UAAU,GAAGF,aAAa,CAAC;IACzC,CAAC;AACH,CAAC"}