{"version": 3, "sources": ["../../../src/utils/modifyConfigAsync.ts"], "sourcesContent": ["import { ExpoConfig, modifyConfigAsync } from '@expo/config';\nimport chalk from 'chalk';\n\nimport { SilentError } from './errors';\nimport * as Log from '../log';\n\n/** Wraps `[@expo/config] modifyConfigAsync()` and adds additional logging. */\nexport async function attemptModification(\n  projectRoot: string,\n  edits: Partial<ExpoConfig>,\n  exactEdits: Partial<ExpoConfig>\n): Promise<void> {\n  const modification = await modifyConfigAsync(projectRoot, edits, {\n    skipSDKVersionRequirement: true,\n  });\n  if (modification.type === 'success') {\n    Log.log();\n  } else {\n    warnAboutConfigAndThrow(modification.type, modification.message!, exactEdits);\n  }\n}\n\nfunction logNoConfig() {\n  Log.log(\n    chalk.yellow(\n      `No Expo config was found. Please create an Expo config (${chalk.bold`app.json`} or ${chalk.bold`app.config.js`}) in your project root.`\n    )\n  );\n}\n\nexport function warnAboutConfigAndThrow(type: string, message: string, edits: Partial<ExpoConfig>) {\n  Log.log();\n  if (type === 'warn') {\n    // The project is using a dynamic config, give the user a helpful log and bail out.\n    Log.log(chalk.yellow(message));\n  } else {\n    logNoConfig();\n  }\n\n  notifyAboutManualConfigEdits(edits);\n  throw new SilentError();\n}\n\nfunction notifyAboutManualConfigEdits(edits: Partial<ExpoConfig>) {\n  Log.log(chalk.cyan(`Please add the following to your Expo config`));\n  Log.log();\n  Log.log(JSON.stringify(edits, null, 2));\n  Log.log();\n}\n"], "names": ["attemptModification", "warnAboutConfigAndThrow", "projectRoot", "edits", "exactEdits", "modification", "modifyConfigAsync", "skipSDKVersionRequirement", "type", "Log", "log", "message", "logNoConfig", "chalk", "yellow", "bold", "notifyAboutManualConfigEdits", "SilentError", "cyan", "JSON", "stringify"], "mappings": "AAAA;;;;;;;;;;;IAOsBA,mBAAmB,MAAnBA,mBAAmB;IAuBzBC,uBAAuB,MAAvBA,uBAAuB;;;yBA9BO,cAAc;;;;;;;8DAC1C,OAAO;;;;;;wBAEG,UAAU;2DACjB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGtB,eAAeD,mBAAmB,CACvCE,WAAmB,EACnBC,KAA0B,EAC1BC,UAA+B,EAChB;IACf,MAAMC,YAAY,GAAG,MAAMC,IAAAA,OAAiB,EAAA,kBAAA,EAACJ,WAAW,EAAEC,KAAK,EAAE;QAC/DI,yBAAyB,EAAE,IAAI;KAChC,CAAC,AAAC;IACH,IAAIF,YAAY,CAACG,IAAI,KAAK,SAAS,EAAE;QACnCC,IAAG,CAACC,GAAG,EAAE,CAAC;IACZ,OAAO;QACLT,uBAAuB,CAACI,YAAY,CAACG,IAAI,EAAEH,YAAY,CAACM,OAAO,EAAGP,UAAU,CAAC,CAAC;IAChF,CAAC;AACH,CAAC;AAED,SAASQ,WAAW,GAAG;IACrBH,IAAG,CAACC,GAAG,CACLG,MAAK,EAAA,QAAA,CAACC,MAAM,CACV,CAAC,wDAAwD,EAAED,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAEF,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,aAAa,CAAC,CAAC,uBAAuB,CAAC,CACzI,CACF,CAAC;AACJ,CAAC;AAEM,SAASd,uBAAuB,CAACO,IAAY,EAAEG,OAAe,EAAER,KAA0B,EAAE;IACjGM,IAAG,CAACC,GAAG,EAAE,CAAC;IACV,IAAIF,IAAI,KAAK,MAAM,EAAE;QACnB,mFAAmF;QACnFC,IAAG,CAACC,GAAG,CAACG,MAAK,EAAA,QAAA,CAACC,MAAM,CAACH,OAAO,CAAC,CAAC,CAAC;IACjC,OAAO;QACLC,WAAW,EAAE,CAAC;IAChB,CAAC;IAEDI,4BAA4B,CAACb,KAAK,CAAC,CAAC;IACpC,MAAM,IAAIc,OAAW,YAAA,EAAE,CAAC;AAC1B,CAAC;AAED,SAASD,4BAA4B,CAACb,KAA0B,EAAE;IAChEM,IAAG,CAACC,GAAG,CAACG,MAAK,EAAA,QAAA,CAACK,IAAI,CAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC,CAAC;IACpET,IAAG,CAACC,GAAG,EAAE,CAAC;IACVD,IAAG,CAACC,GAAG,CAACS,IAAI,CAACC,SAAS,CAACjB,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACxCM,IAAG,CAACC,GAAG,EAAE,CAAC;AACZ,CAAC"}