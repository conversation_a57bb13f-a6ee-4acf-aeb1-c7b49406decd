{"version": 3, "sources": ["../../../../../src/start/platforms/ios/xcrun.ts"], "sourcesContent": ["import spawnAsync, { SpawnOptions } from '@expo/spawn-async';\nimport chalk from 'chalk';\n\nimport { CommandError } from '../../../utils/errors';\n\nconst debug = require('debug')('expo:start:platforms:ios:xcrun') as typeof console.log;\n\nexport async function xcrunAsync(args: (string | undefined)[], options?: SpawnOptions) {\n  debug('Running: xcrun ' + args.join(' '));\n  try {\n    return await spawnAsync('xcrun', args.filter(Boolean) as string[], options);\n  } catch (e) {\n    throwXcrunError(e);\n  }\n}\n\nfunction throwXcrunError(e: any): never {\n  if (isLicenseOutOfDate(e.stdout) || isLicenseOutOfDate(e.stderr)) {\n    throw new CommandError(\n      'XCODE_LICENSE_NOT_ACCEPTED',\n      'Xcode license is not accepted. Please run `sudo xcodebuild -license`.'\n    );\n  } else if (e.stderr?.includes('not a developer tool or in PATH')) {\n    throw new CommandError(\n      'SIMCTL_NOT_AVAILABLE',\n      `You may need to run ${chalk.bold(\n        'sudo xcode-select -s /Applications/Xcode.app'\n      )} and try again.`\n    );\n  }\n  // Attempt to craft a better error message...\n  if (Array.isArray(e.output)) {\n    e.message += '\\n' + e.output.join('\\n').trim();\n  } else if (e.stderr) {\n    e.message += '\\n' + e.stderr;\n  }\n  throw e;\n}\n\nfunction isLicenseOutOfDate(text: string) {\n  if (!text) {\n    return false;\n  }\n\n  const lower = text.toLowerCase();\n  return lower.includes('xcode') && lower.includes('license');\n}\n"], "names": ["xcrunAsync", "debug", "require", "args", "options", "join", "spawnAsync", "filter", "Boolean", "e", "throwXcrunError", "isLicenseOutOfDate", "stdout", "stderr", "CommandError", "includes", "chalk", "bold", "Array", "isArray", "output", "message", "trim", "text", "lower", "toLowerCase"], "mappings": "AAAA;;;;+BAOsBA,YAAU;;aAAVA,UAAU;;;8DAPS,mBAAmB;;;;;;;8DAC1C,OAAO;;;;;;wBAEI,uBAAuB;;;;;;AAEpD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gCAAgC,CAAC,AAAsB,AAAC;AAEhF,eAAeF,UAAU,CAACG,IAA4B,EAAEC,OAAsB,EAAE;IACrFH,KAAK,CAAC,iBAAiB,GAAGE,IAAI,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1C,IAAI;QACF,OAAO,MAAMC,IAAAA,WAAU,EAAA,QAAA,EAAC,OAAO,EAAEH,IAAI,CAACI,MAAM,CAACC,OAAO,CAAC,EAAcJ,OAAO,CAAC,CAAC;IAC9E,EAAE,OAAOK,CAAC,EAAE;QACVC,eAAe,CAACD,CAAC,CAAC,CAAC;IACrB,CAAC;AACH,CAAC;AAED,SAASC,eAAe,CAACD,CAAM,EAAS;QAM3BA,GAAQ;IALnB,IAAIE,kBAAkB,CAACF,CAAC,CAACG,MAAM,CAAC,IAAID,kBAAkB,CAACF,CAAC,CAACI,MAAM,CAAC,EAAE;QAChE,MAAM,IAAIC,OAAY,aAAA,CACpB,4BAA4B,EAC5B,uEAAuE,CACxE,CAAC;IACJ,OAAO,IAAIL,CAAAA,GAAQ,GAARA,CAAC,CAACI,MAAM,SAAU,GAAlBJ,KAAAA,CAAkB,GAAlBA,GAAQ,CAAEM,QAAQ,CAAC,iCAAiC,CAAC,EAAE;QAChE,MAAM,IAAID,OAAY,aAAA,CACpB,sBAAsB,EACtB,CAAC,oBAAoB,EAAEE,MAAK,EAAA,QAAA,CAACC,IAAI,CAC/B,8CAA8C,CAC/C,CAAC,eAAe,CAAC,CACnB,CAAC;IACJ,CAAC;IACD,6CAA6C;IAC7C,IAAIC,KAAK,CAACC,OAAO,CAACV,CAAC,CAACW,MAAM,CAAC,EAAE;QAC3BX,CAAC,CAACY,OAAO,IAAI,IAAI,GAAGZ,CAAC,CAACW,MAAM,CAACf,IAAI,CAAC,IAAI,CAAC,CAACiB,IAAI,EAAE,CAAC;IACjD,OAAO,IAAIb,CAAC,CAACI,MAAM,EAAE;QACnBJ,CAAC,CAACY,OAAO,IAAI,IAAI,GAAGZ,CAAC,CAACI,MAAM,CAAC;IAC/B,CAAC;IACD,MAAMJ,CAAC,CAAC;AACV,CAAC;AAED,SAASE,kBAAkB,CAACY,IAAY,EAAE;IACxC,IAAI,CAACA,IAAI,EAAE;QACT,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAMC,KAAK,GAAGD,IAAI,CAACE,WAAW,EAAE,AAAC;IACjC,OAAOD,KAAK,CAACT,QAAQ,CAAC,OAAO,CAAC,IAAIS,KAAK,CAACT,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC9D,CAAC"}