{"version": 3, "sources": ["../../../../../src/start/platforms/ios/simctl.ts"], "sourcesContent": ["import spawnAsync, { SpawnOptions, SpawnResult } from '@expo/spawn-async';\nimport bplistCreator from 'bplist-creator';\nimport fs from 'fs';\nimport os from 'os';\nimport path from 'path';\n\nimport { xcrunAsync } from './xcrun';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { memoize } from '../../../utils/fn';\nimport { parsePlistAsync } from '../../../utils/plist';\nimport { profile } from '../../../utils/profile';\n\nconst debug = require('debug')('expo:simctl') as typeof console.log;\n\ntype DeviceState = 'Shutdown' | 'Booted';\n\nexport type OSType = 'iOS' | 'tvOS' | 'watchOS' | 'macOS' | 'xrOS';\n\nexport type Device = {\n  availabilityError?: 'runtime profile not found';\n  /** '/Users/<USER>/Library/Developer/CoreSimulator/Devices/00E55DC0-0364-49DF-9EC6-77BE587137D4/data' */\n  dataPath: string;\n  /** @example `2811236352` */\n  dataPathSize?: number;\n  /** '/Users/<USER>/Library/Logs/CoreSimulator/00E55DC0-0364-49DF-9EC6-77BE587137D4' */\n  logPath: string;\n  /** @example `479232` */\n  logPathSize?: number;\n  /** '00E55DC0-0364-49DF-9EC6-77BE587137D4' */\n  udid: string;\n  /** 'com.apple.CoreSimulator.SimRuntime.iOS-15-1' */\n  runtime: string;\n  /** If the device is \"available\" which generally means that the OS files haven't been deleted (this can happen when Xcode updates).  */\n  isAvailable: boolean;\n  /** 'com.apple.CoreSimulator.SimDeviceType.iPhone-13-Pro' */\n  deviceTypeIdentifier: string;\n  state: DeviceState;\n  /** 'iPhone 13 Pro' */\n  name: string;\n  /** Type of OS the device uses. */\n  osType: OSType;\n  /** '15.1' */\n  osVersion: string;\n  /** 'iPhone 13 Pro (15.1)' */\n  windowName: string;\n};\n\ntype SimulatorDeviceList = {\n  devices: {\n    [runtime: string]: Device[];\n  };\n};\n\ntype DeviceContext = Pick<Device, 'udid'>;\n\n/** Returns true if the given value is an `OSType`, if we don't recognize the value we continue anyways but warn. */\nexport function isOSType(value: any): value is OSType {\n  if (!value || typeof value !== 'string') return false;\n\n  const knownTypes = ['iOS', 'tvOS', 'watchOS', 'macOS'];\n  if (!knownTypes.includes(value)) {\n    Log.warn(`Unknown OS type: ${value}. Expected one of: ${knownTypes.join(', ')}`);\n  }\n  return true;\n}\n\n/**\n * Returns the local path for the installed tar.app. Returns null when the app isn't installed.\n *\n * @param device context for selecting a device.\n * @param props.appId bundle identifier for app.\n * @returns local file path to installed app binary, e.g. '/Users/<USER>/Library/Developer/CoreSimulator/Devices/EFEEA6EF-E3F5-4EDE-9B72-29EAFA7514AE/data/Containers/Bundle/Application/FA43A0C6-C2AD-442D-B8B1-EAF3E88CF3BF/Exponent-2.21.3.tar.app'\n */\nexport async function getContainerPathAsync(\n  device: Partial<DeviceContext>,\n  {\n    appId,\n  }: {\n    appId: string;\n  }\n): Promise<string | null> {\n  try {\n    const { stdout } = await simctlAsync(['get_app_container', resolveId(device), appId]);\n    return stdout.trim();\n  } catch (error: any) {\n    if (error.stderr?.match(/No such file or directory/)) {\n      return null;\n    }\n    throw error;\n  }\n}\n\n/** Return a value from an installed app's Info.plist. */\nexport async function getInfoPlistValueAsync(\n  device: Partial<DeviceContext>,\n  {\n    appId,\n    key,\n    containerPath,\n  }: {\n    appId: string;\n    key: string;\n    containerPath?: string;\n  }\n): Promise<string | null> {\n  const ensuredContainerPath = containerPath ?? (await getContainerPathAsync(device, { appId }));\n  if (ensuredContainerPath) {\n    try {\n      const { output } = await spawnAsync(\n        'defaults',\n        ['read', `${ensuredContainerPath}/Info`, key],\n        {\n          stdio: 'pipe',\n        }\n      );\n      return output.join('\\n').trim();\n    } catch {\n      return null;\n    }\n  }\n  return null;\n}\n\n/** Rewrite the simulator permissions to allow opening deep links without needing to prompt the user first. */\nasync function updateSimulatorLinkingPermissionsAsync(\n  device: Partial<DeviceContext>,\n  { url, appId }: { url: string; appId?: string }\n) {\n  if (!device.udid || !appId) {\n    debug('Skipping deep link permissions as missing properties could not be found:', {\n      url,\n      appId,\n      udid: device.udid,\n    });\n    return;\n  }\n  debug('Rewriting simulator permissions to support deep linking:', {\n    url,\n    appId,\n    udid: device.udid,\n  });\n  let scheme: string;\n  try {\n    // Attempt to extract the scheme from the URL.\n    scheme = new URL(url).protocol.slice(0, -1);\n  } catch (error: any) {\n    debug(`Could not parse the URL scheme: ${error.message}`);\n    return;\n  }\n\n  // Get the hard-coded path to the simulator's scheme approval plist file.\n  const plistPath = path.join(\n    os.homedir(),\n    `Library/Developer/CoreSimulator/Devices`,\n    device.udid,\n    `data/Library/Preferences/com.apple.launchservices.schemeapproval.plist`\n  );\n\n  const plistData = fs.existsSync(plistPath)\n    ? // If the file exists, then read it in the bplist format.\n      await parsePlistAsync(plistPath)\n    : // The file doesn't exist when we first launch the simulator, but an empty object can be used to create it (June 2024 x Xcode 15.3).\n      // Can be tested by launching a new simulator or by deleting the file and relaunching the simulator.\n      {};\n\n  debug('Allowed links:', plistData);\n  const key = `com.apple.CoreSimulator.CoreSimulatorBridge-->${scheme}`;\n  // Replace any existing value for the scheme with the new appId.\n  plistData[key] = appId;\n  debug('Allowing deep link:', { key, appId });\n\n  try {\n    const data = bplistCreator(plistData);\n    // Write the updated plist back to disk\n    await fs.promises.writeFile(plistPath, data);\n  } catch (error: any) {\n    Log.warn(`Could not update simulator linking permissions: ${error.message}`);\n  }\n}\n\nconst updateSimulatorLinkingPermissionsAsyncMemo = memoize(updateSimulatorLinkingPermissionsAsync);\n\n/** Open a URL on a device. The url can have any protocol. */\nexport async function openUrlAsync(\n  device: Partial<DeviceContext>,\n  options: { url: string; appId?: string }\n): Promise<void> {\n  if (options.appId) {\n    await profile(\n      updateSimulatorLinkingPermissionsAsyncMemo,\n      'updateSimulatorLinkingPermissionsAsync'\n    )({ udid: device.udid }, options);\n  }\n\n  try {\n    // Skip logging since this is likely to fail.\n    await simctlAsync(['openurl', resolveId(device), options.url]);\n  } catch (error: any) {\n    if (!error.stderr?.match(/Unable to lookup in current state: Shut/)) {\n      throw error;\n    }\n\n    // If the device was in a weird in-between state (\"Shutting Down\" or \"Shutdown\"), then attempt to reboot it and try again.\n    // This can happen when quitting the Simulator app, and immediately pressing `i` to reopen the project.\n\n    // First boot the simulator\n    await bootDeviceAsync({ udid: resolveId(device) });\n\n    // Finally, try again...\n    return await openUrlAsync(device, options);\n  }\n}\n\n/** Open a simulator using a bundle identifier. If no app with a matching bundle identifier is installed then an error will be thrown. */\nexport async function openAppIdAsync(\n  device: Partial<DeviceContext>,\n  options: {\n    appId: string;\n  }\n): Promise<SpawnResult> {\n  const results = await openAppIdInternalAsync(device, options);\n  // Similar to 194, this is a conformance issue which indicates that the given device has no app that can handle our launch request.\n  if (results.status === 4) {\n    throw new CommandError('APP_NOT_INSTALLED', results.stderr);\n  }\n  return results;\n}\nasync function openAppIdInternalAsync(\n  device: Partial<DeviceContext>,\n  options: {\n    appId: string;\n  }\n): Promise<SpawnResult> {\n  try {\n    return await simctlAsync(['launch', resolveId(device), options.appId]);\n  } catch (error: any) {\n    if ('status' in error) {\n      return error;\n    }\n    throw error;\n  }\n}\n\n// This will only boot in headless mode if the Simulator app is not running.\nexport async function bootAsync(device: DeviceContext): Promise<Device | null> {\n  await bootDeviceAsync(device);\n  return isDeviceBootedAsync(device);\n}\n\n/** Returns a list of devices whose current state is 'Booted' as an array. */\nexport async function getBootedSimulatorsAsync(): Promise<Device[]> {\n  const simulatorDeviceInfo = await getRuntimesAsync('devices');\n  return Object.values(simulatorDeviceInfo.devices).flatMap((runtime) =>\n    runtime.filter((device) => device.state === 'Booted')\n  );\n}\n\n/** Returns the current device if its state is 'Booted'. */\nexport async function isDeviceBootedAsync(device: Partial<DeviceContext>): Promise<Device | null> {\n  // Simulators can be booted even if the app isn't running :(\n  const devices = await getBootedSimulatorsAsync();\n  if (device.udid) {\n    return devices.find((bootedDevice) => bootedDevice.udid === device.udid) ?? null;\n  }\n\n  return devices[0] ?? null;\n}\n\n/** Boot a device. */\nexport async function bootDeviceAsync(device: DeviceContext): Promise<void> {\n  try {\n    // Skip logging since this is likely to fail.\n    await simctlAsync(['boot', device.udid]);\n  } catch (error: any) {\n    if (!error.stderr?.match(/Unable to boot device in current state: Booted/)) {\n      throw error;\n    }\n  }\n}\n\n/** Install a binary file on the device. */\nexport async function installAsync(\n  device: Partial<DeviceContext>,\n  options: {\n    /** Local absolute file path to an app binary that is built and provisioned for iOS simulators. */\n    filePath: string;\n  }\n): Promise<any> {\n  return simctlAsync(['install', resolveId(device), options.filePath]);\n}\n\n/** Uninstall an app from the provided device. */\nexport async function uninstallAsync(\n  device: Partial<DeviceContext>,\n  options: {\n    /** Bundle identifier */\n    appId: string;\n  }\n): Promise<any> {\n  return simctlAsync(['uninstall', resolveId(device), options.appId]);\n}\n\nfunction parseSimControlJSONResults(input: string): any {\n  try {\n    return JSON.parse(input);\n  } catch (error: any) {\n    // Nov 15, 2020: Observed this can happen when opening the simulator and the simulator prompts the user to update the xcode command line tools.\n    // Unexpected token I in JSON at position 0\n    if (error.message.includes('Unexpected token')) {\n      Log.error(`Apple's simctl returned malformed JSON:\\n${input}`);\n    }\n    throw error;\n  }\n}\n\n/** Get all runtime devices given a certain type. */\nasync function getRuntimesAsync(\n  type: 'devices' | 'devicetypes' | 'runtimes' | 'pairs',\n  query?: string | 'available'\n): Promise<SimulatorDeviceList> {\n  const result = await simctlAsync(['list', type, '--json', query]);\n  const info = parseSimControlJSONResults(result.stdout) as SimulatorDeviceList;\n\n  for (const runtime of Object.keys(info.devices)) {\n    // Given a string like 'com.apple.CoreSimulator.SimRuntime.tvOS-13-4'\n    const runtimeSuffix = runtime.split('com.apple.CoreSimulator.SimRuntime.').pop()!;\n    // Create an array [tvOS, 13, 4]\n    const [osType, ...osVersionComponents] = runtimeSuffix.split('-');\n    // Join the end components [13, 4] -> '13.4'\n    const osVersion = osVersionComponents.join('.');\n    const sims = info.devices[runtime];\n    for (const device of sims) {\n      device.runtime = runtime;\n      device.osVersion = osVersion;\n      device.windowName = `${device.name} (${osVersion})`;\n      device.osType = osType as OSType;\n    }\n  }\n  return info;\n}\n\n/** Return a list of iOS simulators. */\nexport async function getDevicesAsync(): Promise<Device[]> {\n  const simulatorDeviceInfo = await getRuntimesAsync('devices');\n  return Object.values(simulatorDeviceInfo.devices).flat();\n}\n\n/** Run a `simctl` command. */\nexport async function simctlAsync(\n  args: (string | undefined)[],\n  options?: SpawnOptions\n): Promise<SpawnResult> {\n  return xcrunAsync(['simctl', ...args], options);\n}\n\nfunction resolveId(device: Partial<DeviceContext>): string {\n  return device.udid ?? 'booted';\n}\n"], "names": ["isOSType", "getContainerPathAsync", "getInfoPlistValueAsync", "openUrlAsync", "openAppIdAsync", "bootAsync", "getBootedSimulatorsAsync", "isDeviceBootedAsync", "bootDeviceAsync", "installAsync", "uninstallAsync", "getDevicesAsync", "simctlAsync", "debug", "require", "value", "knownTypes", "includes", "Log", "warn", "join", "device", "appId", "stdout", "resolveId", "trim", "error", "stderr", "match", "key", "containerPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "output", "spawnAsync", "stdio", "updateSimulatorLinkingPermissionsAsync", "url", "udid", "scheme", "URL", "protocol", "slice", "message", "plist<PERSON><PERSON>", "path", "os", "homedir", "plistData", "fs", "existsSync", "parsePlistAsync", "data", "bplistCreator", "promises", "writeFile", "updateSimulatorLinkingPermissionsAsyncMemo", "memoize", "options", "profile", "results", "openAppIdInternalAsync", "status", "CommandError", "simulatorDeviceInfo", "getRuntimesAsync", "Object", "values", "devices", "flatMap", "runtime", "filter", "state", "find", "bootedDevice", "filePath", "parseSimControlJSONResults", "input", "JSON", "parse", "type", "query", "result", "info", "keys", "runtimeSuffix", "split", "pop", "osType", "osVersionComponents", "osVersion", "sims", "windowName", "name", "flat", "args", "xcrunAsync"], "mappings": "AAAA;;;;;;;;;;;IAyDgBA,QAAQ,MAARA,QAAQ;IAiBFC,qBAAqB,MAArBA,qBAAqB;IAoBrBC,sBAAsB,MAAtBA,sBAAsB;IA0FtBC,YAAY,MAAZA,YAAY;IA+BZC,cAAc,MAAdA,cAAc;IA8BdC,SAAS,MAATA,SAAS;IAMTC,wBAAwB,MAAxBA,wBAAwB;IAQxBC,mBAAmB,MAAnBA,mBAAmB;IAWnBC,eAAe,MAAfA,eAAe;IAYfC,YAAY,MAAZA,YAAY;IAWZC,cAAc,MAAdA,cAAc;IAkDdC,eAAe,MAAfA,eAAe;IAMfC,WAAW,MAAXA,WAAW;;;8DA7VqB,mBAAmB;;;;;;;8DAC/C,gBAAgB;;;;;;;8DAC3B,IAAI;;;;;;;8DACJ,IAAI;;;;;;;8DACF,MAAM;;;;;;uBAEI,SAAS;2DACf,cAAc;wBACN,uBAAuB;oBAC5B,mBAAmB;uBACX,sBAAsB;yBAC9B,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,AAAsB,AAAC;AA4C7D,SAASd,QAAQ,CAACe,KAAU,EAAmB;IACpD,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,KAAK,CAAC;IAEtD,MAAMC,UAAU,GAAG;QAAC,KAAK;QAAE,MAAM;QAAE,SAAS;QAAE,OAAO;KAAC,AAAC;IACvD,IAAI,CAACA,UAAU,CAACC,QAAQ,CAACF,KAAK,CAAC,EAAE;QAC/BG,IAAG,CAACC,IAAI,CAAC,CAAC,iBAAiB,EAAEJ,KAAK,CAAC,mBAAmB,EAAEC,UAAU,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AASM,eAAenB,qBAAqB,CACzCoB,MAA8B,EAC9B,EACEC,KAAK,CAAA,EAGN,EACuB;IACxB,IAAI;QACF,MAAM,EAAEC,MAAM,CAAA,EAAE,GAAG,MAAMX,WAAW,CAAC;YAAC,mBAAmB;YAAEY,SAAS,CAACH,MAAM,CAAC;YAAEC,KAAK;SAAC,CAAC,AAAC;QACtF,OAAOC,MAAM,CAACE,IAAI,EAAE,CAAC;IACvB,EAAE,OAAOC,KAAK,EAAO;YACfA,GAAY;QAAhB,IAAIA,CAAAA,GAAY,GAAZA,KAAK,CAACC,MAAM,SAAO,GAAnBD,KAAAA,CAAmB,GAAnBA,GAAY,CAAEE,KAAK,6BAA6B,EAAE;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAMF,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAGM,eAAexB,sBAAsB,CAC1CmB,MAA8B,EAC9B,EACEC,KAAK,CAAA,EACLO,GAAG,CAAA,EACHC,aAAa,CAAA,EAKd,EACuB;IACxB,MAAMC,oBAAoB,GAAGD,aAAa,WAAbA,aAAa,GAAK,MAAM7B,qBAAqB,CAACoB,MAAM,EAAE;QAAEC,KAAK;KAAE,CAAC,AAAC,AAAC;IAC/F,IAAIS,oBAAoB,EAAE;QACxB,IAAI;YACF,MAAM,EAAEC,MAAM,CAAA,EAAE,GAAG,MAAMC,IAAAA,WAAU,EAAA,QAAA,EACjC,UAAU,EACV;gBAAC,MAAM;gBAAE,CAAC,EAAEF,oBAAoB,CAAC,KAAK,CAAC;gBAAEF,GAAG;aAAC,EAC7C;gBACEK,KAAK,EAAE,MAAM;aACd,CACF,AAAC;YACF,OAAOF,MAAM,CAACZ,IAAI,CAAC,IAAI,CAAC,CAACK,IAAI,EAAE,CAAC;QAClC,EAAE,OAAM;YACN,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,4GAA4G,GAC5G,eAAeU,sCAAsC,CACnDd,MAA8B,EAC9B,EAAEe,GAAG,CAAA,EAAEd,KAAK,CAAA,EAAmC,EAC/C;IACA,IAAI,CAACD,MAAM,CAACgB,IAAI,IAAI,CAACf,KAAK,EAAE;QAC1BT,KAAK,CAAC,0EAA0E,EAAE;YAChFuB,GAAG;YACHd,KAAK;YACLe,IAAI,EAAEhB,MAAM,CAACgB,IAAI;SAClB,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IACDxB,KAAK,CAAC,0DAA0D,EAAE;QAChEuB,GAAG;QACHd,KAAK;QACLe,IAAI,EAAEhB,MAAM,CAACgB,IAAI;KAClB,CAAC,CAAC;IACH,IAAIC,MAAM,AAAQ,AAAC;IACnB,IAAI;QACF,8CAA8C;QAC9CA,MAAM,GAAG,IAAIC,GAAG,CAACH,GAAG,CAAC,CAACI,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9C,EAAE,OAAOf,KAAK,EAAO;QACnBb,KAAK,CAAC,CAAC,gCAAgC,EAAEa,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1D,OAAO;IACT,CAAC;IAED,yEAAyE;IACzE,MAAMC,SAAS,GAAGC,KAAI,EAAA,QAAA,CAACxB,IAAI,CACzByB,GAAE,EAAA,QAAA,CAACC,OAAO,EAAE,EACZ,CAAC,uCAAuC,CAAC,EACzCzB,MAAM,CAACgB,IAAI,EACX,CAAC,sEAAsE,CAAC,CACzE,AAAC;IAEF,MAAMU,SAAS,GAAGC,GAAE,EAAA,QAAA,CAACC,UAAU,CAACN,SAAS,CAAC,GAEtC,MAAMO,IAAAA,MAAe,gBAAA,EAACP,SAAS,CAAC,GAEhC,oGAAoG;IACpG,EAAE,AAAC;IAEP9B,KAAK,CAAC,gBAAgB,EAAEkC,SAAS,CAAC,CAAC;IACnC,MAAMlB,GAAG,GAAG,CAAC,8CAA8C,EAAES,MAAM,CAAC,CAAC,AAAC;IACtE,gEAAgE;IAChES,SAAS,CAAClB,GAAG,CAAC,GAAGP,KAAK,CAAC;IACvBT,KAAK,CAAC,qBAAqB,EAAE;QAAEgB,GAAG;QAAEP,KAAK;KAAE,CAAC,CAAC;IAE7C,IAAI;QACF,MAAM6B,IAAI,GAAGC,IAAAA,cAAa,EAAA,QAAA,EAACL,SAAS,CAAC,AAAC;QACtC,uCAAuC;QACvC,MAAMC,GAAE,EAAA,QAAA,CAACK,QAAQ,CAACC,SAAS,CAACX,SAAS,EAAEQ,IAAI,CAAC,CAAC;IAC/C,EAAE,OAAOzB,MAAK,EAAO;QACnBR,IAAG,CAACC,IAAI,CAAC,CAAC,gDAAgD,EAAEO,MAAK,CAACgB,OAAO,CAAC,CAAC,CAAC,CAAC;IAC/E,CAAC;AACH,CAAC;AAED,MAAMa,0CAA0C,GAAGC,IAAAA,GAAO,QAAA,EAACrB,sCAAsC,CAAC,AAAC;AAG5F,eAAehC,YAAY,CAChCkB,MAA8B,EAC9BoC,OAAwC,EACzB;IACf,IAAIA,OAAO,CAACnC,KAAK,EAAE;QACjB,MAAMoC,IAAAA,QAAO,QAAA,EACXH,0CAA0C,EAC1C,wCAAwC,CACzC,CAAC;YAAElB,IAAI,EAAEhB,MAAM,CAACgB,IAAI;SAAE,EAAEoB,OAAO,CAAC,CAAC;IACpC,CAAC;IAED,IAAI;QACF,6CAA6C;QAC7C,MAAM7C,WAAW,CAAC;YAAC,SAAS;YAAEY,SAAS,CAACH,MAAM,CAAC;YAAEoC,OAAO,CAACrB,GAAG;SAAC,CAAC,CAAC;IACjE,EAAE,OAAOV,KAAK,EAAO;YACdA,GAAY;QAAjB,IAAI,EAACA,CAAAA,GAAY,GAAZA,KAAK,CAACC,MAAM,SAAO,GAAnBD,KAAAA,CAAmB,GAAnBA,GAAY,CAAEE,KAAK,2CAA2C,CAAA,EAAE;YACnE,MAAMF,KAAK,CAAC;QACd,CAAC;QAED,0HAA0H;QAC1H,uGAAuG;QAEvG,2BAA2B;QAC3B,MAAMlB,eAAe,CAAC;YAAE6B,IAAI,EAAEb,SAAS,CAACH,MAAM,CAAC;SAAE,CAAC,CAAC;QAEnD,wBAAwB;QACxB,OAAO,MAAMlB,YAAY,CAACkB,MAAM,EAAEoC,OAAO,CAAC,CAAC;IAC7C,CAAC;AACH,CAAC;AAGM,eAAerD,cAAc,CAClCiB,MAA8B,EAC9BoC,OAEC,EACqB;IACtB,MAAME,OAAO,GAAG,MAAMC,sBAAsB,CAACvC,MAAM,EAAEoC,OAAO,CAAC,AAAC;IAC9D,mIAAmI;IACnI,IAAIE,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;QACxB,MAAM,IAAIC,OAAY,aAAA,CAAC,mBAAmB,EAAEH,OAAO,CAAChC,MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,OAAOgC,OAAO,CAAC;AACjB,CAAC;AACD,eAAeC,sBAAsB,CACnCvC,MAA8B,EAC9BoC,OAEC,EACqB;IACtB,IAAI;QACF,OAAO,MAAM7C,WAAW,CAAC;YAAC,QAAQ;YAAEY,SAAS,CAACH,MAAM,CAAC;YAAEoC,OAAO,CAACnC,KAAK;SAAC,CAAC,CAAC;IACzE,EAAE,OAAOI,KAAK,EAAO;QACnB,IAAI,QAAQ,IAAIA,KAAK,EAAE;YACrB,OAAOA,KAAK,CAAC;QACf,CAAC;QACD,MAAMA,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAGM,eAAerB,SAAS,CAACgB,MAAqB,EAA0B;IAC7E,MAAMb,eAAe,CAACa,MAAM,CAAC,CAAC;IAC9B,OAAOd,mBAAmB,CAACc,MAAM,CAAC,CAAC;AACrC,CAAC;AAGM,eAAef,wBAAwB,GAAsB;IAClE,MAAMyD,mBAAmB,GAAG,MAAMC,gBAAgB,CAAC,SAAS,CAAC,AAAC;IAC9D,OAAOC,MAAM,CAACC,MAAM,CAACH,mBAAmB,CAACI,OAAO,CAAC,CAACC,OAAO,CAAC,CAACC,OAAO,GAChEA,OAAO,CAACC,MAAM,CAAC,CAACjD,MAAM,GAAKA,MAAM,CAACkD,KAAK,KAAK,QAAQ,CAAC,CACtD,CAAC;AACJ,CAAC;AAGM,eAAehE,mBAAmB,CAACc,MAA8B,EAA0B;IAChG,4DAA4D;IAC5D,MAAM8C,OAAO,GAAG,MAAM7D,wBAAwB,EAAE,AAAC;IACjD,IAAIe,MAAM,CAACgB,IAAI,EAAE;YACR8B,GAAiE;QAAxE,OAAOA,CAAAA,GAAiE,GAAjEA,OAAO,CAACK,IAAI,CAAC,CAACC,YAAY,GAAKA,YAAY,CAACpC,IAAI,KAAKhB,MAAM,CAACgB,IAAI,CAAC,YAAjE8B,GAAiE,GAAI,IAAI,CAAC;IACnF,CAAC;QAEMA,IAAU;IAAjB,OAAOA,CAAAA,IAAU,GAAVA,OAAO,CAAC,CAAC,CAAC,YAAVA,IAAU,GAAI,IAAI,CAAC;AAC5B,CAAC;AAGM,eAAe3D,eAAe,CAACa,MAAqB,EAAiB;IAC1E,IAAI;QACF,6CAA6C;QAC7C,MAAMT,WAAW,CAAC;YAAC,MAAM;YAAES,MAAM,CAACgB,IAAI;SAAC,CAAC,CAAC;IAC3C,EAAE,OAAOX,KAAK,EAAO;YACdA,GAAY;QAAjB,IAAI,EAACA,CAAAA,GAAY,GAAZA,KAAK,CAACC,MAAM,SAAO,GAAnBD,KAAAA,CAAmB,GAAnBA,GAAY,CAAEE,KAAK,kDAAkD,CAAA,EAAE;YAC1E,MAAMF,KAAK,CAAC;QACd,CAAC;IACH,CAAC;AACH,CAAC;AAGM,eAAejB,YAAY,CAChCY,MAA8B,EAC9BoC,OAGC,EACa;IACd,OAAO7C,WAAW,CAAC;QAAC,SAAS;QAAEY,SAAS,CAACH,MAAM,CAAC;QAAEoC,OAAO,CAACiB,QAAQ;KAAC,CAAC,CAAC;AACvE,CAAC;AAGM,eAAehE,cAAc,CAClCW,MAA8B,EAC9BoC,OAGC,EACa;IACd,OAAO7C,WAAW,CAAC;QAAC,WAAW;QAAEY,SAAS,CAACH,MAAM,CAAC;QAAEoC,OAAO,CAACnC,KAAK;KAAC,CAAC,CAAC;AACtE,CAAC;AAED,SAASqD,0BAA0B,CAACC,KAAa,EAAO;IACtD,IAAI;QACF,OAAOC,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC,CAAC;IAC3B,EAAE,OAAOlD,KAAK,EAAO;QACnB,+IAA+I;QAC/I,2CAA2C;QAC3C,IAAIA,KAAK,CAACgB,OAAO,CAACzB,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC9CC,IAAG,CAACQ,KAAK,CAAC,CAAC,yCAAyC,EAAEkD,KAAK,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC;QACD,MAAMlD,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,kDAAkD,GAClD,eAAesC,gBAAgB,CAC7Be,IAAsD,EACtDC,KAA4B,EACE;IAC9B,MAAMC,MAAM,GAAG,MAAMrE,WAAW,CAAC;QAAC,MAAM;QAAEmE,IAAI;QAAE,QAAQ;QAAEC,KAAK;KAAC,CAAC,AAAC;IAClE,MAAME,IAAI,GAAGP,0BAA0B,CAACM,MAAM,CAAC1D,MAAM,CAAC,AAAuB,AAAC;IAE9E,KAAK,MAAM8C,OAAO,IAAIJ,MAAM,CAACkB,IAAI,CAACD,IAAI,CAACf,OAAO,CAAC,CAAE;QAC/C,qEAAqE;QACrE,MAAMiB,aAAa,GAAGf,OAAO,CAACgB,KAAK,CAAC,qCAAqC,CAAC,CAACC,GAAG,EAAE,AAAC,AAAC;QAClF,gCAAgC;QAChC,MAAM,CAACC,MAAM,EAAE,GAAGC,mBAAmB,CAAC,GAAGJ,aAAa,CAACC,KAAK,CAAC,GAAG,CAAC,AAAC;QAClE,4CAA4C;QAC5C,MAAMI,SAAS,GAAGD,mBAAmB,CAACpE,IAAI,CAAC,GAAG,CAAC,AAAC;QAChD,MAAMsE,IAAI,GAAGR,IAAI,CAACf,OAAO,CAACE,OAAO,CAAC,AAAC;QACnC,KAAK,MAAMhD,MAAM,IAAIqE,IAAI,CAAE;YACzBrE,MAAM,CAACgD,OAAO,GAAGA,OAAO,CAAC;YACzBhD,MAAM,CAACoE,SAAS,GAAGA,SAAS,CAAC;YAC7BpE,MAAM,CAACsE,UAAU,GAAG,CAAC,EAAEtE,MAAM,CAACuE,IAAI,CAAC,EAAE,EAAEH,SAAS,CAAC,CAAC,CAAC,CAAC;YACpDpE,MAAM,CAACkE,MAAM,GAAGA,MAAM,AAAU,CAAC;QACnC,CAAC;IACH,CAAC;IACD,OAAOL,IAAI,CAAC;AACd,CAAC;AAGM,eAAevE,eAAe,GAAsB;IACzD,MAAMoD,mBAAmB,GAAG,MAAMC,gBAAgB,CAAC,SAAS,CAAC,AAAC;IAC9D,OAAOC,MAAM,CAACC,MAAM,CAACH,mBAAmB,CAACI,OAAO,CAAC,CAAC0B,IAAI,EAAE,CAAC;AAC3D,CAAC;AAGM,eAAejF,WAAW,CAC/BkF,IAA4B,EAC5BrC,OAAsB,EACA;IACtB,OAAOsC,IAAAA,MAAU,WAAA,EAAC;QAAC,QAAQ;WAAKD,IAAI;KAAC,EAAErC,OAAO,CAAC,CAAC;AAClD,CAAC;AAED,SAASjC,SAAS,CAACH,MAA8B,EAAU;QAClDA,KAAW;IAAlB,OAAOA,CAAAA,KAAW,GAAXA,MAAM,CAACgB,IAAI,YAAXhB,KAAW,GAAI,QAAQ,CAAC;AACjC,CAAC"}