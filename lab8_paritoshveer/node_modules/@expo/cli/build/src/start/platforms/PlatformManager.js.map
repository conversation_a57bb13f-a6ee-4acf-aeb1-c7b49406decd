{"version": 3, "sources": ["../../../../src/start/platforms/PlatformManager.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport assert from 'assert';\nimport chalk from 'chalk';\n\nimport { AppIdResolver } from './AppIdResolver';\nimport { DeviceManager } from './DeviceManager';\nimport { Log } from '../../log';\nimport { CommandError, UnimplementedError } from '../../utils/errors';\nimport { learnMore } from '../../utils/link';\nimport { logEventAsync } from '../../utils/telemetry';\n\nconst debug = require('debug')('expo:start:platforms:platformManager') as typeof console.log;\n\nexport interface BaseOpenInCustomProps {\n  scheme?: string;\n  applicationId?: string | null;\n}\n\nexport interface BaseResolveDeviceProps<IDevice> {\n  /** Should prompt the user to select a device. */\n  shouldPrompt?: boolean;\n  /** The target device to use. */\n  device?: IDevice;\n}\n\n/** An abstract class for launching a URL on a device. */\nexport class PlatformManager<\n  IDevice,\n  IOpenInCustomProps extends BaseOpenInCustomProps = BaseOpenInCustomProps,\n  IResolveDeviceProps extends BaseResolveDeviceProps<IDevice> = BaseResolveDeviceProps<IDevice>,\n> {\n  constructor(\n    protected projectRoot: string,\n    protected props: {\n      platform: 'ios' | 'android';\n      /** Get the base URL for the dev server hosting this platform manager. */\n      getDevServerUrl: () => string | null;\n      /** Expo Go URL. */\n      getExpoGoUrl: () => string;\n      /**\n       * Get redirect URL for native disambiguation.\n       * @returns a URL like `http://localhost:8081/_expo/loading`\n       */\n      getRedirectUrl: () => string | null;\n      /** Dev Client */\n      getCustomRuntimeUrl: (props?: { scheme?: string }) => string | null;\n      /** Resolve a device, this function should automatically handle opening the device and asserting any system validations. */\n      resolveDeviceAsync: (\n        resolver?: Partial<IResolveDeviceProps>\n      ) => Promise<DeviceManager<IDevice>>;\n    }\n  ) {}\n\n  /** Returns the project application identifier or asserts that one is not defined. Exposed for testing. */\n  _getAppIdResolver(): AppIdResolver {\n    throw new UnimplementedError();\n  }\n\n  /**\n   * Get the URL for users intending to launch the project in Expo Go.\n   * The CLI will check if the project has a custom dev client and if the redirect page feature is enabled.\n   * If both are true, the CLI will return the redirect page URL.\n   */\n  protected async getExpoGoOrCustomRuntimeUrlAsync(\n    deviceManager: DeviceManager<IDevice>\n  ): Promise<string> {\n    // Determine if the redirect page feature is enabled first since it's the cheapest to check.\n    const redirectUrl = this.props.getRedirectUrl();\n    if (redirectUrl) {\n      // If the redirect page feature is enabled, check if the project has a resolvable native identifier.\n      let applicationId;\n      try {\n        applicationId = await this._getAppIdResolver().getAppIdAsync();\n      } catch {\n        Log.warn(\n          chalk`\\u203A Launching in Expo Go. If you want to use a ` +\n            `development build, you need to create and install one first, or, if you already ` +\n            chalk`have a build, add {bold ios.bundleIdentifier} and {bold android.package} to ` +\n            `this project's app config.\\n${learnMore('https://docs.expo.dev/development/build/')}`\n        );\n      }\n      if (applicationId) {\n        debug(`Resolving launch URL: (appId: ${applicationId}, redirect URL: ${redirectUrl})`);\n        // NOTE(EvanBacon): This adds considerable amount of time to the command, we should consider removing or memoizing it.\n        // Finally determine if the target device has a custom dev client installed.\n        if (\n          await deviceManager.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(applicationId)\n        ) {\n          return redirectUrl;\n        } else {\n          // Log a warning if no development build is available on the device, but the\n          // interstitial page would otherwise be opened.\n          Log.warn(\n            chalk`\\u203A The {bold expo-dev-client} package is installed, but a development build is not ` +\n              chalk`installed on {bold ${deviceManager.name}}.\\nLaunching in Expo Go. If you want to use a ` +\n              `development build, you need to create and install one first.\\n${learnMore(\n                'https://docs.expo.dev/development/build/'\n              )}`\n          );\n        }\n      }\n    }\n\n    return this.props.getExpoGoUrl();\n  }\n\n  protected async openProjectInExpoGoAsync(\n    resolveSettings: Partial<IResolveDeviceProps> = {}\n  ): Promise<{ url: string }> {\n    const deviceManager = await this.props.resolveDeviceAsync(resolveSettings);\n    const url = await this.getExpoGoOrCustomRuntimeUrlAsync(deviceManager);\n\n    deviceManager.logOpeningUrl(url);\n\n    // TODO: Expensive, we should only do this once.\n    const { exp } = getConfig(this.projectRoot);\n    const sdkVersion = exp.sdkVersion;\n    assert(sdkVersion, 'sdkVersion should be resolved by getConfig');\n    const installedExpo = await deviceManager.ensureExpoGoAsync(sdkVersion);\n\n    deviceManager.activateWindowAsync();\n    await deviceManager.openUrlAsync(url, { appId: deviceManager.getExpoGoAppId() });\n\n    await logEventAsync('Open Url on Device', {\n      platform: this.props.platform,\n      installedExpo,\n    });\n\n    return { url };\n  }\n\n  private async openProjectInCustomRuntimeAsync(\n    resolveSettings: Partial<IResolveDeviceProps> = {},\n    props: Partial<IOpenInCustomProps> = {}\n  ): Promise<{ url: string }> {\n    debug(\n      `open custom (${Object.entries(props)\n        .map(([k, v]) => `${k}: ${v}`)\n        .join(', ')})`\n    );\n\n    let url = this.props.getCustomRuntimeUrl({ scheme: props.scheme });\n    debug(`Opening project in custom runtime: ${url} -- %O`, props);\n    // TODO: It's unclear why we do application id validation when opening with a URL\n    // NOTE: But having it enables us to allow the deep link to directly open on iOS simulators without the modal.\n    const applicationId = props.applicationId ?? (await this._getAppIdResolver().getAppIdAsync());\n\n    const deviceManager = await this.props.resolveDeviceAsync(resolveSettings);\n\n    if (!(await deviceManager.isAppInstalledAndIfSoReturnContainerPathForIOSAsync(applicationId))) {\n      throw new CommandError(\n        `No development build (${applicationId}) for this project is installed. ` +\n          `Please make and install a development build on the device first.\\n${learnMore(\n            'https://docs.expo.dev/development/build/'\n          )}`\n      );\n    }\n\n    // TODO: Rethink analytics\n    await logEventAsync('Open Url on Device', {\n      platform: this.props.platform,\n      installedExpo: false,\n    });\n\n    if (!url) {\n      url = this._resolveAlternativeLaunchUrl(applicationId, props);\n    }\n\n    deviceManager.logOpeningUrl(url);\n    await deviceManager.activateWindowAsync();\n\n    await deviceManager.openUrlAsync(url, {\n      appId: applicationId,\n    });\n\n    return {\n      url,\n    };\n  }\n\n  /** Launch the project on a device given the input runtime. */\n  async openAsync(\n    options:\n      | {\n          runtime: 'expo' | 'web';\n        }\n      | {\n          runtime: 'custom';\n          props?: Partial<IOpenInCustomProps>;\n        },\n    resolveSettings: Partial<IResolveDeviceProps> = {}\n  ): Promise<{ url: string }> {\n    debug(\n      `open (runtime: ${options.runtime}, platform: ${this.props.platform}, device: %O, shouldPrompt: ${resolveSettings.shouldPrompt})`,\n      resolveSettings.device\n    );\n    if (options.runtime === 'expo') {\n      return this.openProjectInExpoGoAsync(resolveSettings);\n    } else if (options.runtime === 'web') {\n      return this.openWebProjectAsync(resolveSettings);\n    } else if (options.runtime === 'custom') {\n      return this.openProjectInCustomRuntimeAsync(resolveSettings, options.props);\n    } else {\n      throw new CommandError(`Invalid runtime target: ${options.runtime}`);\n    }\n  }\n\n  /** Open the current web project (Webpack) in a device . */\n  private async openWebProjectAsync(resolveSettings: Partial<IResolveDeviceProps> = {}): Promise<{\n    url: string;\n  }> {\n    const url = this.props.getDevServerUrl();\n    assert(url, 'Dev server is not running.');\n\n    const deviceManager = await this.props.resolveDeviceAsync(resolveSettings);\n    deviceManager.logOpeningUrl(url);\n    await deviceManager.activateWindowAsync();\n    await deviceManager.openUrlAsync(url);\n\n    return { url };\n  }\n\n  /** If the launch URL cannot be determined (`custom` runtimes) then an alternative string can be provided to open the device. Often a device ID or activity to launch. Exposed for testing. */\n  _resolveAlternativeLaunchUrl(\n    applicationId: string,\n    props: Partial<IOpenInCustomProps> = {}\n  ): string {\n    throw new UnimplementedError();\n  }\n}\n"], "names": ["PlatformManager", "debug", "require", "constructor", "projectRoot", "props", "_getAppIdResolver", "UnimplementedError", "getExpoGoOrCustomRuntimeUrlAsync", "deviceManager", "redirectUrl", "getRedirectUrl", "applicationId", "getAppIdAsync", "Log", "warn", "chalk", "learnMore", "isAppInstalledAndIfSoReturnContainerPathForIOSAsync", "name", "getExpoGoUrl", "openProjectInExpoGoAsync", "resolveSettings", "resolveDeviceAsync", "url", "logOpeningUrl", "exp", "getConfig", "sdkVersion", "assert", "installedExpo", "ensureExpoGoAsync", "activateWindowAsync", "openUrlAsync", "appId", "getExpoGoAppId", "logEventAsync", "platform", "openProjectInCustomRuntimeAsync", "Object", "entries", "map", "k", "v", "join", "getCustomRuntimeUrl", "scheme", "CommandError", "_resolveAlternativeLaunchUrl", "openAsync", "options", "runtime", "should<PERSON>rompt", "device", "openWebProjectAsync", "getDevServerUrl"], "mappings": "AAAA;;;;+BA0BaA,iBAAe;;aAAfA,eAAe;;;yBA1BF,cAAc;;;;;;;8DACrB,QAAQ;;;;;;;8DACT,OAAO;;;;;;qBAIL,WAAW;wBACkB,oBAAoB;sBAC3C,kBAAkB;2BACd,uBAAuB;;;;;;AAErD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,sCAAsC,CAAC,AAAsB,AAAC;AAetF,MAAMF,eAAe;IAK1BG,YACYC,WAAmB,EACnBC,KAiBT,CACD;QAnBUD,mBAAAA,WAAmB,CAAA;QACnBC,aAAAA,KAiBT,CAAA;IACA;IAEH,wGAAwG,GACxGC,iBAAiB,GAAkB;QACjC,MAAM,IAAIC,OAAkB,mBAAA,EAAE,CAAC;IACjC;IAEA;;;;GAIC,SACeC,gCAAgC,CAC9CC,aAAqC,EACpB;QACjB,4FAA4F;QAC5F,MAAMC,WAAW,GAAG,IAAI,CAACL,KAAK,CAACM,cAAc,EAAE,AAAC;QAChD,IAAID,WAAW,EAAE;YACf,oGAAoG;YACpG,IAAIE,aAAa,AAAC;YAClB,IAAI;gBACFA,aAAa,GAAG,MAAM,IAAI,CAACN,iBAAiB,EAAE,CAACO,aAAa,EAAE,CAAC;YACjE,EAAE,OAAM;gBACNC,IAAG,IAAA,CAACC,IAAI,CACNC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,kDAAkD,CAAC,GACvD,CAAC,gFAAgF,CAAC,GAClFA,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,4EAA4E,CAAC,GACnF,CAAC,4BAA4B,EAAEC,IAAAA,KAAS,UAAA,EAAC,0CAA0C,CAAC,CAAC,CAAC,CACzF,CAAC;YACJ,CAAC;YACD,IAAIL,aAAa,EAAE;gBACjBX,KAAK,CAAC,CAAC,8BAA8B,EAAEW,aAAa,CAAC,gBAAgB,EAAEF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvF,sHAAsH;gBACtH,4EAA4E;gBAC5E,IACE,MAAMD,aAAa,CAACS,mDAAmD,CAACN,aAAa,CAAC,EACtF;oBACA,OAAOF,WAAW,CAAC;gBACrB,OAAO;oBACL,4EAA4E;oBAC5E,+CAA+C;oBAC/CI,IAAG,IAAA,CAACC,IAAI,CACNC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,uFAAuF,CAAC,GAC5FA,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,mBAAmB,EAAEP,aAAa,CAACU,IAAI,CAAC,+CAA+C,CAAC,GAC9F,CAAC,8DAA8D,EAAEF,IAAAA,KAAS,UAAA,EACxE,0CAA0C,CAC3C,CAAC,CAAC,CACN,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAACZ,KAAK,CAACe,YAAY,EAAE,CAAC;IACnC;UAEgBC,wBAAwB,CACtCC,eAA6C,GAAG,EAAE,EACxB;QAC1B,MAAMb,aAAa,GAAG,MAAM,IAAI,CAACJ,KAAK,CAACkB,kBAAkB,CAACD,eAAe,CAAC,AAAC;QAC3E,MAAME,GAAG,GAAG,MAAM,IAAI,CAAChB,gCAAgC,CAACC,aAAa,CAAC,AAAC;QAEvEA,aAAa,CAACgB,aAAa,CAACD,GAAG,CAAC,CAAC;QAEjC,gDAAgD;QAChD,MAAM,EAAEE,GAAG,CAAA,EAAE,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAAC,IAAI,CAACvB,WAAW,CAAC,AAAC;QAC5C,MAAMwB,UAAU,GAAGF,GAAG,CAACE,UAAU,AAAC;QAClCC,IAAAA,OAAM,EAAA,QAAA,EAACD,UAAU,EAAE,4CAA4C,CAAC,CAAC;QACjE,MAAME,aAAa,GAAG,MAAMrB,aAAa,CAACsB,iBAAiB,CAACH,UAAU,CAAC,AAAC;QAExEnB,aAAa,CAACuB,mBAAmB,EAAE,CAAC;QACpC,MAAMvB,aAAa,CAACwB,YAAY,CAACT,GAAG,EAAE;YAAEU,KAAK,EAAEzB,aAAa,CAAC0B,cAAc,EAAE;SAAE,CAAC,CAAC;QAEjF,MAAMC,IAAAA,UAAa,cAAA,EAAC,oBAAoB,EAAE;YACxCC,QAAQ,EAAE,IAAI,CAAChC,KAAK,CAACgC,QAAQ;YAC7BP,aAAa;SACd,CAAC,CAAC;QAEH,OAAO;YAAEN,GAAG;SAAE,CAAC;IACjB;UAEcc,+BAA+B,CAC3ChB,eAA6C,GAAG,EAAE,EAClDjB,KAAkC,GAAG,EAAE,EACb;QAC1BJ,KAAK,CACH,CAAC,aAAa,EAAEsC,MAAM,CAACC,OAAO,CAACnC,KAAK,CAAC,CAClCoC,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAK,CAAC,EAAED,CAAC,CAAC,EAAE,EAAEC,CAAC,CAAC,CAAC,CAAC,CAC7BC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CACjB,CAAC;QAEF,IAAIpB,GAAG,GAAG,IAAI,CAACnB,KAAK,CAACwC,mBAAmB,CAAC;YAAEC,MAAM,EAAEzC,KAAK,CAACyC,MAAM;SAAE,CAAC,AAAC;QACnE7C,KAAK,CAAC,CAAC,mCAAmC,EAAEuB,GAAG,CAAC,MAAM,CAAC,EAAEnB,KAAK,CAAC,CAAC;YAG1CA,cAAmB;QAFzC,iFAAiF;QACjF,8GAA8G;QAC9G,MAAMO,aAAa,GAAGP,CAAAA,cAAmB,GAAnBA,KAAK,CAACO,aAAa,YAAnBP,cAAmB,GAAK,MAAM,IAAI,CAACC,iBAAiB,EAAE,CAACO,aAAa,EAAE,AAAC,AAAC;QAE9F,MAAMJ,aAAa,GAAG,MAAM,IAAI,CAACJ,KAAK,CAACkB,kBAAkB,CAACD,eAAe,CAAC,AAAC;QAE3E,IAAI,CAAE,MAAMb,aAAa,CAACS,mDAAmD,CAACN,aAAa,CAAC,AAAC,EAAE;YAC7F,MAAM,IAAImC,OAAY,aAAA,CACpB,CAAC,sBAAsB,EAAEnC,aAAa,CAAC,iCAAiC,CAAC,GACvE,CAAC,kEAAkE,EAAEK,IAAAA,KAAS,UAAA,EAC5E,0CAA0C,CAC3C,CAAC,CAAC,CACN,CAAC;QACJ,CAAC;QAED,0BAA0B;QAC1B,MAAMmB,IAAAA,UAAa,cAAA,EAAC,oBAAoB,EAAE;YACxCC,QAAQ,EAAE,IAAI,CAAChC,KAAK,CAACgC,QAAQ;YAC7BP,aAAa,EAAE,KAAK;SACrB,CAAC,CAAC;QAEH,IAAI,CAACN,GAAG,EAAE;YACRA,GAAG,GAAG,IAAI,CAACwB,4BAA4B,CAACpC,aAAa,EAAEP,KAAK,CAAC,CAAC;QAChE,CAAC;QAEDI,aAAa,CAACgB,aAAa,CAACD,GAAG,CAAC,CAAC;QACjC,MAAMf,aAAa,CAACuB,mBAAmB,EAAE,CAAC;QAE1C,MAAMvB,aAAa,CAACwB,YAAY,CAACT,GAAG,EAAE;YACpCU,KAAK,EAAEtB,aAAa;SACrB,CAAC,CAAC;QAEH,OAAO;YACLY,GAAG;SACJ,CAAC;IACJ;IAEA,4DAA4D,SACtDyB,SAAS,CACbC,OAOK,EACL5B,eAA6C,GAAG,EAAE,EACxB;QAC1BrB,KAAK,CACH,CAAC,eAAe,EAAEiD,OAAO,CAACC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC9C,KAAK,CAACgC,QAAQ,CAAC,4BAA4B,EAAEf,eAAe,CAAC8B,YAAY,CAAC,CAAC,CAAC,EACjI9B,eAAe,CAAC+B,MAAM,CACvB,CAAC;QACF,IAAIH,OAAO,CAACC,OAAO,KAAK,MAAM,EAAE;YAC9B,OAAO,IAAI,CAAC9B,wBAAwB,CAACC,eAAe,CAAC,CAAC;QACxD,OAAO,IAAI4B,OAAO,CAACC,OAAO,KAAK,KAAK,EAAE;YACpC,OAAO,IAAI,CAACG,mBAAmB,CAAChC,eAAe,CAAC,CAAC;QACnD,OAAO,IAAI4B,OAAO,CAACC,OAAO,KAAK,QAAQ,EAAE;YACvC,OAAO,IAAI,CAACb,+BAA+B,CAAChB,eAAe,EAAE4B,OAAO,CAAC7C,KAAK,CAAC,CAAC;QAC9E,OAAO;YACL,MAAM,IAAI0C,OAAY,aAAA,CAAC,CAAC,wBAAwB,EAAEG,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;QACvE,CAAC;IACH;IAEA,yDAAyD,SAC3CG,mBAAmB,CAAChC,eAA6C,GAAG,EAAE,EAEjF;QACD,MAAME,GAAG,GAAG,IAAI,CAACnB,KAAK,CAACkD,eAAe,EAAE,AAAC;QACzC1B,IAAAA,OAAM,EAAA,QAAA,EAACL,GAAG,EAAE,4BAA4B,CAAC,CAAC;QAE1C,MAAMf,aAAa,GAAG,MAAM,IAAI,CAACJ,KAAK,CAACkB,kBAAkB,CAACD,eAAe,CAAC,AAAC;QAC3Eb,aAAa,CAACgB,aAAa,CAACD,GAAG,CAAC,CAAC;QACjC,MAAMf,aAAa,CAACuB,mBAAmB,EAAE,CAAC;QAC1C,MAAMvB,aAAa,CAACwB,YAAY,CAACT,GAAG,CAAC,CAAC;QAEtC,OAAO;YAAEA,GAAG;SAAE,CAAC;IACjB;IAEA,4LAA4L,GAC5LwB,4BAA4B,CAC1BpC,aAAqB,EACrBP,KAAkC,GAAG,EAAE,EAC/B;QACR,MAAM,IAAIE,OAAkB,mBAAA,EAAE,CAAC;IACjC;CACD"}