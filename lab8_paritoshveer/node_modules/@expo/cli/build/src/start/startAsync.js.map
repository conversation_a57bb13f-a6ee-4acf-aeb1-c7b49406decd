{"version": 3, "sources": ["../../../src/start/startAsync.ts"], "sourcesContent": ["import { ExpoConfig, getConfig } from '@expo/config';\nimport chalk from 'chalk';\n\nimport { SimulatorAppPrerequisite } from './doctor/apple/SimulatorAppPrerequisite';\nimport { getXcodeVersionAsync } from './doctor/apple/XcodePrerequisite';\nimport { validateDependenciesVersionsAsync } from './doctor/dependencies/validateDependenciesVersions';\nimport { WebSupportProjectPrerequisite } from './doctor/web/WebSupportProjectPrerequisite';\nimport { startInterfaceAsync } from './interface/startInterface';\nimport { Options, resolvePortsAsync } from './resolveOptions';\nimport { BundlerStartOptions } from './server/BundlerDevServer';\nimport { DevServerManager, MultiBundlerStartOptions } from './server/DevServerManager';\nimport { openPlatformsAsync } from './server/openPlatforms';\nimport { getPlatformBundlers, PlatformBundlers } from './server/platformBundlers';\nimport * as Log from '../log';\nimport getDevClientProperties from '../utils/analytics/getDevClientProperties';\nimport { env } from '../utils/env';\nimport { installExitHooks } from '../utils/exit';\nimport { isInteractive } from '../utils/interactive';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { profile } from '../utils/profile';\nimport { logEventAsync } from '../utils/telemetry';\n\nasync function getMultiBundlerStartOptions(\n  projectRoot: string,\n  options: Options,\n  settings: { webOnly?: boolean },\n  platformBundlers: PlatformBundlers\n): Promise<[BundlerStartOptions, MultiBundlerStartOptions]> {\n  const commonOptions: BundlerStartOptions = {\n    mode: options.dev ? 'development' : 'production',\n    devClient: options.devClient,\n    privateKeyPath: options.privateKeyPath ?? undefined,\n    https: options.https,\n    maxWorkers: options.maxWorkers,\n    resetDevServer: options.clear,\n    minify: options.minify,\n    location: {\n      hostType: options.host,\n      scheme: options.scheme,\n    },\n  };\n  const multiBundlerSettings = await resolvePortsAsync(projectRoot, options, settings);\n\n  const optionalBundlers: Partial<PlatformBundlers> = { ...platformBundlers };\n  // In the default case, we don't want to start multiple bundlers since this is\n  // a bit slower. Our priority (for legacy) is native platforms.\n  if (!options.web) {\n    delete optionalBundlers['web'];\n  }\n\n  const bundlers = [...new Set(Object.values(optionalBundlers))];\n  const multiBundlerStartOptions = bundlers.map((bundler) => {\n    const port =\n      bundler === 'webpack' ? multiBundlerSettings.webpackPort : multiBundlerSettings.metroPort;\n    return {\n      type: bundler,\n      options: {\n        ...commonOptions,\n        port,\n      },\n    };\n  });\n\n  return [commonOptions, multiBundlerStartOptions];\n}\n\nexport async function startAsync(\n  projectRoot: string,\n  options: Options,\n  settings: { webOnly?: boolean }\n) {\n  Log.log(chalk.gray(`Starting project at ${projectRoot}`));\n\n  setNodeEnv(options.dev ? 'development' : 'production');\n  require('@expo/env').load(projectRoot);\n  const { exp, pkg } = profile(getConfig)(projectRoot);\n\n  if (exp.platforms?.includes('ios') && process.platform !== 'win32') {\n    // If Xcode could potentially be used, then we should eagerly perform the\n    // assertions since they can take a while on cold boots.\n    getXcodeVersionAsync({ silent: true });\n    SimulatorAppPrerequisite.instance.assertAsync().catch(() => {\n      // noop -- this will be thrown again when the user attempts to open the project.\n    });\n  }\n\n  const platformBundlers = getPlatformBundlers(projectRoot, exp);\n\n  const [defaultOptions, startOptions] = await getMultiBundlerStartOptions(\n    projectRoot,\n    options,\n    settings,\n    platformBundlers\n  );\n\n  const devServerManager = new DevServerManager(projectRoot, defaultOptions);\n\n  // Validations\n\n  if (options.web || settings.webOnly) {\n    await devServerManager.ensureProjectPrerequisiteAsync(WebSupportProjectPrerequisite);\n  }\n\n  // Start the server as soon as possible.\n  await profile(devServerManager.startAsync.bind(devServerManager))(startOptions);\n\n  if (!settings.webOnly) {\n    await devServerManager.watchEnvironmentVariables();\n\n    // After the server starts, we can start attempting to bootstrap TypeScript.\n    await devServerManager.bootstrapTypeScriptAsync();\n  }\n\n  if (!settings.webOnly && !options.devClient) {\n    await profile(validateDependenciesVersionsAsync)(projectRoot, exp, pkg);\n  }\n\n  // Some tracking thing\n\n  if (options.devClient) {\n    await trackAsync(projectRoot, exp);\n  }\n\n  // Open project on devices.\n  await profile(openPlatformsAsync)(devServerManager, options);\n\n  // Present the Terminal UI.\n  if (isInteractive()) {\n    await profile(startInterfaceAsync)(devServerManager, {\n      platforms: exp.platforms ?? ['ios', 'android', 'web'],\n    });\n  } else {\n    // Display the server location in CI...\n    const url = devServerManager.getDefaultDevServer()?.getDevServerUrl();\n    if (url) {\n      if (env.__EXPO_E2E_TEST) {\n        // Print the URL to stdout for tests\n        console.info(`[__EXPO_E2E_TEST:server] ${JSON.stringify({ url })}`);\n      }\n      Log.log(chalk`Waiting on {underline ${url}}`);\n    }\n  }\n\n  // Final note about closing the server.\n  const logLocation = settings.webOnly ? 'in the browser console' : 'below';\n  Log.log(\n    chalk`Logs for your project will appear ${logLocation}.${\n      isInteractive() ? chalk.dim(` Press Ctrl+C to exit.`) : ''\n    }`\n  );\n}\n\nasync function trackAsync(projectRoot: string, exp: ExpoConfig): Promise<void> {\n  await logEventAsync('dev client start command', {\n    status: 'started',\n    ...getDevClientProperties(projectRoot, exp),\n  });\n  installExitHooks(async () => {\n    await logEventAsync('dev client start command', {\n      status: 'finished',\n      ...getDevClientProperties(projectRoot, exp),\n    });\n    // UnifiedAnalytics.flush();\n  });\n}\n"], "names": ["startAsync", "getMultiBundlerStartOptions", "projectRoot", "options", "settings", "platformBundlers", "commonOptions", "mode", "dev", "devClient", "privateKeyPath", "undefined", "https", "maxWorkers", "resetDevServer", "clear", "minify", "location", "hostType", "host", "scheme", "multiBundlerSettings", "resolvePortsAsync", "optionalBundlers", "web", "bundlers", "Set", "Object", "values", "multiBundlerStartOptions", "map", "bundler", "port", "webpackPort", "metroPort", "type", "exp", "Log", "log", "chalk", "gray", "setNodeEnv", "require", "load", "pkg", "profile", "getConfig", "platforms", "includes", "process", "platform", "getXcodeVersionAsync", "silent", "SimulatorAppPrerequisite", "instance", "assertAsync", "catch", "getPlatformBundlers", "defaultOptions", "startOptions", "devServerManager", "DevServerManager", "webOnly", "ensureProjectPrerequisiteAsync", "WebSupportProjectPrerequisite", "bind", "watchEnvironmentVariables", "bootstrapTypeScriptAsync", "validateDependenciesVersionsAsync", "trackAsync", "openPlatformsAsync", "isInteractive", "startInterfaceAsync", "url", "getDefaultDevServer", "getDevServerUrl", "env", "__EXPO_E2E_TEST", "console", "info", "JSON", "stringify", "logLocation", "dim", "logEventAsync", "status", "getDevClientProperties", "installExitHooks"], "mappings": "AAAA;;;;+BAk<PERSON><PERSON>,YAAU;;aAAVA,UAAU;;;yBAlEM,cAAc;;;;;;;8DAClC,OAAO;;;;;;0CAEgB,yCAAyC;mCAC7C,kCAAkC;8CACrB,oDAAoD;+CACxD,4CAA4C;gCACtD,4BAA4B;gCACrB,kBAAkB;kCAEF,2BAA2B;+BACnD,wBAAwB;kCACL,2BAA2B;2DAC5D,QAAQ;6EACM,2CAA2C;qBAC1D,cAAc;sBACD,eAAe;6BAClB,sBAAsB;yBACzB,kBAAkB;yBACrB,kBAAkB;2BACZ,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElD,eAAeC,2BAA2B,CACxCC,WAAmB,EACnBC,OAAgB,EAChBC,QAA+B,EAC/BC,gBAAkC,EACwB;QAIxCF,eAAsB;IAHxC,MAAMG,aAAa,GAAwB;QACzCC,IAAI,EAAEJ,OAAO,CAACK,GAAG,GAAG,aAAa,GAAG,YAAY;QAChDC,SAAS,EAAEN,OAAO,CAACM,SAAS;QAC5BC,cAAc,EAAEP,CAAAA,eAAsB,GAAtBA,OAAO,CAACO,cAAc,YAAtBP,eAAsB,GAAIQ,SAAS;QACnDC,KAAK,EAAET,OAAO,CAACS,KAAK;QACpBC,UAAU,EAAEV,OAAO,CAACU,UAAU;QAC9BC,cAAc,EAAEX,OAAO,CAACY,KAAK;QAC7BC,MAAM,EAAEb,OAAO,CAACa,MAAM;QACtBC,QAAQ,EAAE;YACRC,QAAQ,EAAEf,OAAO,CAACgB,IAAI;YACtBC,MAAM,EAAEjB,OAAO,CAACiB,MAAM;SACvB;KACF,AAAC;IACF,MAAMC,oBAAoB,GAAG,MAAMC,IAAAA,eAAiB,kBAAA,EAACpB,WAAW,EAAEC,OAAO,EAAEC,QAAQ,CAAC,AAAC;IAErF,MAAMmB,gBAAgB,GAA8B;QAAE,GAAGlB,gBAAgB;KAAE,AAAC;IAC5E,8EAA8E;IAC9E,+DAA+D;IAC/D,IAAI,CAACF,OAAO,CAACqB,GAAG,EAAE;QAChB,OAAOD,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,MAAME,QAAQ,GAAG;WAAI,IAAIC,GAAG,CAACC,MAAM,CAACC,MAAM,CAACL,gBAAgB,CAAC,CAAC;KAAC,AAAC;IAC/D,MAAMM,wBAAwB,GAAGJ,QAAQ,CAACK,GAAG,CAAC,CAACC,OAAO,GAAK;QACzD,MAAMC,IAAI,GACRD,OAAO,KAAK,SAAS,GAAGV,oBAAoB,CAACY,WAAW,GAAGZ,oBAAoB,CAACa,SAAS,AAAC;QAC5F,OAAO;YACLC,IAAI,EAAEJ,OAAO;YACb5B,OAAO,EAAE;gBACP,GAAGG,aAAa;gBAChB0B,IAAI;aACL;SACF,CAAC;IACJ,CAAC,CAAC,AAAC;IAEH,OAAO;QAAC1B,aAAa;QAAEuB,wBAAwB;KAAC,CAAC;AACnD,CAAC;AAEM,eAAe7B,UAAU,CAC9BE,WAAmB,EACnBC,OAAgB,EAChBC,QAA+B,EAC/B;QAOIgC,GAAa;IANjBC,IAAG,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,CAAC,oBAAoB,EAAEtC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1DuC,IAAAA,QAAU,WAAA,EAACtC,OAAO,CAACK,GAAG,GAAG,aAAa,GAAG,YAAY,CAAC,CAAC;IACvDkC,OAAO,CAAC,WAAW,CAAC,CAACC,IAAI,CAACzC,WAAW,CAAC,CAAC;IACvC,MAAM,EAAEkC,GAAG,CAAA,EAAEQ,GAAG,CAAA,EAAE,GAAGC,IAAAA,QAAO,QAAA,EAACC,OAAS,EAAA,UAAA,CAAC,CAAC5C,WAAW,CAAC,AAAC;IAErD,IAAIkC,CAAAA,CAAAA,GAAa,GAAbA,GAAG,CAACW,SAAS,SAAU,GAAvBX,KAAAA,CAAuB,GAAvBA,GAAa,CAAEY,QAAQ,CAAC,KAAK,CAAC,KAAIC,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;QAClE,yEAAyE;QACzE,wDAAwD;QACxDC,IAAAA,kBAAoB,qBAAA,EAAC;YAAEC,MAAM,EAAE,IAAI;SAAE,CAAC,CAAC;QACvCC,yBAAwB,yBAAA,CAACC,QAAQ,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,IAAM;QAC1D,gFAAgF;QAClF,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAMnD,gBAAgB,GAAGoD,IAAAA,iBAAmB,oBAAA,EAACvD,WAAW,EAAEkC,GAAG,CAAC,AAAC;IAE/D,MAAM,CAACsB,cAAc,EAAEC,YAAY,CAAC,GAAG,MAAM1D,2BAA2B,CACtEC,WAAW,EACXC,OAAO,EACPC,QAAQ,EACRC,gBAAgB,CACjB,AAAC;IAEF,MAAMuD,gBAAgB,GAAG,IAAIC,iBAAgB,iBAAA,CAAC3D,WAAW,EAAEwD,cAAc,CAAC,AAAC;IAE3E,cAAc;IAEd,IAAIvD,OAAO,CAACqB,GAAG,IAAIpB,QAAQ,CAAC0D,OAAO,EAAE;QACnC,MAAMF,gBAAgB,CAACG,8BAA8B,CAACC,8BAA6B,8BAAA,CAAC,CAAC;IACvF,CAAC;IAED,wCAAwC;IACxC,MAAMnB,IAAAA,QAAO,QAAA,EAACe,gBAAgB,CAAC5D,UAAU,CAACiE,IAAI,CAACL,gBAAgB,CAAC,CAAC,CAACD,YAAY,CAAC,CAAC;IAEhF,IAAI,CAACvD,QAAQ,CAAC0D,OAAO,EAAE;QACrB,MAAMF,gBAAgB,CAACM,yBAAyB,EAAE,CAAC;QAEnD,4EAA4E;QAC5E,MAAMN,gBAAgB,CAACO,wBAAwB,EAAE,CAAC;IACpD,CAAC;IAED,IAAI,CAAC/D,QAAQ,CAAC0D,OAAO,IAAI,CAAC3D,OAAO,CAACM,SAAS,EAAE;QAC3C,MAAMoC,IAAAA,QAAO,QAAA,EAACuB,6BAAiC,kCAAA,CAAC,CAAClE,WAAW,EAAEkC,GAAG,EAAEQ,GAAG,CAAC,CAAC;IAC1E,CAAC;IAED,sBAAsB;IAEtB,IAAIzC,OAAO,CAACM,SAAS,EAAE;QACrB,MAAM4D,UAAU,CAACnE,WAAW,EAAEkC,GAAG,CAAC,CAAC;IACrC,CAAC;IAED,2BAA2B;IAC3B,MAAMS,IAAAA,QAAO,QAAA,EAACyB,cAAkB,mBAAA,CAAC,CAACV,gBAAgB,EAAEzD,OAAO,CAAC,CAAC;IAE7D,2BAA2B;IAC3B,IAAIoE,IAAAA,YAAa,cAAA,GAAE,EAAE;YAENnC,UAAa;QAD1B,MAAMS,IAAAA,QAAO,QAAA,EAAC2B,eAAmB,oBAAA,CAAC,CAACZ,gBAAgB,EAAE;YACnDb,SAAS,EAAEX,CAAAA,UAAa,GAAbA,GAAG,CAACW,SAAS,YAAbX,UAAa,GAAI;gBAAC,KAAK;gBAAE,SAAS;gBAAE,KAAK;aAAC;SACtD,CAAC,CAAC;IACL,OAAO;YAEOwB,IAAsC;QADlD,uCAAuC;QACvC,MAAMa,GAAG,GAAGb,CAAAA,IAAsC,GAAtCA,gBAAgB,CAACc,mBAAmB,EAAE,SAAiB,GAAvDd,KAAAA,CAAuD,GAAvDA,IAAsC,CAAEe,eAAe,EAAE,AAAC;QACtE,IAAIF,GAAG,EAAE;YACP,IAAIG,IAAG,IAAA,CAACC,eAAe,EAAE;gBACvB,oCAAoC;gBACpCC,OAAO,CAACC,IAAI,CAAC,CAAC,yBAAyB,EAAEC,IAAI,CAACC,SAAS,CAAC;oBAAER,GAAG;iBAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,CAAC;YACDpC,IAAG,CAACC,GAAG,CAACC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,sBAAsB,EAAEkC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,uCAAuC;IACvC,MAAMS,WAAW,GAAG9E,QAAQ,CAAC0D,OAAO,GAAG,wBAAwB,GAAG,OAAO,AAAC;IAC1EzB,IAAG,CAACC,GAAG,CACLC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,kCAAkC,EAAE2C,WAAW,CAAC,CAAC,EACrDX,IAAAA,YAAa,cAAA,GAAE,GAAGhC,MAAK,EAAA,QAAA,CAAC4C,GAAG,CAAC,CAAC,sBAAsB,CAAC,CAAC,GAAG,EAAE,CAC3D,CAAC,CACH,CAAC;AACJ,CAAC;AAED,eAAed,UAAU,CAACnE,WAAmB,EAAEkC,GAAe,EAAiB;IAC7E,MAAMgD,IAAAA,UAAa,cAAA,EAAC,0BAA0B,EAAE;QAC9CC,MAAM,EAAE,SAAS;QACjB,GAAGC,IAAAA,uBAAsB,QAAA,EAACpF,WAAW,EAAEkC,GAAG,CAAC;KAC5C,CAAC,CAAC;IACHmD,IAAAA,KAAgB,iBAAA,EAAC,UAAY;QAC3B,MAAMH,IAAAA,UAAa,cAAA,EAAC,0BAA0B,EAAE;YAC9CC,MAAM,EAAE,UAAU;YAClB,GAAGC,IAAAA,uBAAsB,QAAA,EAACpF,WAAW,EAAEkC,GAAG,CAAC;SAC5C,CAAC,CAAC;IACH,4BAA4B;IAC9B,CAAC,CAAC,CAAC;AACL,CAAC"}