{"version": 3, "sources": ["../../../../../src/start/server/metro/withMetroMultiPlatform.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { ExpoConfig, Platform } from '@expo/config';\nimport fs from 'fs';\nimport Bundler from 'metro/src/Bundler';\nimport { ConfigT } from 'metro-config';\nimport { Resolution, ResolutionContext, CustomResolutionContext } from 'metro-resolver';\nimport * as metroResolver from 'metro-resolver';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { createFastResolver } from './createExpoMetroResolver';\nimport { isNodeExternal, shouldCreateVirtualCanary, shouldCreateVirtualShim } from './externals';\nimport { isFailedToResolveNameError, isFailedToResolvePathError } from './metroErrors';\nimport { getMetroBundlerWithVirtualModules } from './metroVirtualModules';\nimport {\n  withMetroErrorReportingResolver,\n  withMetroMutatedResolverContext,\n  withMetroResolvers,\n} from './withMetroResolvers';\nimport { Log } from '../../../log';\nimport { FileNotifier } from '../../../utils/FileNotifier';\nimport { env } from '../../../utils/env';\nimport { installExitHooks } from '../../../utils/exit';\nimport { isInteractive } from '../../../utils/interactive';\nimport { loadTsConfigPathsAsync, TsConfigPaths } from '../../../utils/tsconfig/loadTsConfigPaths';\nimport { resolveWithTsConfigPaths } from '../../../utils/tsconfig/resolveWithTsConfigPaths';\nimport { isServerEnvironment } from '../middleware/metroOptions';\nimport { PlatformBundlers } from '../platformBundlers';\n\ntype Mutable<T> = { -readonly [K in keyof T]: T[K] };\n\nconst debug = require('debug')('expo:start:server:metro:multi-platform') as typeof console.log;\n\nfunction withWebPolyfills(\n  config: ConfigT,\n  {\n    getMetroBundler,\n  }: {\n    getMetroBundler: () => Bundler;\n  }\n): ConfigT {\n  const originalGetPolyfills = config.serializer.getPolyfills\n    ? config.serializer.getPolyfills.bind(config.serializer)\n    : () => [];\n\n  const getPolyfills = (ctx: { platform: string | null }): readonly string[] => {\n    const virtualEnvVarId = `\\0polyfill:environment-variables`;\n\n    getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n      virtualEnvVarId,\n      (() => {\n        return `//`;\n      })()\n    );\n\n    const virtualModuleId = `\\0polyfill:external-require`;\n\n    getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n      virtualModuleId,\n      (() => {\n        if (ctx.platform === 'web') {\n          return `global.$$require_external = typeof window === \"undefined\" ? require : () => null;`;\n        } else {\n          // Wrap in try/catch to support Android.\n          return 'try { global.$$require_external = typeof expo === \"undefined\" ? eval(\"require\") : (moduleId) => { throw new Error(`Node.js standard library module ${moduleId} is not available in this JavaScript environment`);} } catch { global.$$require_external = (moduleId) => { throw new Error(`Node.js standard library module ${moduleId} is not available in this JavaScript environment`);} }';\n        }\n      })()\n    );\n\n    if (ctx.platform === 'web') {\n      return [\n        virtualModuleId,\n        virtualEnvVarId,\n        // Ensure that the error-guard polyfill is included in the web polyfills to\n        // make metro-runtime work correctly.\n        // TODO: This module is pretty big for a function that simply re-throws an error that doesn't need to be caught.\n        require.resolve('@react-native/js-polyfills/error-guard'),\n      ];\n    }\n\n    // Generally uses `rn-get-polyfills`\n    const polyfills = originalGetPolyfills(ctx);\n    return [...polyfills, virtualModuleId, virtualEnvVarId];\n  };\n\n  return {\n    ...config,\n    serializer: {\n      ...config.serializer,\n      getPolyfills,\n    },\n  };\n}\n\nfunction normalizeSlashes(p: string) {\n  return p.replace(/\\\\/g, '/');\n}\n\nexport function getNodejsExtensions(srcExts: readonly string[]): string[] {\n  const mjsExts = srcExts.filter((ext) => /mjs$/.test(ext));\n  const nodejsSourceExtensions = srcExts.filter((ext) => !/mjs$/.test(ext));\n  // find index of last `*.js` extension\n  const jsIndex = nodejsSourceExtensions.reduce((index, ext, i) => {\n    return /jsx?$/.test(ext) ? i : index;\n  }, -1);\n\n  // insert `*.mjs` extensions after `*.js` extensions\n  nodejsSourceExtensions.splice(jsIndex + 1, 0, ...mjsExts);\n\n  return nodejsSourceExtensions;\n}\n\n/**\n * Apply custom resolvers to do the following:\n * - Disable `.native.js` extensions on web.\n * - Alias `react-native` to `react-native-web` on web.\n * - Redirect `react-native-web/dist/modules/AssetRegistry/index.js` to `@react-native/assets/registry.js` on web.\n * - Add support for `tsconfig.json`/`jsconfig.json` aliases via `compilerOptions.paths`.\n * - Alias react-native renderer code to a vendored React canary build on native.\n */\nexport function withExtendedResolver(\n  config: ConfigT,\n  {\n    tsconfig,\n    isTsconfigPathsEnabled,\n    isFastResolverEnabled,\n    isExporting,\n    isReactCanaryEnabled,\n    getMetroBundler,\n  }: {\n    tsconfig: TsConfigPaths | null;\n    isTsconfigPathsEnabled?: boolean;\n    isFastResolverEnabled?: boolean;\n    isExporting?: boolean;\n    isReactCanaryEnabled?: boolean;\n    getMetroBundler: () => Bundler;\n  }\n) {\n  if (isFastResolverEnabled) {\n    Log.warn(`Experimental bundling features are enabled.`);\n  }\n  if (isReactCanaryEnabled) {\n    Log.warn(`Experimental React Canary version is enabled.`);\n  }\n\n  // Get the `transformer.assetRegistryPath`\n  // this needs to be unified since you can't dynamically\n  // swap out the transformer based on platform.\n  const assetRegistryPath = fs.realpathSync(\n    path.resolve(resolveFrom(config.projectRoot, '@react-native/assets-registry/registry.js'))\n  );\n\n  const defaultResolver = metroResolver.resolve;\n  const resolver = isFastResolverEnabled\n    ? createFastResolver({\n        preserveSymlinks: config.resolver?.unstable_enableSymlinks ?? true,\n        blockList: Array.isArray(config.resolver?.blockList)\n          ? config.resolver?.blockList\n          : [config.resolver?.blockList],\n      })\n    : defaultResolver;\n\n  const aliases: { [key: string]: Record<string, string> } = {\n    web: {\n      'react-native': 'react-native-web',\n      'react-native/index': 'react-native-web',\n    },\n  };\n\n  const universalAliases: [RegExp, string][] = [];\n\n  // This package is currently always installed as it is included in the `expo` package.\n  if (resolveFrom.silent(config.projectRoot, '@expo/vector-icons')) {\n    debug('Enabling alias: react-native-vector-icons -> @expo/vector-icons');\n    universalAliases.push([/^react-native-vector-icons(\\/.*)?/, '@expo/vector-icons$1']);\n  }\n\n  const preferredMainFields: { [key: string]: string[] } = {\n    // Defaults from Expo Webpack. Most packages using `react-native` don't support web\n    // in the `react-native` field, so we should prefer the `browser` field.\n    // https://github.com/expo/router/issues/37\n    web: ['browser', 'module', 'main'],\n  };\n\n  let tsConfigResolve =\n    isTsconfigPathsEnabled && (tsconfig?.paths || tsconfig?.baseUrl != null)\n      ? resolveWithTsConfigPaths.bind(resolveWithTsConfigPaths, {\n          paths: tsconfig.paths ?? {},\n          baseUrl: tsconfig.baseUrl ?? config.projectRoot,\n          hasBaseUrl: !!tsconfig.baseUrl,\n        })\n      : null;\n\n  // TODO: Move this to be a transform key for invalidation.\n  if (!isExporting && isInteractive()) {\n    if (isTsconfigPathsEnabled) {\n      // TODO: We should track all the files that used imports and invalidate them\n      // currently the user will need to save all the files that use imports to\n      // use the new aliases.\n      const configWatcher = new FileNotifier(config.projectRoot, [\n        './tsconfig.json',\n        './jsconfig.json',\n      ]);\n      configWatcher.startObserving(() => {\n        debug('Reloading tsconfig.json');\n        loadTsConfigPathsAsync(config.projectRoot).then((tsConfigPaths) => {\n          if (tsConfigPaths?.paths && !!Object.keys(tsConfigPaths.paths).length) {\n            debug('Enabling tsconfig.json paths support');\n            tsConfigResolve = resolveWithTsConfigPaths.bind(resolveWithTsConfigPaths, {\n              paths: tsConfigPaths.paths ?? {},\n              baseUrl: tsConfigPaths.baseUrl ?? config.projectRoot,\n              hasBaseUrl: !!tsConfigPaths.baseUrl,\n            });\n          } else {\n            debug('Disabling tsconfig.json paths support');\n            tsConfigResolve = null;\n          }\n        });\n      });\n\n      // TODO: This probably prevents the process from exiting.\n      installExitHooks(() => {\n        configWatcher.stopObserving();\n      });\n    } else {\n      debug('Skipping tsconfig.json paths support');\n    }\n  }\n\n  let nodejsSourceExtensions: string[] | null = null;\n\n  function getStrictResolver(\n    { resolveRequest, ...context }: ResolutionContext,\n    platform: string | null\n  ) {\n    return function doResolve(moduleName: string): Resolution {\n      return resolver(context, moduleName, platform);\n    };\n  }\n\n  function getOptionalResolver(context: ResolutionContext, platform: string | null) {\n    const doResolve = getStrictResolver(context, platform);\n    return function optionalResolve(moduleName: string): Resolution | null {\n      try {\n        return doResolve(moduleName);\n      } catch (error) {\n        // If the error is directly related to a resolver not being able to resolve a module, then\n        // we can ignore the error and try the next resolver. Otherwise, we should throw the error.\n        const isResolutionError =\n          isFailedToResolveNameError(error) || isFailedToResolvePathError(error);\n        if (!isResolutionError) {\n          throw error;\n        }\n      }\n      return null;\n    };\n  }\n\n  const metroConfigWithCustomResolver = withMetroResolvers(config, [\n    // Mock out production react imports in development.\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      // This resolution is dev-only to prevent bundling the production React packages in development.\n      // @ts-expect-error: dev is not on type.\n      if (!context.dev) return null;\n\n      if (\n        // Match react-native renderers.\n        (platform !== 'web' &&\n          context.originModulePath.match(/[\\\\/]node_modules[\\\\/]react-native[\\\\/]/) &&\n          moduleName.match(/([\\\\/]ReactFabric|ReactNativeRenderer)-prod/)) ||\n        // Match react production imports.\n        (moduleName.match(/\\.production(\\.min)?\\.js$/) &&\n          // Match if the import originated from a react package.\n          context.originModulePath.match(/[\\\\/]node_modules[\\\\/](react[-\\\\/]|scheduler[\\\\/])/))\n      ) {\n        debug(`Skipping production module: ${moduleName}`);\n        // /Users/<USER>/to/expo/node_modules/react/index.js ./cjs/react.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react/jsx-dev-runtime.js ./cjs/react-jsx-dev-runtime.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-is/index.js ./cjs/react-is.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-refresh/runtime.js ./cjs/react-refresh-runtime.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-native/node_modules/scheduler/index.native.js ./cjs/scheduler.native.production.min.js\n        // /Users/<USER>/to/expo/node_modules/react-native/node_modules/react-is/index.js ./cjs/react-is.production.min.js\n        return {\n          type: 'empty',\n        };\n      }\n      return null;\n    },\n    // tsconfig paths\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      return (\n        tsConfigResolve?.(\n          {\n            originModulePath: context.originModulePath,\n            moduleName,\n          },\n          getOptionalResolver(context, platform)\n        ) ?? null\n      );\n    },\n\n    // Node.js externals support\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      const isServer =\n        context.customResolverOptions?.environment === 'node' ||\n        context.customResolverOptions?.environment === 'react-server';\n\n      if (platform !== 'web' && !isServer) {\n        // This is a web/server-only feature, we may extend the shimming to native platforms in the future.\n        return null;\n      }\n\n      const moduleId = isNodeExternal(moduleName);\n      if (!moduleId) {\n        return null;\n      }\n\n      if (\n        // In browser runtimes, we want to either resolve a local node module by the same name, or shim the module to\n        // prevent crashing when Node.js built-ins are imported.\n        !isServer\n      ) {\n        // Perform optional resolve first. If the module doesn't exist (no module in the node_modules)\n        // then we can mock the file to use an empty module.\n        const result = getOptionalResolver(context, platform)(moduleName);\n        return (\n          result ?? {\n            // In this case, mock the file to use an empty module.\n            type: 'empty',\n          }\n        );\n      }\n\n      const contents = `module.exports=$$require_external('node:${moduleId}');`;\n      debug(`Virtualizing Node.js \"${moduleId}\"`);\n      const virtualModuleId = `\\0node:${moduleId}`;\n      getMetroBundlerWithVirtualModules(getMetroBundler()).setVirtualModule(\n        virtualModuleId,\n        contents\n      );\n      return {\n        type: 'sourceFile',\n        filePath: virtualModuleId,\n      };\n    },\n\n    // Basic moduleId aliases\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      // Conditionally remap `react-native` to `react-native-web` on web in\n      // a way that doesn't require Babel to resolve the alias.\n      if (platform && platform in aliases && aliases[platform][moduleName]) {\n        const redirectedModuleName = aliases[platform][moduleName];\n        return getStrictResolver(context, platform)(redirectedModuleName);\n      }\n\n      for (const [matcher, alias] of universalAliases) {\n        const match = moduleName.match(matcher);\n        if (match) {\n          const aliasedModule = alias.replace(\n            /\\$(\\d+)/g,\n            (_, index) => match[parseInt(index, 10)] ?? ''\n          );\n          const doResolve = getStrictResolver(context, platform);\n          debug(`Alias \"${moduleName}\" to \"${aliasedModule}\"`);\n          return doResolve(aliasedModule);\n        }\n      }\n\n      return null;\n    },\n\n    // TODO: Reduce these as much as possible in the future.\n    // Complex post-resolution rewrites.\n    (context: ResolutionContext, moduleName: string, platform: string | null) => {\n      const doResolve = getStrictResolver(context, platform);\n\n      const result = doResolve(moduleName);\n\n      if (result.type !== 'sourceFile') {\n        return result;\n      }\n\n      if (platform === 'web') {\n        // Replace the web resolver with the original one.\n        // This is basically an alias for web-only.\n        // TODO: Drop this in favor of the standalone asset registry module.\n        if (shouldAliasAssetRegistryForWeb(platform, result)) {\n          // @ts-expect-error: `readonly` for some reason.\n          result.filePath = assetRegistryPath;\n        }\n\n        if (platform === 'web' && result.filePath.includes('node_modules')) {\n          // Replace with static shims\n\n          const normalName = normalizeSlashes(result.filePath)\n            // Drop everything up until the `node_modules` folder.\n            .replace(/.*node_modules\\//, '');\n\n          const shimFile = shouldCreateVirtualShim(normalName);\n          if (shimFile) {\n            const virtualId = `\\0shim:${normalName}`;\n            const bundler = getMetroBundlerWithVirtualModules(getMetroBundler());\n            if (!bundler.hasVirtualModule(virtualId)) {\n              bundler.setVirtualModule(virtualId, fs.readFileSync(shimFile, 'utf8'));\n            }\n            debug(`Redirecting module \"${result.filePath}\" to shim`);\n\n            return {\n              ...result,\n              filePath: virtualId,\n            };\n          }\n        }\n      } else {\n        // When server components are enabled, redirect React Native's renderer to the canary build\n        // this will enable the use hook and other requisite features from React 19.\n        if (isReactCanaryEnabled && result.filePath.includes('node_modules')) {\n          const normalName = normalizeSlashes(result.filePath)\n            // Drop everything up until the `node_modules` folder.\n            .replace(/.*node_modules\\//, '');\n\n          const canaryFile = shouldCreateVirtualCanary(normalName);\n          if (canaryFile) {\n            debug(`Redirecting React Native module \"${result.filePath}\" to canary build`);\n            return {\n              ...result,\n              filePath: canaryFile,\n            };\n          }\n        }\n      }\n\n      return result;\n    },\n  ]);\n\n  // Ensure we mutate the resolution context to include the custom resolver options for server and web.\n  const metroConfigWithCustomContext = withMetroMutatedResolverContext(\n    metroConfigWithCustomResolver,\n    (\n      immutableContext: CustomResolutionContext,\n      moduleName: string,\n      platform: string | null\n    ): CustomResolutionContext => {\n      const context: Mutable<CustomResolutionContext> = {\n        ...immutableContext,\n        preferNativePlatform: platform !== 'web',\n      };\n\n      if (isServerEnvironment(context.customResolverOptions?.environment)) {\n        // Adjust nodejs source extensions to sort mjs after js, including platform variants.\n        if (nodejsSourceExtensions === null) {\n          nodejsSourceExtensions = getNodejsExtensions(context.sourceExts);\n        }\n        context.sourceExts = nodejsSourceExtensions;\n\n        context.unstable_enablePackageExports = true;\n        context.unstable_conditionsByPlatform = {};\n        // Node.js runtimes should only be importing main at the moment.\n        // This is a temporary fix until we can support the package.json exports.\n        context.mainFields = ['main', 'module'];\n\n        // Enable react-server import conditions.\n        if (context.customResolverOptions?.environment === 'react-server') {\n          context.unstable_conditionNames = ['node', 'require', 'react-server', 'workerd'];\n        } else {\n          context.unstable_conditionNames = ['node', 'require'];\n        }\n      } else {\n        // Non-server changes\n\n        if (!env.EXPO_METRO_NO_MAIN_FIELD_OVERRIDE && platform && platform in preferredMainFields) {\n          context.mainFields = preferredMainFields[platform];\n        }\n      }\n\n      return context;\n    }\n  );\n\n  return withMetroErrorReportingResolver(metroConfigWithCustomContext);\n}\n\n/** @returns `true` if the incoming resolution should be swapped on web. */\nexport function shouldAliasAssetRegistryForWeb(\n  platform: string | null,\n  result: Resolution\n): boolean {\n  return (\n    platform === 'web' &&\n    result?.type === 'sourceFile' &&\n    typeof result?.filePath === 'string' &&\n    normalizeSlashes(result.filePath).endsWith(\n      'react-native-web/dist/modules/AssetRegistry/index.js'\n    )\n  );\n}\n/** @returns `true` if the incoming resolution should be swapped. */\nexport function shouldAliasModule(\n  input: {\n    platform: string | null;\n    result: Resolution;\n  },\n  alias: { platform: string; output: string }\n): boolean {\n  return (\n    input.platform === alias.platform &&\n    input.result?.type === 'sourceFile' &&\n    typeof input.result?.filePath === 'string' &&\n    normalizeSlashes(input.result.filePath).endsWith(alias.output)\n  );\n}\n\n/** Add support for `react-native-web` and the Web platform. */\nexport async function withMetroMultiPlatformAsync(\n  projectRoot: string,\n  {\n    config,\n    exp,\n    platformBundlers,\n    isTsconfigPathsEnabled,\n    webOutput,\n    isFastResolverEnabled,\n    isExporting,\n    isReactCanaryEnabled,\n    getMetroBundler,\n  }: {\n    config: ConfigT;\n    exp: ExpoConfig;\n    isTsconfigPathsEnabled: boolean;\n    platformBundlers: PlatformBundlers;\n    webOutput?: 'single' | 'static' | 'server';\n    isFastResolverEnabled?: boolean;\n    isExporting?: boolean;\n    isReactCanaryEnabled: boolean;\n    getMetroBundler: () => Bundler;\n  }\n) {\n  if (!config.projectRoot) {\n    // @ts-expect-error: read-only types\n    config.projectRoot = projectRoot;\n  }\n\n  // Required for @expo/metro-runtime to format paths in the web LogBox.\n  process.env.EXPO_PUBLIC_PROJECT_ROOT = process.env.EXPO_PUBLIC_PROJECT_ROOT ?? projectRoot;\n\n  if (['static', 'server'].includes(webOutput ?? '')) {\n    // Enable static rendering in runtime space.\n    process.env.EXPO_PUBLIC_USE_STATIC = '1';\n  }\n\n  // This is used for running Expo CLI in development against projects outside the monorepo.\n  if (!isDirectoryIn(__dirname, projectRoot)) {\n    if (!config.watchFolders) {\n      // @ts-expect-error: watchFolders is readonly\n      config.watchFolders = [];\n    }\n    // @ts-expect-error: watchFolders is readonly\n    config.watchFolders.push(path.join(require.resolve('metro-runtime/package.json'), '../..'));\n    if (isReactCanaryEnabled) {\n      // @ts-expect-error: watchFolders is readonly\n      config.watchFolders.push(path.join(require.resolve('@expo/cli/package.json'), '..'));\n    }\n  }\n\n  // @ts-expect-error\n  config.transformer._expoRouterWebRendering = webOutput;\n  // @ts-expect-error: Invalidate the cache when the location of expo-router changes on-disk.\n  config.transformer._expoRouterPath = resolveFrom.silent(projectRoot, 'expo-router');\n\n  let tsconfig: null | TsConfigPaths = null;\n\n  if (isTsconfigPathsEnabled) {\n    tsconfig = await loadTsConfigPathsAsync(projectRoot);\n  }\n\n  let expoConfigPlatforms = Object.entries(platformBundlers)\n    .filter(\n      ([platform, bundler]) => bundler === 'metro' && exp.platforms?.includes(platform as Platform)\n    )\n    .map(([platform]) => platform);\n\n  if (Array.isArray(config.resolver.platforms)) {\n    expoConfigPlatforms = [...new Set(expoConfigPlatforms.concat(config.resolver.platforms))];\n  }\n\n  // @ts-expect-error: typed as `readonly`.\n  config.resolver.platforms = expoConfigPlatforms;\n\n  config = withWebPolyfills(config, { getMetroBundler });\n\n  return withExtendedResolver(config, {\n    tsconfig,\n    isExporting,\n    isTsconfigPathsEnabled,\n    isFastResolverEnabled,\n    isReactCanaryEnabled,\n    getMetroBundler,\n  });\n}\n\nfunction isDirectoryIn(targetPath: string, rootPath: string) {\n  return targetPath.startsWith(rootPath) && targetPath.length >= rootPath.length;\n}\n"], "names": ["getNodejsExtensions", "withExtendedResolver", "shouldAliasAssetRegistryForWeb", "shouldAliasModule", "withMetroMultiPlatformAsync", "debug", "require", "withWebPolyfills", "config", "getMetroBundler", "originalGetPolyfills", "serializer", "getPolyfills", "bind", "ctx", "virtualEnvVarId", "getMetroBundlerWithVirtualModules", "setVirtualModule", "virtualModuleId", "platform", "resolve", "polyfills", "normalizeSlashes", "p", "replace", "srcExts", "mjsExts", "filter", "ext", "test", "nodejsSourceExtensions", "jsIndex", "reduce", "index", "i", "splice", "tsconfig", "isTsconfigPathsEnabled", "isFastResolverEnabled", "isExporting", "isReactCanaryEnabled", "Log", "warn", "assetRegistryPath", "fs", "realpathSync", "path", "resolveFrom", "projectRoot", "defaultResolver", "metroResolver", "resolver", "createFastResolver", "preserveSymlinks", "unstable_enableSymlinks", "blockList", "Array", "isArray", "aliases", "web", "universalAliases", "silent", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tsConfigResolve", "paths", "baseUrl", "resolveWithTsConfigPaths", "hasBaseUrl", "isInteractive", "config<PERSON><PERSON><PERSON>", "FileNotifier", "startObserving", "loadTsConfigPathsAsync", "then", "tsConfigPaths", "Object", "keys", "length", "installExitHooks", "stopObserving", "getStrictResolver", "resolveRequest", "context", "doResolve", "moduleName", "getOptionalResolver", "optionalResolve", "error", "isResolutionError", "isFailedToResolveNameError", "isFailedToResolvePathError", "metroConfigWithCustomResolver", "withMetroResolvers", "dev", "originModulePath", "match", "type", "isServer", "customResolverOptions", "environment", "moduleId", "isNodeExternal", "result", "contents", "filePath", "redirectedModuleName", "matcher", "alias", "aliasedModule", "_", "parseInt", "includes", "normalName", "shimFile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "virtualId", "bundler", "hasVirtualModule", "readFileSync", "canaryFile", "shouldCreateVirtualCanary", "metroConfigWithCustomContext", "withMetroMutatedResolverContext", "immutableContext", "preferNativePlatform", "isServerEnvironment", "sourceExts", "unstable_enablePackageExports", "unstable_conditionsByPlatform", "mainFields", "unstable_conditionNames", "env", "EXPO_METRO_NO_MAIN_FIELD_OVERRIDE", "withMetroErrorReportingResolver", "endsWith", "input", "output", "exp", "platformBundlers", "webOutput", "process", "EXPO_PUBLIC_PROJECT_ROOT", "EXPO_PUBLIC_USE_STATIC", "isDirectoryIn", "__dirname", "watchFolders", "join", "transformer", "_expoRouterWebRendering", "_expoRouterPath", "expoConfigPlatforms", "entries", "platforms", "map", "Set", "concat", "targetPath", "rootPath", "startsWith"], "mappings": "AAAA;;;;;CAKC,GACD;;;;;;;;;;;IAiGgBA,mBAAmB,MAAnBA,mBAAmB;IAsBnBC,oBAAoB,MAApBA,oBAAoB;IA4WpBC,8BAA8B,MAA9BA,8BAA8B;IAc9BC,iBAAiB,MAAjBA,iBAAiB;IAgBXC,2BAA2B,MAA3BA,2BAA2B;;;8DAhgBlC,IAAI;;;;;;;+DAIY,gBAAgB;;;;;;;8DAC9B,MAAM;;;;;;;8DACC,cAAc;;;;;;yCAEH,2BAA2B;2BACqB,aAAa;6BACzB,eAAe;qCACpC,uBAAuB;oCAKlE,sBAAsB;qBACT,cAAc;8BACL,6BAA6B;qBACtC,oBAAoB;sBACP,qBAAqB;6BACxB,4BAA4B;mCACJ,2CAA2C;0CACxD,kDAAkD;8BACvD,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKhE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,wCAAwC,CAAC,AAAsB,AAAC;AAE/F,SAASC,gBAAgB,CACvBC,MAAe,EACf,EACEC,eAAe,CAAA,EAGhB,EACQ;IACT,MAAMC,oBAAoB,GAAGF,MAAM,CAACG,UAAU,CAACC,YAAY,GACvDJ,MAAM,CAACG,UAAU,CAACC,YAAY,CAACC,IAAI,CAACL,MAAM,CAACG,UAAU,CAAC,GACtD,IAAM,EAAE,AAAC;IAEb,MAAMC,YAAY,GAAG,CAACE,GAAgC,GAAwB;QAC5E,MAAMC,eAAe,GAAG,CAAC,gCAAgC,CAAC,AAAC;QAE3DC,IAAAA,oBAAiC,kCAAA,EAACP,eAAe,EAAE,CAAC,CAACQ,gBAAgB,CACnEF,eAAe,EACf,CAAC,IAAM;YACL,OAAO,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,EAAE,CACL,CAAC;QAEF,MAAMG,eAAe,GAAG,CAAC,2BAA2B,CAAC,AAAC;QAEtDF,IAAAA,oBAAiC,kCAAA,EAACP,eAAe,EAAE,CAAC,CAACQ,gBAAgB,CACnEC,eAAe,EACf,CAAC,IAAM;YACL,IAAIJ,GAAG,CAACK,QAAQ,KAAK,KAAK,EAAE;gBAC1B,OAAO,CAAC,iFAAiF,CAAC,CAAC;YAC7F,OAAO;gBACL,wCAAwC;gBACxC,OAAO,6XAA6X,CAAC;YACvY,CAAC;QACH,CAAC,CAAC,EAAE,CACL,CAAC;QAEF,IAAIL,GAAG,CAACK,QAAQ,KAAK,KAAK,EAAE;YAC1B,OAAO;gBACLD,eAAe;gBACfH,eAAe;gBACf,2EAA2E;gBAC3E,qCAAqC;gBACrC,gHAAgH;gBAChHT,OAAO,CAACc,OAAO,CAAC,wCAAwC,CAAC;aAC1D,CAAC;QACJ,CAAC;QAED,oCAAoC;QACpC,MAAMC,SAAS,GAAGX,oBAAoB,CAACI,GAAG,CAAC,AAAC;QAC5C,OAAO;eAAIO,SAAS;YAAEH,eAAe;YAAEH,eAAe;SAAC,CAAC;IAC1D,CAAC,AAAC;IAEF,OAAO;QACL,GAAGP,MAAM;QACTG,UAAU,EAAE;YACV,GAAGH,MAAM,CAACG,UAAU;YACpBC,YAAY;SACb;KACF,CAAC;AACJ,CAAC;AAED,SAASU,gBAAgB,CAACC,CAAS,EAAE;IACnC,OAAOA,CAAC,CAACC,OAAO,QAAQ,GAAG,CAAC,CAAC;AAC/B,CAAC;AAEM,SAASxB,mBAAmB,CAACyB,OAA0B,EAAY;IACxE,MAAMC,OAAO,GAAGD,OAAO,CAACE,MAAM,CAAC,CAACC,GAAG,GAAK,OAAOC,IAAI,CAACD,GAAG,CAAC,CAAC,AAAC;IAC1D,MAAME,sBAAsB,GAAGL,OAAO,CAACE,MAAM,CAAC,CAACC,GAAG,GAAK,CAAC,OAAOC,IAAI,CAACD,GAAG,CAAC,CAAC,AAAC;IAC1E,sCAAsC;IACtC,MAAMG,OAAO,GAAGD,sBAAsB,CAACE,MAAM,CAAC,CAACC,KAAK,EAAEL,GAAG,EAAEM,CAAC,GAAK;QAC/D,OAAO,QAAQL,IAAI,CAACD,GAAG,CAAC,GAAGM,CAAC,GAAGD,KAAK,CAAC;IACvC,CAAC,EAAE,CAAC,CAAC,CAAC,AAAC;IAEP,oDAAoD;IACpDH,sBAAsB,CAACK,MAAM,CAACJ,OAAO,GAAG,CAAC,EAAE,CAAC,KAAKL,OAAO,CAAC,CAAC;IAE1D,OAAOI,sBAAsB,CAAC;AAChC,CAAC;AAUM,SAAS7B,oBAAoB,CAClCO,MAAe,EACf,EACE4B,QAAQ,CAAA,EACRC,sBAAsB,CAAA,EACtBC,qBAAqB,CAAA,EACrBC,WAAW,CAAA,EACXC,oBAAoB,CAAA,EACpB/B,eAAe,CAAA,EAQhB,EACD;QAkBwBD,GAAe,EACRA,IAAe,EACpCA,IAAe,EACdA,IAAe;IApB1B,IAAI8B,qBAAqB,EAAE;QACzBG,IAAG,IAAA,CAACC,IAAI,CAAC,CAAC,2CAA2C,CAAC,CAAC,CAAC;IAC1D,CAAC;IACD,IAAIF,oBAAoB,EAAE;QACxBC,IAAG,IAAA,CAACC,IAAI,CAAC,CAAC,6CAA6C,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,0CAA0C;IAC1C,uDAAuD;IACvD,8CAA8C;IAC9C,MAAMC,iBAAiB,GAAGC,GAAE,EAAA,QAAA,CAACC,YAAY,CACvCC,KAAI,EAAA,QAAA,CAAC1B,OAAO,CAAC2B,IAAAA,YAAW,EAAA,QAAA,EAACvC,MAAM,CAACwC,WAAW,EAAE,2CAA2C,CAAC,CAAC,CAC3F,AAAC;IAEF,MAAMC,eAAe,GAAGC,cAAa,EAAA,CAAC9B,OAAO,AAAC;QAGtBZ,IAAwC;IAFhE,MAAM2C,QAAQ,GAAGb,qBAAqB,GAClCc,IAAAA,wBAAkB,mBAAA,EAAC;QACjBC,gBAAgB,EAAE7C,CAAAA,IAAwC,GAAxCA,CAAAA,GAAe,GAAfA,MAAM,CAAC2C,QAAQ,SAAyB,GAAxC3C,KAAAA,CAAwC,GAAxCA,GAAe,CAAE8C,uBAAuB,YAAxC9C,IAAwC,GAAI,IAAI;QAClE+C,SAAS,EAAEC,KAAK,CAACC,OAAO,CAACjD,CAAAA,IAAe,GAAfA,MAAM,CAAC2C,QAAQ,SAAW,GAA1B3C,KAAAA,CAA0B,GAA1BA,IAAe,CAAE+C,SAAS,CAAC,GAChD/C,CAAAA,IAAe,GAAfA,MAAM,CAAC2C,QAAQ,SAAW,GAA1B3C,KAAAA,CAA0B,GAA1BA,IAAe,CAAE+C,SAAS,GAC1B;YAAC/C,CAAAA,IAAe,GAAfA,MAAM,CAAC2C,QAAQ,SAAW,GAA1B3C,KAAAA,CAA0B,GAA1BA,IAAe,CAAE+C,SAAS;SAAC;KACjC,CAAC,GACFN,eAAe,AAAC;IAEpB,MAAMS,OAAO,GAA8C;QACzDC,GAAG,EAAE;YACH,cAAc,EAAE,kBAAkB;YAClC,oBAAoB,EAAE,kBAAkB;SACzC;KACF,AAAC;IAEF,MAAMC,gBAAgB,GAAuB,EAAE,AAAC;IAEhD,sFAAsF;IACtF,IAAIb,YAAW,EAAA,QAAA,CAACc,MAAM,CAACrD,MAAM,CAACwC,WAAW,EAAE,oBAAoB,CAAC,EAAE;QAChE3C,KAAK,CAAC,iEAAiE,CAAC,CAAC;QACzEuD,gBAAgB,CAACE,IAAI,CAAC;;YAAsC,sBAAsB;SAAC,CAAC,CAAC;IACvF,CAAC;IAED,MAAMC,mBAAmB,GAAgC;QACvD,mFAAmF;QACnF,wEAAwE;QACxE,2CAA2C;QAC3CJ,GAAG,EAAE;YAAC,SAAS;YAAE,QAAQ;YAAE,MAAM;SAAC;KACnC,AAAC;QAKavB,MAAc,EACZA,QAAgB;IAJjC,IAAI4B,eAAe,GACjB3B,sBAAsB,IAAI,CAACD,CAAAA,QAAQ,QAAO,GAAfA,KAAAA,CAAe,GAAfA,QAAQ,CAAE6B,KAAK,CAAA,IAAI7B,CAAAA,QAAQ,QAAS,GAAjBA,KAAAA,CAAiB,GAAjBA,QAAQ,CAAE8B,OAAO,CAAA,IAAI,IAAI,CAAC,GACpEC,yBAAwB,yBAAA,CAACtD,IAAI,CAACsD,yBAAwB,yBAAA,EAAE;QACtDF,KAAK,EAAE7B,CAAAA,MAAc,GAAdA,QAAQ,CAAC6B,KAAK,YAAd7B,MAAc,GAAI,EAAE;QAC3B8B,OAAO,EAAE9B,CAAAA,QAAgB,GAAhBA,QAAQ,CAAC8B,OAAO,YAAhB9B,QAAgB,GAAI5B,MAAM,CAACwC,WAAW;QAC/CoB,UAAU,EAAE,CAAC,CAAChC,QAAQ,CAAC8B,OAAO;KAC/B,CAAC,GACF,IAAI,AAAC;IAEX,0DAA0D;IAC1D,IAAI,CAAC3B,WAAW,IAAI8B,IAAAA,YAAa,cAAA,GAAE,EAAE;QACnC,IAAIhC,sBAAsB,EAAE;YAC1B,4EAA4E;YAC5E,yEAAyE;YACzE,uBAAuB;YACvB,MAAMiC,aAAa,GAAG,IAAIC,aAAY,aAAA,CAAC/D,MAAM,CAACwC,WAAW,EAAE;gBACzD,iBAAiB;gBACjB,iBAAiB;aAClB,CAAC,AAAC;YACHsB,aAAa,CAACE,cAAc,CAAC,IAAM;gBACjCnE,KAAK,CAAC,yBAAyB,CAAC,CAAC;gBACjCoE,IAAAA,kBAAsB,uBAAA,EAACjE,MAAM,CAACwC,WAAW,CAAC,CAAC0B,IAAI,CAAC,CAACC,aAAa,GAAK;oBACjE,IAAIA,CAAAA,aAAa,QAAO,GAApBA,KAAAA,CAAoB,GAApBA,aAAa,CAAEV,KAAK,CAAA,IAAI,CAAC,CAACW,MAAM,CAACC,IAAI,CAACF,aAAa,CAACV,KAAK,CAAC,CAACa,MAAM,EAAE;wBACrEzE,KAAK,CAAC,sCAAsC,CAAC,CAAC;4BAErCsE,MAAmB,EACjBA,QAAqB;wBAFhCX,eAAe,GAAGG,yBAAwB,yBAAA,CAACtD,IAAI,CAACsD,yBAAwB,yBAAA,EAAE;4BACxEF,KAAK,EAAEU,CAAAA,MAAmB,GAAnBA,aAAa,CAACV,KAAK,YAAnBU,MAAmB,GAAI,EAAE;4BAChCT,OAAO,EAAES,CAAAA,QAAqB,GAArBA,aAAa,CAACT,OAAO,YAArBS,QAAqB,GAAInE,MAAM,CAACwC,WAAW;4BACpDoB,UAAU,EAAE,CAAC,CAACO,aAAa,CAACT,OAAO;yBACpC,CAAC,CAAC;oBACL,OAAO;wBACL7D,KAAK,CAAC,uCAAuC,CAAC,CAAC;wBAC/C2D,eAAe,GAAG,IAAI,CAAC;oBACzB,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,yDAAyD;YACzDe,IAAAA,KAAgB,iBAAA,EAAC,IAAM;gBACrBT,aAAa,CAACU,aAAa,EAAE,CAAC;YAChC,CAAC,CAAC,CAAC;QACL,OAAO;YACL3E,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,IAAIyB,sBAAsB,GAAoB,IAAI,AAAC;IAEnD,SAASmD,iBAAiB,CACxB,EAAEC,cAAc,CAAA,EAAE,GAAGC,OAAO,EAAqB,EACjDhE,QAAuB,EACvB;QACA,OAAO,SAASiE,SAAS,CAACC,UAAkB,EAAc;YACxD,OAAOlC,QAAQ,CAACgC,OAAO,EAAEE,UAAU,EAAElE,QAAQ,CAAC,CAAC;QACjD,CAAC,CAAC;IACJ,CAAC;IAED,SAASmE,mBAAmB,CAACH,OAA0B,EAAEhE,QAAuB,EAAE;QAChF,MAAMiE,SAAS,GAAGH,iBAAiB,CAACE,OAAO,EAAEhE,QAAQ,CAAC,AAAC;QACvD,OAAO,SAASoE,eAAe,CAACF,UAAkB,EAAqB;YACrE,IAAI;gBACF,OAAOD,SAAS,CAACC,UAAU,CAAC,CAAC;YAC/B,EAAE,OAAOG,KAAK,EAAE;gBACd,0FAA0F;gBAC1F,2FAA2F;gBAC3F,MAAMC,iBAAiB,GACrBC,IAAAA,YAA0B,2BAAA,EAACF,KAAK,CAAC,IAAIG,IAAAA,YAA0B,2BAAA,EAACH,KAAK,CAAC,AAAC;gBACzE,IAAI,CAACC,iBAAiB,EAAE;oBACtB,MAAMD,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;IACJ,CAAC;IAED,MAAMI,6BAA6B,GAAGC,IAAAA,mBAAkB,mBAAA,EAACrF,MAAM,EAAE;QAC/D,oDAAoD;QACpD,CAAC2E,OAA0B,EAAEE,UAAkB,EAAElE,QAAuB,GAAK;YAC3E,gGAAgG;YAChG,wCAAwC;YACxC,IAAI,CAACgE,OAAO,CAACW,GAAG,EAAE,OAAO,IAAI,CAAC;YAE9B,IACE,gCAAgC;YAChC,CAAC3E,QAAQ,KAAK,KAAK,IACjBgE,OAAO,CAACY,gBAAgB,CAACC,KAAK,2CAA2C,IACzEX,UAAU,CAACW,KAAK,+CAA+C,CAAC,IAClE,kCAAkC;YAClC,CAACX,UAAU,CAACW,KAAK,6BAA6B,IAC5C,uDAAuD;YACvDb,OAAO,CAACY,gBAAgB,CAACC,KAAK,sDAAsD,CAAC,EACvF;gBACA3F,KAAK,CAAC,CAAC,4BAA4B,EAAEgF,UAAU,CAAC,CAAC,CAAC,CAAC;gBACnD,gFAAgF;gBAChF,0GAA0G;gBAC1G,sFAAsF;gBACtF,0GAA0G;gBAC1G,gIAAgI;gBAChI,gHAAgH;gBAChH,OAAO;oBACLY,IAAI,EAAE,OAAO;iBACd,CAAC;YACJ,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,iBAAiB;QACjB,CAACd,OAA0B,EAAEE,UAAkB,EAAElE,QAAuB,GAAK;gBAEzE6C,GAMC;YAPH,OACEA,CAAAA,GAMC,GANDA,eAAe,QAMd,GANDA,KAAAA,CAMC,GANDA,eAAe,CACb;gBACE+B,gBAAgB,EAAEZ,OAAO,CAACY,gBAAgB;gBAC1CV,UAAU;aACX,EACDC,mBAAmB,CAACH,OAAO,EAAEhE,QAAQ,CAAC,CACvC,YAND6C,GAMC,GAAI,IAAI,CACT;QACJ,CAAC;QAED,4BAA4B;QAC5B,CAACmB,OAA0B,EAAEE,UAAkB,EAAElE,QAAuB,GAAK;gBAEzEgE,GAA6B,EAC7BA,IAA6B;YAF/B,MAAMe,QAAQ,GACZf,CAAAA,CAAAA,GAA6B,GAA7BA,OAAO,CAACgB,qBAAqB,SAAa,GAA1ChB,KAAAA,CAA0C,GAA1CA,GAA6B,CAAEiB,WAAW,CAAA,KAAK,MAAM,IACrDjB,CAAAA,CAAAA,IAA6B,GAA7BA,OAAO,CAACgB,qBAAqB,SAAa,GAA1ChB,KAAAA,CAA0C,GAA1CA,IAA6B,CAAEiB,WAAW,CAAA,KAAK,cAAc,AAAC;YAEhE,IAAIjF,QAAQ,KAAK,KAAK,IAAI,CAAC+E,QAAQ,EAAE;gBACnC,mGAAmG;gBACnG,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAMG,QAAQ,GAAGC,IAAAA,UAAc,eAAA,EAACjB,UAAU,CAAC,AAAC;YAC5C,IAAI,CAACgB,QAAQ,EAAE;gBACb,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IACE,6GAA6G;YAC7G,wDAAwD;YACxD,CAACH,QAAQ,EACT;gBACA,8FAA8F;gBAC9F,oDAAoD;gBACpD,MAAMK,MAAM,GAAGjB,mBAAmB,CAACH,OAAO,EAAEhE,QAAQ,CAAC,CAACkE,UAAU,CAAC,AAAC;gBAClE,OACEkB,MAAM,WAANA,MAAM,GAAI;oBACR,sDAAsD;oBACtDN,IAAI,EAAE,OAAO;iBACd,CACD;YACJ,CAAC;YAED,MAAMO,QAAQ,GAAG,CAAC,wCAAwC,EAAEH,QAAQ,CAAC,GAAG,CAAC,AAAC;YAC1EhG,KAAK,CAAC,CAAC,sBAAsB,EAAEgG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAMnF,eAAe,GAAG,CAAC,OAAO,EAAEmF,QAAQ,CAAC,CAAC,AAAC;YAC7CrF,IAAAA,oBAAiC,kCAAA,EAACP,eAAe,EAAE,CAAC,CAACQ,gBAAgB,CACnEC,eAAe,EACfsF,QAAQ,CACT,CAAC;YACF,OAAO;gBACLP,IAAI,EAAE,YAAY;gBAClBQ,QAAQ,EAAEvF,eAAe;aAC1B,CAAC;QACJ,CAAC;QAED,yBAAyB;QACzB,CAACiE,OAA0B,EAAEE,UAAkB,EAAElE,QAAuB,GAAK;YAC3E,qEAAqE;YACrE,yDAAyD;YACzD,IAAIA,QAAQ,IAAIA,QAAQ,IAAIuC,OAAO,IAAIA,OAAO,CAACvC,QAAQ,CAAC,CAACkE,UAAU,CAAC,EAAE;gBACpE,MAAMqB,oBAAoB,GAAGhD,OAAO,CAACvC,QAAQ,CAAC,CAACkE,UAAU,CAAC,AAAC;gBAC3D,OAAOJ,iBAAiB,CAACE,OAAO,EAAEhE,QAAQ,CAAC,CAACuF,oBAAoB,CAAC,CAAC;YACpE,CAAC;YAED,KAAK,MAAM,CAACC,OAAO,EAAEC,KAAK,CAAC,IAAIhD,gBAAgB,CAAE;gBAC/C,MAAMoC,KAAK,GAAGX,UAAU,CAACW,KAAK,CAACW,OAAO,CAAC,AAAC;gBACxC,IAAIX,KAAK,EAAE;wBAGOA,GAA0B;oBAF1C,MAAMa,aAAa,GAAGD,KAAK,CAACpF,OAAO,aAEjC,CAACsF,CAAC,EAAE7E,KAAK,GAAK+D,CAAAA,GAA0B,GAA1BA,KAAK,CAACe,QAAQ,CAAC9E,KAAK,EAAE,EAAE,CAAC,CAAC,YAA1B+D,GAA0B,GAAI,EAAE,CAC/C,AAAC;oBACF,MAAMZ,SAAS,GAAGH,iBAAiB,CAACE,OAAO,EAAEhE,QAAQ,CAAC,AAAC;oBACvDd,KAAK,CAAC,CAAC,OAAO,EAAEgF,UAAU,CAAC,MAAM,EAAEwB,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrD,OAAOzB,SAAS,CAACyB,aAAa,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,wDAAwD;QACxD,oCAAoC;QACpC,CAAC1B,OAA0B,EAAEE,UAAkB,EAAElE,QAAuB,GAAK;YAC3E,MAAMiE,SAAS,GAAGH,iBAAiB,CAACE,OAAO,EAAEhE,QAAQ,CAAC,AAAC;YAEvD,MAAMoF,MAAM,GAAGnB,SAAS,CAACC,UAAU,CAAC,AAAC;YAErC,IAAIkB,MAAM,CAACN,IAAI,KAAK,YAAY,EAAE;gBAChC,OAAOM,MAAM,CAAC;YAChB,CAAC;YAED,IAAIpF,QAAQ,KAAK,KAAK,EAAE;gBACtB,kDAAkD;gBAClD,2CAA2C;gBAC3C,oEAAoE;gBACpE,IAAIjB,8BAA8B,CAACiB,QAAQ,EAAEoF,MAAM,CAAC,EAAE;oBACpD,gDAAgD;oBAChDA,MAAM,CAACE,QAAQ,GAAG9D,iBAAiB,CAAC;gBACtC,CAAC;gBAED,IAAIxB,QAAQ,KAAK,KAAK,IAAIoF,MAAM,CAACE,QAAQ,CAACO,QAAQ,CAAC,cAAc,CAAC,EAAE;oBAClE,4BAA4B;oBAE5B,MAAMC,UAAU,GAAG3F,gBAAgB,CAACiF,MAAM,CAACE,QAAQ,CAAC,AAClD,sDAAsD;qBACrDjF,OAAO,qBAAqB,EAAE,CAAC,AAAC;oBAEnC,MAAM0F,QAAQ,GAAGC,IAAAA,UAAuB,wBAAA,EAACF,UAAU,CAAC,AAAC;oBACrD,IAAIC,QAAQ,EAAE;wBACZ,MAAME,SAAS,GAAG,CAAC,OAAO,EAAEH,UAAU,CAAC,CAAC,AAAC;wBACzC,MAAMI,OAAO,GAAGrG,IAAAA,oBAAiC,kCAAA,EAACP,eAAe,EAAE,CAAC,AAAC;wBACrE,IAAI,CAAC4G,OAAO,CAACC,gBAAgB,CAACF,SAAS,CAAC,EAAE;4BACxCC,OAAO,CAACpG,gBAAgB,CAACmG,SAAS,EAAExE,GAAE,EAAA,QAAA,CAAC2E,YAAY,CAACL,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;wBACzE,CAAC;wBACD7G,KAAK,CAAC,CAAC,oBAAoB,EAAEkG,MAAM,CAACE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;wBAEzD,OAAO;4BACL,GAAGF,MAAM;4BACTE,QAAQ,EAAEW,SAAS;yBACpB,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,OAAO;gBACL,2FAA2F;gBAC3F,4EAA4E;gBAC5E,IAAI5E,oBAAoB,IAAI+D,MAAM,CAACE,QAAQ,CAACO,QAAQ,CAAC,cAAc,CAAC,EAAE;oBACpE,MAAMC,WAAU,GAAG3F,gBAAgB,CAACiF,MAAM,CAACE,QAAQ,CAAC,AAClD,sDAAsD;qBACrDjF,OAAO,qBAAqB,EAAE,CAAC,AAAC;oBAEnC,MAAMgG,UAAU,GAAGC,IAAAA,UAAyB,0BAAA,EAACR,WAAU,CAAC,AAAC;oBACzD,IAAIO,UAAU,EAAE;wBACdnH,KAAK,CAAC,CAAC,iCAAiC,EAAEkG,MAAM,CAACE,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC;wBAC9E,OAAO;4BACL,GAAGF,MAAM;4BACTE,QAAQ,EAAEe,UAAU;yBACrB,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAOjB,MAAM,CAAC;QAChB,CAAC;KACF,CAAC,AAAC;IAEH,qGAAqG;IACrG,MAAMmB,4BAA4B,GAAGC,IAAAA,mBAA+B,gCAAA,EAClE/B,6BAA6B,EAC7B,CACEgC,gBAAyC,EACzCvC,UAAkB,EAClBlE,QAAuB,GACK;YAMJgE,GAA6B;QALrD,MAAMA,OAAO,GAAqC;YAChD,GAAGyC,gBAAgB;YACnBC,oBAAoB,EAAE1G,QAAQ,KAAK,KAAK;SACzC,AAAC;QAEF,IAAI2G,IAAAA,aAAmB,oBAAA,EAAC3C,CAAAA,GAA6B,GAA7BA,OAAO,CAACgB,qBAAqB,SAAa,GAA1ChB,KAAAA,CAA0C,GAA1CA,GAA6B,CAAEiB,WAAW,CAAC,EAAE;gBAc/DjB,IAA6B;YAbjC,qFAAqF;YACrF,IAAIrD,sBAAsB,KAAK,IAAI,EAAE;gBACnCA,sBAAsB,GAAG9B,mBAAmB,CAACmF,OAAO,CAAC4C,UAAU,CAAC,CAAC;YACnE,CAAC;YACD5C,OAAO,CAAC4C,UAAU,GAAGjG,sBAAsB,CAAC;YAE5CqD,OAAO,CAAC6C,6BAA6B,GAAG,IAAI,CAAC;YAC7C7C,OAAO,CAAC8C,6BAA6B,GAAG,EAAE,CAAC;YAC3C,gEAAgE;YAChE,yEAAyE;YACzE9C,OAAO,CAAC+C,UAAU,GAAG;gBAAC,MAAM;gBAAE,QAAQ;aAAC,CAAC;YAExC,yCAAyC;YACzC,IAAI/C,CAAAA,CAAAA,IAA6B,GAA7BA,OAAO,CAACgB,qBAAqB,SAAa,GAA1ChB,KAAAA,CAA0C,GAA1CA,IAA6B,CAAEiB,WAAW,CAAA,KAAK,cAAc,EAAE;gBACjEjB,OAAO,CAACgD,uBAAuB,GAAG;oBAAC,MAAM;oBAAE,SAAS;oBAAE,cAAc;oBAAE,SAAS;iBAAC,CAAC;YACnF,OAAO;gBACLhD,OAAO,CAACgD,uBAAuB,GAAG;oBAAC,MAAM;oBAAE,SAAS;iBAAC,CAAC;YACxD,CAAC;QACH,OAAO;YACL,qBAAqB;YAErB,IAAI,CAACC,IAAG,IAAA,CAACC,iCAAiC,IAAIlH,QAAQ,IAAIA,QAAQ,IAAI4C,mBAAmB,EAAE;gBACzFoB,OAAO,CAAC+C,UAAU,GAAGnE,mBAAmB,CAAC5C,QAAQ,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,OAAOgE,OAAO,CAAC;IACjB,CAAC,CACF,AAAC;IAEF,OAAOmD,IAAAA,mBAA+B,gCAAA,EAACZ,4BAA4B,CAAC,CAAC;AACvE,CAAC;AAGM,SAASxH,8BAA8B,CAC5CiB,QAAuB,EACvBoF,MAAkB,EACT;IACT,OACEpF,QAAQ,KAAK,KAAK,IAClBoF,CAAAA,MAAM,QAAM,GAAZA,KAAAA,CAAY,GAAZA,MAAM,CAAEN,IAAI,CAAA,KAAK,YAAY,IAC7B,OAAOM,CAAAA,MAAM,QAAU,GAAhBA,KAAAA,CAAgB,GAAhBA,MAAM,CAAEE,QAAQ,CAAA,KAAK,QAAQ,IACpCnF,gBAAgB,CAACiF,MAAM,CAACE,QAAQ,CAAC,CAAC8B,QAAQ,CACxC,sDAAsD,CACvD,CACD;AACJ,CAAC;AAEM,SAASpI,iBAAiB,CAC/BqI,KAGC,EACD5B,KAA2C,EAClC;QAGP4B,GAAY,EACLA,IAAY;IAHrB,OACEA,KAAK,CAACrH,QAAQ,KAAKyF,KAAK,CAACzF,QAAQ,IACjCqH,CAAAA,CAAAA,GAAY,GAAZA,KAAK,CAACjC,MAAM,SAAM,GAAlBiC,KAAAA,CAAkB,GAAlBA,GAAY,CAAEvC,IAAI,CAAA,KAAK,YAAY,IACnC,OAAOuC,CAAAA,CAAAA,IAAY,GAAZA,KAAK,CAACjC,MAAM,SAAU,GAAtBiC,KAAAA,CAAsB,GAAtBA,IAAY,CAAE/B,QAAQ,CAAA,KAAK,QAAQ,IAC1CnF,gBAAgB,CAACkH,KAAK,CAACjC,MAAM,CAACE,QAAQ,CAAC,CAAC8B,QAAQ,CAAC3B,KAAK,CAAC6B,MAAM,CAAC,CAC9D;AACJ,CAAC;AAGM,eAAerI,2BAA2B,CAC/C4C,WAAmB,EACnB,EACExC,MAAM,CAAA,EACNkI,GAAG,CAAA,EACHC,gBAAgB,CAAA,EAChBtG,sBAAsB,CAAA,EACtBuG,SAAS,CAAA,EACTtG,qBAAqB,CAAA,EACrBC,WAAW,CAAA,EACXC,oBAAoB,CAAA,EACpB/B,eAAe,CAAA,EAWhB,EACD;IACA,IAAI,CAACD,MAAM,CAACwC,WAAW,EAAE;QACvB,oCAAoC;QACpCxC,MAAM,CAACwC,WAAW,GAAGA,WAAW,CAAC;IACnC,CAAC;QAGsC6F,yBAAoC;IAD3E,sEAAsE;IACtEA,OAAO,CAACT,GAAG,CAACU,wBAAwB,GAAGD,CAAAA,yBAAoC,GAApCA,OAAO,CAACT,GAAG,CAACU,wBAAwB,YAApCD,yBAAoC,GAAI7F,WAAW,CAAC;IAE3F,IAAI;QAAC,QAAQ;QAAE,QAAQ;KAAC,CAACgE,QAAQ,CAAC4B,SAAS,WAATA,SAAS,GAAI,EAAE,CAAC,EAAE;QAClD,4CAA4C;QAC5CC,OAAO,CAACT,GAAG,CAACW,sBAAsB,GAAG,GAAG,CAAC;IAC3C,CAAC;IAED,0FAA0F;IAC1F,IAAI,CAACC,aAAa,CAACC,SAAS,EAAEjG,WAAW,CAAC,EAAE;QAC1C,IAAI,CAACxC,MAAM,CAAC0I,YAAY,EAAE;YACxB,6CAA6C;YAC7C1I,MAAM,CAAC0I,YAAY,GAAG,EAAE,CAAC;QAC3B,CAAC;QACD,6CAA6C;QAC7C1I,MAAM,CAAC0I,YAAY,CAACpF,IAAI,CAAChB,KAAI,EAAA,QAAA,CAACqG,IAAI,CAAC7I,OAAO,CAACc,OAAO,CAAC,4BAA4B,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;QAC5F,IAAIoB,oBAAoB,EAAE;YACxB,6CAA6C;YAC7ChC,MAAM,CAAC0I,YAAY,CAACpF,IAAI,CAAChB,KAAI,EAAA,QAAA,CAACqG,IAAI,CAAC7I,OAAO,CAACc,OAAO,CAAC,wBAAwB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAED,mBAAmB;IACnBZ,MAAM,CAAC4I,WAAW,CAACC,uBAAuB,GAAGT,SAAS,CAAC;IACvD,2FAA2F;IAC3FpI,MAAM,CAAC4I,WAAW,CAACE,eAAe,GAAGvG,YAAW,EAAA,QAAA,CAACc,MAAM,CAACb,WAAW,EAAE,aAAa,CAAC,CAAC;IAEpF,IAAIZ,QAAQ,GAAyB,IAAI,AAAC;IAE1C,IAAIC,sBAAsB,EAAE;QAC1BD,QAAQ,GAAG,MAAMqC,IAAAA,kBAAsB,uBAAA,EAACzB,WAAW,CAAC,CAAC;IACvD,CAAC;IAED,IAAIuG,mBAAmB,GAAG3E,MAAM,CAAC4E,OAAO,CAACb,gBAAgB,CAAC,CACvDhH,MAAM,CACL,CAAC,CAACR,QAAQ,EAAEkG,OAAO,CAAC;YAA4BqB,GAAa;QAApCrB,OAAAA,OAAO,KAAK,OAAO,KAAIqB,CAAAA,GAAa,GAAbA,GAAG,CAACe,SAAS,SAAU,GAAvBf,KAAAA,CAAuB,GAAvBA,GAAa,CAAE1B,QAAQ,CAAC7F,QAAQ,CAAa,CAAA,CAAA;KAAA,CAC9F,CACAuI,GAAG,CAAC,CAAC,CAACvI,QAAQ,CAAC,GAAKA,QAAQ,CAAC,AAAC;IAEjC,IAAIqC,KAAK,CAACC,OAAO,CAACjD,MAAM,CAAC2C,QAAQ,CAACsG,SAAS,CAAC,EAAE;QAC5CF,mBAAmB,GAAG;eAAI,IAAII,GAAG,CAACJ,mBAAmB,CAACK,MAAM,CAACpJ,MAAM,CAAC2C,QAAQ,CAACsG,SAAS,CAAC,CAAC;SAAC,CAAC;IAC5F,CAAC;IAED,yCAAyC;IACzCjJ,MAAM,CAAC2C,QAAQ,CAACsG,SAAS,GAAGF,mBAAmB,CAAC;IAEhD/I,MAAM,GAAGD,gBAAgB,CAACC,MAAM,EAAE;QAAEC,eAAe;KAAE,CAAC,CAAC;IAEvD,OAAOR,oBAAoB,CAACO,MAAM,EAAE;QAClC4B,QAAQ;QACRG,WAAW;QACXF,sBAAsB;QACtBC,qBAAqB;QACrBE,oBAAoB;QACpB/B,eAAe;KAChB,CAAC,CAAC;AACL,CAAC;AAED,SAASuI,aAAa,CAACa,UAAkB,EAAEC,QAAgB,EAAE;IAC3D,OAAOD,UAAU,CAACE,UAAU,CAACD,QAAQ,CAAC,IAAID,UAAU,CAAC/E,MAAM,IAAIgF,QAAQ,CAAChF,MAAM,CAAC;AACjF,CAAC"}