{"version": 3, "sources": ["../../../../../src/start/server/middleware/server.types.ts"], "sourcesContent": ["import express from 'express';\nimport http from 'http';\n\n/** Headers */\nexport type ServerHeaders = Map<string, number | string | readonly string[]>;\n/** Request */\nexport type ServerRequest = express.Request | http.IncomingMessage;\n/** Response */\nexport type ServerResponse = express.Response | http.ServerResponse;\n/** Next function */\nexport type ServerNext = (err?: Error | null) => void;\n\n/** The `connect()` app that is a http.RequestListener and having the `use()` function for middlewares. */\nexport interface ConnectAppType extends http.RequestListener {\n  use: Function;\n}\n"], "names": [], "mappings": "AAAA"}