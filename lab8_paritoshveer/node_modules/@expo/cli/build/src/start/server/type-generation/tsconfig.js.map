{"version": 3, "sources": ["../../../../../src/start/server/type-generation/tsconfig.ts"], "sourcesContent": ["import JsonFile, { JSONObject } from '@expo/json-file';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport { Log } from '../../../log';\n\n/**\n * Force updates a project tsconfig with Expo values.\n */\nexport async function forceUpdateTSConfig(projectRoot: string) {\n  // This runs after the TypeScript prerequisite, so we know the tsconfig.json exists\n  const tsConfigPath = path.join(projectRoot, 'tsconfig.json');\n  const { tsConfig, updates } = getTSConfigUpdates(\n    JsonFile.read(tsConfigPath, {\n      json5: true,\n    })\n  );\n\n  await writeUpdates(tsConfigPath, tsConfig, updates);\n}\n\nexport function getTSConfigUpdates(tsConfig: JSONObject) {\n  const updates = new Set<string>();\n\n  if (!tsConfig.include) {\n    tsConfig.include = ['**/*.ts', '**/*.tsx', '.expo/types/**/*.ts', 'expo-env.d.ts'];\n    updates.add('include');\n  } else if (Array.isArray(tsConfig.include)) {\n    if (!tsConfig.include.includes('.expo/types/**/*.ts')) {\n      tsConfig.include = [...tsConfig.include, '.expo/types/**/*.ts'];\n      updates.add('include');\n    }\n\n    if (!tsConfig.include.includes('expo-env.d.ts')) {\n      tsConfig.include = [...tsConfig.include, 'expo-env.d.ts'];\n      updates.add('include');\n    }\n  }\n\n  return { tsConfig, updates };\n}\n\nexport async function forceRemovalTSConfig(projectRoot: string) {\n  // This runs after the TypeScript prerequisite, so we know the tsconfig.json exists\n  const tsConfigPath = path.join(projectRoot, 'tsconfig.json');\n  const { tsConfig, updates } = getTSConfigRemoveUpdates(\n    JsonFile.read(tsConfigPath, {\n      json5: true,\n    })\n  );\n\n  await writeUpdates(tsConfigPath, tsConfig, updates);\n}\n\nexport function getTSConfigRemoveUpdates(tsConfig: JSONObject) {\n  const updates = new Set<string>();\n\n  if (Array.isArray(tsConfig.include)) {\n    const filtered = (tsConfig.include as string[]).filter(\n      (i) => i !== 'expo-env.d.ts' && i !== '.expo/types/**/*.ts'\n    );\n\n    if (filtered.length !== tsConfig.include.length) {\n      updates.add('include');\n    }\n\n    tsConfig.include = filtered;\n  }\n\n  return { tsConfig, updates };\n}\n\nasync function writeUpdates(tsConfigPath: string, tsConfig: JSONObject, updates: Set<string>) {\n  if (updates.size) {\n    await JsonFile.writeAsync(tsConfigPath, tsConfig);\n    for (const update of updates) {\n      Log.log(\n        chalk`{bold TypeScript}: The {cyan tsconfig.json#${update}} property has been updated`\n      );\n    }\n  }\n}\n"], "names": ["forceUpdateTSConfig", "getTSConfigUpdates", "forceRemovalTSConfig", "getTSConfigRemoveUpdates", "projectRoot", "tsConfigPath", "path", "join", "tsConfig", "updates", "JsonFile", "read", "json5", "writeUpdates", "Set", "include", "add", "Array", "isArray", "includes", "filtered", "filter", "i", "length", "size", "writeAsync", "update", "Log", "log", "chalk"], "mappings": "AAAA;;;;;;;;;;;IASsBA,mBAAmB,MAAnBA,mBAAmB;IAYzBC,kBAAkB,MAAlBA,kBAAkB;IAqBZC,oBAAoB,MAApBA,oBAAoB;IAY1BC,wBAAwB,MAAxBA,wBAAwB;;;8DAtDH,iBAAiB;;;;;;;8DACpC,OAAO;;;;;;;8DACR,MAAM;;;;;;qBAEH,cAAc;;;;;;AAK3B,eAAeH,mBAAmB,CAACI,WAAmB,EAAE;IAC7D,mFAAmF;IACnF,MAAMC,YAAY,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACH,WAAW,EAAE,eAAe,CAAC,AAAC;IAC7D,MAAM,EAAEI,QAAQ,CAAA,EAAEC,OAAO,CAAA,EAAE,GAAGR,kBAAkB,CAC9CS,SAAQ,EAAA,QAAA,CAACC,IAAI,CAACN,YAAY,EAAE;QAC1BO,KAAK,EAAE,IAAI;KACZ,CAAC,CACH,AAAC;IAEF,MAAMC,YAAY,CAACR,YAAY,EAAEG,QAAQ,EAAEC,OAAO,CAAC,CAAC;AACtD,CAAC;AAEM,SAASR,kBAAkB,CAACO,QAAoB,EAAE;IACvD,MAAMC,OAAO,GAAG,IAAIK,GAAG,EAAU,AAAC;IAElC,IAAI,CAACN,QAAQ,CAACO,OAAO,EAAE;QACrBP,QAAQ,CAACO,OAAO,GAAG;YAAC,SAAS;YAAE,UAAU;YAAE,qBAAqB;YAAE,eAAe;SAAC,CAAC;QACnFN,OAAO,CAACO,GAAG,CAAC,SAAS,CAAC,CAAC;IACzB,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACV,QAAQ,CAACO,OAAO,CAAC,EAAE;QAC1C,IAAI,CAACP,QAAQ,CAACO,OAAO,CAACI,QAAQ,CAAC,qBAAqB,CAAC,EAAE;YACrDX,QAAQ,CAACO,OAAO,GAAG;mBAAIP,QAAQ,CAACO,OAAO;gBAAE,qBAAqB;aAAC,CAAC;YAChEN,OAAO,CAACO,GAAG,CAAC,SAAS,CAAC,CAAC;QACzB,CAAC;QAED,IAAI,CAACR,QAAQ,CAACO,OAAO,CAACI,QAAQ,CAAC,eAAe,CAAC,EAAE;YAC/CX,QAAQ,CAACO,OAAO,GAAG;mBAAIP,QAAQ,CAACO,OAAO;gBAAE,eAAe;aAAC,CAAC;YAC1DN,OAAO,CAACO,GAAG,CAAC,SAAS,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAED,OAAO;QAAER,QAAQ;QAAEC,OAAO;KAAE,CAAC;AAC/B,CAAC;AAEM,eAAeP,oBAAoB,CAACE,WAAmB,EAAE;IAC9D,mFAAmF;IACnF,MAAMC,YAAY,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACH,WAAW,EAAE,eAAe,CAAC,AAAC;IAC7D,MAAM,EAAEI,QAAQ,CAAA,EAAEC,OAAO,CAAA,EAAE,GAAGN,wBAAwB,CACpDO,SAAQ,EAAA,QAAA,CAACC,IAAI,CAACN,YAAY,EAAE;QAC1BO,KAAK,EAAE,IAAI;KACZ,CAAC,CACH,AAAC;IAEF,MAAMC,YAAY,CAACR,YAAY,EAAEG,QAAQ,EAAEC,OAAO,CAAC,CAAC;AACtD,CAAC;AAEM,SAASN,wBAAwB,CAACK,QAAoB,EAAE;IAC7D,MAAMC,OAAO,GAAG,IAAIK,GAAG,EAAU,AAAC;IAElC,IAAIG,KAAK,CAACC,OAAO,CAACV,QAAQ,CAACO,OAAO,CAAC,EAAE;QACnC,MAAMK,QAAQ,GAAG,AAACZ,QAAQ,CAACO,OAAO,CAAcM,MAAM,CACpD,CAACC,CAAC,GAAKA,CAAC,KAAK,eAAe,IAAIA,CAAC,KAAK,qBAAqB,CAC5D,AAAC;QAEF,IAAIF,QAAQ,CAACG,MAAM,KAAKf,QAAQ,CAACO,OAAO,CAACQ,MAAM,EAAE;YAC/Cd,OAAO,CAACO,GAAG,CAAC,SAAS,CAAC,CAAC;QACzB,CAAC;QAEDR,QAAQ,CAACO,OAAO,GAAGK,QAAQ,CAAC;IAC9B,CAAC;IAED,OAAO;QAAEZ,QAAQ;QAAEC,OAAO;KAAE,CAAC;AAC/B,CAAC;AAED,eAAeI,YAAY,CAACR,YAAoB,EAAEG,QAAoB,EAAEC,OAAoB,EAAE;IAC5F,IAAIA,OAAO,CAACe,IAAI,EAAE;QAChB,MAAMd,SAAQ,EAAA,QAAA,CAACe,UAAU,CAACpB,YAAY,EAAEG,QAAQ,CAAC,CAAC;QAClD,KAAK,MAAMkB,MAAM,IAAIjB,OAAO,CAAE;YAC5BkB,IAAG,IAAA,CAACC,GAAG,CACLC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,2CAA2C,EAAEH,MAAM,CAAC,2BAA2B,CAAC,CACvF,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC"}