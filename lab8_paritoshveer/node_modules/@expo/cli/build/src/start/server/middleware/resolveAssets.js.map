{"version": 3, "sources": ["../../../../../src/start/server/middleware/resolveAssets.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nimport { getAssetSchemasAsync } from '../../../api/getExpoSchema';\nimport { BundleAssetWithFileHashes } from '../../../export/saveAssets';\nimport * as Log from '../../../log';\nimport { fileExistsAsync } from '../../../utils/dir';\nimport { CommandError } from '../../../utils/errors';\nimport { get, set } from '../../../utils/obj';\nimport { validateUrl } from '../../../utils/url';\n\ntype ManifestAsset = { fileHashes: string[]; files: string[]; hash: string };\n\nexport type Asset = ManifestAsset | BundleAssetWithFileHashes;\n\ntype ManifestResolutionError = Error & {\n  localAssetPath?: string;\n  manifestField?: string;\n};\n\n/** Inline the contents of each platform's `googleServicesFile` so runtimes can access them. */\nexport async function resolveGoogleServicesFile(\n  projectRoot: string,\n  manifest: Partial<Pick<ExpoConfig, 'android' | 'ios'>>\n) {\n  if (manifest.android?.googleServicesFile) {\n    try {\n      const contents = await fs.readFile(\n        path.resolve(projectRoot, manifest.android.googleServicesFile),\n        'utf8'\n      );\n      manifest.android.googleServicesFile = contents;\n    } catch {\n      Log.warn(\n        `Could not parse Expo config: android.googleServicesFile: \"${manifest.android.googleServicesFile}\"`\n      );\n      // Delete the field so Expo Go doesn't attempt to read it.\n      delete manifest.android.googleServicesFile;\n    }\n  }\n  if (manifest.ios?.googleServicesFile) {\n    try {\n      const contents = await fs.readFile(\n        path.resolve(projectRoot, manifest.ios.googleServicesFile),\n        'base64'\n      );\n      manifest.ios.googleServicesFile = contents;\n    } catch {\n      Log.warn(\n        `Could not parse Expo config: ios.googleServicesFile: \"${manifest.ios.googleServicesFile}\"`\n      );\n      // Delete the field so Expo Go doesn't attempt to read it.\n      delete manifest.ios.googleServicesFile;\n    }\n  }\n  return manifest;\n}\n\n/**\n * Get all fields in the manifest that match assets, then filter the ones that aren't set.\n *\n * @param manifest\n * @returns Asset fields that the user has set like [\"icon\", \"splash.image\", ...]\n */\nexport async function getAssetFieldPathsForManifestAsync(manifest: ExpoConfig): Promise<string[]> {\n  // String array like [\"icon\", \"notification.icon\", \"loading.icon\", \"loading.backgroundImage\", \"ios.icon\", ...]\n  const sdkAssetFieldPaths = await getAssetSchemasAsync(manifest.sdkVersion);\n  return sdkAssetFieldPaths.filter((assetSchema) => get(manifest, assetSchema));\n}\n\n/** Resolve all assets in the app.json inline. */\nexport async function resolveManifestAssets(\n  projectRoot: string,\n  {\n    manifest,\n    resolver,\n    strict,\n  }: {\n    manifest: ExpoConfig;\n    resolver: (assetPath: string) => Promise<string>;\n    strict?: boolean;\n  }\n) {\n  try {\n    // Asset fields that the user has set like [\"icon\", \"splash.image\"]\n    const assetSchemas = await getAssetFieldPathsForManifestAsync(manifest);\n    // Get the URLs\n    const urls = await Promise.all(\n      assetSchemas.map(async (manifestField) => {\n        const pathOrURL = get(manifest, manifestField);\n        // URL\n        if (validateUrl(pathOrURL, { requireProtocol: true })) {\n          return pathOrURL;\n        }\n\n        // File path\n        if (await fileExistsAsync(path.resolve(projectRoot, pathOrURL))) {\n          return await resolver(pathOrURL);\n        }\n\n        // Unknown\n        const err: ManifestResolutionError = new CommandError(\n          'MANIFEST_ASSET',\n          'Could not resolve local asset: ' + pathOrURL\n        );\n        err.localAssetPath = pathOrURL;\n        err.manifestField = manifestField;\n        throw err;\n      })\n    );\n\n    // Set the corresponding URL fields\n    assetSchemas.forEach((manifestField, index: number) =>\n      set(manifest, `${manifestField}Url`, urls[index])\n    );\n  } catch (error: any) {\n    if (error.localAssetPath) {\n      Log.warn(\n        `Unable to resolve asset \"${error.localAssetPath}\" from \"${error.manifestField}\" in your app.json or app.config.js`\n      );\n    } else {\n      Log.warn(\n        `Warning: Unable to resolve manifest assets. Icons and fonts might not work. ${error.message}.`\n      );\n    }\n\n    if (strict) {\n      throw new CommandError(\n        'MANIFEST_ASSET',\n        'Failed to export manifest assets: ' + error.message\n      );\n    }\n  }\n}\n"], "names": ["resolveGoogleServicesFile", "getAssetFieldPathsForManifestAsync", "resolveManifestAssets", "projectRoot", "manifest", "android", "googleServicesFile", "contents", "fs", "readFile", "path", "resolve", "Log", "warn", "ios", "sdkAssetFieldPaths", "getAssetSchemasAsync", "sdkVersion", "filter", "assetSchema", "get", "resolver", "strict", "assetSchemas", "urls", "Promise", "all", "map", "manifestField", "pathOrURL", "validateUrl", "requireProtocol", "fileExistsAsync", "err", "CommandError", "localAssetPath", "for<PERSON>ach", "index", "set", "error", "message"], "mappings": "AAAA;;;;;;;;;;;IAsBsBA,yBAAyB,MAAzBA,yBAAyB;IA2CzBC,kCAAkC,MAAlCA,kCAAkC;IAOlCC,qBAAqB,MAArBA,qBAAqB;;;8DAvE5B,aAAa;;;;;;;8DACX,MAAM;;;;;;+BAEc,4BAA4B;2DAE5C,cAAc;qBACH,oBAAoB;wBACvB,uBAAuB;qBAC3B,oBAAoB;qBACjB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzC,eAAeF,yBAAyB,CAC7CG,WAAmB,EACnBC,QAAsD,EACtD;QACIA,GAAgB,EAehBA,IAAY;IAfhB,IAAIA,CAAAA,GAAgB,GAAhBA,QAAQ,CAACC,OAAO,SAAoB,GAApCD,KAAAA,CAAoC,GAApCA,GAAgB,CAAEE,kBAAkB,EAAE;QACxC,IAAI;YACF,MAAMC,QAAQ,GAAG,MAAMC,SAAE,EAAA,QAAA,CAACC,QAAQ,CAChCC,KAAI,EAAA,QAAA,CAACC,OAAO,CAACR,WAAW,EAAEC,QAAQ,CAACC,OAAO,CAACC,kBAAkB,CAAC,EAC9D,MAAM,CACP,AAAC;YACFF,QAAQ,CAACC,OAAO,CAACC,kBAAkB,GAAGC,QAAQ,CAAC;QACjD,EAAE,OAAM;YACNK,IAAG,CAACC,IAAI,CACN,CAAC,0DAA0D,EAAET,QAAQ,CAACC,OAAO,CAACC,kBAAkB,CAAC,CAAC,CAAC,CACpG,CAAC;YACF,0DAA0D;YAC1D,OAAOF,QAAQ,CAACC,OAAO,CAACC,kBAAkB,CAAC;QAC7C,CAAC;IACH,CAAC;IACD,IAAIF,CAAAA,IAAY,GAAZA,QAAQ,CAACU,GAAG,SAAoB,GAAhCV,KAAAA,CAAgC,GAAhCA,IAAY,CAAEE,kBAAkB,EAAE;QACpC,IAAI;YACF,MAAMC,SAAQ,GAAG,MAAMC,SAAE,EAAA,QAAA,CAACC,QAAQ,CAChCC,KAAI,EAAA,QAAA,CAACC,OAAO,CAACR,WAAW,EAAEC,QAAQ,CAACU,GAAG,CAACR,kBAAkB,CAAC,EAC1D,QAAQ,CACT,AAAC;YACFF,QAAQ,CAACU,GAAG,CAACR,kBAAkB,GAAGC,SAAQ,CAAC;QAC7C,EAAE,OAAM;YACNK,IAAG,CAACC,IAAI,CACN,CAAC,sDAAsD,EAAET,QAAQ,CAACU,GAAG,CAACR,kBAAkB,CAAC,CAAC,CAAC,CAC5F,CAAC;YACF,0DAA0D;YAC1D,OAAOF,QAAQ,CAACU,GAAG,CAACR,kBAAkB,CAAC;QACzC,CAAC;IACH,CAAC;IACD,OAAOF,QAAQ,CAAC;AAClB,CAAC;AAQM,eAAeH,kCAAkC,CAACG,QAAoB,EAAqB;IAChG,8GAA8G;IAC9G,MAAMW,kBAAkB,GAAG,MAAMC,IAAAA,cAAoB,qBAAA,EAACZ,QAAQ,CAACa,UAAU,CAAC,AAAC;IAC3E,OAAOF,kBAAkB,CAACG,MAAM,CAAC,CAACC,WAAW,GAAKC,IAAAA,IAAG,IAAA,EAAChB,QAAQ,EAAEe,WAAW,CAAC,CAAC,CAAC;AAChF,CAAC;AAGM,eAAejB,qBAAqB,CACzCC,WAAmB,EACnB,EACEC,QAAQ,CAAA,EACRiB,QAAQ,CAAA,EACRC,MAAM,CAAA,EAKP,EACD;IACA,IAAI;QACF,mEAAmE;QACnE,MAAMC,YAAY,GAAG,MAAMtB,kCAAkC,CAACG,QAAQ,CAAC,AAAC;QACxE,eAAe;QACf,MAAMoB,IAAI,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC5BH,YAAY,CAACI,GAAG,CAAC,OAAOC,aAAa,GAAK;YACxC,MAAMC,SAAS,GAAGT,IAAAA,IAAG,IAAA,EAAChB,QAAQ,EAAEwB,aAAa,CAAC,AAAC;YAC/C,MAAM;YACN,IAAIE,IAAAA,IAAW,YAAA,EAACD,SAAS,EAAE;gBAAEE,eAAe,EAAE,IAAI;aAAE,CAAC,EAAE;gBACrD,OAAOF,SAAS,CAAC;YACnB,CAAC;YAED,YAAY;YACZ,IAAI,MAAMG,IAAAA,IAAe,gBAAA,EAACtB,KAAI,EAAA,QAAA,CAACC,OAAO,CAACR,WAAW,EAAE0B,SAAS,CAAC,CAAC,EAAE;gBAC/D,OAAO,MAAMR,QAAQ,CAACQ,SAAS,CAAC,CAAC;YACnC,CAAC;YAED,UAAU;YACV,MAAMI,GAAG,GAA4B,IAAIC,OAAY,aAAA,CACnD,gBAAgB,EAChB,iCAAiC,GAAGL,SAAS,CAC9C,AAAC;YACFI,GAAG,CAACE,cAAc,GAAGN,SAAS,CAAC;YAC/BI,GAAG,CAACL,aAAa,GAAGA,aAAa,CAAC;YAClC,MAAMK,GAAG,CAAC;QACZ,CAAC,CAAC,CACH,AAAC;QAEF,mCAAmC;QACnCV,YAAY,CAACa,OAAO,CAAC,CAACR,aAAa,EAAES,KAAa,GAChDC,IAAAA,IAAG,IAAA,EAAClC,QAAQ,EAAE,CAAC,EAAEwB,aAAa,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAACa,KAAK,CAAC,CAAC,CAClD,CAAC;IACJ,EAAE,OAAOE,KAAK,EAAO;QACnB,IAAIA,KAAK,CAACJ,cAAc,EAAE;YACxBvB,IAAG,CAACC,IAAI,CACN,CAAC,yBAAyB,EAAE0B,KAAK,CAACJ,cAAc,CAAC,QAAQ,EAAEI,KAAK,CAACX,aAAa,CAAC,mCAAmC,CAAC,CACpH,CAAC;QACJ,OAAO;YACLhB,IAAG,CAACC,IAAI,CACN,CAAC,4EAA4E,EAAE0B,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAChG,CAAC;QACJ,CAAC;QAED,IAAIlB,MAAM,EAAE;YACV,MAAM,IAAIY,OAAY,aAAA,CACpB,gBAAgB,EAChB,oCAAoC,GAAGK,KAAK,CAACC,OAAO,CACrD,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC"}