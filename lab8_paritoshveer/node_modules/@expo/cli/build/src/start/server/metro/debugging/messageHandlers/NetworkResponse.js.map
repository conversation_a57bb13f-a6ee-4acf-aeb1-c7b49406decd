{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/NetworkResponse.ts"], "sourcesContent": ["import type { Protocol } from 'devtools-protocol';\n\nimport { MessageHandler } from '../MessageHandler';\nimport type {\n  CdpMessage,\n  DeviceRequest,\n  DebuggerRequest,\n  DebuggerR<PERSON>ponse,\n  DeviceResponse,\n} from '../types';\n\nexport class NetworkResponseHandler extends MessageHandler {\n  /** All known responses, mapped by request id */\n  storage = new Map<string, DebuggerResponse<NetworkGetResponseBody>['result']>();\n\n  isEnabled() {\n    return this.page.capabilities.nativeNetworkInspection !== true;\n  }\n\n  handleDeviceMessage(message: DeviceRequest<NetworkReceivedResponseBody>) {\n    if (message.method === 'Expo(Network.receivedResponseBody)') {\n      const { requestId, ...requestInfo } = message.params;\n      this.storage.set(requestId, requestInfo);\n      return true;\n    }\n\n    return false;\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<NetworkGetResponseBody>) {\n    if (\n      message.method === 'Network.getResponseBody' &&\n      this.storage.has(message.params.requestId)\n    ) {\n      return this.sendToDebugger<DeviceResponse<NetworkGetResponseBody>>({\n        id: message.id,\n        result: this.storage.get(message.params.requestId)!,\n      });\n    }\n\n    return false;\n  }\n}\n\n/** Custom message to transfer the response body data to the proxy */\nexport type NetworkReceivedResponseBody = CdpMessage<\n  'Expo(Network.receivedResponseBody)',\n  Protocol.Network.GetResponseBodyRequest & Protocol.Network.GetResponseBodyResponse,\n  never\n>;\n\n/** @see https://chromedevtools.github.io/devtools-protocol/1-2/Network/#method-getResponseBody */\nexport type NetworkGetResponseBody = CdpMessage<\n  'Network.getResponseBody',\n  Protocol.Network.GetResponseBodyRequest,\n  Protocol.Network.GetResponseBodyResponse\n>;\n"], "names": ["NetworkResponseHandler", "MessageHandler", "storage", "Map", "isEnabled", "page", "capabilities", "nativeNetworkInspection", "handleDeviceMessage", "message", "method", "requestId", "requestInfo", "params", "set", "handleDebuggerMessage", "has", "sendToDebugger", "id", "result", "get"], "mappings": "AAAA;;;;+BAWa<PERSON>,wBAAsB;;aAAtBA,sBAAsB;;gCATJ,mBAAmB;AAS3C,MAAMA,sBAAsB,SAASC,eAAc,eAAA;IACxD,8CAA8C,GAC9CC,OAAO,GAAG,IAAIC,GAAG,EAA8D,CAAC;IAEhFC,SAAS,GAAG;QACV,OAAO,IAAI,CAACC,IAAI,CAACC,YAAY,CAACC,uBAAuB,KAAK,IAAI,CAAC;IACjE;IAEAC,mBAAmB,CAACC,OAAmD,EAAE;QACvE,IAAIA,OAAO,CAACC,MAAM,KAAK,oCAAoC,EAAE;YAC3D,MAAM,EAAEC,SAAS,CAAA,EAAE,GAAGC,WAAW,EAAE,GAAGH,OAAO,CAACI,MAAM,AAAC;YACrD,IAAI,CAACX,OAAO,CAACY,GAAG,CAACH,SAAS,EAAEC,WAAW,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf;IAEAG,qBAAqB,CAACN,OAAgD,EAAE;QACtE,IACEA,OAAO,CAACC,MAAM,KAAK,yBAAyB,IAC5C,IAAI,CAACR,OAAO,CAACc,GAAG,CAACP,OAAO,CAACI,MAAM,CAACF,SAAS,CAAC,EAC1C;YACA,OAAO,IAAI,CAACM,cAAc,CAAyC;gBACjEC,EAAE,EAAET,OAAO,CAACS,EAAE;gBACdC,MAAM,EAAE,IAAI,CAACjB,OAAO,CAACkB,GAAG,CAACX,OAAO,CAACI,MAAM,CAACF,SAAS,CAAC;aACnD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACf;CACD"}