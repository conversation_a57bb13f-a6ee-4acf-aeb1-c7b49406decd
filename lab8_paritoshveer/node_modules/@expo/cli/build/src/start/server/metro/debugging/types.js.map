{"version": 3, "sources": ["../../../../../../src/start/server/metro/debugging/types.ts"], "sourcesContent": ["import type { CustomMessageHandlerConnection } from '@react-native/dev-middleware';\n\nexport type Connection = CustomMessageHandlerConnection;\nexport type DeviceMetadata = Connection['device'];\nexport type DebuggerMetadata = Connection['debugger'];\nexport type Page = Connection['page'];\nexport type TargetCapabilityFlags = Page['capabilities'];\n\n/**\n * The outline of a basic Chrome DevTools Protocol request, either from device or debugger.\n * Both the request and response parameters could be optional, use `never` to enforce these fields.\n */\nexport type CdpMessage<\n  Method extends string = string,\n  Request extends object = object,\n  Response extends object = object,\n> = {\n  id: number;\n  method: Method;\n  params: Request;\n  result: Response;\n};\n\nexport type DeviceRequest<M extends CdpMessage = CdpMessage> = Pick<M, 'method' | 'params'>;\nexport type DeviceResponse<M extends CdpMessage = CdpMessage> = Pick<M, 'id' | 'result'>;\n\nexport type DebuggerRequest<M extends CdpMessage = CdpMessage> = Pick<\n  M,\n  'id' | 'method' | 'params'\n>;\nexport type DebuggerResponse<M extends CdpMessage = CdpMessage> = Pick<M, 'result'>;\n"], "names": [], "mappings": "AAAA"}