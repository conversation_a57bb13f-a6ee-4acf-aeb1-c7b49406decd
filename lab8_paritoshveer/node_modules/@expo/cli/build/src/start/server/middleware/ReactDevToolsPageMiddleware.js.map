{"version": 3, "sources": ["../../../../../src/start/server/middleware/ReactDevToolsPageMiddleware.ts"], "sourcesContent": ["import assert from 'assert';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport send from 'send';\n\nimport { ExpoMiddleware } from './ExpoMiddleware';\nimport { ServerRequest, ServerResponse } from './server.types';\n\nexport const ReactDevToolsEndpoint = '/_expo/react-devtools';\n\nexport class ReactDevToolsPageMiddleware extends ExpoMiddleware {\n  constructor(projectRoot: string) {\n    super(projectRoot, [ReactDevToolsEndpoint]);\n  }\n\n  override shouldHandleRequest(req: ServerRequest): boolean {\n    if (!req.url?.startsWith(ReactDevToolsEndpoint)) {\n      return false;\n    }\n    return true;\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    assert(req.headers.host, 'Request headers must include host');\n    const { pathname } = new URL(req.url ?? '/', `http://${req.headers.host}`);\n    const requestPath = pathname.substring(ReactDevToolsEndpoint.length) || '/';\n\n    const entryPath =\n      // Production: This will resolve when installed in the project.\n      resolveFrom.silent(this.projectRoot, 'expo/static/react-devtools-page/index.html') ??\n      // Development: This will resolve when testing locally.\n      path.resolve(__dirname, '../../../../../static/react-devtools-page/index.html');\n\n    const staticRoot = path.dirname(entryPath);\n    send(req, requestPath, { root: staticRoot }).pipe(res);\n  }\n}\n"], "names": ["ReactDevToolsEndpoint", "ReactDevToolsPageMiddleware", "ExpoMiddleware", "constructor", "projectRoot", "shouldHandleRequest", "req", "url", "startsWith", "handleRequestAsync", "res", "assert", "headers", "host", "pathname", "URL", "requestPath", "substring", "length", "resolveFrom", "entryPath", "silent", "path", "resolve", "__dirname", "staticRoot", "dirname", "send", "root", "pipe"], "mappings": "AAAA;;;;;;;;;;;IAQaA,qBAAqB,MAArBA,qBAAqB;IAErBC,2BAA2B,MAA3BA,2BAA2B;;;8DAVrB,QAAQ;;;;;;;8DACV,MAAM;;;;;;;8DACC,cAAc;;;;;;;8DACrB,MAAM;;;;;;gCAEQ,kBAAkB;;;;;;AAG1C,MAAMD,qBAAqB,GAAG,uBAAuB,AAAC;AAEtD,MAAMC,2BAA2B,SAASC,eAAc,eAAA;IAC7DC,YAAYC,WAAmB,CAAE;QAC/B,KAAK,CAACA,WAAW,EAAE;YAACJ,qBAAqB;SAAC,CAAC,CAAC;IAC9C;IAESK,mBAAmB,CAACC,GAAkB,EAAW;YACnDA,GAAO;QAAZ,IAAI,EAACA,CAAAA,GAAO,GAAPA,GAAG,CAACC,GAAG,SAAY,GAAnBD,KAAAA,CAAmB,GAAnBA,GAAO,CAAEE,UAAU,CAACR,qBAAqB,CAAC,CAAA,EAAE;YAC/C,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd;UAEMS,kBAAkB,CAACH,GAAkB,EAAEI,GAAmB,EAAiB;QAC/EC,IAAAA,OAAM,EAAA,QAAA,EAACL,GAAG,CAACM,OAAO,CAACC,IAAI,EAAE,mCAAmC,CAAC,CAAC;YACjCP,IAAO;QAApC,MAAM,EAAEQ,QAAQ,CAAA,EAAE,GAAG,IAAIC,GAAG,CAACT,CAAAA,IAAO,GAAPA,GAAG,CAACC,GAAG,YAAPD,IAAO,GAAI,GAAG,EAAE,CAAC,OAAO,EAAEA,GAAG,CAACM,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,AAAC;QAC3E,MAAMG,WAAW,GAAGF,QAAQ,CAACG,SAAS,CAACjB,qBAAqB,CAACkB,MAAM,CAAC,IAAI,GAAG,AAAC;YAG1E,+DAA+D;QAC/DC,GAAkF;QAFpF,MAAMC,SAAS,GAEbD,CAAAA,GAAkF,GAAlFA,YAAW,EAAA,QAAA,CAACE,MAAM,CAAC,IAAI,CAACjB,WAAW,EAAE,4CAA4C,CAAC,YAAlFe,GAAkF,GAClF,uDAAuD;QACvDG,KAAI,EAAA,QAAA,CAACC,OAAO,CAACC,SAAS,EAAE,sDAAsD,CAAC,AAAC;QAElF,MAAMC,UAAU,GAAGH,KAAI,EAAA,QAAA,CAACI,OAAO,CAACN,SAAS,CAAC,AAAC;QAC3CO,IAAAA,KAAI,EAAA,QAAA,EAACrB,GAAG,EAAEU,WAAW,EAAE;YAAEY,IAAI,EAAEH,UAAU;SAAE,CAAC,CAACI,IAAI,CAACnB,GAAG,CAAC,CAAC;IACzD;CACD"}