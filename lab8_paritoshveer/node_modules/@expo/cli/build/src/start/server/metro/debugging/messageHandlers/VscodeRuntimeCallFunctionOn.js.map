{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/VscodeRuntimeCallFunctionOn.ts"], "sourcesContent": ["import type Protocol from 'devtools-protocol';\n\nimport { MessageHand<PERSON> } from '../MessageHandler';\nimport { getDebuggerType } from '../getDebuggerType';\nimport type { CdpMessage, DebuggerRequest, DeviceResponse } from '../types';\n\n/**\n * Vscode is trying to inject a script to fetch information about \"Stringy\" variables.\n * Unfortunately, this script causes a <PERSON><PERSON> exception and crashes the app.\n *\n * @see https://github.com/expo/vscode-expo/issues/231\n * @see https://github.com/microsoft/vscode-js-debug/blob/dcccaf3972d675cc1e5c776450bb4c3dc8c178c1/src/adapter/stackTrace.ts#L319-L324\n */\nexport class VscodeRuntimeCallFunctionOnHandler extends MessageHandler {\n  isEnabled() {\n    return getDebuggerType(this.debugger.userAgent) === 'vscode';\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<RuntimeCallFunctionOn>) {\n    if (message.method === 'Runtime.callFunctionOn') {\n      return this.sendToDebugger<DeviceResponse<RuntimeCallFunctionOn>>({\n        id: message.id,\n        result: {\n          // We don't know the `type` and vscode allows `type: undefined`\n          result: { objectId: message.params.objectId } as any,\n        },\n      });\n    }\n\n    return false;\n  }\n}\n\n/** @see https://chromedevtools.github.io/devtools-protocol/v8/Runtime/#method-callFunctionOn */\nexport type RuntimeCallFunctionOn = CdpMessage<\n  'Runtime.callFunctionOn',\n  Protocol.Runtime.CallFunctionOnRequest,\n  Protocol.Runtime.CallFunctionOnResponse\n>;\n"], "names": ["VscodeRuntimeCallFunctionOnHandler", "MessageHandler", "isEnabled", "getDebuggerType", "debugger", "userAgent", "handleDebuggerMessage", "message", "method", "sendToDebugger", "id", "result", "objectId", "params"], "mappings": "AAAA;;;;+BAaaA,oCAAkC;;aAAlCA,kCAAkC;;gCAXhB,mBAAmB;iCAClB,oBAAoB;AAU7C,MAAMA,kCAAkC,SAASC,eAAc,eAAA;IACpEC,SAAS,GAAG;QACV,OAAOC,IAAAA,gBAAe,gBAAA,EAAC,IAAI,CAACC,QAAQ,CAACC,SAAS,CAAC,KAAK,QAAQ,CAAC;IAC/D;IAEAC,qBAAqB,CAACC,OAA+C,EAAE;QACrE,IAAIA,OAAO,CAACC,MAAM,KAAK,wBAAwB,EAAE;YAC/C,OAAO,IAAI,CAACC,cAAc,CAAwC;gBAChEC,EAAE,EAAEH,OAAO,CAACG,EAAE;gBACdC,MAAM,EAAE;oBACN,+DAA+D;oBAC/DA,MAAM,EAAE;wBAAEC,QAAQ,EAAEL,OAAO,CAACM,MAAM,CAACD,QAAQ;qBAAE;iBAC9C;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACf;CACD"}