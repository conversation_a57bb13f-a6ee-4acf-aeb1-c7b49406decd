{"version": 3, "sources": ["../../../../../src/start/server/webpack/tls.ts"], "sourcesContent": ["import { certificateFor } from '@expo/devcert';\nimport chalk from 'chalk';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nimport * as Log from '../../../log';\nimport { ensureDirectoryAsync } from '../../../utils/dir';\nimport { ensureDotExpoProjectDirectoryInitialized } from '../../project/dotExpo';\n\n// TODO: Move to doctor as a prereq.\n\n/** Ensure TLS is setup and environment variables are set. */\nexport async function ensureEnvironmentSupportsTLSAsync(projectRoot: string) {\n  if (!process.env.SSL_CRT_FILE || !process.env.SSL_KEY_FILE) {\n    const tls = await getTLSCertAsync(projectRoot);\n    if (tls) {\n      process.env.SSL_CRT_FILE = tls.certPath;\n      process.env.SSL_KEY_FILE = tls.keyPath;\n    }\n  }\n}\n\n/** Create TLS and write to files in the temporary directory. Exposed for testing. */\nexport async function getTLSCertAsync(\n  projectRoot: string\n): Promise<{ keyPath: string; certPath: string } | false> {\n  Log.log(\n    chalk`Creating TLS certificate for localhost. {dim This functionality may not work on all computers.}`\n  );\n\n  const name = 'localhost';\n  const result = await certificateFor(name);\n  if (result) {\n    const dotExpoDir = ensureDotExpoProjectDirectoryInitialized(projectRoot);\n\n    const { key, cert } = result;\n    const folder = path.join(dotExpoDir, 'tls');\n    const keyPath = path.join(folder, `key-${name}.pem`);\n    const certPath = path.join(folder, `cert-${name}.pem`);\n\n    await ensureDirectoryAsync(folder);\n    await Promise.allSettled([fs.writeFile(keyPath, key), fs.writeFile(certPath, cert)]);\n\n    return {\n      keyPath,\n      certPath,\n    };\n  }\n  return result;\n}\n"], "names": ["ensureEnvironmentSupportsTLSAsync", "getTLSCertAsync", "projectRoot", "process", "env", "SSL_CRT_FILE", "SSL_KEY_FILE", "tls", "certPath", "keyP<PERSON>", "Log", "log", "chalk", "name", "result", "certificateFor", "dotExpoDir", "ensureDotExpoProjectDirectoryInitialized", "key", "cert", "folder", "path", "join", "ensureDirectoryAsync", "Promise", "allSettled", "fs", "writeFile"], "mappings": "AAAA;;;;;;;;;;;IAYsBA,iCAAiC,MAAjCA,iCAAiC;IAWjCC,eAAe,MAAfA,eAAe;;;yBAvBN,eAAe;;;;;;;8DAC5B,OAAO;;;;;;;8DACV,aAAa;;;;;;;8DACX,MAAM;;;;;;2DAEF,cAAc;qBACE,oBAAoB;yBACA,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKzE,eAAeD,iCAAiC,CAACE,WAAmB,EAAE;IAC3E,IAAI,CAACC,OAAO,CAACC,GAAG,CAACC,YAAY,IAAI,CAACF,OAAO,CAACC,GAAG,CAACE,YAAY,EAAE;QAC1D,MAAMC,GAAG,GAAG,MAAMN,eAAe,CAACC,WAAW,CAAC,AAAC;QAC/C,IAAIK,GAAG,EAAE;YACPJ,OAAO,CAACC,GAAG,CAACC,YAAY,GAAGE,GAAG,CAACC,QAAQ,CAAC;YACxCL,OAAO,CAACC,GAAG,CAACE,YAAY,GAAGC,GAAG,CAACE,OAAO,CAAC;QACzC,CAAC;IACH,CAAC;AACH,CAAC;AAGM,eAAeR,eAAe,CACnCC,WAAmB,EACqC;IACxDQ,IAAG,CAACC,GAAG,CACLC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,+FAA+F,CAAC,CACvG,CAAC;IAEF,MAAMC,IAAI,GAAG,WAAW,AAAC;IACzB,MAAMC,MAAM,GAAG,MAAMC,IAAAA,QAAc,EAAA,eAAA,EAACF,IAAI,CAAC,AAAC;IAC1C,IAAIC,MAAM,EAAE;QACV,MAAME,UAAU,GAAGC,IAAAA,QAAwC,yCAAA,EAACf,WAAW,CAAC,AAAC;QAEzE,MAAM,EAAEgB,GAAG,CAAA,EAAEC,IAAI,CAAA,EAAE,GAAGL,MAAM,AAAC;QAC7B,MAAMM,MAAM,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACN,UAAU,EAAE,KAAK,CAAC,AAAC;QAC5C,MAAMP,OAAO,GAAGY,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,MAAM,EAAE,CAAC,IAAI,EAAEP,IAAI,CAAC,IAAI,CAAC,CAAC,AAAC;QACrD,MAAML,QAAQ,GAAGa,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,MAAM,EAAE,CAAC,KAAK,EAAEP,IAAI,CAAC,IAAI,CAAC,CAAC,AAAC;QAEvD,MAAMU,IAAAA,IAAoB,qBAAA,EAACH,MAAM,CAAC,CAAC;QACnC,MAAMI,OAAO,CAACC,UAAU,CAAC;YAACC,SAAE,EAAA,QAAA,CAACC,SAAS,CAAClB,OAAO,EAAES,GAAG,CAAC;YAAEQ,SAAE,EAAA,QAAA,CAACC,SAAS,CAACnB,QAAQ,EAAEW,IAAI,CAAC;SAAC,CAAC,CAAC;QAErF,OAAO;YACLV,OAAO;YACPD,QAAQ;SACT,CAAC;IACJ,CAAC;IACD,OAAOM,MAAM,CAAC;AAChB,CAAC"}