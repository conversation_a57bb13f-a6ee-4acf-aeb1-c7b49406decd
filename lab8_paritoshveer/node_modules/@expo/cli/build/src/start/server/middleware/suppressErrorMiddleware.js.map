{"version": 3, "sources": ["../../../../../src/start/server/middleware/suppressErrorMiddleware.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http';\n\n// Middleware to suppress `EISDIR` error when opening javascript inspector in remote debugging.\n// A workaround for https://github.com/facebook/react-native/issues/28844\n// The root cause is that metro cannot serve sourcemap requests for /debugger-ui/\nexport function suppressRemoteDebuggingErrorMiddleware(\n  req: IncomingMessage,\n  res: ServerResponse,\n  next: (err?: Error) => void\n) {\n  if (req.url?.match(/\\/debugger-ui\\/.+\\.map$/)) {\n    res.writeHead(404);\n    res.end('Sourcemap for /debugger-ui/ is not supported.');\n    return;\n  }\n  next();\n}\n"], "names": ["suppressRemoteDebuggingErrorMiddleware", "req", "res", "next", "url", "match", "writeHead", "end"], "mappings": "AAAA;;;;+BAKg<PERSON>,wCAAsC;;aAAtCA,sCAAsC;;AAA/C,SAASA,sCAAsC,CACpDC,GAAoB,EACpBC,GAAmB,EACnBC,IAA2B,EAC3B;QACIF,GAAO;IAAX,IAAIA,CAAAA,GAAO,GAAPA,GAAG,CAACG,GAAG,SAAO,GAAdH,KAAAA,CAAc,GAAdA,GAAO,CAAEI,KAAK,2BAA2B,EAAE;QAC7CH,GAAG,CAACI,SAAS,CAAC,GAAG,CAAC,CAAC;QACnBJ,GAAG,CAACK,GAAG,CAAC,+CAA+C,CAAC,CAAC;QACzD,OAAO;IACT,CAAC;IACDJ,IAAI,EAAE,CAAC;AACT,CAAC"}