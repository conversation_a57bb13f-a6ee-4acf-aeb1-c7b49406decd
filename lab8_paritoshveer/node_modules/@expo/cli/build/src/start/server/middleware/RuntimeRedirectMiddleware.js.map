{"version": 3, "sources": ["../../../../../src/start/server/middleware/RuntimeRedirectMiddleware.ts"], "sourcesContent": ["import { parse } from 'url';\n\nimport { disableResponseCache, ExpoMiddleware } from './ExpoMiddleware';\nimport {\n  assertMissingRuntimePlatform,\n  assertRuntimePlatform,\n  parsePlatformHeader,\n  resolvePlatformFromUserAgentHeader,\n  RuntimePlatform,\n} from './resolvePlatform';\nimport { ServerRequest, ServerResponse } from './server.types';\nimport * as Log from '../../../log';\n\nconst debug = require('debug')(\n  'expo:start:server:middleware:runtimeRedirect'\n) as typeof console.log;\n\n/** Runtime to target: expo = Expo Go, custom = Dev Client. */\ntype RuntimeTarget = 'expo' | 'custom';\n\nexport type DeepLinkHandler = (props: {\n  runtime: RuntimeTarget;\n  platform: RuntimePlatform;\n}) => void | Promise<void>;\n\nexport class RuntimeRedirectMiddleware extends ExpoMiddleware {\n  constructor(\n    protected projectRoot: string,\n    protected options: {\n      onDeepLink: DeepLinkHandler;\n      getLocation: (props: { runtime: RuntimeTarget }) => string | null | undefined;\n    }\n  ) {\n    super(projectRoot, ['/_expo/link']);\n  }\n\n  async handleRequestAsync(req: ServerRequest, res: ServerResponse): Promise<void> {\n    const { query } = parse(req.url!, true);\n    const isDevClient = query['choice'] === 'expo-dev-client';\n    const platform = parsePlatformHeader(req) ?? resolvePlatformFromUserAgentHeader(req);\n    assertMissingRuntimePlatform(platform);\n    assertRuntimePlatform(platform);\n    const runtime = isDevClient ? 'custom' : 'expo';\n\n    debug(`props:`, { platform, runtime });\n\n    this.options.onDeepLink({ runtime, platform });\n\n    const redirect = this.options.getLocation({ runtime });\n    if (!redirect) {\n      Log.warn(\n        `[redirect middleware]: Unable to determine redirect location for runtime '${runtime}' and platform '${platform}'`\n      );\n      res.statusCode = 404;\n      res.end();\n      return;\n    }\n    debug('Redirect ->', redirect);\n    res.setHeader('Location', redirect);\n\n    // Disable caching\n    disableResponseCache(res);\n\n    // 'Temporary Redirect'\n    res.statusCode = 307;\n    res.end();\n  }\n}\n"], "names": ["RuntimeRedirectMiddleware", "debug", "require", "ExpoMiddleware", "constructor", "projectRoot", "options", "handleRequestAsync", "req", "res", "query", "parse", "url", "isDevClient", "parsePlatformHeader", "platform", "resolvePlatformFromUserAgentHeader", "assertMissingRuntimePlatform", "assertRuntimePlatform", "runtime", "onDeepLink", "redirect", "getLocation", "Log", "warn", "statusCode", "end", "<PERSON><PERSON><PERSON><PERSON>", "disableResponseCache"], "mappings": "AAAA;;;;+BAyBaA,2BAAyB;;aAAzBA,yBAAyB;;;yBAzBhB,KAAK;;;;;;gCAE0B,kBAAkB;iCAOhE,mBAAmB;2DAEL,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAC5B,8CAA8C,CAC/C,AAAsB,AAAC;AAUjB,MAAMF,yBAAyB,SAASG,eAAc,eAAA;IAC3DC,YACYC,WAAmB,EACnBC,OAGT,CACD;QACA,KAAK,CAACD,WAAW,EAAE;YAAC,aAAa;SAAC,CAAC,CAAC;QAN1BA,mBAAAA,WAAmB,CAAA;QACnBC,eAAAA,OAGT,CAAA;IAGH;UAEMC,kBAAkB,CAACC,GAAkB,EAAEC,GAAmB,EAAiB;QAC/E,MAAM,EAAEC,KAAK,CAAA,EAAE,GAAGC,IAAAA,IAAK,EAAA,MAAA,EAACH,GAAG,CAACI,GAAG,EAAG,IAAI,CAAC,AAAC;QACxC,MAAMC,WAAW,GAAGH,KAAK,CAAC,QAAQ,CAAC,KAAK,iBAAiB,AAAC;YACzCI,GAAwB;QAAzC,MAAMC,QAAQ,GAAGD,CAAAA,GAAwB,GAAxBA,IAAAA,gBAAmB,oBAAA,EAACN,GAAG,CAAC,YAAxBM,GAAwB,GAAIE,IAAAA,gBAAkC,mCAAA,EAACR,GAAG,CAAC,AAAC;QACrFS,IAAAA,gBAA4B,6BAAA,EAACF,QAAQ,CAAC,CAAC;QACvCG,IAAAA,gBAAqB,sBAAA,EAACH,QAAQ,CAAC,CAAC;QAChC,MAAMI,OAAO,GAAGN,WAAW,GAAG,QAAQ,GAAG,MAAM,AAAC;QAEhDZ,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;YAAEc,QAAQ;YAAEI,OAAO;SAAE,CAAC,CAAC;QAEvC,IAAI,CAACb,OAAO,CAACc,UAAU,CAAC;YAAED,OAAO;YAAEJ,QAAQ;SAAE,CAAC,CAAC;QAE/C,MAAMM,QAAQ,GAAG,IAAI,CAACf,OAAO,CAACgB,WAAW,CAAC;YAAEH,OAAO;SAAE,CAAC,AAAC;QACvD,IAAI,CAACE,QAAQ,EAAE;YACbE,IAAG,CAACC,IAAI,CACN,CAAC,0EAA0E,EAAEL,OAAO,CAAC,gBAAgB,EAAEJ,QAAQ,CAAC,CAAC,CAAC,CACnH,CAAC;YACFN,GAAG,CAACgB,UAAU,GAAG,GAAG,CAAC;YACrBhB,GAAG,CAACiB,GAAG,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QACDzB,KAAK,CAAC,aAAa,EAAEoB,QAAQ,CAAC,CAAC;QAC/BZ,GAAG,CAACkB,SAAS,CAAC,UAAU,EAAEN,QAAQ,CAAC,CAAC;QAEpC,kBAAkB;QAClBO,IAAAA,eAAoB,qBAAA,EAACnB,GAAG,CAAC,CAAC;QAE1B,uBAAuB;QACvBA,GAAG,CAACgB,UAAU,GAAG,GAAG,CAAC;QACrBhB,GAAG,CAACiB,GAAG,EAAE,CAAC;IACZ;CACD"}