{"version": 3, "sources": ["../../../../../src/start/server/type-generation/startTypescriptTypeGeneration.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport fs from 'fs/promises';\nimport { Server } from 'metro';\nimport path from 'path';\n\nimport { removeExpoEnvDTS, writeExpoEnvDTS } from './expo-env';\nimport { setupTypedRoutes } from './routes';\nimport { forceRemovalTSConfig, forceUpdateTSConfig } from './tsconfig';\nimport { removeFromGitIgnore, upsertGitIgnoreContents } from '../../../utils/mergeGitIgnorePaths';\nimport { ensureDotExpoProjectDirectoryInitialized } from '../../project/dotExpo';\nimport { ServerLike } from '../BundlerDevServer';\nimport { getRouterDirectoryModuleIdWithManifest } from '../metro/router';\n\nexport interface TypeScriptTypeGenerationOptions {\n  server?: ServerLike;\n  metro?: Server | null;\n  projectRoot: string;\n}\n\nconst debug = require('debug')('expo:typed-routes') as typeof console.log;\n\n/** Setup all requisite features for statically typed routes in Expo Router v2 / SDK +49. */\nexport async function startTypescriptTypeGenerationAsync({\n  metro,\n  projectRoot,\n  server,\n}: TypeScriptTypeGenerationOptions) {\n  const { exp } = getConfig(projectRoot);\n\n  // If typed routes are disabled, remove any files that were added.\n  if (!exp.experiments?.typedRoutes) {\n    debug('Removing typed routes side-effects (experiments.typedRoutes: false)');\n    const gitIgnorePath = path.join(projectRoot, '.gitignore');\n    await Promise.all([\n      forceRemovalTSConfig(projectRoot),\n      removeExpoEnvDTS(projectRoot),\n      removeFromGitIgnore(gitIgnorePath, 'expo-env.d.ts'),\n    ]);\n  } else {\n    const dotExpoDir = ensureDotExpoProjectDirectoryInitialized(projectRoot);\n    const typesDirectory = path.resolve(dotExpoDir, './types');\n    debug(\n      'Ensuring typed routes side-effects are setup (experiments.typedRoutes: true, typesDirectory: %s)',\n      typesDirectory\n    );\n\n    // Ensure the types directory exists.\n    await fs.mkdir(typesDirectory, { recursive: true });\n\n    await Promise.all([\n      upsertGitIgnoreContents(path.join(projectRoot, '.gitignore'), 'expo-env.d.ts'),\n      writeExpoEnvDTS(projectRoot),\n      forceUpdateTSConfig(projectRoot),\n      setupTypedRoutes({\n        metro,\n        server,\n        typesDirectory,\n        projectRoot,\n        routerDirectory: path.join(\n          projectRoot,\n          getRouterDirectoryModuleIdWithManifest(projectRoot, exp)\n        ),\n      }),\n    ]);\n  }\n}\n"], "names": ["startTypescriptTypeGenerationAsync", "debug", "require", "metro", "projectRoot", "server", "exp", "getConfig", "experiments", "typedRoutes", "gitIgnorePath", "path", "join", "Promise", "all", "forceRemovalTSConfig", "removeExpoEnvDTS", "removeFromGitIgnore", "dotExpoDir", "ensureDotExpoProjectDirectoryInitialized", "typesDirectory", "resolve", "fs", "mkdir", "recursive", "upsertGitIgnoreContents", "writeExpoEnvDTS", "forceUpdateTSConfig", "setupTypedRoutes", "routerDirectory", "getRouterDirectoryModuleIdWithManifest"], "mappings": "AAAA;;;;+BAsBsBA,oCAAkC;;aAAlCA,kCAAkC;;;yBAtB9B,cAAc;;;;;;;8DACzB,aAAa;;;;;;;8DAEX,MAAM;;;;;;yBAE2B,YAAY;wBAC7B,UAAU;0BACe,YAAY;qCACT,oCAAoC;yBACxC,uBAAuB;wBAEzB,iBAAiB;;;;;;AAQxE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,mBAAmB,CAAC,AAAsB,AAAC;AAGnE,eAAeF,kCAAkC,CAAC,EACvDG,KAAK,CAAA,EACLC,WAAW,CAAA,EACXC,MAAM,CAAA,EAC0B,EAAE;QAI7BC,GAAe;IAHpB,MAAM,EAAEA,GAAG,CAAA,EAAE,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAACH,WAAW,CAAC,AAAC;IAEvC,kEAAkE;IAClE,IAAI,CAACE,CAAAA,CAAAA,GAAe,GAAfA,GAAG,CAACE,WAAW,SAAa,GAA5BF,KAAAA,CAA4B,GAA5BA,GAAe,CAAEG,WAAW,CAAA,EAAE;QACjCR,KAAK,CAAC,qEAAqE,CAAC,CAAC;QAC7E,MAAMS,aAAa,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACR,WAAW,EAAE,YAAY,CAAC,AAAC;QAC3D,MAAMS,OAAO,CAACC,GAAG,CAAC;YAChBC,IAAAA,SAAoB,qBAAA,EAACX,WAAW,CAAC;YACjCY,IAAAA,QAAgB,iBAAA,EAACZ,WAAW,CAAC;YAC7Ba,IAAAA,oBAAmB,oBAAA,EAACP,aAAa,EAAE,eAAe,CAAC;SACpD,CAAC,CAAC;IACL,OAAO;QACL,MAAMQ,UAAU,GAAGC,IAAAA,QAAwC,yCAAA,EAACf,WAAW,CAAC,AAAC;QACzE,MAAMgB,cAAc,GAAGT,KAAI,EAAA,QAAA,CAACU,OAAO,CAACH,UAAU,EAAE,SAAS,CAAC,AAAC;QAC3DjB,KAAK,CACH,kGAAkG,EAClGmB,cAAc,CACf,CAAC;QAEF,qCAAqC;QACrC,MAAME,SAAE,EAAA,QAAA,CAACC,KAAK,CAACH,cAAc,EAAE;YAAEI,SAAS,EAAE,IAAI;SAAE,CAAC,CAAC;QAEpD,MAAMX,OAAO,CAACC,GAAG,CAAC;YAChBW,IAAAA,oBAAuB,wBAAA,EAACd,KAAI,EAAA,QAAA,CAACC,IAAI,CAACR,WAAW,EAAE,YAAY,CAAC,EAAE,eAAe,CAAC;YAC9EsB,IAAAA,QAAe,gBAAA,EAACtB,WAAW,CAAC;YAC5BuB,IAAAA,SAAmB,oBAAA,EAACvB,WAAW,CAAC;YAChCwB,IAAAA,OAAgB,iBAAA,EAAC;gBACfzB,KAAK;gBACLE,MAAM;gBACNe,cAAc;gBACdhB,WAAW;gBACXyB,eAAe,EAAElB,KAAI,EAAA,QAAA,CAACC,IAAI,CACxBR,WAAW,EACX0B,IAAAA,OAAsC,uCAAA,EAAC1B,WAAW,EAAEE,GAAG,CAAC,CACzD;aACF,CAAC;SACH,CAAC,CAAC;IACL,CAAC;AACH,CAAC"}