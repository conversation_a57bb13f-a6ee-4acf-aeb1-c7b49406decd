{"version": 3, "sources": ["../../../../../src/start/server/metro/symbolicate.ts"], "sourcesContent": ["import { StackFrame } from 'stacktrace-parser';\n\nexport type CodeFrame = {\n  content: string;\n  location?: {\n    row: number;\n    column: number;\n    [key: string]: any;\n  };\n  fileName: string;\n};\n\nexport type MetroStackFrame = StackFrame & { collapse?: boolean };\nexport type Stack = StackFrame[];\n"], "names": [], "mappings": "AAAA"}