{"version": 3, "sources": ["../../../../../src/start/server/metro/serializeHtml.ts"], "sourcesContent": ["import { SerialAsset } from '@expo/metro-config/build/serializer/serializerAssets';\nimport { RouteNode } from 'expo-router/build/Route';\n\nconst debug = require('debug')('expo:metro:html') as typeof console.log;\n\nexport function serializeHtmlWithAssets({\n  resources,\n  template,\n  devBundleUrl,\n  baseUrl,\n  route,\n  isExporting,\n}: {\n  resources: SerialAsset[];\n  template: string;\n  /** asset prefix used for deploying to non-standard origins like GitHub pages. */\n  baseUrl: string;\n  devBundleUrl?: string;\n  route?: RouteNode;\n  isExporting: boolean;\n}): string {\n  if (!resources) {\n    return '';\n  }\n  return htmlFromSerialAssets(resources, {\n    isExporting,\n    template,\n    baseUrl,\n    bundleUrl: isExporting ? undefined : devBundleUrl,\n    route,\n  });\n}\n\n/**\n * Combine the path segments of a URL.\n * This filters out empty segments and avoids duplicate slashes when joining.\n * If base url is empty, it will be treated as a root path, adding `/` to the beginning.\n */\nfunction combineUrlPath(baseUrl: string, ...segments: string[]) {\n  return [baseUrl || '/', ...segments]\n    .filter(Boolean)\n    .map((segment, index) => {\n      const segmentIsBaseUrl = index === 0;\n      // Do not remove leading slashes from baseUrl\n      return segment.replace(segmentIsBaseUrl ? /\\/+$/g : /^\\/+|\\/+$/g, '');\n    })\n    .join('/');\n}\n\nfunction htmlFromSerialAssets(\n  assets: SerialAsset[],\n  {\n    isExporting,\n    template,\n    baseUrl,\n    bundleUrl,\n    route,\n  }: {\n    isExporting: boolean;\n    template: string;\n    baseUrl: string;\n    /** This is dev-only. */\n    bundleUrl?: string;\n    route?: RouteNode;\n  }\n) {\n  // Combine the CSS modules into tags that have hot refresh data attributes.\n  const styleString = assets\n    .filter((asset) => asset.type === 'css')\n    .map(({ metadata, filename, source }) => {\n      if (isExporting) {\n        return [\n          `<link rel=\"preload\" href=\"${combineUrlPath(baseUrl, filename)}\" as=\"style\">`,\n          `<link rel=\"stylesheet\" href=\"${combineUrlPath(baseUrl, filename)}\">`,\n        ].join('');\n      } else {\n        return `<style data-expo-css-hmr=\"${metadata.hmrId}\">` + source + '\\n</style>';\n      }\n    })\n    .join('');\n\n  const jsAssets = assets.filter((asset) => asset.type === 'js');\n\n  const scripts = bundleUrl\n    ? `<script src=\"${bundleUrl}\" defer></script>`\n    : jsAssets\n        .map(({ filename, metadata }) => {\n          // TODO: Mark dependencies of the HTML and include them to prevent waterfalls.\n          if (metadata.isAsync) {\n            // We have the data required to match async chunks to the route's HTML file.\n            if (\n              route?.entryPoints &&\n              metadata.modulePaths &&\n              Array.isArray(route.entryPoints) &&\n              Array.isArray(metadata.modulePaths)\n            ) {\n              // TODO: Handle module IDs like `expo-router/build/views/Unmatched.js`\n              const doesAsyncChunkContainRouteEntryPoint = route.entryPoints.some((entryPoint) =>\n                (metadata.modulePaths as string[]).includes(entryPoint)\n              );\n              if (!doesAsyncChunkContainRouteEntryPoint) {\n                return '';\n              }\n              debug('Linking async chunk %s to HTML for route %s', filename, route.contextKey);\n              // Pass through to the next condition.\n            } else {\n              return '';\n            }\n            // Mark async chunks as defer so they don't block the page load.\n            // return `<script src=\"${combineUrlPath(baseUrl, filename)\" defer></script>`;\n          }\n\n          return `<script src=\"${combineUrlPath(baseUrl, filename)}\" defer></script>`;\n        })\n        .join('');\n\n  return template\n    .replace('</head>', `${styleString}</head>`)\n    .replace('</body>', `${scripts}\\n</body>`);\n}\n"], "names": ["serializeHtmlWithAssets", "debug", "require", "resources", "template", "devBundleUrl", "baseUrl", "route", "isExporting", "htmlFromSerialAssets", "bundleUrl", "undefined", "combineUrlPath", "segments", "filter", "Boolean", "map", "segment", "index", "segmentIsBaseUrl", "replace", "join", "assets", "styleString", "asset", "type", "metadata", "filename", "source", "hmrId", "jsAssets", "scripts", "isAsync", "entryPoints", "modulePaths", "Array", "isArray", "doesAsyncChunkContainRouteEntryPoint", "some", "entryPoint", "includes", "<PERSON><PERSON>ey"], "mappings": "AAAA;;;;+BAKgBA,yBAAuB;;aAAvBA,uBAAuB;;AAFvC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,iBAAiB,CAAC,AAAsB,AAAC;AAEjE,SAASF,uBAAuB,CAAC,EACtCG,SAAS,CAAA,EACTC,QAAQ,CAAA,EACRC,YAAY,CAAA,EACZC,OAAO,CAAA,EACPC,KAAK,CAAA,EACLC,WAAW,CAAA,EASZ,EAAU;IACT,IAAI,CAACL,SAAS,EAAE;QACd,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,OAAOM,oBAAoB,CAACN,SAAS,EAAE;QACrCK,WAAW;QACXJ,QAAQ;QACRE,OAAO;QACPI,SAAS,EAAEF,WAAW,GAAGG,SAAS,GAAGN,YAAY;QACjDE,KAAK;KACN,CAAC,CAAC;AACL,CAAC;AAED;;;;CAIC,GACD,SAASK,cAAc,CAACN,OAAe,EAAE,GAAGO,QAAQ,AAAU,EAAE;IAC9D,OAAO;QAACP,OAAO,IAAI,GAAG;WAAKO,QAAQ;KAAC,CACjCC,MAAM,CAACC,OAAO,CAAC,CACfC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,GAAK;QACvB,MAAMC,gBAAgB,GAAGD,KAAK,KAAK,CAAC,AAAC;QACrC,6CAA6C;QAC7C,OAAOD,OAAO,CAACG,OAAO,CAACD,gBAAgB,yBAAyB,EAAE,EAAE,CAAC,CAAC;IACxE,CAAC,CAAC,CACDE,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC;AAED,SAASZ,oBAAoB,CAC3Ba,MAAqB,EACrB,EACEd,WAAW,CAAA,EACXJ,QAAQ,CAAA,EACRE,OAAO,CAAA,EACPI,SAAS,CAAA,EACTH,KAAK,CAAA,EAQN,EACD;IACA,2EAA2E;IAC3E,MAAMgB,WAAW,GAAGD,MAAM,CACvBR,MAAM,CAAC,CAACU,KAAK,GAAKA,KAAK,CAACC,IAAI,KAAK,KAAK,CAAC,CACvCT,GAAG,CAAC,CAAC,EAAEU,QAAQ,CAAA,EAAEC,QAAQ,CAAA,EAAEC,MAAM,CAAA,EAAE,GAAK;QACvC,IAAIpB,WAAW,EAAE;YACf,OAAO;gBACL,CAAC,0BAA0B,EAAEI,cAAc,CAACN,OAAO,EAAEqB,QAAQ,CAAC,CAAC,aAAa,CAAC;gBAC7E,CAAC,6BAA6B,EAAEf,cAAc,CAACN,OAAO,EAAEqB,QAAQ,CAAC,CAAC,EAAE,CAAC;aACtE,CAACN,IAAI,CAAC,EAAE,CAAC,CAAC;QACb,OAAO;YACL,OAAO,CAAC,0BAA0B,EAAEK,QAAQ,CAACG,KAAK,CAAC,EAAE,CAAC,GAAGD,MAAM,GAAG,YAAY,CAAC;QACjF,CAAC;IACH,CAAC,CAAC,CACDP,IAAI,CAAC,EAAE,CAAC,AAAC;IAEZ,MAAMS,QAAQ,GAAGR,MAAM,CAACR,MAAM,CAAC,CAACU,KAAK,GAAKA,KAAK,CAACC,IAAI,KAAK,IAAI,CAAC,AAAC;IAE/D,MAAMM,OAAO,GAAGrB,SAAS,GACrB,CAAC,aAAa,EAAEA,SAAS,CAAC,iBAAiB,CAAC,GAC5CoB,QAAQ,CACLd,GAAG,CAAC,CAAC,EAAEW,QAAQ,CAAA,EAAED,QAAQ,CAAA,EAAE,GAAK;QAC/B,8EAA8E;QAC9E,IAAIA,QAAQ,CAACM,OAAO,EAAE;YACpB,4EAA4E;YAC5E,IACEzB,CAAAA,KAAK,QAAa,GAAlBA,KAAAA,CAAkB,GAAlBA,KAAK,CAAE0B,WAAW,CAAA,IAClBP,QAAQ,CAACQ,WAAW,IACpBC,KAAK,CAACC,OAAO,CAAC7B,KAAK,CAAC0B,WAAW,CAAC,IAChCE,KAAK,CAACC,OAAO,CAACV,QAAQ,CAACQ,WAAW,CAAC,EACnC;gBACA,sEAAsE;gBACtE,MAAMG,oCAAoC,GAAG9B,KAAK,CAAC0B,WAAW,CAACK,IAAI,CAAC,CAACC,UAAU,GAC7E,AAACb,QAAQ,CAACQ,WAAW,CAAcM,QAAQ,CAACD,UAAU,CAAC,CACxD,AAAC;gBACF,IAAI,CAACF,oCAAoC,EAAE;oBACzC,OAAO,EAAE,CAAC;gBACZ,CAAC;gBACDpC,KAAK,CAAC,6CAA6C,EAAE0B,QAAQ,EAAEpB,KAAK,CAACkC,UAAU,CAAC,CAAC;YACjF,sCAAsC;YACxC,OAAO;gBACL,OAAO,EAAE,CAAC;YACZ,CAAC;QACD,gEAAgE;QAChE,8EAA8E;QAChF,CAAC;QAED,OAAO,CAAC,aAAa,EAAE7B,cAAc,CAACN,OAAO,EAAEqB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;IAC9E,CAAC,CAAC,CACDN,IAAI,CAAC,EAAE,CAAC,AAAC;IAEhB,OAAOjB,QAAQ,CACZgB,OAAO,CAAC,SAAS,EAAE,CAAC,EAAEG,WAAW,CAAC,OAAO,CAAC,CAAC,CAC3CH,OAAO,CAAC,SAAS,EAAE,CAAC,EAAEW,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;AAC/C,CAAC"}