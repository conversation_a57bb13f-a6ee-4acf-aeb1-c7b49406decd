{"version": 3, "sources": ["../../../../../src/start/server/metro/runServer-fork.ts"], "sourcesContent": ["// Copyright © 2023 650 Industries.\n// Copyright (c) Meta Platforms, Inc. and affiliates.\n//\n// Forks https://github.com/facebook/metro/blob/b80d9a0f638ee9fb82ff69cd3c8d9f4309ca1da2/packages/metro/src/index.flow.js#L57\n// and adds the ability to access the bundler instance.\nimport assert from 'assert';\nimport http from 'http';\nimport https from 'https';\nimport Metro, { RunServerOptions, Server } from 'metro';\nimport MetroHmrServer from 'metro/src/HmrServer';\nimport createWebsocketServer from 'metro/src/lib/createWebsocketServer';\nimport { ConfigT } from 'metro-config';\nimport { parse } from 'url';\nimport type { WebSocketServer } from 'ws';\n\nimport { MetroBundlerDevServer } from './MetroBundlerDevServer';\nimport { Log } from '../../../log';\nimport { getRunningProcess } from '../../../utils/getRunningProcess';\nimport type { ConnectAppType } from '../middleware/server.types';\n\nexport const runServer = async (\n  metroBundler: MetroBundlerDevServer,\n  config: ConfigT,\n  {\n    hasReducedPerformance = false,\n    host,\n    onError,\n    onReady,\n    secureServerOptions,\n    waitForBundler = false,\n    websocketEndpoints = {},\n    watch,\n  }: RunServerOptions,\n  {\n    mockServer,\n  }: {\n    // Use a mock server object instead of creating a real server, this is used in export cases where we want to reuse codepaths but not actually start a server.\n    mockServer: boolean;\n  }\n): Promise<{ server: http.Server | https.Server; metro: Server }> => {\n  // await earlyPortCheck(host, config.server.port);\n\n  // if (secure != null || secureCert != null || secureKey != null) {\n  //   // eslint-disable-next-line no-console\n  //   console.warn(\n  //     chalk.inverse.yellow.bold(' DEPRECATED '),\n  //     'The `secure`, `secureCert`, and `secureKey` options are now deprecated. ' +\n  //       'Please use the `secureServerOptions` object instead to pass options to ' +\n  //       \"Metro's https development server.\",\n  //   );\n  // }\n\n  const { middleware, end, metroServer } = await Metro.createConnectMiddleware(config, {\n    hasReducedPerformance,\n    waitForBundler,\n    watch,\n  });\n\n  if (!mockServer) {\n    assert(typeof (middleware as any).use === 'function');\n  }\n  const serverApp = middleware as ConnectAppType;\n\n  let httpServer: http.Server | https.Server;\n\n  if (secureServerOptions != null) {\n    httpServer = https.createServer(secureServerOptions, serverApp);\n  } else {\n    httpServer = http.createServer(serverApp);\n  }\n\n  httpServer.on('error', (error) => {\n    if ('code' in error && error.code === 'EADDRINUSE') {\n      // If `Error: listen EADDRINUSE: address already in use :::8081` then print additional info\n      // about the process before throwing.\n      const info = getRunningProcess(config.server.port);\n      if (info) {\n        Log.error(\n          `Port ${config.server.port} is busy running ${info.command} in: ${info.directory}`\n        );\n      }\n    }\n\n    if (onError) {\n      onError(error);\n    }\n    end();\n  });\n\n  // Disable any kind of automatic timeout behavior for incoming\n  // requests in case it takes the packager more than the default\n  // timeout of 120 seconds to respond to a request.\n  httpServer.timeout = 0;\n\n  httpServer.on('close', () => {\n    end();\n  });\n\n  // Extend the close method to ensure all websocket servers are closed, and connections are terminated\n  const originalClose = httpServer.close.bind(httpServer);\n\n  httpServer.close = function closeHttpServer(callback) {\n    originalClose(callback);\n\n    // Close all websocket servers, including possible client connections (see: https://github.com/websockets/ws/issues/2137#issuecomment-1507469375)\n    for (const endpoint of Object.values(websocketEndpoints) as WebSocketServer[]) {\n      endpoint.close();\n      endpoint.clients.forEach((client) => client.terminate());\n    }\n\n    // Forcibly close active connections\n    this.closeAllConnections();\n    return this;\n  };\n\n  if (mockServer) {\n    return { server: httpServer, metro: metroServer };\n  }\n\n  return new Promise<{ server: http.Server | https.Server; metro: Server }>((resolve, reject) => {\n    httpServer.on('error', (error) => {\n      reject(error);\n    });\n\n    httpServer.listen(config.server.port, host, () => {\n      if (onReady) {\n        onReady(httpServer);\n      }\n\n      Object.assign(websocketEndpoints, {\n        // @ts-expect-error: incorrect types\n        '/hot': createWebsocketServer({\n          websocketServer: new MetroHmrServer(\n            metroServer.getBundler(),\n            metroServer.getCreateModuleId(),\n            config\n          ),\n        }),\n      });\n\n      httpServer.on('upgrade', (request, socket, head) => {\n        const { pathname } = parse(request.url!);\n        if (pathname != null && websocketEndpoints[pathname]) {\n          websocketEndpoints[pathname].handleUpgrade(request, socket, head, (ws) => {\n            websocketEndpoints[pathname].emit('connection', ws, request);\n          });\n        } else {\n          socket.destroy();\n        }\n      });\n\n      resolve({ server: httpServer, metro: metroServer });\n    });\n  });\n};\n"], "names": ["runServer", "metroBundler", "config", "hasReducedPerformance", "host", "onError", "onReady", "secureServerOptions", "waitFor<PERSON><PERSON>ler", "websocketEndpoints", "watch", "mockServer", "middleware", "end", "metroServer", "Metro", "createConnectMiddleware", "assert", "use", "serverApp", "httpServer", "https", "createServer", "http", "on", "error", "code", "info", "getRunningProcess", "server", "port", "Log", "command", "directory", "timeout", "originalClose", "close", "bind", "closeHttpServer", "callback", "endpoint", "Object", "values", "clients", "for<PERSON>ach", "client", "terminate", "closeAllConnections", "metro", "Promise", "resolve", "reject", "listen", "assign", "createWebsocketServer", "websocketServer", "MetroHmrServer", "getBundler", "getCreateModuleId", "request", "socket", "head", "pathname", "parse", "url", "handleUpgrade", "ws", "emit", "destroy"], "mappings": "AAAA,mCAAmC;AACnC,qDAAqD;AACrD,EAAE;AACF,6HAA6H;AAC7H,uDAAuD;AACvD;;;;+BAeaA,WAAS;;aAATA,SAAS;;;8DAfH,QAAQ;;;;;;;8DACV,MAAM;;;;;;;8DACL,OAAO;;;;;;;8DACuB,OAAO;;;;;;;8DAC5B,qBAAqB;;;;;;;8DACd,qCAAqC;;;;;;;yBAEjD,KAAK;;;;;;qBAIP,cAAc;mCACA,kCAAkC;;;;;;AAG7D,MAAMA,SAAS,GAAG,OACvBC,YAAmC,EACnCC,MAAe,EACf,EACEC,qBAAqB,EAAG,KAAK,CAAA,EAC7BC,IAAI,CAAA,EACJC,OAAO,CAAA,EACPC,OAAO,CAAA,EACPC,mBAAmB,CAAA,EACnBC,cAAc,EAAG,KAAK,CAAA,EACtBC,kBAAkB,EAAG,EAAE,CAAA,EACvBC,KAAK,CAAA,EACY,EACnB,EACEC,UAAU,CAAA,EAIX,GACkE;IACnE,kDAAkD;IAElD,mEAAmE;IACnE,2CAA2C;IAC3C,kBAAkB;IAClB,iDAAiD;IACjD,mFAAmF;IACnF,oFAAoF;IACpF,6CAA6C;IAC7C,OAAO;IACP,IAAI;IAEJ,MAAM,EAAEC,UAAU,CAAA,EAAEC,GAAG,CAAA,EAAEC,WAAW,CAAA,EAAE,GAAG,MAAMC,MAAK,EAAA,QAAA,CAACC,uBAAuB,CAACd,MAAM,EAAE;QACnFC,qBAAqB;QACrBK,cAAc;QACdE,KAAK;KACN,CAAC,AAAC;IAEH,IAAI,CAACC,UAAU,EAAE;QACfM,IAAAA,OAAM,EAAA,QAAA,EAAC,OAAO,AAACL,UAAU,CAASM,GAAG,KAAK,UAAU,CAAC,CAAC;IACxD,CAAC;IACD,MAAMC,SAAS,GAAGP,UAAU,AAAkB,AAAC;IAE/C,IAAIQ,UAAU,AAA4B,AAAC;IAE3C,IAAIb,mBAAmB,IAAI,IAAI,EAAE;QAC/Ba,UAAU,GAAGC,MAAK,EAAA,QAAA,CAACC,YAAY,CAACf,mBAAmB,EAAEY,SAAS,CAAC,CAAC;IAClE,OAAO;QACLC,UAAU,GAAGG,KAAI,EAAA,QAAA,CAACD,YAAY,CAACH,SAAS,CAAC,CAAC;IAC5C,CAAC;IAEDC,UAAU,CAACI,EAAE,CAAC,OAAO,EAAE,CAACC,KAAK,GAAK;QAChC,IAAI,MAAM,IAAIA,KAAK,IAAIA,KAAK,CAACC,IAAI,KAAK,YAAY,EAAE;YAClD,2FAA2F;YAC3F,qCAAqC;YACrC,MAAMC,IAAI,GAAGC,IAAAA,kBAAiB,kBAAA,EAAC1B,MAAM,CAAC2B,MAAM,CAACC,IAAI,CAAC,AAAC;YACnD,IAAIH,IAAI,EAAE;gBACRI,IAAG,IAAA,CAACN,KAAK,CACP,CAAC,KAAK,EAAEvB,MAAM,CAAC2B,MAAM,CAACC,IAAI,CAAC,iBAAiB,EAAEH,IAAI,CAACK,OAAO,CAAC,KAAK,EAAEL,IAAI,CAACM,SAAS,CAAC,CAAC,CACnF,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI5B,OAAO,EAAE;YACXA,OAAO,CAACoB,KAAK,CAAC,CAAC;QACjB,CAAC;QACDZ,GAAG,EAAE,CAAC;IACR,CAAC,CAAC,CAAC;IAEH,8DAA8D;IAC9D,+DAA+D;IAC/D,kDAAkD;IAClDO,UAAU,CAACc,OAAO,GAAG,CAAC,CAAC;IAEvBd,UAAU,CAACI,EAAE,CAAC,OAAO,EAAE,IAAM;QAC3BX,GAAG,EAAE,CAAC;IACR,CAAC,CAAC,CAAC;IAEH,qGAAqG;IACrG,MAAMsB,aAAa,GAAGf,UAAU,CAACgB,KAAK,CAACC,IAAI,CAACjB,UAAU,CAAC,AAAC;IAExDA,UAAU,CAACgB,KAAK,GAAG,SAASE,eAAe,CAACC,QAAQ,EAAE;QACpDJ,aAAa,CAACI,QAAQ,CAAC,CAAC;QAExB,iJAAiJ;QACjJ,KAAK,MAAMC,QAAQ,IAAIC,MAAM,CAACC,MAAM,CAACjC,kBAAkB,CAAC,CAAuB;YAC7E+B,QAAQ,CAACJ,KAAK,EAAE,CAAC;YACjBI,QAAQ,CAACG,OAAO,CAACC,OAAO,CAAC,CAACC,MAAM,GAAKA,MAAM,CAACC,SAAS,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,oCAAoC;QACpC,IAAI,CAACC,mBAAmB,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IAEF,IAAIpC,UAAU,EAAE;QACd,OAAO;YAAEkB,MAAM,EAAET,UAAU;YAAE4B,KAAK,EAAElC,WAAW;SAAE,CAAC;IACpD,CAAC;IAED,OAAO,IAAImC,OAAO,CAAwD,CAACC,OAAO,EAAEC,MAAM,GAAK;QAC7F/B,UAAU,CAACI,EAAE,CAAC,OAAO,EAAE,CAACC,KAAK,GAAK;YAChC0B,MAAM,CAAC1B,KAAK,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;QAEHL,UAAU,CAACgC,MAAM,CAAClD,MAAM,CAAC2B,MAAM,CAACC,IAAI,EAAE1B,IAAI,EAAE,IAAM;YAChD,IAAIE,OAAO,EAAE;gBACXA,OAAO,CAACc,UAAU,CAAC,CAAC;YACtB,CAAC;YAEDqB,MAAM,CAACY,MAAM,CAAC5C,kBAAkB,EAAE;gBAChC,oCAAoC;gBACpC,MAAM,EAAE6C,IAAAA,sBAAqB,EAAA,QAAA,EAAC;oBAC5BC,eAAe,EAAE,IAAIC,CAAAA,UAAc,EAAA,CAAA,QAAA,CACjC1C,WAAW,CAAC2C,UAAU,EAAE,EACxB3C,WAAW,CAAC4C,iBAAiB,EAAE,EAC/BxD,MAAM,CACP;iBACF,CAAC;aACH,CAAC,CAAC;YAEHkB,UAAU,CAACI,EAAE,CAAC,SAAS,EAAE,CAACmC,OAAO,EAAEC,MAAM,EAAEC,IAAI,GAAK;gBAClD,MAAM,EAAEC,QAAQ,CAAA,EAAE,GAAGC,IAAAA,IAAK,EAAA,MAAA,EAACJ,OAAO,CAACK,GAAG,CAAE,AAAC;gBACzC,IAAIF,QAAQ,IAAI,IAAI,IAAIrD,kBAAkB,CAACqD,QAAQ,CAAC,EAAE;oBACpDrD,kBAAkB,CAACqD,QAAQ,CAAC,CAACG,aAAa,CAACN,OAAO,EAAEC,MAAM,EAAEC,IAAI,EAAE,CAACK,EAAE,GAAK;wBACxEzD,kBAAkB,CAACqD,QAAQ,CAAC,CAACK,IAAI,CAAC,YAAY,EAAED,EAAE,EAAEP,OAAO,CAAC,CAAC;oBAC/D,CAAC,CAAC,CAAC;gBACL,OAAO;oBACLC,MAAM,CAACQ,OAAO,EAAE,CAAC;gBACnB,CAAC;YACH,CAAC,CAAC,CAAC;YAEHlB,OAAO,CAAC;gBAAErB,MAAM,EAAET,UAAU;gBAAE4B,KAAK,EAAElC,WAAW;aAAE,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,AAAC"}