{"version": 3, "sources": ["../../../../src/start/server/webTemplate.ts"], "sourcesContent": ["import { ExpoConfig, getConfig, getNameFromConfig } from '@expo/config';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { TEMPLATES } from '../../customize/templates';\nimport { appendLinkToHtml, appendScriptsToHtml } from '../../export/html';\nimport { env } from '../../utils/env';\n\n/**\n * Create a static HTML for SPA styled websites.\n * This method attempts to reuse the same patterns as `@expo/webpack-config`.\n */\nexport async function createTemplateHtmlFromExpoConfigAsync(\n  projectRoot: string,\n  {\n    scripts,\n    cssLinks,\n    exp = getConfig(projectRoot, { skipSDKVersionRequirement: true }).exp,\n  }: {\n    scripts: string[];\n    cssLinks?: string[];\n    exp?: ExpoConfig;\n  }\n) {\n  return createTemplateHtmlAsync(projectRoot, {\n    langIsoCode: exp.web?.lang ?? 'en',\n    scripts,\n    cssLinks,\n    title: getNameFromConfig(exp).webName ?? 'Expo App',\n    description: exp.web?.description,\n    themeColor: exp.web?.themeColor,\n  });\n}\n\nfunction getFileFromLocalPublicFolder(\n  projectRoot: string,\n  { publicFolder, filePath }: { publicFolder: string; filePath: string }\n): string | null {\n  const localFilePath = path.resolve(projectRoot, publicFolder, filePath);\n  if (!fs.existsSync(localFilePath)) {\n    return null;\n  }\n  return localFilePath;\n}\n\n/** Attempt to read the `index.html` from the local project before falling back on the template `index.html`. */\nasync function getTemplateIndexHtmlAsync(projectRoot: string): Promise<string> {\n  let filePath = getFileFromLocalPublicFolder(projectRoot, {\n    // TODO: Maybe use the app.json override.\n    publicFolder: env.EXPO_PUBLIC_FOLDER,\n    filePath: 'index.html',\n  });\n  if (!filePath) {\n    filePath = TEMPLATES.find((value) => value.id === 'index.html')!.file(projectRoot);\n  }\n  return fs.promises.readFile(filePath, 'utf8');\n}\n\n/** Return an `index.html` string with template values added. */\nexport async function createTemplateHtmlAsync(\n  projectRoot: string,\n  {\n    scripts,\n    cssLinks,\n    description,\n    langIsoCode,\n    title,\n    themeColor,\n  }: {\n    scripts: string[];\n    cssLinks?: string[];\n    description?: string;\n    langIsoCode: string;\n    title: string;\n    themeColor?: string;\n  }\n): Promise<string> {\n  // Resolve the best possible index.html template file.\n  let contents = await getTemplateIndexHtmlAsync(projectRoot);\n\n  contents = contents.replace('%LANG_ISO_CODE%', langIsoCode);\n  contents = contents.replace('%WEB_TITLE%', title);\n\n  contents = appendScriptsToHtml(contents, scripts);\n\n  if (cssLinks) {\n    contents = appendLinkToHtml(\n      contents,\n      cssLinks\n        .map((href) => [\n          // NOTE: We probably don't have to preload the CSS files for SPA-styled websites.\n          {\n            as: 'style',\n            rel: 'preload',\n            href,\n          },\n          {\n            rel: 'stylesheet',\n            href,\n          },\n        ])\n        .flat()\n    );\n  }\n\n  if (themeColor) {\n    contents = addMeta(contents, `name=\"theme-color\" content=\"${themeColor}\"`);\n  }\n\n  if (description) {\n    contents = addMeta(contents, `name=\"description\" content=\"${description}\"`);\n  }\n\n  return contents;\n}\n\n/** Add a `<meta />` tag to the `<head />` element. */\nfunction addMeta(contents: string, meta: string): string {\n  return contents.replace('</head>', `<meta ${meta}>\\n</head>`);\n}\n"], "names": ["createTemplateHtmlFromExpoConfigAsync", "createTemplateHtmlAsync", "projectRoot", "scripts", "cssLinks", "exp", "getConfig", "skipSDKVersionRequirement", "getNameFromConfig", "langIsoCode", "web", "lang", "title", "webName", "description", "themeColor", "getFileFromLocalPublicFolder", "publicFolder", "filePath", "localFilePath", "path", "resolve", "fs", "existsSync", "getTemplateIndexHtmlAsync", "env", "EXPO_PUBLIC_FOLDER", "TEMPLATES", "find", "value", "id", "file", "promises", "readFile", "contents", "replace", "appendScriptsToHtml", "appendLinkToHtml", "map", "href", "as", "rel", "flat", "addMeta", "meta"], "mappings": "AAAA;;;;;;;;;;;IAYsBA,qCAAqC,MAArCA,qCAAqC;IA+CrCC,uBAAuB,MAAvBA,uBAAuB;;;yBA3DY,cAAc;;;;;;;8DACxD,IAAI;;;;;;;8DACF,MAAM;;;;;;2BAEG,2BAA2B;sBACC,mBAAmB;qBACrD,iBAAiB;;;;;;AAM9B,eAAeD,qCAAqC,CACzDE,WAAmB,EACnB,EACEC,OAAO,CAAA,EACPC,QAAQ,CAAA,EACRC,GAAG,EAAGC,IAAAA,OAAS,EAAA,UAAA,EAACJ,WAAW,EAAE;IAAEK,yBAAyB,EAAE,IAAI;CAAE,CAAC,CAACF,GAAG,CAAA,EAKtE,EACD;QAEeA,GAAO,EAIPA,IAAO,EACRA,IAAO;QALNA,IAAa,EAGnBG,QAA8B;IAJvC,OAAOP,uBAAuB,CAACC,WAAW,EAAE;QAC1CO,WAAW,EAAEJ,CAAAA,IAAa,GAAbA,CAAAA,GAAO,GAAPA,GAAG,CAACK,GAAG,SAAM,GAAbL,KAAAA,CAAa,GAAbA,GAAO,CAAEM,IAAI,YAAbN,IAAa,GAAI,IAAI;QAClCF,OAAO;QACPC,QAAQ;QACRQ,KAAK,EAAEJ,CAAAA,QAA8B,GAA9BA,IAAAA,OAAiB,EAAA,kBAAA,EAACH,GAAG,CAAC,CAACQ,OAAO,YAA9BL,QAA8B,GAAI,UAAU;QACnDM,WAAW,EAAET,CAAAA,IAAO,GAAPA,GAAG,CAACK,GAAG,SAAa,GAApBL,KAAAA,CAAoB,GAApBA,IAAO,CAAES,WAAW;QACjCC,UAAU,EAAEV,CAAAA,IAAO,GAAPA,GAAG,CAACK,GAAG,SAAY,GAAnBL,KAAAA,CAAmB,GAAnBA,IAAO,CAAEU,UAAU;KAChC,CAAC,CAAC;AACL,CAAC;AAED,SAASC,4BAA4B,CACnCd,WAAmB,EACnB,EAAEe,YAAY,CAAA,EAAEC,QAAQ,CAAA,EAA8C,EACvD;IACf,MAAMC,aAAa,GAAGC,KAAI,EAAA,QAAA,CAACC,OAAO,CAACnB,WAAW,EAAEe,YAAY,EAAEC,QAAQ,CAAC,AAAC;IACxE,IAAI,CAACI,GAAE,EAAA,QAAA,CAACC,UAAU,CAACJ,aAAa,CAAC,EAAE;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAOA,aAAa,CAAC;AACvB,CAAC;AAED,8GAA8G,GAC9G,eAAeK,yBAAyB,CAACtB,WAAmB,EAAmB;IAC7E,IAAIgB,QAAQ,GAAGF,4BAA4B,CAACd,WAAW,EAAE;QACvD,yCAAyC;QACzCe,YAAY,EAAEQ,IAAG,IAAA,CAACC,kBAAkB;QACpCR,QAAQ,EAAE,YAAY;KACvB,CAAC,AAAC;IACH,IAAI,CAACA,QAAQ,EAAE;QACbA,QAAQ,GAAGS,UAAS,UAAA,CAACC,IAAI,CAAC,CAACC,KAAK,GAAKA,KAAK,CAACC,EAAE,KAAK,YAAY,CAAC,CAAEC,IAAI,CAAC7B,WAAW,CAAC,CAAC;IACrF,CAAC;IACD,OAAOoB,GAAE,EAAA,QAAA,CAACU,QAAQ,CAACC,QAAQ,CAACf,QAAQ,EAAE,MAAM,CAAC,CAAC;AAChD,CAAC;AAGM,eAAejB,uBAAuB,CAC3CC,WAAmB,EACnB,EACEC,OAAO,CAAA,EACPC,QAAQ,CAAA,EACRU,WAAW,CAAA,EACXL,WAAW,CAAA,EACXG,KAAK,CAAA,EACLG,UAAU,CAAA,EAQX,EACgB;IACjB,sDAAsD;IACtD,IAAImB,QAAQ,GAAG,MAAMV,yBAAyB,CAACtB,WAAW,CAAC,AAAC;IAE5DgC,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAAC,iBAAiB,EAAE1B,WAAW,CAAC,CAAC;IAC5DyB,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAAC,aAAa,EAAEvB,KAAK,CAAC,CAAC;IAElDsB,QAAQ,GAAGE,IAAAA,KAAmB,oBAAA,EAACF,QAAQ,EAAE/B,OAAO,CAAC,CAAC;IAElD,IAAIC,QAAQ,EAAE;QACZ8B,QAAQ,GAAGG,IAAAA,KAAgB,iBAAA,EACzBH,QAAQ,EACR9B,QAAQ,CACLkC,GAAG,CAAC,CAACC,IAAI,GAAK;gBACb,iFAAiF;gBACjF;oBACEC,EAAE,EAAE,OAAO;oBACXC,GAAG,EAAE,SAAS;oBACdF,IAAI;iBACL;gBACD;oBACEE,GAAG,EAAE,YAAY;oBACjBF,IAAI;iBACL;aACF,CAAC,CACDG,IAAI,EAAE,CACV,CAAC;IACJ,CAAC;IAED,IAAI3B,UAAU,EAAE;QACdmB,QAAQ,GAAGS,OAAO,CAACT,QAAQ,EAAE,CAAC,4BAA4B,EAAEnB,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,IAAID,WAAW,EAAE;QACfoB,QAAQ,GAAGS,OAAO,CAACT,QAAQ,EAAE,CAAC,4BAA4B,EAAEpB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED,OAAOoB,QAAQ,CAAC;AAClB,CAAC;AAED,oDAAoD,GACpD,SAASS,OAAO,CAACT,QAAgB,EAAEU,IAAY,EAAU;IACvD,OAAOV,QAAQ,CAACC,OAAO,CAAC,SAAS,EAAE,CAAC,MAAM,EAAES,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;AAChE,CAAC"}