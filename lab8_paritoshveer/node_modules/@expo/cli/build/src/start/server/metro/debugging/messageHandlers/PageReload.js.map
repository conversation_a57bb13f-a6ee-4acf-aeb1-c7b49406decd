{"version": 3, "sources": ["../../../../../../../src/start/server/metro/debugging/messageHandlers/PageReload.ts"], "sourcesContent": ["import type { Protocol } from 'devtools-protocol';\n\nimport type { MetroBundlerDevServer } from '../../MetroBundlerDevServer';\nimport { MessageHandler } from '../MessageHandler';\nimport type { CdpMessage, Connection, DebuggerRequest } from '../types';\n\nexport class PageReloadHandler extends MessageHandler {\n  private metroBundler: Pick<MetroBundlerDevServer, 'broadcastMessage'>;\n\n  constructor(\n    connection: Connection,\n    metroBundler: Pick<MetroBundlerDevServer, 'broadcastMessage'>\n  ) {\n    super(connection);\n    this.metroBundler = metroBundler;\n  }\n\n  handleDebuggerMessage(message: DebuggerRequest<PageReload>) {\n    if (message.method === 'Page.reload') {\n      this.metroBundler.broadcastMessage('reload');\n      return this.sendToDebugger({ id: message.id });\n    }\n\n    return false;\n  }\n}\n\n/** @see https://chromedevtools.github.io/devtools-protocol/1-2/Page/#method-reload */\nexport type PageReload = CdpMessage<'Page.reload', Protocol.Page.ReloadRequest, never>;\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MessageHandler", "constructor", "connection", "metroBundler", "handleDebuggerMessage", "message", "method", "broadcastMessage", "sendToDebugger", "id"], "mappings": "AAAA;;;;+BAMaA,mBAAiB;;aAAjBA,iBAAiB;;gCAHC,mBAAmB;AAG3C,MAAMA,iBAAiB,SAASC,eAAc,eAAA;IAGnDC,YACEC,UAAsB,EACtBC,YAA6D,CAC7D;QACA,KAAK,CAACD,UAAU,CAAC,CAAC;QAClB,IAAI,CAACC,YAAY,GAAGA,YAAY,CAAC;IACnC;IAEAC,qBAAqB,CAACC,OAAoC,EAAE;QAC1D,IAAIA,OAAO,CAACC,MAAM,KAAK,aAAa,EAAE;YACpC,IAAI,CAACH,YAAY,CAACI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAC7C,OAAO,IAAI,CAACC,cAAc,CAAC;gBAAEC,EAAE,EAAEJ,OAAO,CAACI,EAAE;aAAE,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,KAAK,CAAC;IACf;CACD"}