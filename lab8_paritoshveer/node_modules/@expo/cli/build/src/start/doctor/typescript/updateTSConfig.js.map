{"version": 3, "sources": ["../../../../../src/start/doctor/typescript/updateTSConfig.ts"], "sourcesContent": ["import JsonFile from '@expo/json-file';\nimport chalk from 'chalk';\nimport fs from 'fs';\n\nimport * as Log from '../../../log';\n\nexport const baseTSConfigName = 'expo/tsconfig.base';\n\nexport async function updateTSConfigAsync({\n  tsConfigPath,\n}: {\n  tsConfigPath: string;\n}): Promise<void> {\n  const shouldGenerate = !fs.existsSync(tsConfigPath) || fs.statSync(tsConfigPath).size === 0;\n  if (shouldGenerate) {\n    await JsonFile.writeAsync(tsConfigPath, { compilerOptions: {} });\n  }\n\n  const projectTSConfig = JsonFile.read(tsConfigPath, {\n    // Some tsconfig.json files have a generated comment in the file.\n    json5: true,\n  });\n\n  projectTSConfig.compilerOptions ??= {};\n\n  const modifications: [string, string][] = [];\n\n  // If the extends field isn't defined, set it to the expo default\n  if (!projectTSConfig.extends) {\n    // if (projectTSConfig.extends !== baseTSConfigName) {\n    projectTSConfig.extends = baseTSConfigName;\n    modifications.push(['extends', baseTSConfigName]);\n  }\n\n  // If no changes, then quietly bail out\n  if (!modifications.length) {\n    return;\n  }\n\n  // Write changes and log out a summary of what changed\n  await JsonFile.writeAsync(tsConfigPath, projectTSConfig);\n\n  // If no changes, then quietly bail out\n  if (modifications.length === 0) {\n    return;\n  }\n\n  Log.log();\n\n  if (shouldGenerate) {\n    Log.log(chalk`{bold TypeScript}: A {cyan tsconfig.json} has been auto-generated`);\n  } else {\n    Log.log(\n      chalk`{bold TypeScript}: The {cyan tsconfig.json} has been updated {dim (Use EXPO_NO_TYPESCRIPT_SETUP to skip)}`\n    );\n    logModifications(modifications);\n  }\n  Log.log();\n}\n\nfunction logModifications(modifications: string[][]) {\n  Log.log();\n\n  Log.log(chalk`\\u203A {bold Required} modifications made to the {cyan tsconfig.json}:`);\n\n  Log.log();\n\n  // Sort the items based on key name length\n  printTable(modifications.sort((a, b) => a[0].length - b[0].length));\n\n  Log.log();\n}\n\nfunction printTable(items: string[][]) {\n  const tableFormat = (name: string, msg: string) =>\n    `  ${chalk.bold`${name}`} is now ${chalk.cyan(msg)}`;\n  for (const [key, value] of items) {\n    Log.log(tableFormat(key, value));\n  }\n}\n"], "names": ["baseTSConfigName", "updateTSConfigAsync", "projectTSConfig", "tsConfigPath", "shouldGenerate", "fs", "existsSync", "statSync", "size", "JsonFile", "writeAsync", "compilerOptions", "read", "json5", "modifications", "extends", "push", "length", "Log", "log", "chalk", "logModifications", "printTable", "sort", "a", "b", "items", "tableFormat", "name", "msg", "bold", "cyan", "key", "value"], "mappings": "AAAA;;;;;;;;;;;IAMaA,gBAAgB,MAAhBA,gBAAgB;IAEPC,mBAAmB,MAAnBA,mBAAmB;;;8DARpB,iBAAiB;;;;;;;8DACpB,OAAO;;;;;;;8DACV,IAAI;;;;;;2DAEE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmBjCC,gBAAe;AAjBV,MAAMF,gBAAgB,GAAG,oBAAoB,AAAC;AAE9C,eAAeC,mBAAmB,CAAC,EACxCE,YAAY,CAAA,EAGb,EAAiB;IAChB,MAAMC,cAAc,GAAG,CAACC,GAAE,EAAA,QAAA,CAACC,UAAU,CAACH,YAAY,CAAC,IAAIE,GAAE,EAAA,QAAA,CAACE,QAAQ,CAACJ,YAAY,CAAC,CAACK,IAAI,KAAK,CAAC,AAAC;IAC5F,IAAIJ,cAAc,EAAE;QAClB,MAAMK,SAAQ,EAAA,QAAA,CAACC,UAAU,CAACP,YAAY,EAAE;YAAEQ,eAAe,EAAE,EAAE;SAAE,CAAC,CAAC;IACnE,CAAC;IAED,MAAMT,eAAe,GAAGO,SAAQ,EAAA,QAAA,CAACG,IAAI,CAACT,YAAY,EAAE;QAClD,iEAAiE;QACjEU,KAAK,EAAE,IAAI;KACZ,CAAC,AAAC;;IAEHX,qBAAAA,gBAAe,GAAfA,eAAe,EAACS,eAAe,+BAA/BT,gBAAe,CAACS,eAAe,GAAK,EAAE,CAAC;IAEvC,MAAMG,aAAa,GAAuB,EAAE,AAAC;IAE7C,iEAAiE;IACjE,IAAI,CAACZ,eAAe,CAACa,OAAO,EAAE;QAC5B,sDAAsD;QACtDb,eAAe,CAACa,OAAO,GAAGf,gBAAgB,CAAC;QAC3Cc,aAAa,CAACE,IAAI,CAAC;YAAC,SAAS;YAAEhB,gBAAgB;SAAC,CAAC,CAAC;IACpD,CAAC;IAED,uCAAuC;IACvC,IAAI,CAACc,aAAa,CAACG,MAAM,EAAE;QACzB,OAAO;IACT,CAAC;IAED,sDAAsD;IACtD,MAAMR,SAAQ,EAAA,QAAA,CAACC,UAAU,CAACP,YAAY,EAAED,eAAe,CAAC,CAAC;IAEzD,uCAAuC;IACvC,IAAIY,aAAa,CAACG,MAAM,KAAK,CAAC,EAAE;QAC9B,OAAO;IACT,CAAC;IAEDC,IAAG,CAACC,GAAG,EAAE,CAAC;IAEV,IAAIf,cAAc,EAAE;QAClBc,IAAG,CAACC,GAAG,CAACC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,iEAAiE,CAAC,CAAC,CAAC;IACpF,OAAO;QACLF,IAAG,CAACC,GAAG,CACLC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,yGAAyG,CAAC,CACjH,CAAC;QACFC,gBAAgB,CAACP,aAAa,CAAC,CAAC;IAClC,CAAC;IACDI,IAAG,CAACC,GAAG,EAAE,CAAC;AACZ,CAAC;AAED,SAASE,gBAAgB,CAACP,aAAyB,EAAE;IACnDI,IAAG,CAACC,GAAG,EAAE,CAAC;IAEVD,IAAG,CAACC,GAAG,CAACC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,sEAAsE,CAAC,CAAC,CAAC;IAEvFF,IAAG,CAACC,GAAG,EAAE,CAAC;IAEV,0CAA0C;IAC1CG,UAAU,CAACR,aAAa,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,GAAKD,CAAC,CAAC,CAAC,CAAC,CAACP,MAAM,GAAGQ,CAAC,CAAC,CAAC,CAAC,CAACR,MAAM,CAAC,CAAC,CAAC;IAEpEC,IAAG,CAACC,GAAG,EAAE,CAAC;AACZ,CAAC;AAED,SAASG,UAAU,CAACI,KAAiB,EAAE;IACrC,MAAMC,WAAW,GAAG,CAACC,IAAY,EAAEC,GAAW,GAC5C,CAAC,EAAE,EAAET,MAAK,EAAA,QAAA,CAACU,IAAI,CAAC,EAAEF,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAER,MAAK,EAAA,QAAA,CAACW,IAAI,CAACF,GAAG,CAAC,CAAC,CAAC,AAAC;IACvD,KAAK,MAAM,CAACG,GAAG,EAAEC,KAAK,CAAC,IAAIP,KAAK,CAAE;QAChCR,IAAG,CAACC,GAAG,CAACQ,WAAW,CAACK,GAAG,EAAEC,KAAK,CAAC,CAAC,CAAC;IACnC,CAAC;AACH,CAAC"}