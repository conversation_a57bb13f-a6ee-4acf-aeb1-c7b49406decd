{"version": 3, "sources": ["../../../../src/start/doctor/SecurityBinPrerequisite.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\n\nimport { Prerequisite, PrerequisiteCommandError } from './Prerequisite';\n\nexport class SecurityBinPrerequisite extends Prerequisite {\n  static instance = new SecurityBinPrerequisite();\n\n  async assertImplementation(): Promise<void> {\n    try {\n      // make sure we can run security\n      await spawnAsync('which', ['security']);\n    } catch {\n      throw new PrerequisiteCommandError(\n        'SECURITY_BIN',\n        \"Cannot code sign project because the CLI `security` is not available on your computer.\\nPlease ensure it's installed and try again.\"\n      );\n    }\n  }\n}\n"], "names": ["SecurityBinPrerequisite", "Prerequisite", "instance", "assertImplementation", "spawnAsync", "PrerequisiteCommandError"], "mappings": "AAAA;;;;+BAIaA,yBAAuB;;aAAvBA,uBAAuB;;;8DAJb,mBAAmB;;;;;;8BAEa,gBAAgB;;;;;;AAEhE,MAAMA,uBAAuB,SAASC,aAAY,aAAA;IACvD,OAAOC,QAAQ,GAAG,IAAIF,uBAAuB,EAAE,CAAC;UAE1CG,oBAAoB,GAAkB;QAC1C,IAAI;YACF,gCAAgC;YAChC,MAAMC,IAAAA,WAAU,EAAA,QAAA,EAAC,OAAO,EAAE;gBAAC,UAAU;aAAC,CAAC,CAAC;QAC1C,EAAE,OAAM;YACN,MAAM,IAAIC,aAAwB,yBAAA,CAChC,cAAc,EACd,qIAAqI,CACtI,CAAC;QACJ,CAAC;IACH;CACD"}