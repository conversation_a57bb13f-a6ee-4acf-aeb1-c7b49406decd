{"version": 3, "sources": ["../../../../../src/start/doctor/ngrok/NgrokResolver.ts"], "sourcesContent": ["import { ExternalModule } from './ExternalModule';\n\nexport interface NgrokClientError {\n  body: {\n    error_code: number;\n    status_code: number;\n    msg: string;\n    details: {\n      err: string;\n    };\n  };\n}\n\nexport interface NgrokOptions {\n  authtoken?: string;\n  port?: string | number | null;\n  host?: string;\n  httpauth?: string;\n  region?: string;\n  configPath?: string;\n\n  proto?: 'http' | 'tcp' | 'tls';\n  addr?: string;\n  inspect?: boolean;\n  auth?: string;\n  host_header?: string;\n  bind_tls?: true | false | 'both';\n  subdomain?: string;\n  hostname?: string;\n  crt?: string;\n  key?: string;\n  client_cas?: string;\n  remote_addr?: string;\n}\n\nexport interface NgrokInstance {\n  getActiveProcess(): { pid: number };\n  connect(\n    props: {\n      hostname?: string;\n      configPath: string;\n      onStatusChange: (status: string) => void;\n    } & NgrokOptions\n  ): Promise<string>;\n  kill(): Promise<void>;\n}\n\n/** Resolves the ngrok instance from local or globally installed package. */\nexport class NgrokResolver extends ExternalModule<NgrokInstance> {\n  constructor(projectRoot: string) {\n    super(\n      projectRoot,\n      {\n        name: '@expo/ngrok',\n        versionRange: '^4.1.0',\n      },\n      (packageName) =>\n        `The package ${packageName} is required to use tunnels, would you like to install it globally?`\n    );\n  }\n}\n\nexport function isNgrokClientError(error: any): error is NgrokClientError {\n  return error?.body?.msg;\n}\n"], "names": ["NgrokResolver", "isNgrokClientError", "ExternalModule", "constructor", "projectRoot", "name", "versionRange", "packageName", "error", "body", "msg"], "mappings": "AAAA;;;;;;;;;;;IAgDaA,aAAa,MAAbA,aAAa;IAcVC,kBAAkB,MAAlBA,kBAAkB;;gCA9DH,kBAAkB;AAgD1C,MAAMD,aAAa,SAASE,eAAc,eAAA;IAC/CC,YAAYC,WAAmB,CAAE;QAC/B,KAAK,CACHA,WAAW,EACX;YACEC,IAAI,EAAE,aAAa;YACnBC,YAAY,EAAE,QAAQ;SACvB,EACD,CAACC,WAAW,GACV,CAAC,YAAY,EAAEA,WAAW,CAAC,mEAAmE,CAAC,CAClG,CAAC;IACJ;CACD;AAEM,SAASN,kBAAkB,CAACO,KAAU,EAA6B;QACjEA,GAAW;IAAlB,OAAOA,KAAK,QAAM,GAAXA,KAAAA,CAAW,GAAXA,CAAAA,GAAW,GAAXA,KAAK,CAAEC,IAAI,SAAA,GAAXD,KAAAA,CAAW,GAAXA,GAAW,CAAEE,GAAG,AAAL,CAAM;AAC1B,CAAC"}