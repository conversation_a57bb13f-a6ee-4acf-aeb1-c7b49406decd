{"version": 3, "sources": ["../../../../../src/start/doctor/apple/XcrunPrerequisite.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport { execSync } from 'child_process';\n\nimport { delayAsync } from '../../../utils/delay';\nimport { AbortCommandError } from '../../../utils/errors';\nimport { confirmAsync } from '../../../utils/prompts';\nimport { Prerequisite } from '../Prerequisite';\n\nasync function isXcrunInstalledAsync() {\n  try {\n    execSync('xcrun --version', { stdio: 'ignore' });\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nexport class XcrunPrerequisite extends Prerequisite {\n  static instance = new XcrunPrerequisite();\n\n  /**\n   * Ensure Xcode CLI is installed.\n   */\n  async assertImplementation(): Promise<void> {\n    if (await isXcrunInstalledAsync()) {\n      // Run this second to ensure the Xcode version check is run.\n      return;\n    }\n\n    async function pendingAsync(): Promise<void> {\n      if (!(await isXcrunInstalledAsync())) {\n        await delayAsync(100);\n        return await pendingAsync();\n      }\n    }\n\n    // This prompt serves no purpose accept informing the user what to do next, we could just open the App Store but it could be confusing if they don't know what's going on.\n    const confirm = await confirmAsync({\n      initial: true,\n      message: chalk`Xcode {bold Command Line Tools} needs to be installed (requires {bold sudo}), continue?`,\n    });\n\n    if (confirm) {\n      try {\n        await spawnAsync('sudo', [\n          'xcode-select',\n          '--install',\n          // TODO: Is there any harm in skipping this?\n          // '--switch', '/Applications/Xcode.app'\n        ]);\n        // Most likely the user will cancel the process, but if they don't this will continue checking until the CLI is available.\n        return await pendingAsync();\n      } catch {\n        // TODO: Figure out why this might get called (cancel early, network issues, server problems)\n        // TODO: Handle me\n      }\n    }\n\n    throw new AbortCommandError();\n  }\n}\n"], "names": ["XcrunPrerequisite", "isXcrunInstalledAsync", "execSync", "stdio", "Prerequisite", "instance", "assertImplementation", "pendingAsync", "delayAsync", "confirm", "<PERSON><PERSON><PERSON>", "initial", "message", "chalk", "spawnAsync", "AbortCommandError"], "mappings": "AAAA;;;;+BAkBaA,mBAAiB;;aAAjBA,iBAAiB;;;8DAlBP,mBAAmB;;;;;;;8DACxB,OAAO;;;;;;;yBACA,eAAe;;;;;;uBAEb,sBAAsB;wBACf,uBAAuB;yBAC5B,wBAAwB;8BACxB,iBAAiB;;;;;;AAE9C,eAAeC,qBAAqB,GAAG;IACrC,IAAI;QACFC,IAAAA,aAAQ,EAAA,SAAA,EAAC,iBAAiB,EAAE;YAAEC,KAAK,EAAE,QAAQ;SAAE,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC;IACd,EAAE,OAAM;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAEM,MAAMH,iBAAiB,SAASI,aAAY,aAAA;IACjD,OAAOC,QAAQ,GAAG,IAAIL,iBAAiB,EAAE,CAAC;IAE1C;;GAEC,SACKM,oBAAoB,GAAkB;QAC1C,IAAI,MAAML,qBAAqB,EAAE,EAAE;YACjC,4DAA4D;YAC5D,OAAO;QACT,CAAC;QAED,eAAeM,YAAY,GAAkB;YAC3C,IAAI,CAAE,MAAMN,qBAAqB,EAAE,AAAC,EAAE;gBACpC,MAAMO,IAAAA,MAAU,WAAA,EAAC,GAAG,CAAC,CAAC;gBACtB,OAAO,MAAMD,YAAY,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,0KAA0K;QAC1K,MAAME,OAAO,GAAG,MAAMC,IAAAA,QAAY,aAAA,EAAC;YACjCC,OAAO,EAAE,IAAI;YACbC,OAAO,EAAEC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,uFAAuF,CAAC;SACxG,CAAC,AAAC;QAEH,IAAIJ,OAAO,EAAE;YACX,IAAI;gBACF,MAAMK,IAAAA,WAAU,EAAA,QAAA,EAAC,MAAM,EAAE;oBACvB,cAAc;oBACd,WAAW;iBAGZ,CAAC,CAAC;gBACH,0HAA0H;gBAC1H,OAAO,MAAMP,YAAY,EAAE,CAAC;YAC9B,EAAE,OAAM;YACN,6FAA6F;YAC7F,kBAAkB;YACpB,CAAC;QACH,CAAC;QAED,MAAM,IAAIQ,OAAiB,kBAAA,EAAE,CAAC;IAChC;CACD"}