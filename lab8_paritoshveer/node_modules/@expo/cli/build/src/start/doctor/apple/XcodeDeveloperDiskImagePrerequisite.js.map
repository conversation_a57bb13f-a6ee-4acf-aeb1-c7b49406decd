{"version": 3, "sources": ["../../../../../src/start/doctor/apple/XcodeDeveloperDiskImagePrerequisite.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport fs from 'fs/promises';\n\nimport * as Log from '../../../log';\nimport { Prerequisite, PrerequisiteCommandError } from '../Prerequisite';\n\nconst ERROR_CODE = 'XCODE_DEVELOPER_DISK_IMAGE';\nasync function getXcodePathAsync(): Promise<string> {\n  try {\n    const { stdout } = await spawnAsync('xcode-select', ['-p']);\n    if (stdout) {\n      return stdout.trim();\n    }\n  } catch (error: any) {\n    Log.debug(`Could not find Xcode path: %O`, error);\n  }\n  throw new PrerequisiteCommandError(ERROR_CODE, 'Unable to locate Xcode.');\n}\n\nexport class XcodeDeveloperDiskImagePrerequisite extends Prerequisite<string, { version: string }> {\n  static instance = new XcodeDeveloperDiskImagePrerequisite();\n\n  async assertImplementation({ version }: { version: string }): Promise<string> {\n    const xcodePath = await getXcodePathAsync();\n    // Like \"11.2 (15C107)\"\n    const versions = await fs.readdir(`${xcodePath}/Platforms/iPhoneOS.platform/DeviceSupport/`);\n    const prefix = version.match(/\\d+\\.\\d+/);\n    if (prefix === null) {\n      throw new PrerequisiteCommandError(ERROR_CODE, `Invalid iOS version: ${version}`);\n    }\n    for (const directory of versions) {\n      if (directory.includes(prefix[0])) {\n        return `${xcodePath}/Platforms/iPhoneOS.platform/DeviceSupport/${directory}/DeveloperDiskImage.dmg`;\n      }\n    }\n    throw new PrerequisiteCommandError(\n      ERROR_CODE,\n      `Unable to find Developer Disk Image path for SDK ${version}.`\n    );\n  }\n}\n"], "names": ["XcodeDeveloperDiskImagePrerequisite", "ERROR_CODE", "getXcodePathAsync", "stdout", "spawnAsync", "trim", "error", "Log", "debug", "PrerequisiteCommandError", "Prerequisite", "instance", "assertImplementation", "version", "xcodePath", "versions", "fs", "readdir", "prefix", "match", "directory", "includes"], "mappings": "AAAA;;;;+BAmBaA,qCAAmC;;aAAnCA,mCAAmC;;;8DAnBzB,mBAAmB;;;;;;;8DAC3B,aAAa;;;;;;2DAEP,cAAc;8BACoB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExE,MAAMC,UAAU,GAAG,4BAA4B,AAAC;AAChD,eAAeC,iBAAiB,GAAoB;IAClD,IAAI;QACF,MAAM,EAAEC,MAAM,CAAA,EAAE,GAAG,MAAMC,IAAAA,WAAU,EAAA,QAAA,EAAC,cAAc,EAAE;YAAC,IAAI;SAAC,CAAC,AAAC;QAC5D,IAAID,MAAM,EAAE;YACV,OAAOA,MAAM,CAACE,IAAI,EAAE,CAAC;QACvB,CAAC;IACH,EAAE,OAAOC,KAAK,EAAO;QACnBC,IAAG,CAACC,KAAK,CAAC,CAAC,6BAA6B,CAAC,EAAEF,KAAK,CAAC,CAAC;IACpD,CAAC;IACD,MAAM,IAAIG,aAAwB,yBAAA,CAACR,UAAU,EAAE,yBAAyB,CAAC,CAAC;AAC5E,CAAC;AAEM,MAAMD,mCAAmC,SAASU,aAAY,aAAA;IACnE,OAAOC,QAAQ,GAAG,IAAIX,mCAAmC,EAAE,CAAC;UAEtDY,oBAAoB,CAAC,EAAEC,OAAO,CAAA,EAAuB,EAAmB;QAC5E,MAAMC,SAAS,GAAG,MAAMZ,iBAAiB,EAAE,AAAC;QAC5C,uBAAuB;QACvB,MAAMa,QAAQ,GAAG,MAAMC,SAAE,EAAA,QAAA,CAACC,OAAO,CAAC,CAAC,EAAEH,SAAS,CAAC,2CAA2C,CAAC,CAAC,AAAC;QAC7F,MAAMI,MAAM,GAAGL,OAAO,CAACM,KAAK,YAAY,AAAC;QACzC,IAAID,MAAM,KAAK,IAAI,EAAE;YACnB,MAAM,IAAIT,aAAwB,yBAAA,CAACR,UAAU,EAAE,CAAC,qBAAqB,EAAEY,OAAO,CAAC,CAAC,CAAC,CAAC;QACpF,CAAC;QACD,KAAK,MAAMO,SAAS,IAAIL,QAAQ,CAAE;YAChC,IAAIK,SAAS,CAACC,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;gBACjC,OAAO,CAAC,EAAEJ,SAAS,CAAC,2CAA2C,EAAEM,SAAS,CAAC,uBAAuB,CAAC,CAAC;YACtG,CAAC;QACH,CAAC;QACD,MAAM,IAAIX,aAAwB,yBAAA,CAChCR,UAAU,EACV,CAAC,iDAAiD,EAAEY,OAAO,CAAC,CAAC,CAAC,CAC/D,CAAC;IACJ;CACD"}