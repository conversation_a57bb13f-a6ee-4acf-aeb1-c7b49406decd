{"version": 3, "sources": ["../../../../../src/start/doctor/apple/SimulatorAppPrerequisite.ts"], "sourcesContent": ["import { execAsync } from '@expo/osascript';\nimport spawnAsync from '@expo/spawn-async';\n\nimport * as Log from '../../../log';\nimport { Prerequisite, PrerequisiteCommandError } from '../Prerequisite';\n\nconst debug = require('debug')('expo:doctor:apple:simulatorApp') as typeof console.log;\n\nasync function getSimulatorAppIdAsync(): Promise<string | null> {\n  try {\n    return (await execAsync('id of app \"Simulator\"')).trim();\n  } catch {\n    // This error may occur in CI where the users intends to install just the simulators but no Xcode.\n  }\n  return null;\n}\n\nexport class SimulatorAppPrerequisite extends Prerequisite {\n  static instance = new SimulatorAppPrerequisite();\n\n  async assertImplementation(): Promise<void> {\n    const result = await getSimulatorAppIdAsync();\n    if (!result) {\n      // This error may occur in CI where the users intends to install just the simulators but no Xcode.\n      throw new PrerequisiteCommandError(\n        'SIMULATOR_APP',\n        \"Can't determine id of Simulator app; the Simulator is most likely not installed on this machine. Run `sudo xcode-select -s /Applications/Xcode.app`\"\n      );\n    }\n    if (\n      result !== 'com.apple.iphonesimulator' &&\n      result !== 'com.apple.CoreSimulator.SimulatorTrampoline'\n    ) {\n      throw new PrerequisiteCommandError(\n        'SIMULATOR_APP',\n        \"Simulator is installed but is identified as '\" + result + \"'; don't know what that is.\"\n      );\n    }\n    debug(`Simulator app id: ${result}`);\n\n    try {\n      // make sure we can run simctl\n      await spawnAsync('xcrun', ['simctl', 'help']);\n    } catch (error: any) {\n      Log.warn(`Unable to run simctl:\\n${error.toString()}`);\n      throw new PrerequisiteCommandError(\n        'SIMCTL',\n        'xcrun is not configured correctly. Ensure `sudo xcode-select --reset` works before running this command again.'\n      );\n    }\n  }\n}\n"], "names": ["SimulatorAppPrerequisite", "debug", "require", "getSimulatorAppIdAsync", "execAsync", "trim", "Prerequisite", "instance", "assertImplementation", "result", "PrerequisiteCommandError", "spawnAsync", "error", "Log", "warn", "toString"], "mappings": "AAAA;;;;+BAiBaA,0BAAwB;;aAAxBA,wBAAwB;;;yBAjBX,iBAAiB;;;;;;;8DACpB,mBAAmB;;;;;;2DAErB,cAAc;8BACoB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExE,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,gCAAgC,CAAC,AAAsB,AAAC;AAEvF,eAAeC,sBAAsB,GAA2B;IAC9D,IAAI;QACF,OAAO,CAAC,MAAMC,IAAAA,UAAS,EAAA,UAAA,EAAC,uBAAuB,CAAC,CAAC,CAACC,IAAI,EAAE,CAAC;IAC3D,EAAE,OAAM;IACN,kGAAkG;IACpG,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,MAAML,wBAAwB,SAASM,aAAY,aAAA;IACxD,OAAOC,QAAQ,GAAG,IAAIP,wBAAwB,EAAE,CAAC;UAE3CQ,oBAAoB,GAAkB;QAC1C,MAAMC,MAAM,GAAG,MAAMN,sBAAsB,EAAE,AAAC;QAC9C,IAAI,CAACM,MAAM,EAAE;YACX,kGAAkG;YAClG,MAAM,IAAIC,aAAwB,yBAAA,CAChC,eAAe,EACf,qJAAqJ,CACtJ,CAAC;QACJ,CAAC;QACD,IACED,MAAM,KAAK,2BAA2B,IACtCA,MAAM,KAAK,6CAA6C,EACxD;YACA,MAAM,IAAIC,aAAwB,yBAAA,CAChC,eAAe,EACf,+CAA+C,GAAGD,MAAM,GAAG,6BAA6B,CACzF,CAAC;QACJ,CAAC;QACDR,KAAK,CAAC,CAAC,kBAAkB,EAAEQ,MAAM,CAAC,CAAC,CAAC,CAAC;QAErC,IAAI;YACF,8BAA8B;YAC9B,MAAME,IAAAA,WAAU,EAAA,QAAA,EAAC,OAAO,EAAE;gBAAC,QAAQ;gBAAE,MAAM;aAAC,CAAC,CAAC;QAChD,EAAE,OAAOC,KAAK,EAAO;YACnBC,IAAG,CAACC,IAAI,CAAC,CAAC,uBAAuB,EAAEF,KAAK,CAACG,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,IAAIL,aAAwB,yBAAA,CAChC,QAAQ,EACR,gHAAgH,CACjH,CAAC;QACJ,CAAC;IACH;CACD"}