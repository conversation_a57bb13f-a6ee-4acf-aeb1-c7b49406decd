{"version": 3, "sources": ["../../../../../src/start/doctor/typescript/TypeScriptProjectPrerequisite.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nimport { updateTSConfigAsync } from './updateTSConfig';\nimport * as Log from '../../../log';\nimport { fileExistsAsync } from '../../../utils/dir';\nimport { env } from '../../../utils/env';\nimport { memoize } from '../../../utils/fn';\nimport { everyMatchAsync, wrapGlobWithTimeout } from '../../../utils/glob';\nimport { ProjectPrerequisite } from '../Prerequisite';\nimport { ensureDependenciesAsync } from '../dependencies/ensureDependenciesAsync';\n\nconst debug = require('debug')('expo:doctor:typescriptSupport') as typeof console.log;\n\nconst warnDisabled = memoize(() => {\n  Log.warn('Skipping TypeScript setup: EXPO_NO_TYPESCRIPT_SETUP is enabled.');\n});\n\n/** Ensure the project has the required TypeScript support settings. */\nexport class TypeScriptProjectPrerequisite extends ProjectPrerequisite<boolean> {\n  /**\n   * Ensure a project that hasn't explicitly disabled typescript support has all the required packages for running in the browser.\n   *\n   * @returns `true` if the setup finished and no longer needs to be run again.\n   */\n  async assertImplementation(): Promise<boolean> {\n    if (env.EXPO_NO_TYPESCRIPT_SETUP) {\n      warnDisabled();\n      return true;\n    }\n    debug('Ensuring TypeScript support is setup');\n\n    const tsConfigPath = path.join(this.projectRoot, 'tsconfig.json');\n\n    // Ensure the project is TypeScript before continuing.\n    const intent = await this._getSetupRequirements();\n    if (!intent) {\n      return false;\n    }\n\n    // Ensure TypeScript packages are installed\n    await this._ensureDependenciesInstalledAsync();\n\n    // Update the config\n    await updateTSConfigAsync({ tsConfigPath });\n\n    return true;\n  }\n\n  async bootstrapAsync(): Promise<void> {\n    if (env.EXPO_NO_TYPESCRIPT_SETUP) {\n      warnDisabled();\n      return;\n    }\n    // Ensure TypeScript packages are installed\n    await this._ensureDependenciesInstalledAsync({\n      skipPrompt: true,\n      isProjectMutable: true,\n    });\n\n    const tsConfigPath = path.join(this.projectRoot, 'tsconfig.json');\n\n    // Update the config\n    await updateTSConfigAsync({ tsConfigPath });\n  }\n\n  /** Exposed for testing. */\n  async _getSetupRequirements(): Promise<{\n    /** Indicates that TypeScript support is being bootstrapped. */\n    isBootstrapping: boolean;\n  } | null> {\n    const tsConfigPath = await this._hasTSConfig();\n\n    // Enable TS setup if the project has a `tsconfig.json`\n    if (tsConfigPath) {\n      const content = await fs.readFile(tsConfigPath, { encoding: 'utf8' }).then(\n        (txt) => txt.trim(),\n        // null when the file doesn't exist.\n        () => null\n      );\n      const isBlankConfig = content === '' || content === '{}';\n      return { isBootstrapping: isBlankConfig };\n    }\n    // This is a somewhat heavy check in larger projects.\n    // Test that this is reasonably paced by running expo start in `expo/apps/native-component-list`\n    const typescriptFile = await this._queryFirstTypeScriptFileAsync();\n    if (typescriptFile) {\n      return { isBootstrapping: true };\n    }\n\n    return null;\n  }\n\n  /** Exposed for testing. */\n  async _ensureDependenciesInstalledAsync({\n    exp,\n    skipPrompt,\n    isProjectMutable,\n  }: {\n    exp?: ExpoConfig;\n    skipPrompt?: boolean;\n    isProjectMutable?: boolean;\n  } = {}): Promise<boolean> {\n    try {\n      return await ensureDependenciesAsync(this.projectRoot, {\n        exp,\n        skipPrompt,\n        isProjectMutable,\n        installMessage: `It looks like you're trying to use TypeScript but don't have the required dependencies installed.`,\n        warningMessage:\n          \"If you're not using TypeScript, please remove the TypeScript files from your project\",\n        requiredPackages: [\n          // use typescript/package.json to skip node module cache issues when the user installs\n          // the package and attempts to resolve the module in the same process.\n          { file: 'typescript/package.json', pkg: 'typescript' },\n          { file: '@types/react/package.json', pkg: '@types/react' },\n        ],\n      });\n    } catch (error) {\n      // Reset the cached check so we can re-run the check if the user re-runs the command by pressing 'w' in the Terminal UI.\n      this.resetAssertion();\n      throw error;\n    }\n  }\n\n  /** Return the first TypeScript file in the project. */\n  async _queryFirstTypeScriptFileAsync(): Promise<null | string> {\n    const results = await wrapGlobWithTimeout(\n      () =>\n        // TODO(Bacon): Use `everyMatch` since a bug causes `anyMatch` to return inaccurate results when used multiple times.\n        everyMatchAsync('**/*.@(ts|tsx)', {\n          cwd: this.projectRoot,\n          ignore: [\n            '**/@(Carthage|Pods|node_modules)/**',\n            '**/*.d.ts',\n            '@(ios|android|web|web-build|dist)/**',\n          ],\n        }),\n      5000\n    );\n\n    if (results === false) {\n      return null;\n    }\n    return results[0] ?? null;\n  }\n\n  async _hasTSConfig(): Promise<string | null> {\n    const tsConfigPath = path.join(this.projectRoot, 'tsconfig.json');\n    if (await fileExistsAsync(tsConfigPath)) {\n      return tsConfigPath;\n    }\n    return null;\n  }\n}\n"], "names": ["TypeScriptProjectPrerequisite", "debug", "require", "warnDisabled", "memoize", "Log", "warn", "ProjectPrerequisite", "assertImplementation", "env", "EXPO_NO_TYPESCRIPT_SETUP", "tsConfigPath", "path", "join", "projectRoot", "intent", "_getSetupRequirements", "_ensureDependenciesInstalledAsync", "updateTSConfigAsync", "bootstrapAsync", "skip<PERSON>rompt", "isProjectMutable", "_hasTSConfig", "content", "fs", "readFile", "encoding", "then", "txt", "trim", "isBlankConfig", "isBootstrapping", "typescriptFile", "_queryFirstTypeScriptFileAsync", "exp", "ensureDependenciesAsync", "installMessage", "warningMessage", "requiredPackages", "file", "pkg", "error", "resetAssertion", "results", "wrapGlobWithTimeout", "everyMatchAsync", "cwd", "ignore", "fileExistsAsync"], "mappings": "AAAA;;;;+BAoBaA,+BAA6B;;aAA7BA,6BAA6B;;;8DAnB3B,aAAa;;;;;;;8DACX,MAAM;;;;;;gCAEa,kBAAkB;2DACjC,cAAc;qBACH,oBAAoB;qBAChC,oBAAoB;oBAChB,mBAAmB;sBACU,qBAAqB;8BACtC,iBAAiB;yCACb,yCAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjF,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,+BAA+B,CAAC,AAAsB,AAAC;AAEtF,MAAMC,YAAY,GAAGC,IAAAA,GAAO,QAAA,EAAC,IAAM;IACjCC,IAAG,CAACC,IAAI,CAAC,iEAAiE,CAAC,CAAC;AAC9E,CAAC,CAAC,AAAC;AAGI,MAAMN,6BAA6B,SAASO,aAAmB,oBAAA;IACpE;;;;GAIC,SACKC,oBAAoB,GAAqB;QAC7C,IAAIC,IAAG,IAAA,CAACC,wBAAwB,EAAE;YAChCP,YAAY,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;QACDF,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAE9C,MAAMU,YAAY,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,eAAe,CAAC,AAAC;QAElE,sDAAsD;QACtD,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACC,qBAAqB,EAAE,AAAC;QAClD,IAAI,CAACD,MAAM,EAAE;YACX,OAAO,KAAK,CAAC;QACf,CAAC;QAED,2CAA2C;QAC3C,MAAM,IAAI,CAACE,iCAAiC,EAAE,CAAC;QAE/C,oBAAoB;QACpB,MAAMC,IAAAA,eAAmB,oBAAA,EAAC;YAAEP,YAAY;SAAE,CAAC,CAAC;QAE5C,OAAO,IAAI,CAAC;IACd;UAEMQ,cAAc,GAAkB;QACpC,IAAIV,IAAG,IAAA,CAACC,wBAAwB,EAAE;YAChCP,YAAY,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QACD,2CAA2C;QAC3C,MAAM,IAAI,CAACc,iCAAiC,CAAC;YAC3CG,UAAU,EAAE,IAAI;YAChBC,gBAAgB,EAAE,IAAI;SACvB,CAAC,CAAC;QAEH,MAAMV,YAAY,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,eAAe,CAAC,AAAC;QAElE,oBAAoB;QACpB,MAAMI,IAAAA,eAAmB,oBAAA,EAAC;YAAEP,YAAY;SAAE,CAAC,CAAC;IAC9C;IAEA,yBAAyB,SACnBK,qBAAqB,GAGjB;QACR,MAAML,YAAY,GAAG,MAAM,IAAI,CAACW,YAAY,EAAE,AAAC;QAE/C,uDAAuD;QACvD,IAAIX,YAAY,EAAE;YAChB,MAAMY,OAAO,GAAG,MAAMC,SAAE,EAAA,QAAA,CAACC,QAAQ,CAACd,YAAY,EAAE;gBAAEe,QAAQ,EAAE,MAAM;aAAE,CAAC,CAACC,IAAI,CACxE,CAACC,GAAG,GAAKA,GAAG,CAACC,IAAI,EAAE,EACnB,oCAAoC;YACpC,IAAM,IAAI,CACX,AAAC;YACF,MAAMC,aAAa,GAAGP,OAAO,KAAK,EAAE,IAAIA,OAAO,KAAK,IAAI,AAAC;YACzD,OAAO;gBAAEQ,eAAe,EAAED,aAAa;aAAE,CAAC;QAC5C,CAAC;QACD,qDAAqD;QACrD,gGAAgG;QAChG,MAAME,cAAc,GAAG,MAAM,IAAI,CAACC,8BAA8B,EAAE,AAAC;QACnE,IAAID,cAAc,EAAE;YAClB,OAAO;gBAAED,eAAe,EAAE,IAAI;aAAE,CAAC;QACnC,CAAC;QAED,OAAO,IAAI,CAAC;IACd;IAEA,yBAAyB,SACnBd,iCAAiC,CAAC,EACtCiB,GAAG,CAAA,EACHd,UAAU,CAAA,EACVC,gBAAgB,CAAA,EAKjB,GAAG,EAAE,EAAoB;QACxB,IAAI;YACF,OAAO,MAAMc,IAAAA,wBAAuB,wBAAA,EAAC,IAAI,CAACrB,WAAW,EAAE;gBACrDoB,GAAG;gBACHd,UAAU;gBACVC,gBAAgB;gBAChBe,cAAc,EAAE,CAAC,iGAAiG,CAAC;gBACnHC,cAAc,EACZ,sFAAsF;gBACxFC,gBAAgB,EAAE;oBAChB,sFAAsF;oBACtF,sEAAsE;oBACtE;wBAAEC,IAAI,EAAE,yBAAyB;wBAAEC,GAAG,EAAE,YAAY;qBAAE;oBACtD;wBAAED,IAAI,EAAE,2BAA2B;wBAAEC,GAAG,EAAE,cAAc;qBAAE;iBAC3D;aACF,CAAC,CAAC;QACL,EAAE,OAAOC,KAAK,EAAE;YACd,wHAAwH;YACxH,IAAI,CAACC,cAAc,EAAE,CAAC;YACtB,MAAMD,KAAK,CAAC;QACd,CAAC;IACH;IAEA,qDAAqD,SAC/CR,8BAA8B,GAA2B;QAC7D,MAAMU,OAAO,GAAG,MAAMC,IAAAA,KAAmB,oBAAA,EACvC,IACE,qHAAqH;YACrHC,IAAAA,KAAe,gBAAA,EAAC,gBAAgB,EAAE;gBAChCC,GAAG,EAAE,IAAI,CAAChC,WAAW;gBACrBiC,MAAM,EAAE;oBACN,qCAAqC;oBACrC,WAAW;oBACX,sCAAsC;iBACvC;aACF,CAAC,EACJ,IAAI,CACL,AAAC;QAEF,IAAIJ,OAAO,KAAK,KAAK,EAAE;YACrB,OAAO,IAAI,CAAC;QACd,CAAC;YACMA,GAAU;QAAjB,OAAOA,CAAAA,GAAU,GAAVA,OAAO,CAAC,CAAC,CAAC,YAAVA,GAAU,GAAI,IAAI,CAAC;IAC5B;UAEMrB,YAAY,GAA2B;QAC3C,MAAMX,YAAY,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,eAAe,CAAC,AAAC;QAClE,IAAI,MAAMkC,IAAAA,IAAe,gBAAA,EAACrC,YAAY,CAAC,EAAE;YACvC,OAAOA,YAAY,CAAC;QACtB,CAAC;QACD,OAAO,IAAI,CAAC;IACd;CACD"}