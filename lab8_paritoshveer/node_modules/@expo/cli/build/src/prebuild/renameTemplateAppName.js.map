{"version": 3, "sources": ["../../../src/prebuild/renameTemplateAppName.ts"], "sourcesContent": ["import { IOSConfig } from '@expo/config-plugins';\nimport { glob } from 'fast-glob';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { ExtractProps } from '../utils/npm';\n\nconst debug = require('debug')('expo:prebuild:copyTemplateFiles') as typeof console.log;\n\nfunction escapeXMLCharacters(original: string): string {\n  const noAmps = original.replace('&', '&amp;');\n  const noLt = noAmps.replace('<', '&lt;');\n  const noGt = noLt.replace('>', '&gt;');\n  const noApos = noGt.replace('\"', '\\\\\"');\n  return noApos.replace(\"'\", \"\\\\'\");\n}\n\n/**\n * # Background\n *\n * `@expo/cli` and `create-expo` extract a template from a tarball (whether from\n * a local npm project or a GitHub repository), but these templates have a\n * static name that needs to be updated to match whatever app name the user\n * specified.\n *\n * By convention, the app name of all templates is \"HelloWorld\". During\n * extraction, filepaths are transformed via `createEntryResolver()` in\n * `createFileTransform.ts`, but the contents of files are left untouched.\n * Technically, the contents used to be transformed during extraction as well,\n * but due to poor configurability, we've moved to a post-extraction approach.\n *\n * # The new approach: Renaming the app post-extraction\n *\n * In this new approach, we take a list of file patterns, otherwise known as the\n * \"rename config\" to determine explicitly which files – relative to the root of\n * the template – to perform find-and-replace on, to update the app name.\n *\n * ## The rename config\n *\n * The rename config can be passed directly as a string array to\n * `getTemplateFilesToRenameAsync()`.\n *\n * The file patterns are formatted as glob expressions to be interpreted by\n * [fast-glob](https://github.com/mrmlnc/fast-glob). Comments are supported with\n * the `#` symbol, both in the plain-text file and string array formats.\n * Whitespace is trimmed and whitespace-only lines are ignored.\n *\n * If no rename config has been passed directly to\n * `getTemplateFilesToRenameAsync()` then this default rename config will be\n * used instead.\n */\nexport const defaultRenameConfig = [\n  // Common\n  '!**/node_modules',\n  'app.json',\n\n  // Android\n  'android/**/*.gradle',\n  'android/app/BUCK',\n  'android/app/src/**/*.java',\n  'android/app/src/**/*.kt',\n  'android/app/src/**/*.xml',\n\n  // iOS\n  'ios/Podfile',\n  'ios/**/*.xcodeproj/project.pbxproj',\n  'ios/**/*.xcodeproj/xcshareddata/xcschemes/*.xcscheme',\n] as const;\n\n/**\n * Returns a list of files within a template matched by the resolved rename\n * config.\n *\n * The rename config is resolved in the order of preference:\n * Config provided as function param > defaultRenameConfig\n */\nexport async function getTemplateFilesToRenameAsync({\n  cwd,\n  /**\n   * An array of patterns following the rename config format. If omitted, then\n   * we fall back to defaultRenameConfig.\n   * @see defaultRenameConfig\n   */\n  renameConfig: userConfig,\n}: Pick<ExtractProps, 'cwd'> & { renameConfig?: string[] }) {\n  let config = userConfig ?? defaultRenameConfig;\n\n  // Strip comments, trim whitespace, and remove empty lines.\n  config = config.map((line) => line.split(/(?<!\\\\)#/, 2)[0].trim()).filter((line) => line !== '');\n\n  return await glob(config, {\n    cwd,\n    // `true` is consistent with .gitignore. Allows `*.xml` to match .xml files\n    // in all subdirs.\n    baseNameMatch: true,\n    dot: true,\n    // Prevent climbing out of the template directory in case a template\n    // includes a symlink to an external directory.\n    followSymbolicLinks: false,\n  });\n}\n\nexport async function renameTemplateAppNameAsync({\n  cwd,\n  name,\n  files,\n}: Pick<ExtractProps, 'cwd' | 'name'> & {\n  /**\n   * An array of files to transform. Usually provided by calling\n   * getTemplateFilesToRenameAsync().\n   * @see getTemplateFilesToRenameAsync\n   */\n  files: string[];\n}) {\n  debug(`Got files to transform: ${JSON.stringify(files)}`);\n\n  await Promise.all(\n    files.map(async (file) => {\n      const absoluteFilePath = path.resolve(cwd, file);\n\n      let contents: string;\n      try {\n        contents = await fs.promises.readFile(absoluteFilePath, { encoding: 'utf-8' });\n      } catch (error) {\n        throw new Error(\n          `Failed to read template file: \"${absoluteFilePath}\". Was it removed mid-operation?`,\n          { cause: error }\n        );\n      }\n\n      debug(`Renaming app name in file: ${absoluteFilePath}`);\n\n      const safeName = ['.xml', '.plist'].includes(path.extname(file))\n        ? escapeXMLCharacters(name)\n        : name;\n\n      try {\n        const replacement = contents\n          .replace(/Hello App Display Name/g, safeName)\n          .replace(/HelloWorld/g, IOSConfig.XcodeUtils.sanitizedName(safeName))\n          .replace(/helloworld/g, IOSConfig.XcodeUtils.sanitizedName(safeName.toLowerCase()));\n\n        if (replacement === contents) {\n          return;\n        }\n\n        await fs.promises.writeFile(absoluteFilePath, replacement);\n      } catch (error) {\n        throw new Error(\n          `Failed to overwrite template file: \"${absoluteFilePath}\". Was it removed mid-operation?`,\n          { cause: error }\n        );\n      }\n    })\n  );\n}\n"], "names": ["defaultRenameConfig", "getTemplateFilesToRenameAsync", "renameTemplateAppNameAsync", "debug", "require", "escapeXMLCharacters", "original", "noAmps", "replace", "noLt", "noGt", "noApos", "cwd", "renameConfig", "userConfig", "config", "map", "line", "split", "trim", "filter", "glob", "baseNameMatch", "dot", "followSymbolicLinks", "name", "files", "JSON", "stringify", "Promise", "all", "file", "absoluteFilePath", "path", "resolve", "contents", "fs", "promises", "readFile", "encoding", "error", "Error", "cause", "safeName", "includes", "extname", "replacement", "IOSConfig", "XcodeUtils", "sanitizedName", "toLowerCase", "writeFile"], "mappings": "AAAA;;;;;;;;;;;IAmDaA,mBAAmB,MAAnBA,mBAAmB;IAyBVC,6BAA6B,MAA7BA,6BAA6B;IA0B7BC,0BAA0B,MAA1BA,0BAA0B;;;yBAtGtB,sBAAsB;;;;;;;yBAC3B,WAAW;;;;;;;8DACjB,IAAI;;;;;;;8DACF,MAAM;;;;;;;;;;;AAIvB,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,iCAAiC,CAAC,AAAsB,AAAC;AAExF,SAASC,mBAAmB,CAACC,QAAgB,EAAU;IACrD,MAAMC,MAAM,GAAGD,QAAQ,CAACE,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,AAAC;IAC9C,MAAMC,IAAI,GAAGF,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,AAAC;IACzC,MAAME,IAAI,GAAGD,IAAI,CAACD,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,AAAC;IACvC,MAAMG,MAAM,GAAGD,IAAI,CAACF,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,AAAC;IACxC,OAAOG,MAAM,CAACH,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACpC,CAAC;AAoCM,MAAMR,mBAAmB,GAAG;IACjC,SAAS;IACT,kBAAkB;IAClB,UAAU;IAEV,UAAU;IACV,qBAAqB;IACrB,kBAAkB;IAClB,2BAA2B;IAC3B,yBAAyB;IACzB,0BAA0B;IAE1B,MAAM;IACN,aAAa;IACb,oCAAoC;IACpC,sDAAsD;CACvD,AAAS,AAAC;AASJ,eAAeC,6BAA6B,CAAC,EAClDW,GAAG,CAAA,EACH;;;;GAIC,GACDC,YAAY,EAAEC,UAAU,CAAA,EACgC,EAAE;IAC1D,IAAIC,MAAM,GAAGD,UAAU,WAAVA,UAAU,GAAId,mBAAmB,AAAC;IAE/C,2DAA2D;IAC3De,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,GAAKA,IAAI,CAACC,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE,CAAC,CAACC,MAAM,CAAC,CAACH,IAAI,GAAKA,IAAI,KAAK,EAAE,CAAC,CAAC;IAEjG,OAAO,MAAMI,IAAAA,SAAI,EAAA,KAAA,EAACN,MAAM,EAAE;QACxBH,GAAG;QACH,2EAA2E;QAC3E,kBAAkB;QAClBU,aAAa,EAAE,IAAI;QACnBC,GAAG,EAAE,IAAI;QACT,oEAAoE;QACpE,+CAA+C;QAC/CC,mBAAmB,EAAE,KAAK;KAC3B,CAAC,CAAC;AACL,CAAC;AAEM,eAAetB,0BAA0B,CAAC,EAC/CU,GAAG,CAAA,EACHa,IAAI,CAAA,EACJC,KAAK,CAAA,EAQN,EAAE;IACDvB,KAAK,CAAC,CAAC,wBAAwB,EAAEwB,IAAI,CAACC,SAAS,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1D,MAAMG,OAAO,CAACC,GAAG,CACfJ,KAAK,CAACV,GAAG,CAAC,OAAOe,IAAI,GAAK;QACxB,MAAMC,gBAAgB,GAAGC,KAAI,EAAA,QAAA,CAACC,OAAO,CAACtB,GAAG,EAAEmB,IAAI,CAAC,AAAC;QAEjD,IAAII,QAAQ,AAAQ,AAAC;QACrB,IAAI;YACFA,QAAQ,GAAG,MAAMC,GAAE,EAAA,QAAA,CAACC,QAAQ,CAACC,QAAQ,CAACN,gBAAgB,EAAE;gBAAEO,QAAQ,EAAE,OAAO;aAAE,CAAC,CAAC;QACjF,EAAE,OAAOC,KAAK,EAAE;YACd,MAAM,IAAIC,KAAK,CACb,CAAC,+BAA+B,EAAET,gBAAgB,CAAC,gCAAgC,CAAC,EACpF;gBAAEU,KAAK,EAAEF,KAAK;aAAE,CACjB,CAAC;QACJ,CAAC;QAEDrC,KAAK,CAAC,CAAC,2BAA2B,EAAE6B,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAExD,MAAMW,QAAQ,GAAG;YAAC,MAAM;YAAE,QAAQ;SAAC,CAACC,QAAQ,CAACX,KAAI,EAAA,QAAA,CAACY,OAAO,CAACd,IAAI,CAAC,CAAC,GAC5D1B,mBAAmB,CAACoB,IAAI,CAAC,GACzBA,IAAI,AAAC;QAET,IAAI;YACF,MAAMqB,WAAW,GAAGX,QAAQ,CACzB3B,OAAO,4BAA4BmC,QAAQ,CAAC,CAC5CnC,OAAO,gBAAgBuC,cAAS,EAAA,UAAA,CAACC,UAAU,CAACC,aAAa,CAACN,QAAQ,CAAC,CAAC,CACpEnC,OAAO,gBAAgBuC,cAAS,EAAA,UAAA,CAACC,UAAU,CAACC,aAAa,CAACN,QAAQ,CAACO,WAAW,EAAE,CAAC,CAAC,AAAC;YAEtF,IAAIJ,WAAW,KAAKX,QAAQ,EAAE;gBAC5B,OAAO;YACT,CAAC;YAED,MAAMC,GAAE,EAAA,QAAA,CAACC,QAAQ,CAACc,SAAS,CAACnB,gBAAgB,EAAEc,WAAW,CAAC,CAAC;QAC7D,EAAE,OAAON,MAAK,EAAE;YACd,MAAM,IAAIC,KAAK,CACb,CAAC,oCAAoC,EAAET,gBAAgB,CAAC,gCAAgC,CAAC,EACzF;gBAAEU,KAAK,EAAEF,MAAK;aAAE,CACjB,CAAC;QACJ,CAAC;IACH,CAAC,CAAC,CACH,CAAC;AACJ,CAAC"}