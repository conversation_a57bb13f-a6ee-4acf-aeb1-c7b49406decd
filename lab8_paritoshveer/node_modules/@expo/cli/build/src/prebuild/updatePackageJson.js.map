{"version": 3, "sources": ["../../../src/prebuild/updatePackageJson.ts"], "sourcesContent": ["import { getPackageJson, PackageJSONConfig } from '@expo/config';\nimport chalk from 'chalk';\nimport crypto from 'crypto';\nimport fs from 'fs';\nimport path from 'path';\nimport { intersects as semverIntersects, Range as SemverRange } from 'semver';\n\nimport * as Log from '../log';\nimport { isModuleSymlinked } from '../utils/isModuleSymlinked';\nimport { logNewSection } from '../utils/ora';\n\nexport type DependenciesMap = { [key: string]: string | number };\n\nexport type DependenciesModificationResults = {\n  /** A list of new values were added to the `dependencies` object in the `package.json`. */\n  changedDependencies: string[];\n};\n\n/** Modifies the `package.json` with `modifyPackageJson` and format/displays the results. */\nexport async function updatePackageJSONAsync(\n  projectRoot: string,\n  {\n    templateDirectory,\n    templatePkg = getPackageJson(templateDirectory),\n    pkg,\n    skipDependencyUpdate,\n  }: {\n    templateDirectory: string;\n    templatePkg?: PackageJSONConfig;\n    pkg: PackageJSONConfig;\n    skipDependencyUpdate?: string[];\n  }\n): Promise<DependenciesModificationResults> {\n  const updatingPackageJsonStep = logNewSection('Updating package.json');\n\n  const results = modifyPackageJson(projectRoot, {\n    templatePkg,\n    pkg,\n    skipDependencyUpdate,\n  });\n\n  const hasChanges = results.changedDependencies.length || results.scriptsChanged;\n\n  // NOTE: This is effectively bundler caching and subject to breakage if the inputs don't match the mutations.\n  if (hasChanges) {\n    await fs.promises.writeFile(\n      path.resolve(projectRoot, 'package.json'),\n      // Add new line to match the format of running yarn.\n      // This prevents the `package.json` from changing when running `prebuild --no-install` multiple times.\n      JSON.stringify(pkg, null, 2) + '\\n'\n    );\n  }\n\n  updatingPackageJsonStep.succeed(\n    'Updated package.json' + (hasChanges ? '' : chalk.dim(` | no changes`))\n  );\n\n  return results;\n}\n\n/**\n * Make required modifications to the `package.json` file as a JSON object.\n *\n * 1. Update `package.json` `scripts`.\n * 2. Update `package.json` `dependencies` (not `devDependencies`).\n * 3. Update `package.json` `main`.\n *\n * @param projectRoot The root directory of the project.\n * @param props.templatePkg Template project package.json as JSON.\n * @param props.pkg Current package.json as JSON.\n * @param props.skipDependencyUpdate Array of dependencies to skip updating.\n * @returns\n */\nfunction modifyPackageJson(\n  projectRoot: string,\n  {\n    templatePkg,\n    pkg,\n    skipDependencyUpdate,\n  }: {\n    templatePkg: PackageJSONConfig;\n    pkg: PackageJSONConfig;\n    /** @deprecated Required packages are not overwritten, only added when missing */\n    skipDependencyUpdate?: string[];\n  }\n) {\n  const scriptsChanged = updatePkgScripts({ pkg });\n\n  // TODO: Move to `npx expo-doctor`\n  return {\n    scriptsChanged,\n    ...updatePkgDependencies(projectRoot, {\n      pkg,\n      templatePkg,\n      skipDependencyUpdate,\n    }),\n  };\n}\n\n/**\n * Update `package.json` dependencies by combining the `dependencies` in the\n * project we are creating with the dependencies in the template project.\n *\n * > Exposed for testing.\n */\nexport function updatePkgDependencies(\n  projectRoot: string,\n  {\n    pkg,\n    templatePkg,\n    skipDependencyUpdate = [],\n  }: {\n    pkg: PackageJSONConfig;\n    templatePkg: PackageJSONConfig;\n    /** @deprecated Required packages are not overwritten, only added when missing */\n    skipDependencyUpdate?: string[];\n  }\n): DependenciesModificationResults {\n  const { dependencies } = templatePkg;\n  // The default values come from the bare-minimum template's package.json.\n  // Users can change this by using different templates with the `--template` flag.\n  // The main reason for allowing the changing of dependencies would be to include\n  // dependencies that are required for the native project to build. For example,\n  // it does not need to include dependencies that are used in the JS-code only.\n  const defaultDependencies = createDependenciesMap(dependencies);\n\n  // NOTE: This is a hack to ensure this doesn't trigger an extraneous change in the `package.json`\n  // it isn't required for anything in the `ios` and `android` folders.\n  delete defaultDependencies['expo-status-bar'];\n  // NOTE: Expo splash screen is installed by default in the template but the config plugin also lives in prebuild-config\n  // so we can delete it to prevent an extraneous change in the `package.json`.\n  delete defaultDependencies['expo-splash-screen'];\n\n  const combinedDependencies: DependenciesMap = createDependenciesMap({\n    ...defaultDependencies,\n    ...pkg.dependencies,\n  });\n\n  // These dependencies are only added, not overwritten from the project\n  const requiredDependencies = [\n    // TODO: This is no longer required because it's this same package.\n    'expo',\n    // TODO: Drop this somehow.\n    'react-native',\n  ].filter((depKey) => !!defaultDependencies[depKey]);\n\n  const symlinkedPackages: string[] = [];\n  const nonRecommendedPackages: string[] = [];\n\n  for (const dependenciesKey of requiredDependencies) {\n    // If the local package.json defined the dependency that we want to overwrite...\n    if (pkg.dependencies?.[dependenciesKey]) {\n      // Then ensure it isn't symlinked (i.e. the user has a custom version in their yarn workspace).\n      if (isModuleSymlinked(projectRoot, { moduleId: dependenciesKey, isSilent: true })) {\n        // If the package is in the project's package.json and it's symlinked, then skip overwriting it.\n        symlinkedPackages.push(dependenciesKey);\n        continue;\n      }\n\n      // Do not modify manually skipped dependencies\n      if (skipDependencyUpdate.includes(dependenciesKey)) {\n        continue;\n      }\n\n      // Warn users for outdated dependencies when prebuilding\n      const hasRecommendedVersion = versionRangesIntersect(\n        pkg.dependencies[dependenciesKey],\n        String(defaultDependencies[dependenciesKey])\n      );\n      if (!hasRecommendedVersion) {\n        nonRecommendedPackages.push(`${dependenciesKey}@${defaultDependencies[dependenciesKey]}`);\n      }\n    }\n  }\n\n  if (symlinkedPackages.length) {\n    Log.log(\n      `\\u203A Using symlinked ${symlinkedPackages\n        .map((pkg) => chalk.bold(pkg))\n        .join(', ')} instead of recommended version(s).`\n    );\n  }\n\n  if (nonRecommendedPackages.length) {\n    Log.warn(\n      `\\u203A Using current versions instead of recommended ${nonRecommendedPackages\n        .map((pkg) => chalk.bold(pkg))\n        .join(', ')}.`\n    );\n  }\n\n  // Only change the dependencies if the normalized hash changes, this helps to reduce meaningless changes.\n  const hasNewDependencies =\n    hashForDependencyMap(pkg.dependencies) !== hashForDependencyMap(combinedDependencies);\n  // Save the dependencies\n  let changedDependencies: string[] = [];\n  if (hasNewDependencies) {\n    changedDependencies = diffKeys(combinedDependencies, pkg.dependencies ?? {}).sort();\n    // Use Object.assign to preserve the original order of dependencies, this makes it easier to see what changed in the git diff.\n    pkg.dependencies = Object.assign(pkg.dependencies ?? {}, combinedDependencies);\n  }\n\n  return {\n    changedDependencies,\n  };\n}\n\nfunction diffKeys(a: Record<string, any>, b: Record<string, any>): string[] {\n  return Object.keys(a).filter((key) => a[key] !== b[key]);\n}\n\n/**\n * Create an object of type DependenciesMap a dependencies object or throw if not valid.\n *\n * @param dependencies - ideally an object of type {[key]: string} - if not then this will error.\n */\nexport function createDependenciesMap(dependencies: any): DependenciesMap {\n  if (typeof dependencies !== 'object') {\n    throw new Error(`Dependency map is invalid, expected object but got ${typeof dependencies}`);\n  } else if (!dependencies) {\n    return {};\n  }\n\n  const outputMap: DependenciesMap = {};\n\n  for (const key of Object.keys(dependencies)) {\n    const value = dependencies[key];\n    if (typeof value === 'string') {\n      outputMap[key] = value;\n    } else {\n      throw new Error(\n        `Dependency for key \\`${key}\\` should be a \\`string\\`, instead got: \\`{ ${key}: ${JSON.stringify(\n          value\n        )} }\\``\n      );\n    }\n  }\n  return outputMap;\n}\n\n/**\n * Update package.json scripts - `npm start` should default to `expo\n * start --dev-client` rather than `expo start` after prebuilding, for example.\n */\nfunction updatePkgScripts({ pkg }: { pkg: PackageJSONConfig }) {\n  let hasChanged = false;\n  if (!pkg.scripts) {\n    pkg.scripts = {};\n  }\n  if (!pkg.scripts.android?.includes('run')) {\n    pkg.scripts.android = 'expo run:android';\n    hasChanged = true;\n  }\n  if (!pkg.scripts.ios?.includes('run')) {\n    pkg.scripts.ios = 'expo run:ios';\n    hasChanged = true;\n  }\n  return hasChanged;\n}\n\nfunction normalizeDependencyMap(deps: DependenciesMap): string[] {\n  return Object.keys(deps)\n    .map((dependency) => `${dependency}@${deps[dependency]}`)\n    .sort();\n}\n\nexport function hashForDependencyMap(deps: DependenciesMap = {}): string {\n  const depsList = normalizeDependencyMap(deps);\n  const depsString = depsList.join('\\n');\n  return createFileHash(depsString);\n}\n\nexport function createFileHash(contents: string): string {\n  // this doesn't need to be secure, the shorter the better.\n  return crypto.createHash('sha1').update(contents).digest('hex');\n}\n\n/**\n * Determine if two semver ranges are overlapping or intersecting.\n * This is a safe version of `semver.intersects` that does not throw.\n */\nfunction versionRangesIntersect(rangeA: string | SemverRange, rangeB: string | SemverRange) {\n  try {\n    return semverIntersects(rangeA, rangeB);\n  } catch {\n    return false;\n  }\n}\n"], "names": ["updatePackageJSONAsync", "updatePkgDependencies", "createDependenciesMap", "hashForDependencyMap", "createFileHash", "projectRoot", "templateDirectory", "templatePkg", "getPackageJson", "pkg", "skipDependencyUpdate", "updatingPackageJsonStep", "logNewSection", "results", "modifyPackageJson", "has<PERSON><PERSON><PERSON>", "changedDependencies", "length", "scriptsChanged", "fs", "promises", "writeFile", "path", "resolve", "JSON", "stringify", "succeed", "chalk", "dim", "updatePkgScripts", "dependencies", "defaultDependencies", "combinedDependencies", "requiredDependencies", "filter", "<PERSON><PERSON><PERSON><PERSON>", "symlinkedPackages", "nonRecommendedPackages", "dependenciesKey", "isModuleSymlinked", "moduleId", "isSilent", "push", "includes", "hasRecommendedVersion", "versionRangesIntersect", "String", "Log", "log", "map", "bold", "join", "warn", "hasNewDependencies", "diff<PERSON>eys", "sort", "Object", "assign", "a", "b", "keys", "key", "Error", "outputMap", "value", "has<PERSON><PERSON>ed", "scripts", "android", "ios", "normalizeDependencyMap", "deps", "dependency", "depsList", "depsString", "contents", "crypto", "createHash", "update", "digest", "rangeA", "rangeB", "semverIntersects"], "mappings": "AAAA;;;;;;;;;;;IAmBsBA,sBAAsB,MAAtBA,sBAAsB;IAsF5BC,qBAAqB,MAArBA,qBAAqB;IA+GrBC,qBAAqB,MAArBA,qBAAqB;IAkDrBC,oBAAoB,MAApBA,oBAAoB;IAMpBC,cAAc,MAAdA,cAAc;;;yBAhRoB,cAAc;;;;;;;8DAC9C,OAAO;;;;;;;8DACN,QAAQ;;;;;;;8DACZ,IAAI;;;;;;;8DACF,MAAM;;;;;;;yBAC8C,QAAQ;;;;;;2DAExD,QAAQ;mCACK,4BAA4B;qBAChC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrC,eAAeJ,sBAAsB,CAC1CK,WAAmB,EACnB,EACEC,iBAAiB,CAAA,EACjBC,WAAW,EAAGC,IAAAA,OAAc,EAAA,eAAA,EAACF,iBAAiB,CAAC,CAAA,EAC/CG,GAAG,CAAA,EACHC,oBAAoB,CAAA,EAMrB,EACyC;IAC1C,MAAMC,uBAAuB,GAAGC,IAAAA,IAAa,cAAA,EAAC,uBAAuB,CAAC,AAAC;IAEvE,MAAMC,OAAO,GAAGC,iBAAiB,CAACT,WAAW,EAAE;QAC7CE,WAAW;QACXE,GAAG;QACHC,oBAAoB;KACrB,CAAC,AAAC;IAEH,MAAMK,UAAU,GAAGF,OAAO,CAACG,mBAAmB,CAACC,MAAM,IAAIJ,OAAO,CAACK,cAAc,AAAC;IAEhF,6GAA6G;IAC7G,IAAIH,UAAU,EAAE;QACd,MAAMI,GAAE,EAAA,QAAA,CAACC,QAAQ,CAACC,SAAS,CACzBC,KAAI,EAAA,QAAA,CAACC,OAAO,CAAClB,WAAW,EAAE,cAAc,CAAC,EACzC,oDAAoD;QACpD,sGAAsG;QACtGmB,IAAI,CAACC,SAAS,CAAChB,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,CACpC,CAAC;IACJ,CAAC;IAEDE,uBAAuB,CAACe,OAAO,CAC7B,sBAAsB,GAAG,CAACX,UAAU,GAAG,EAAE,GAAGY,MAAK,EAAA,QAAA,CAACC,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CACxE,CAAC;IAEF,OAAOf,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;;;;;;;CAYC,GACD,SAASC,iBAAiB,CACxBT,WAAmB,EACnB,EACEE,WAAW,CAAA,EACXE,GAAG,CAAA,EACHC,oBAAoB,CAAA,EAMrB,EACD;IACA,MAAMQ,cAAc,GAAGW,gBAAgB,CAAC;QAAEpB,GAAG;KAAE,CAAC,AAAC;IAEjD,kCAAkC;IAClC,OAAO;QACLS,cAAc;QACd,GAAGjB,qBAAqB,CAACI,WAAW,EAAE;YACpCI,GAAG;YACHF,WAAW;YACXG,oBAAoB;SACrB,CAAC;KACH,CAAC;AACJ,CAAC;AAQM,SAAST,qBAAqB,CACnCI,WAAmB,EACnB,EACEI,GAAG,CAAA,EACHF,WAAW,CAAA,EACXG,oBAAoB,EAAG,EAAE,CAAA,EAM1B,EACgC;IACjC,MAAM,EAAEoB,YAAY,CAAA,EAAE,GAAGvB,WAAW,AAAC;IACrC,yEAAyE;IACzE,iFAAiF;IACjF,gFAAgF;IAChF,+EAA+E;IAC/E,8EAA8E;IAC9E,MAAMwB,mBAAmB,GAAG7B,qBAAqB,CAAC4B,YAAY,CAAC,AAAC;IAEhE,iGAAiG;IACjG,qEAAqE;IACrE,OAAOC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;IAC9C,uHAAuH;IACvH,6EAA6E;IAC7E,OAAOA,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;IAEjD,MAAMC,oBAAoB,GAAoB9B,qBAAqB,CAAC;QAClE,GAAG6B,mBAAmB;QACtB,GAAGtB,GAAG,CAACqB,YAAY;KACpB,CAAC,AAAC;IAEH,sEAAsE;IACtE,MAAMG,oBAAoB,GAAG;QAC3B,mEAAmE;QACnE,MAAM;QACN,2BAA2B;QAC3B,cAAc;KACf,CAACC,MAAM,CAAC,CAACC,MAAM,GAAK,CAAC,CAACJ,mBAAmB,CAACI,MAAM,CAAC,CAAC,AAAC;IAEpD,MAAMC,iBAAiB,GAAa,EAAE,AAAC;IACvC,MAAMC,sBAAsB,GAAa,EAAE,AAAC;IAE5C,KAAK,MAAMC,eAAe,IAAIL,oBAAoB,CAAE;YAE9CxB,GAAgB;QADpB,gFAAgF;QAChF,IAAIA,CAAAA,GAAgB,GAAhBA,GAAG,CAACqB,YAAY,SAAmB,GAAnCrB,KAAAA,CAAmC,GAAnCA,GAAgB,AAAE,CAAC6B,eAAe,CAAC,EAAE;YACvC,+FAA+F;YAC/F,IAAIC,IAAAA,kBAAiB,kBAAA,EAAClC,WAAW,EAAE;gBAAEmC,QAAQ,EAAEF,eAAe;gBAAEG,QAAQ,EAAE,IAAI;aAAE,CAAC,EAAE;gBACjF,gGAAgG;gBAChGL,iBAAiB,CAACM,IAAI,CAACJ,eAAe,CAAC,CAAC;gBACxC,SAAS;YACX,CAAC;YAED,8CAA8C;YAC9C,IAAI5B,oBAAoB,CAACiC,QAAQ,CAACL,eAAe,CAAC,EAAE;gBAClD,SAAS;YACX,CAAC;YAED,wDAAwD;YACxD,MAAMM,qBAAqB,GAAGC,sBAAsB,CAClDpC,GAAG,CAACqB,YAAY,CAACQ,eAAe,CAAC,EACjCQ,MAAM,CAACf,mBAAmB,CAACO,eAAe,CAAC,CAAC,CAC7C,AAAC;YACF,IAAI,CAACM,qBAAqB,EAAE;gBAC1BP,sBAAsB,CAACK,IAAI,CAAC,CAAC,EAAEJ,eAAe,CAAC,CAAC,EAAEP,mBAAmB,CAACO,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAIF,iBAAiB,CAACnB,MAAM,EAAE;QAC5B8B,IAAG,CAACC,GAAG,CACL,CAAC,uBAAuB,EAAEZ,iBAAiB,CACxCa,GAAG,CAAC,CAACxC,GAAG,GAAKkB,MAAK,EAAA,QAAA,CAACuB,IAAI,CAACzC,GAAG,CAAC,CAAC,CAC7B0C,IAAI,CAAC,IAAI,CAAC,CAAC,mCAAmC,CAAC,CACnD,CAAC;IACJ,CAAC;IAED,IAAId,sBAAsB,CAACpB,MAAM,EAAE;QACjC8B,IAAG,CAACK,IAAI,CACN,CAAC,qDAAqD,EAAEf,sBAAsB,CAC3EY,GAAG,CAAC,CAACxC,GAAG,GAAKkB,MAAK,EAAA,QAAA,CAACuB,IAAI,CAACzC,GAAG,CAAC,CAAC,CAC7B0C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CACjB,CAAC;IACJ,CAAC;IAED,yGAAyG;IACzG,MAAME,kBAAkB,GACtBlD,oBAAoB,CAACM,GAAG,CAACqB,YAAY,CAAC,KAAK3B,oBAAoB,CAAC6B,oBAAoB,CAAC,AAAC;IACxF,wBAAwB;IACxB,IAAIhB,mBAAmB,GAAa,EAAE,AAAC;IACvC,IAAIqC,kBAAkB,EAAE;YAC+B5C,aAAgB;QAArEO,mBAAmB,GAAGsC,QAAQ,CAACtB,oBAAoB,EAAEvB,CAAAA,aAAgB,GAAhBA,GAAG,CAACqB,YAAY,YAAhBrB,aAAgB,GAAI,EAAE,CAAC,CAAC8C,IAAI,EAAE,CAAC;YAEnD9C,cAAgB;QADjD,8HAA8H;QAC9HA,GAAG,CAACqB,YAAY,GAAG0B,MAAM,CAACC,MAAM,CAAChD,CAAAA,cAAgB,GAAhBA,GAAG,CAACqB,YAAY,YAAhBrB,cAAgB,GAAI,EAAE,EAAEuB,oBAAoB,CAAC,CAAC;IACjF,CAAC;IAED,OAAO;QACLhB,mBAAmB;KACpB,CAAC;AACJ,CAAC;AAED,SAASsC,QAAQ,CAACI,CAAsB,EAAEC,CAAsB,EAAY;IAC1E,OAAOH,MAAM,CAACI,IAAI,CAACF,CAAC,CAAC,CAACxB,MAAM,CAAC,CAAC2B,GAAG,GAAKH,CAAC,CAACG,GAAG,CAAC,KAAKF,CAAC,CAACE,GAAG,CAAC,CAAC,CAAC;AAC3D,CAAC;AAOM,SAAS3D,qBAAqB,CAAC4B,YAAiB,EAAmB;IACxE,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;QACpC,MAAM,IAAIgC,KAAK,CAAC,CAAC,mDAAmD,EAAE,OAAOhC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/F,OAAO,IAAI,CAACA,YAAY,EAAE;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAMiC,SAAS,GAAoB,EAAE,AAAC;IAEtC,KAAK,MAAMF,GAAG,IAAIL,MAAM,CAACI,IAAI,CAAC9B,YAAY,CAAC,CAAE;QAC3C,MAAMkC,KAAK,GAAGlC,YAAY,CAAC+B,GAAG,CAAC,AAAC;QAChC,IAAI,OAAOG,KAAK,KAAK,QAAQ,EAAE;YAC7BD,SAAS,CAACF,GAAG,CAAC,GAAGG,KAAK,CAAC;QACzB,OAAO;YACL,MAAM,IAAIF,KAAK,CACb,CAAC,qBAAqB,EAAED,GAAG,CAAC,4CAA4C,EAAEA,GAAG,CAAC,EAAE,EAAErC,IAAI,CAACC,SAAS,CAC9FuC,KAAK,CACN,CAAC,IAAI,CAAC,CACR,CAAC;QACJ,CAAC;IACH,CAAC;IACD,OAAOD,SAAS,CAAC;AACnB,CAAC;AAED;;;CAGC,GACD,SAASlC,gBAAgB,CAAC,EAAEpB,GAAG,CAAA,EAA8B,EAAE;QAKxDA,GAAmB,EAInBA,IAAe;IARpB,IAAIwD,UAAU,GAAG,KAAK,AAAC;IACvB,IAAI,CAACxD,GAAG,CAACyD,OAAO,EAAE;QAChBzD,GAAG,CAACyD,OAAO,GAAG,EAAE,CAAC;IACnB,CAAC;IACD,IAAI,EAACzD,CAAAA,GAAmB,GAAnBA,GAAG,CAACyD,OAAO,CAACC,OAAO,SAAU,GAA7B1D,KAAAA,CAA6B,GAA7BA,GAAmB,CAAEkC,QAAQ,CAAC,KAAK,CAAC,CAAA,EAAE;QACzClC,GAAG,CAACyD,OAAO,CAACC,OAAO,GAAG,kBAAkB,CAAC;QACzCF,UAAU,GAAG,IAAI,CAAC;IACpB,CAAC;IACD,IAAI,EAACxD,CAAAA,IAAe,GAAfA,GAAG,CAACyD,OAAO,CAACE,GAAG,SAAU,GAAzB3D,KAAAA,CAAyB,GAAzBA,IAAe,CAAEkC,QAAQ,CAAC,KAAK,CAAC,CAAA,EAAE;QACrClC,GAAG,CAACyD,OAAO,CAACE,GAAG,GAAG,cAAc,CAAC;QACjCH,UAAU,GAAG,IAAI,CAAC;IACpB,CAAC;IACD,OAAOA,UAAU,CAAC;AACpB,CAAC;AAED,SAASI,sBAAsB,CAACC,IAAqB,EAAY;IAC/D,OAAOd,MAAM,CAACI,IAAI,CAACU,IAAI,CAAC,CACrBrB,GAAG,CAAC,CAACsB,UAAU,GAAK,CAAC,EAAEA,UAAU,CAAC,CAAC,EAAED,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC,CACxDhB,IAAI,EAAE,CAAC;AACZ,CAAC;AAEM,SAASpD,oBAAoB,CAACmE,IAAqB,GAAG,EAAE,EAAU;IACvE,MAAME,QAAQ,GAAGH,sBAAsB,CAACC,IAAI,CAAC,AAAC;IAC9C,MAAMG,UAAU,GAAGD,QAAQ,CAACrB,IAAI,CAAC,IAAI,CAAC,AAAC;IACvC,OAAO/C,cAAc,CAACqE,UAAU,CAAC,CAAC;AACpC,CAAC;AAEM,SAASrE,cAAc,CAACsE,QAAgB,EAAU;IACvD,0DAA0D;IAC1D,OAAOC,OAAM,EAAA,QAAA,CAACC,UAAU,CAAC,MAAM,CAAC,CAACC,MAAM,CAACH,QAAQ,CAAC,CAACI,MAAM,CAAC,KAAK,CAAC,CAAC;AAClE,CAAC;AAED;;;CAGC,GACD,SAASjC,sBAAsB,CAACkC,MAA4B,EAAEC,MAA4B,EAAE;IAC1F,IAAI;QACF,OAAOC,IAAAA,OAAgB,EAAA,WAAA,EAACF,MAAM,EAAEC,MAAM,CAAC,CAAC;IAC1C,EAAE,OAAM;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC"}