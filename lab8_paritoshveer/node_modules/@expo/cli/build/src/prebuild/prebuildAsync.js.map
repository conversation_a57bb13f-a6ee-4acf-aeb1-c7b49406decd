{"version": 3, "sources": ["../../../src/prebuild/prebuildAsync.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport { ModPlatform } from '@expo/config-plugins';\nimport chalk from 'chalk';\n\nimport { clearNativeFolder, promptToClearMalformedNativeProjectsAsync } from './clearNativeFolder';\nimport { configureProjectAsync } from './configureProjectAsync';\nimport { ensureConfigAsync } from './ensureConfigAsync';\nimport { assertPlatforms, ensureValidPlatforms, resolveTemplateOption } from './resolveOptions';\nimport { updateFromTemplateAsync } from './updateFromTemplate';\nimport { installAsync } from '../install/installAsync';\nimport { Log } from '../log';\nimport { env } from '../utils/env';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { clearNodeModulesAsync } from '../utils/nodeModules';\nimport { logNewSection } from '../utils/ora';\nimport { profile } from '../utils/profile';\nimport { confirmAsync } from '../utils/prompts';\n\nconst debug = require('debug')('expo:prebuild') as typeof console.log;\n\nexport type PrebuildResults = {\n  /** Expo config. */\n  exp: ExpoConfig;\n  /** Indicates if the process created new files. */\n  hasNewProjectFiles: boolean;\n  /** The platforms that were prebuilt. */\n  platforms: ModPlatform[];\n  /** Indicates if pod install was run. */\n  podInstall: boolean;\n  /** Indicates if node modules were installed. */\n  nodeInstall: boolean;\n};\n\n/**\n * Entry point into the prebuild process, delegates to other helpers to perform various steps.\n *\n * 0. Attempt to clean the project folders.\n * 1. Create native projects (ios, android).\n * 2. Install node modules.\n * 3. Apply config to native projects.\n * 4. Install CocoaPods.\n */\nexport async function prebuildAsync(\n  projectRoot: string,\n  options: {\n    /** Should install node modules and cocoapods. */\n    install?: boolean;\n    /** List of platforms to prebuild. */\n    platforms: ModPlatform[];\n    /** Should delete the native folders before attempting to prebuild. */\n    clean?: boolean;\n    /** URL or file path to the prebuild template. */\n    template?: string;\n    /** Name of the node package manager to install with. */\n    packageManager?: {\n      npm?: boolean;\n      yarn?: boolean;\n      pnpm?: boolean;\n      bun?: boolean;\n    };\n    /** List of node modules to skip updating. */\n    skipDependencyUpdate?: string[];\n  }\n): Promise<PrebuildResults | null> {\n  setNodeEnv('development');\n  require('@expo/env').load(projectRoot);\n\n  if (options.clean) {\n    const { maybeBailOnGitStatusAsync } = await import('../utils/git.js');\n    // Clean the project folders...\n    if (await maybeBailOnGitStatusAsync()) {\n      return null;\n    }\n    // Clear the native folders before syncing\n    await clearNativeFolder(projectRoot, options.platforms);\n  } else {\n    // Check if the existing project folders are malformed.\n    await promptToClearMalformedNativeProjectsAsync(projectRoot, options.platforms);\n  }\n\n  // Warn if the project is attempting to prebuild an unsupported platform (iOS on Windows).\n  options.platforms = ensureValidPlatforms(options.platforms);\n  // Assert if no platforms are left over after filtering.\n  assertPlatforms(options.platforms);\n\n  // Get the Expo config, create it if missing.\n  const { exp, pkg } = await ensureConfigAsync(projectRoot, { platforms: options.platforms });\n\n  // Create native projects from template.\n  const { hasNewProjectFiles, needsPodInstall, templateChecksum, changedDependencies } =\n    await updateFromTemplateAsync(projectRoot, {\n      exp,\n      pkg,\n      template: options.template != null ? resolveTemplateOption(options.template) : undefined,\n      platforms: options.platforms,\n      skipDependencyUpdate: options.skipDependencyUpdate,\n    });\n\n  // Install node modules\n  if (options.install) {\n    if (changedDependencies.length) {\n      if (options.packageManager?.npm) {\n        await clearNodeModulesAsync(projectRoot);\n      }\n\n      Log.log(chalk.gray(chalk`Dependencies in the {bold package.json} changed:`));\n      Log.log(chalk.gray('  ' + changedDependencies.join(', ')));\n\n      // Installing dependencies is a legacy feature from the unversioned\n      // command. We know opt to not change dependencies unless a template\n      // indicates a new dependency is required, or if the core dependencies are wrong.\n      if (\n        await confirmAsync({\n          message: `Install the updated dependencies?`,\n          initial: true,\n        })\n      ) {\n        await installAsync([], {\n          npm: !!options.packageManager?.npm,\n          yarn: !!options.packageManager?.yarn,\n          pnpm: !!options.packageManager?.pnpm,\n          bun: !!options.packageManager?.bun,\n          silent: !(env.EXPO_DEBUG || env.CI),\n        });\n      }\n    }\n  }\n\n  // Apply Expo config to native projects. Prevent log-spew from ora when running in debug mode.\n  const configSyncingStep: { succeed(text?: string): unknown; fail(text?: string): unknown } =\n    env.EXPO_DEBUG\n      ? {\n          succeed(text) {\n            Log.log(text!);\n          },\n          fail(text) {\n            Log.error(text!);\n          },\n        }\n      : logNewSection('Running prebuild');\n  try {\n    await profile(configureProjectAsync)(projectRoot, {\n      platforms: options.platforms,\n      exp,\n      templateChecksum,\n    });\n    configSyncingStep.succeed('Finished prebuild');\n  } catch (error) {\n    configSyncingStep.fail('Prebuild failed');\n    throw error;\n  }\n\n  // Install CocoaPods\n  let podsInstalled: boolean = false;\n  // err towards running pod install less because it's slow and users can easily run npx pod-install afterwards.\n  if (options.platforms.includes('ios') && options.install && needsPodInstall) {\n    const { installCocoaPodsAsync } = await import('../utils/cocoapods.js');\n\n    podsInstalled = await installCocoaPodsAsync(projectRoot);\n  } else {\n    debug('Skipped pod install');\n  }\n\n  return {\n    nodeInstall: !!options.install,\n    podInstall: !podsInstalled,\n    platforms: options.platforms,\n    hasNewProjectFiles,\n    exp,\n  };\n}\n"], "names": ["prebuildAsync", "debug", "require", "projectRoot", "options", "setNodeEnv", "load", "clean", "maybeBailOnGitStatusAsync", "clearNativeFolder", "platforms", "promptToClearMalformedNativeProjectsAsync", "ensureValidPlatforms", "assertPlatforms", "exp", "pkg", "ensureConfigAsync", "hasNewProjectFiles", "needsPodInstall", "templateChecksum", "changedDependencies", "updateFromTemplateAsync", "template", "resolveTemplateOption", "undefined", "skipDependencyUpdate", "install", "length", "packageManager", "npm", "clearNodeModulesAsync", "Log", "log", "chalk", "gray", "join", "<PERSON><PERSON><PERSON>", "message", "initial", "installAsync", "yarn", "pnpm", "bun", "silent", "env", "EXPO_DEBUG", "CI", "configSyncingStep", "succeed", "text", "fail", "error", "logNewSection", "profile", "configureProjectAsync", "podsInstalled", "includes", "installCocoaPodsAsync", "nodeInstall", "podInstall"], "mappings": "AAAA;;;;+BA0Cs<PERSON>,eAAa;;aAAbA,aAAa;;;8DAxCjB,OAAO;;;;;;mCAEoD,qBAAqB;uCAC5D,yBAAyB;mCAC7B,qBAAqB;gCACsB,kBAAkB;oCACvD,sBAAsB;8BACjC,yBAAyB;qBAClC,QAAQ;qBACR,cAAc;yBACP,kBAAkB;6BACP,sBAAsB;qBAC9B,cAAc;yBACpB,kBAAkB;yBACb,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE/C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,AAAsB,AAAC;AAwB/D,eAAeF,aAAa,CACjCG,WAAmB,EACnBC,OAkBC,EACgC;IACjCC,IAAAA,QAAU,WAAA,EAAC,aAAa,CAAC,CAAC;IAC1BH,OAAO,CAAC,WAAW,CAAC,CAACI,IAAI,CAACH,WAAW,CAAC,CAAC;IAEvC,IAAIC,OAAO,CAACG,KAAK,EAAE;QACjB,MAAM,EAAEC,yBAAyB,CAAA,EAAE,GAAG,MAAM,iEAAA,OAAM,CAAC,iBAAiB,GAAC,AAAC;QACtE,+BAA+B;QAC/B,IAAI,MAAMA,yBAAyB,EAAE,EAAE;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,0CAA0C;QAC1C,MAAMC,IAAAA,kBAAiB,kBAAA,EAACN,WAAW,EAAEC,OAAO,CAACM,SAAS,CAAC,CAAC;IAC1D,OAAO;QACL,uDAAuD;QACvD,MAAMC,IAAAA,kBAAyC,0CAAA,EAACR,WAAW,EAAEC,OAAO,CAACM,SAAS,CAAC,CAAC;IAClF,CAAC;IAED,0FAA0F;IAC1FN,OAAO,CAACM,SAAS,GAAGE,IAAAA,eAAoB,qBAAA,EAACR,OAAO,CAACM,SAAS,CAAC,CAAC;IAC5D,wDAAwD;IACxDG,IAAAA,eAAe,gBAAA,EAACT,OAAO,CAACM,SAAS,CAAC,CAAC;IAEnC,6CAA6C;IAC7C,MAAM,EAAEI,GAAG,CAAA,EAAEC,GAAG,CAAA,EAAE,GAAG,MAAMC,IAAAA,kBAAiB,kBAAA,EAACb,WAAW,EAAE;QAAEO,SAAS,EAAEN,OAAO,CAACM,SAAS;KAAE,CAAC,AAAC;IAE5F,wCAAwC;IACxC,MAAM,EAAEO,kBAAkB,CAAA,EAAEC,eAAe,CAAA,EAAEC,gBAAgB,CAAA,EAAEC,mBAAmB,CAAA,EAAE,GAClF,MAAMC,IAAAA,mBAAuB,wBAAA,EAAClB,WAAW,EAAE;QACzCW,GAAG;QACHC,GAAG;QACHO,QAAQ,EAAElB,OAAO,CAACkB,QAAQ,IAAI,IAAI,GAAGC,IAAAA,eAAqB,sBAAA,EAACnB,OAAO,CAACkB,QAAQ,CAAC,GAAGE,SAAS;QACxFd,SAAS,EAAEN,OAAO,CAACM,SAAS;QAC5Be,oBAAoB,EAAErB,OAAO,CAACqB,oBAAoB;KACnD,CAAC,AAAC;IAEL,uBAAuB;IACvB,IAAIrB,OAAO,CAACsB,OAAO,EAAE;QACnB,IAAIN,mBAAmB,CAACO,MAAM,EAAE;gBAC1BvB,GAAsB;YAA1B,IAAIA,CAAAA,GAAsB,GAAtBA,OAAO,CAACwB,cAAc,SAAK,GAA3BxB,KAAAA,CAA2B,GAA3BA,GAAsB,CAAEyB,GAAG,EAAE;gBAC/B,MAAMC,IAAAA,YAAqB,sBAAA,EAAC3B,WAAW,CAAC,CAAC;YAC3C,CAAC;YAED4B,IAAG,IAAA,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACC,IAAI,CAACD,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,gDAAgD,CAAC,CAAC,CAAC,CAAC;YAC7EF,IAAG,IAAA,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,IAAI,GAAGd,mBAAmB,CAACe,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE3D,mEAAmE;YACnE,oEAAoE;YACpE,iFAAiF;YACjF,IACE,MAAMC,IAAAA,QAAY,aAAA,EAAC;gBACjBC,OAAO,EAAE,CAAC,iCAAiC,CAAC;gBAC5CC,OAAO,EAAE,IAAI;aACd,CAAC,EACF;oBAESlC,IAAsB,EACrBA,IAAsB,EACtBA,IAAsB,EACvBA,IAAsB;gBAJ/B,MAAMmC,IAAAA,aAAY,aAAA,EAAC,EAAE,EAAE;oBACrBV,GAAG,EAAE,CAAC,CAACzB,CAAAA,CAAAA,IAAsB,GAAtBA,OAAO,CAACwB,cAAc,SAAK,GAA3BxB,KAAAA,CAA2B,GAA3BA,IAAsB,CAAEyB,GAAG,CAAA;oBAClCW,IAAI,EAAE,CAAC,CAACpC,CAAAA,CAAAA,IAAsB,GAAtBA,OAAO,CAACwB,cAAc,SAAM,GAA5BxB,KAAAA,CAA4B,GAA5BA,IAAsB,CAAEoC,IAAI,CAAA;oBACpCC,IAAI,EAAE,CAAC,CAACrC,CAAAA,CAAAA,IAAsB,GAAtBA,OAAO,CAACwB,cAAc,SAAM,GAA5BxB,KAAAA,CAA4B,GAA5BA,IAAsB,CAAEqC,IAAI,CAAA;oBACpCC,GAAG,EAAE,CAAC,CAACtC,CAAAA,CAAAA,IAAsB,GAAtBA,OAAO,CAACwB,cAAc,SAAK,GAA3BxB,KAAAA,CAA2B,GAA3BA,IAAsB,CAAEsC,GAAG,CAAA;oBAClCC,MAAM,EAAE,CAAC,CAACC,IAAG,IAAA,CAACC,UAAU,IAAID,IAAG,IAAA,CAACE,EAAE,CAAC;iBACpC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,8FAA8F;IAC9F,MAAMC,iBAAiB,GACrBH,IAAG,IAAA,CAACC,UAAU,GACV;QACEG,OAAO,EAACC,IAAI,EAAE;YACZlB,IAAG,IAAA,CAACC,GAAG,CAACiB,IAAI,CAAE,CAAC;QACjB,CAAC;QACDC,IAAI,EAACD,IAAI,EAAE;YACTlB,IAAG,IAAA,CAACoB,KAAK,CAACF,IAAI,CAAE,CAAC;QACnB,CAAC;KACF,GACDG,IAAAA,IAAa,cAAA,EAAC,kBAAkB,CAAC,AAAC;IACxC,IAAI;QACF,MAAMC,IAAAA,QAAO,QAAA,EAACC,sBAAqB,sBAAA,CAAC,CAACnD,WAAW,EAAE;YAChDO,SAAS,EAAEN,OAAO,CAACM,SAAS;YAC5BI,GAAG;YACHK,gBAAgB;SACjB,CAAC,CAAC;QACH4B,iBAAiB,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IACjD,EAAE,OAAOG,KAAK,EAAE;QACdJ,iBAAiB,CAACG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC1C,MAAMC,KAAK,CAAC;IACd,CAAC;IAED,oBAAoB;IACpB,IAAII,aAAa,GAAY,KAAK,AAAC;IACnC,8GAA8G;IAC9G,IAAInD,OAAO,CAACM,SAAS,CAAC8C,QAAQ,CAAC,KAAK,CAAC,IAAIpD,OAAO,CAACsB,OAAO,IAAIR,eAAe,EAAE;QAC3E,MAAM,EAAEuC,qBAAqB,CAAA,EAAE,GAAG,MAAM,iEAAA,OAAM,CAAC,uBAAuB,GAAC,AAAC;QAExEF,aAAa,GAAG,MAAME,qBAAqB,CAACtD,WAAW,CAAC,CAAC;IAC3D,OAAO;QACLF,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO;QACLyD,WAAW,EAAE,CAAC,CAACtD,OAAO,CAACsB,OAAO;QAC9BiC,UAAU,EAAE,CAACJ,aAAa;QAC1B7C,SAAS,EAAEN,OAAO,CAACM,SAAS;QAC5BO,kBAAkB;QAClBH,GAAG;KACJ,CAAC;AACJ,CAAC"}