{"version": 3, "sources": ["../../../src/prebuild/validateTemplatePlatforms.ts"], "sourcesContent": ["import { ModPlatform } from '@expo/config-plugins';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport * as Log from '../log';\nimport { directoryExistsSync } from '../utils/dir';\n\nexport function validateTemplatePlatforms({\n  templateDirectory,\n  platforms,\n}: {\n  templateDirectory: string;\n  platforms: ModPlatform[];\n}) {\n  const existingPlatforms: ModPlatform[] = [];\n\n  for (const platform of platforms) {\n    if (directoryExistsSync(path.join(templateDirectory, platform))) {\n      existingPlatforms.push(platform);\n    } else {\n      Log.warn(\n        chalk`⚠️  Skipping platform ${platform}. Use a template that contains native files for ${platform} (./${platform}).`\n      );\n    }\n  }\n\n  return existingPlatforms;\n}\n"], "names": ["validateTemplatePlatforms", "templateDirectory", "platforms", "existingPlatforms", "platform", "directoryExistsSync", "path", "join", "push", "Log", "warn", "chalk"], "mappings": "AAAA;;;;+BAOgBA,2BAAyB;;aAAzBA,yBAAyB;;;8DANvB,OAAO;;;;;;;8DACR,MAAM;;;;;;2DAEF,QAAQ;qBACO,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3C,SAASA,yBAAyB,CAAC,EACxCC,iBAAiB,CAAA,EACjBC,SAAS,CAAA,EAIV,EAAE;IACD,MAAMC,iBAAiB,GAAkB,EAAE,AAAC;IAE5C,KAAK,MAAMC,QAAQ,IAAIF,SAAS,CAAE;QAChC,IAAIG,IAAAA,IAAmB,oBAAA,EAACC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACN,iBAAiB,EAAEG,QAAQ,CAAC,CAAC,EAAE;YAC/DD,iBAAiB,CAACK,IAAI,CAACJ,QAAQ,CAAC,CAAC;QACnC,OAAO;YACLK,IAAG,CAACC,IAAI,CACNC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,sBAAsB,EAAEP,QAAQ,CAAC,gDAAgD,EAAEA,QAAQ,CAAC,IAAI,EAAEA,QAAQ,CAAC,EAAE,CAAC,CACrH,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAOD,iBAAiB,CAAC;AAC3B,CAAC"}