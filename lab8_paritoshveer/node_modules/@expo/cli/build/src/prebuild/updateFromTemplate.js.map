{"version": 3, "sources": ["../../../src/prebuild/updateFromTemplate.ts"], "sourcesContent": ["import { ExpoConfig, PackageJSONConfig } from '@expo/config';\nimport { ModPlatform } from '@expo/config-plugins';\nimport chalk from 'chalk';\n\nimport { copyTemplateFiles, createCopyFilesSuccessMessage } from './copyTemplateFiles';\nimport { getTemplateFilesToRenameAsync, renameTemplateAppNameAsync } from './renameTemplateAppName';\nimport { cloneTemplateAsync } from './resolveTemplate';\nimport { DependenciesModificationResults, updatePackageJSONAsync } from './updatePackageJson';\nimport { validateTemplatePlatforms } from './validateTemplatePlatforms';\nimport * as Log from '../log';\nimport { AbortCommandError, SilentError } from '../utils/errors';\nimport { logNewSection } from '../utils/ora';\nimport { profile } from '../utils/profile';\n\n/**\n * Creates local native files from an input template file path.\n *\n * @return `true` if the project is prebuilding, and `false` if it's syncing.\n */\nexport async function updateFromTemplateAsync(\n  projectRoot: string,\n  {\n    exp,\n    pkg,\n    template,\n    templateDirectory,\n    platforms,\n    skipDependencyUpdate,\n  }: {\n    /** Expo Config */\n    exp: ExpoConfig;\n    /** package.json as JSON */\n    pkg: PackageJSONConfig;\n    /** Template reference ID. */\n    template?: string;\n    /** Directory to write the template to before copying into the project. */\n    templateDirectory?: string;\n    /** List of platforms to clone. */\n    platforms: ModPlatform[];\n    /** List of dependencies to skip updating. */\n    skipDependencyUpdate?: string[];\n  }\n): Promise<\n  {\n    /** Indicates if new files were created in the project. */\n    hasNewProjectFiles: boolean;\n    /** Indicates that the project needs to run `pod install` */\n    needsPodInstall: boolean;\n    /** The template checksum used to create the native project. */\n    templateChecksum: string;\n  } & DependenciesModificationResults\n> {\n  if (!templateDirectory) {\n    const temporary = await import('tempy');\n    templateDirectory = temporary.directory();\n  }\n\n  const { copiedPaths, templateChecksum } = await profile(cloneTemplateAndCopyToProjectAsync)({\n    projectRoot,\n    template,\n    templateDirectory,\n    exp,\n    platforms,\n  });\n\n  const depsResults = await profile(updatePackageJSONAsync)(projectRoot, {\n    templateDirectory,\n    pkg,\n    skipDependencyUpdate,\n  });\n\n  return {\n    hasNewProjectFiles: !!copiedPaths.length,\n    // If the iOS folder changes or new packages are added, we should rerun pod install.\n    needsPodInstall: copiedPaths.includes('ios') || !!depsResults.changedDependencies.length,\n    templateChecksum,\n    ...depsResults,\n  };\n}\n\n/**\n * Extract the template and copy the ios and android directories over to the project directory.\n *\n * @return `true` if any project files were created.\n */\nexport async function cloneTemplateAndCopyToProjectAsync({\n  projectRoot,\n  templateDirectory,\n  template,\n  exp,\n  platforms: unknownPlatforms,\n}: {\n  projectRoot: string;\n  templateDirectory: string;\n  template?: string;\n  exp: Pick<ExpoConfig, 'name' | 'sdkVersion'>;\n  platforms: ModPlatform[];\n}): Promise<{ copiedPaths: string[]; templateChecksum: string }> {\n  const platformDirectories = unknownPlatforms\n    .map((platform) => `./${platform}`)\n    .reverse()\n    .join(' and ');\n\n  const pluralized = unknownPlatforms.length > 1 ? 'directories' : 'directory';\n  const ora = logNewSection(`Creating native ${pluralized} (${platformDirectories})`);\n\n  try {\n    const templateChecksum = await cloneTemplateAsync({ templateDirectory, template, exp, ora });\n\n    const platforms = validateTemplatePlatforms({\n      templateDirectory,\n      platforms: unknownPlatforms,\n    });\n\n    const results = copyTemplateFiles(projectRoot, {\n      templateDirectory,\n      platforms,\n    });\n\n    const files = await getTemplateFilesToRenameAsync({ cwd: projectRoot });\n    await renameTemplateAppNameAsync({\n      cwd: projectRoot,\n      files,\n      name: exp.name,\n    });\n\n    // Says: \"Created native directories\"\n    ora.succeed(createCopyFilesSuccessMessage(platforms, results));\n\n    return {\n      copiedPaths: results.copiedPaths,\n      templateChecksum,\n    };\n  } catch (e: any) {\n    if (!(e instanceof AbortCommandError)) {\n      Log.error(e.message);\n    }\n    ora.fail(`Failed to create the native ${pluralized}`);\n    Log.log(\n      chalk.yellow(\n        chalk`You may want to delete the {bold ./ios} and/or {bold ./android} directories before trying again.`\n      )\n    );\n    throw new SilentError(e);\n  }\n}\n"], "names": ["updateFromTemplateAsync", "cloneTemplateAndCopyToProjectAsync", "projectRoot", "exp", "pkg", "template", "templateDirectory", "platforms", "skipDependencyUpdate", "temporary", "directory", "copiedPaths", "templateChecksum", "profile", "depsResults", "updatePackageJSONAsync", "hasNewProjectFiles", "length", "needsPodInstall", "includes", "changedDependencies", "unknownPlatforms", "platformDirectories", "map", "platform", "reverse", "join", "pluralized", "ora", "logNewSection", "cloneTemplateAsync", "validateTemplatePlatforms", "results", "copyTemplateFiles", "files", "getTemplateFilesToRenameAsync", "cwd", "renameTemplateAppNameAsync", "name", "succeed", "createCopyFilesSuccessMessage", "e", "AbortCommandError", "Log", "error", "message", "fail", "log", "chalk", "yellow", "SilentError"], "mappings": "AAAA;;;;;;;;;;;IAmBsBA,uBAAuB,MAAvBA,uBAAuB;IAkEvBC,kCAAkC,MAAlCA,kCAAkC;;;8DAnFtC,OAAO;;;;;;mCAEwC,qBAAqB;uCACZ,yBAAyB;iCAChE,mBAAmB;mCACkB,qBAAqB;2CACnD,6BAA6B;2DAClD,QAAQ;wBACkB,iBAAiB;qBAClC,cAAc;yBACpB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC,eAAeD,uBAAuB,CAC3CE,WAAmB,EACnB,EACEC,GAAG,CAAA,EACHC,GAAG,CAAA,EACHC,QAAQ,CAAA,EACRC,iBAAiB,CAAA,EACjBC,SAAS,CAAA,EACTC,oBAAoB,CAAA,EAcrB,EAUD;IACA,IAAI,CAACF,iBAAiB,EAAE;QACtB,MAAMG,SAAS,GAAG,MAAM,iEAAA,OAAM,CAAC,OAAO,GAAC,AAAC;QACxCH,iBAAiB,GAAGG,SAAS,CAACC,SAAS,EAAE,CAAC;IAC5C,CAAC;IAED,MAAM,EAAEC,WAAW,CAAA,EAAEC,gBAAgB,CAAA,EAAE,GAAG,MAAMC,IAAAA,QAAO,QAAA,EAACZ,kCAAkC,CAAC,CAAC;QAC1FC,WAAW;QACXG,QAAQ;QACRC,iBAAiB;QACjBH,GAAG;QACHI,SAAS;KACV,CAAC,AAAC;IAEH,MAAMO,WAAW,GAAG,MAAMD,IAAAA,QAAO,QAAA,EAACE,kBAAsB,uBAAA,CAAC,CAACb,WAAW,EAAE;QACrEI,iBAAiB;QACjBF,GAAG;QACHI,oBAAoB;KACrB,CAAC,AAAC;IAEH,OAAO;QACLQ,kBAAkB,EAAE,CAAC,CAACL,WAAW,CAACM,MAAM;QACxC,oFAAoF;QACpFC,eAAe,EAAEP,WAAW,CAACQ,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAACL,WAAW,CAACM,mBAAmB,CAACH,MAAM;QACxFL,gBAAgB;QAChB,GAAGE,WAAW;KACf,CAAC;AACJ,CAAC;AAOM,eAAeb,kCAAkC,CAAC,EACvDC,WAAW,CAAA,EACXI,iBAAiB,CAAA,EACjBD,QAAQ,CAAA,EACRF,GAAG,CAAA,EACHI,SAAS,EAAEc,gBAAgB,CAAA,EAO5B,EAAgE;IAC/D,MAAMC,mBAAmB,GAAGD,gBAAgB,CACzCE,GAAG,CAAC,CAACC,QAAQ,GAAK,CAAC,EAAE,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAClCC,OAAO,EAAE,CACTC,IAAI,CAAC,OAAO,CAAC,AAAC;IAEjB,MAAMC,UAAU,GAAGN,gBAAgB,CAACJ,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG,WAAW,AAAC;IAC7E,MAAMW,GAAG,GAAGC,IAAAA,IAAa,cAAA,EAAC,CAAC,gBAAgB,EAAEF,UAAU,CAAC,EAAE,EAAEL,mBAAmB,CAAC,CAAC,CAAC,CAAC,AAAC;IAEpF,IAAI;QACF,MAAMV,gBAAgB,GAAG,MAAMkB,IAAAA,gBAAkB,mBAAA,EAAC;YAAExB,iBAAiB;YAAED,QAAQ;YAAEF,GAAG;YAAEyB,GAAG;SAAE,CAAC,AAAC;QAE7F,MAAMrB,SAAS,GAAGwB,IAAAA,0BAAyB,0BAAA,EAAC;YAC1CzB,iBAAiB;YACjBC,SAAS,EAAEc,gBAAgB;SAC5B,CAAC,AAAC;QAEH,MAAMW,OAAO,GAAGC,IAAAA,kBAAiB,kBAAA,EAAC/B,WAAW,EAAE;YAC7CI,iBAAiB;YACjBC,SAAS;SACV,CAAC,AAAC;QAEH,MAAM2B,KAAK,GAAG,MAAMC,IAAAA,sBAA6B,8BAAA,EAAC;YAAEC,GAAG,EAAElC,WAAW;SAAE,CAAC,AAAC;QACxE,MAAMmC,IAAAA,sBAA0B,2BAAA,EAAC;YAC/BD,GAAG,EAAElC,WAAW;YAChBgC,KAAK;YACLI,IAAI,EAAEnC,GAAG,CAACmC,IAAI;SACf,CAAC,CAAC;QAEH,qCAAqC;QACrCV,GAAG,CAACW,OAAO,CAACC,IAAAA,kBAA6B,8BAAA,EAACjC,SAAS,EAAEyB,OAAO,CAAC,CAAC,CAAC;QAE/D,OAAO;YACLrB,WAAW,EAAEqB,OAAO,CAACrB,WAAW;YAChCC,gBAAgB;SACjB,CAAC;IACJ,EAAE,OAAO6B,CAAC,EAAO;QACf,IAAI,CAAC,CAACA,CAAC,YAAYC,OAAiB,kBAAA,CAAC,EAAE;YACrCC,IAAG,CAACC,KAAK,CAACH,CAAC,CAACI,OAAO,CAAC,CAAC;QACvB,CAAC;QACDjB,GAAG,CAACkB,IAAI,CAAC,CAAC,4BAA4B,EAAEnB,UAAU,CAAC,CAAC,CAAC,CAAC;QACtDgB,IAAG,CAACI,GAAG,CACLC,MAAK,EAAA,QAAA,CAACC,MAAM,CACVD,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,gGAAgG,CAAC,CACxG,CACF,CAAC;QACF,MAAM,IAAIE,OAAW,YAAA,CAACT,CAAC,CAAC,CAAC;IAC3B,CAAC;AACH,CAAC"}