{"version": 3, "sources": ["../../../src/prebuild/resolveTemplate.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport assert from 'assert';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport { Ora } from 'ora';\nimport path from 'path';\nimport semver from 'semver';\n\nimport { fetchAsync } from '../api/rest/client';\nimport * as Log from '../log';\nimport { createGlobFilter } from '../utils/createFileTransform';\nimport { AbortCommandError, CommandError } from '../utils/errors';\nimport {\n  ExtractProps,\n  downloadAndExtractNpmModuleAsync,\n  extractLocalNpmTarballAsync,\n  extractNpmTarballFromUrlAsync,\n} from '../utils/npm';\nimport { isUrlOk } from '../utils/url';\n\nconst debug = require('debug')('expo:prebuild:resolveTemplate') as typeof console.log;\n\ntype RepoInfo = {\n  username: string;\n  name: string;\n  branch: string;\n  filePath: string;\n};\n\nexport async function cloneTemplateAsync({\n  templateDirectory,\n  template,\n  exp,\n  ora,\n}: {\n  templateDirectory: string;\n  template?: string;\n  exp: Pick<ExpoConfig, 'name' | 'sdkVersion'>;\n  ora: Ora;\n}): Promise<string> {\n  if (template) {\n    return await resolveTemplateArgAsync(templateDirectory, ora, exp.name, template);\n  } else {\n    const templatePackageName = await getTemplateNpmPackageName(exp.sdkVersion);\n    return await downloadAndExtractNpmModuleAsync(templatePackageName, {\n      cwd: templateDirectory,\n      name: exp.name,\n    });\n  }\n}\n\n/** Given an `sdkVersion` like `44.0.0` return a fully qualified NPM package name like: `expo-template-bare-minimum@sdk-44` */\nfunction getTemplateNpmPackageName(sdkVersion?: string): string {\n  // When undefined or UNVERSIONED, we use the latest version.\n  if (!sdkVersion || sdkVersion === 'UNVERSIONED') {\n    Log.log('Using an unspecified Expo SDK version. The latest template will be used.');\n    return `expo-template-bare-minimum@latest`;\n  }\n  return `expo-template-bare-minimum@sdk-${semver.major(sdkVersion)}`;\n}\n\nasync function getRepoInfo(url: any, examplePath?: string): Promise<RepoInfo | undefined> {\n  const [, username, name, t, _branch, ...file] = url.pathname.split('/');\n  const filePath = examplePath ? examplePath.replace(/^\\//, '') : file.join('/');\n\n  // Support repos whose entire purpose is to be an example, e.g.\n  // https://github.com/:username/:my-cool-example-repo-name.\n  if (t === undefined) {\n    const infoResponse = await fetchAsync(`https://api.github.com/repos/${username}/${name}`);\n    if (infoResponse.status !== 200) {\n      return;\n    }\n    const info = await infoResponse.json();\n    return { username, name, branch: info['default_branch'], filePath };\n  }\n\n  // If examplePath is available, the branch name takes the entire path\n  const branch = examplePath\n    ? `${_branch}/${file.join('/')}`.replace(new RegExp(`/${filePath}|/$`), '')\n    : _branch;\n\n  if (username && name && branch && t === 'tree') {\n    return { username, name, branch, filePath };\n  }\n  return undefined;\n}\n\nfunction hasRepo({ username, name, branch, filePath }: RepoInfo) {\n  const contentsUrl = `https://api.github.com/repos/${username}/${name}/contents`;\n  const packagePath = `${filePath ? `/${filePath}` : ''}/package.json`;\n\n  return isUrlOk(contentsUrl + packagePath + `?ref=${branch}`);\n}\n\nasync function downloadAndExtractRepoAsync(\n  { username, name, branch, filePath }: RepoInfo,\n  props: ExtractProps\n): Promise<string> {\n  const url = `https://codeload.github.com/${username}/${name}/tar.gz/${branch}`;\n\n  debug('Downloading tarball from:', url);\n\n  // Extract the (sub)directory into non-empty path segments\n  const directory = filePath.replace(/^\\//, '').split('/').filter(Boolean);\n  // Remove the (sub)directory paths, and the root folder added by GitHub\n  const strip = directory.length + 1;\n  // Only extract the relevant (sub)directories, ignoring irrelevant files\n  // The filder auto-ignores dotfiles, unless explicitly included\n  const filter = createGlobFilter(\n    !directory.length\n      ? ['*/**', '*/ios/.xcode.env']\n      : [`*/${directory.join('/')}/**`, `*/${directory.join('/')}/ios/.xcode.env`],\n    {\n      // Always ignore the `.xcworkspace` folder\n      ignore: ['**/ios/*.xcworkspace/**'],\n    }\n  );\n\n  return await extractNpmTarballFromUrlAsync(url, { ...props, strip, filter });\n}\n\nexport async function resolveTemplateArgAsync(\n  templateDirectory: string,\n  oraInstance: Ora,\n  appName: string,\n  template: string,\n  templatePath?: string\n): Promise<string> {\n  assert(template, 'template is required');\n\n  let repoUrl: URL | undefined;\n\n  try {\n    repoUrl = new URL(template);\n  } catch (error: any) {\n    if (error.code !== 'ERR_INVALID_URL') {\n      oraInstance.fail(error);\n      throw error;\n    }\n  }\n\n  // On Windows, we can actually create a URL from a local path\n  // Double-check if the created URL is not a path to avoid mixing up URLs and paths\n  if (process.platform === 'win32' && repoUrl && path.isAbsolute(repoUrl.toString())) {\n    repoUrl = undefined;\n  }\n\n  if (!repoUrl) {\n    const templatePath = path.resolve(template);\n    if (!fs.existsSync(templatePath)) {\n      throw new CommandError(`template file does not exist: ${templatePath}`);\n    }\n\n    return await extractLocalNpmTarballAsync(templatePath, {\n      cwd: templateDirectory,\n      name: appName,\n    });\n  }\n\n  if (repoUrl.origin !== 'https://github.com') {\n    oraInstance.fail(\n      `Invalid URL: ${chalk.red(\n        `\"${template}\"`\n      )}. Only GitHub repositories are supported. Please use a GitHub URL and try again.`\n    );\n    throw new AbortCommandError();\n  }\n\n  const repoInfo = await getRepoInfo(repoUrl, templatePath);\n\n  if (!repoInfo) {\n    oraInstance.fail(\n      `Found invalid GitHub URL: ${chalk.red(`\"${template}\"`)}. Please fix the URL and try again.`\n    );\n    throw new AbortCommandError();\n  }\n\n  const found = await hasRepo(repoInfo);\n\n  if (!found) {\n    oraInstance.fail(\n      `Could not locate the repository for ${chalk.red(\n        `\"${template}\"`\n      )}. Please check that the repository exists and try again.`\n    );\n    throw new AbortCommandError();\n  }\n\n  oraInstance.text = chalk.bold(\n    `Downloading files from repo ${chalk.cyan(template)}. This might take a moment.`\n  );\n\n  return await downloadAndExtractRepoAsync(repoInfo, {\n    cwd: templateDirectory,\n    name: appName,\n  });\n}\n"], "names": ["cloneTemplateAsync", "resolveTemplateArgAsync", "debug", "require", "templateDirectory", "template", "exp", "ora", "name", "templatePackageName", "getTemplateNpmPackageName", "sdkVersion", "downloadAndExtractNpmModuleAsync", "cwd", "Log", "log", "semver", "major", "getRepoInfo", "url", "examplePath", "username", "t", "_branch", "file", "pathname", "split", "filePath", "replace", "join", "undefined", "infoResponse", "fetchAsync", "status", "info", "json", "branch", "RegExp", "hasRepo", "contentsUrl", "packagePath", "isUrlOk", "downloadAndExtractRepoAsync", "props", "directory", "filter", "Boolean", "strip", "length", "createGlobFilter", "ignore", "extractNpmTarballFromUrlAsync", "oraInstance", "appName", "templatePath", "assert", "repoUrl", "URL", "error", "code", "fail", "process", "platform", "path", "isAbsolute", "toString", "resolve", "fs", "existsSync", "CommandError", "extractLocalNpmTarballAsync", "origin", "chalk", "red", "AbortCommandError", "repoInfo", "found", "text", "bold", "cyan"], "mappings": "AAAA;;;;;;;;;;;IA6BsBA,kBAAkB,MAAlBA,kBAAkB;IA4FlBC,uBAAuB,MAAvBA,uBAAuB;;;8DAxH1B,QAAQ;;;;;;;8DACT,OAAO;;;;;;;8DACV,IAAI;;;;;;;8DAEF,MAAM;;;;;;;8DACJ,QAAQ;;;;;;wBAEA,oBAAoB;2DAC1B,QAAQ;qCACI,8BAA8B;wBACf,iBAAiB;qBAM1D,cAAc;qBACG,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,+BAA+B,CAAC,AAAsB,AAAC;AAS/E,eAAeH,kBAAkB,CAAC,EACvCI,iBAAiB,CAAA,EACjBC,QAAQ,CAAA,EACRC,GAAG,CAAA,EACHC,GAAG,CAAA,EAMJ,EAAmB;IAClB,IAAIF,QAAQ,EAAE;QACZ,OAAO,MAAMJ,uBAAuB,CAACG,iBAAiB,EAAEG,GAAG,EAAED,GAAG,CAACE,IAAI,EAAEH,QAAQ,CAAC,CAAC;IACnF,OAAO;QACL,MAAMI,mBAAmB,GAAG,MAAMC,yBAAyB,CAACJ,GAAG,CAACK,UAAU,CAAC,AAAC;QAC5E,OAAO,MAAMC,IAAAA,IAAgC,iCAAA,EAACH,mBAAmB,EAAE;YACjEI,GAAG,EAAET,iBAAiB;YACtBI,IAAI,EAAEF,GAAG,CAACE,IAAI;SACf,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,4HAA4H,GAC5H,SAASE,yBAAyB,CAACC,UAAmB,EAAU;IAC9D,4DAA4D;IAC5D,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAK,aAAa,EAAE;QAC/CG,IAAG,CAACC,GAAG,CAAC,0EAA0E,CAAC,CAAC;QACpF,OAAO,CAAC,iCAAiC,CAAC,CAAC;IAC7C,CAAC;IACD,OAAO,CAAC,+BAA+B,EAAEC,OAAM,EAAA,QAAA,CAACC,KAAK,CAACN,UAAU,CAAC,CAAC,CAAC,CAAC;AACtE,CAAC;AAED,eAAeO,WAAW,CAACC,GAAQ,EAAEC,WAAoB,EAAiC;IACxF,MAAM,GAAGC,QAAQ,EAAEb,IAAI,EAAEc,CAAC,EAAEC,OAAO,EAAE,GAAGC,IAAI,CAAC,GAAGL,GAAG,CAACM,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,AAAC;IACxE,MAAMC,QAAQ,GAAGP,WAAW,GAAGA,WAAW,CAACQ,OAAO,QAAQ,EAAE,CAAC,GAAGJ,IAAI,CAACK,IAAI,CAAC,GAAG,CAAC,AAAC;IAE/E,+DAA+D;IAC/D,2DAA2D;IAC3D,IAAIP,CAAC,KAAKQ,SAAS,EAAE;QACnB,MAAMC,YAAY,GAAG,MAAMC,IAAAA,OAAU,WAAA,EAAC,CAAC,6BAA6B,EAAEX,QAAQ,CAAC,CAAC,EAAEb,IAAI,CAAC,CAAC,CAAC,AAAC;QAC1F,IAAIuB,YAAY,CAACE,MAAM,KAAK,GAAG,EAAE;YAC/B,OAAO;QACT,CAAC;QACD,MAAMC,IAAI,GAAG,MAAMH,YAAY,CAACI,IAAI,EAAE,AAAC;QACvC,OAAO;YAAEd,QAAQ;YAAEb,IAAI;YAAE4B,MAAM,EAAEF,IAAI,CAAC,gBAAgB,CAAC;YAAEP,QAAQ;SAAE,CAAC;IACtE,CAAC;IAED,qEAAqE;IACrE,MAAMS,MAAM,GAAGhB,WAAW,GACtB,CAAC,EAAEG,OAAO,CAAC,CAAC,EAAEC,IAAI,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAACD,OAAO,CAAC,IAAIS,MAAM,CAAC,CAAC,CAAC,EAAEV,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GACzEJ,OAAO,AAAC;IAEZ,IAAIF,QAAQ,IAAIb,IAAI,IAAI4B,MAAM,IAAId,CAAC,KAAK,MAAM,EAAE;QAC9C,OAAO;YAAED,QAAQ;YAAEb,IAAI;YAAE4B,MAAM;YAAET,QAAQ;SAAE,CAAC;IAC9C,CAAC;IACD,OAAOG,SAAS,CAAC;AACnB,CAAC;AAED,SAASQ,OAAO,CAAC,EAAEjB,QAAQ,CAAA,EAAEb,IAAI,CAAA,EAAE4B,MAAM,CAAA,EAAET,QAAQ,CAAA,EAAY,EAAE;IAC/D,MAAMY,WAAW,GAAG,CAAC,6BAA6B,EAAElB,QAAQ,CAAC,CAAC,EAAEb,IAAI,CAAC,SAAS,CAAC,AAAC;IAChF,MAAMgC,WAAW,GAAG,CAAC,EAAEb,QAAQ,GAAG,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,aAAa,CAAC,AAAC;IAErE,OAAOc,IAAAA,IAAO,QAAA,EAACF,WAAW,GAAGC,WAAW,GAAG,CAAC,KAAK,EAAEJ,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED,eAAeM,2BAA2B,CACxC,EAAErB,QAAQ,CAAA,EAAEb,IAAI,CAAA,EAAE4B,MAAM,CAAA,EAAET,QAAQ,CAAA,EAAY,EAC9CgB,KAAmB,EACF;IACjB,MAAMxB,GAAG,GAAG,CAAC,4BAA4B,EAAEE,QAAQ,CAAC,CAAC,EAAEb,IAAI,CAAC,QAAQ,EAAE4B,MAAM,CAAC,CAAC,AAAC;IAE/ElC,KAAK,CAAC,2BAA2B,EAAEiB,GAAG,CAAC,CAAC;IAExC,0DAA0D;IAC1D,MAAMyB,SAAS,GAAGjB,QAAQ,CAACC,OAAO,QAAQ,EAAE,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAACmB,MAAM,CAACC,OAAO,CAAC,AAAC;IACzE,uEAAuE;IACvE,MAAMC,KAAK,GAAGH,SAAS,CAACI,MAAM,GAAG,CAAC,AAAC;IACnC,wEAAwE;IACxE,+DAA+D;IAC/D,MAAMH,MAAM,GAAGI,IAAAA,oBAAgB,iBAAA,EAC7B,CAACL,SAAS,CAACI,MAAM,GACb;QAAC,MAAM;QAAE,kBAAkB;KAAC,GAC5B;QAAC,CAAC,EAAE,EAAEJ,SAAS,CAACf,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QAAE,CAAC,EAAE,EAAEe,SAAS,CAACf,IAAI,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC;KAAC,EAC9E;QACE,0CAA0C;QAC1CqB,MAAM,EAAE;YAAC,yBAAyB;SAAC;KACpC,CACF,AAAC;IAEF,OAAO,MAAMC,IAAAA,IAA6B,8BAAA,EAAChC,GAAG,EAAE;QAAE,GAAGwB,KAAK;QAAEI,KAAK;QAAEF,MAAM;KAAE,CAAC,CAAC;AAC/E,CAAC;AAEM,eAAe5C,uBAAuB,CAC3CG,iBAAyB,EACzBgD,WAAgB,EAChBC,OAAe,EACfhD,QAAgB,EAChBiD,YAAqB,EACJ;IACjBC,IAAAA,OAAM,EAAA,QAAA,EAAClD,QAAQ,EAAE,sBAAsB,CAAC,CAAC;IAEzC,IAAImD,OAAO,AAAiB,AAAC;IAE7B,IAAI;QACFA,OAAO,GAAG,IAAIC,GAAG,CAACpD,QAAQ,CAAC,CAAC;IAC9B,EAAE,OAAOqD,KAAK,EAAO;QACnB,IAAIA,KAAK,CAACC,IAAI,KAAK,iBAAiB,EAAE;YACpCP,WAAW,CAACQ,IAAI,CAACF,KAAK,CAAC,CAAC;YACxB,MAAMA,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,6DAA6D;IAC7D,kFAAkF;IAClF,IAAIG,OAAO,CAACC,QAAQ,KAAK,OAAO,IAAIN,OAAO,IAAIO,KAAI,EAAA,QAAA,CAACC,UAAU,CAACR,OAAO,CAACS,QAAQ,EAAE,CAAC,EAAE;QAClFT,OAAO,GAAG1B,SAAS,CAAC;IACtB,CAAC;IAED,IAAI,CAAC0B,OAAO,EAAE;QACZ,MAAMF,aAAY,GAAGS,KAAI,EAAA,QAAA,CAACG,OAAO,CAAC7D,QAAQ,CAAC,AAAC;QAC5C,IAAI,CAAC8D,GAAE,EAAA,QAAA,CAACC,UAAU,CAACd,aAAY,CAAC,EAAE;YAChC,MAAM,IAAIe,OAAY,aAAA,CAAC,CAAC,8BAA8B,EAAEf,aAAY,CAAC,CAAC,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO,MAAMgB,IAAAA,IAA2B,4BAAA,EAAChB,aAAY,EAAE;YACrDzC,GAAG,EAAET,iBAAiB;YACtBI,IAAI,EAAE6C,OAAO;SACd,CAAC,CAAC;IACL,CAAC;IAED,IAAIG,OAAO,CAACe,MAAM,KAAK,oBAAoB,EAAE;QAC3CnB,WAAW,CAACQ,IAAI,CACd,CAAC,aAAa,EAAEY,MAAK,EAAA,QAAA,CAACC,GAAG,CACvB,CAAC,CAAC,EAAEpE,QAAQ,CAAC,CAAC,CAAC,CAChB,CAAC,gFAAgF,CAAC,CACpF,CAAC;QACF,MAAM,IAAIqE,OAAiB,kBAAA,EAAE,CAAC;IAChC,CAAC;IAED,MAAMC,QAAQ,GAAG,MAAMzD,WAAW,CAACsC,OAAO,EAAEF,YAAY,CAAC,AAAC;IAE1D,IAAI,CAACqB,QAAQ,EAAE;QACbvB,WAAW,CAACQ,IAAI,CACd,CAAC,0BAA0B,EAAEY,MAAK,EAAA,QAAA,CAACC,GAAG,CAAC,CAAC,CAAC,EAAEpE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,mCAAmC,CAAC,CAC7F,CAAC;QACF,MAAM,IAAIqE,OAAiB,kBAAA,EAAE,CAAC;IAChC,CAAC;IAED,MAAME,KAAK,GAAG,MAAMtC,OAAO,CAACqC,QAAQ,CAAC,AAAC;IAEtC,IAAI,CAACC,KAAK,EAAE;QACVxB,WAAW,CAACQ,IAAI,CACd,CAAC,oCAAoC,EAAEY,MAAK,EAAA,QAAA,CAACC,GAAG,CAC9C,CAAC,CAAC,EAAEpE,QAAQ,CAAC,CAAC,CAAC,CAChB,CAAC,wDAAwD,CAAC,CAC5D,CAAC;QACF,MAAM,IAAIqE,OAAiB,kBAAA,EAAE,CAAC;IAChC,CAAC;IAEDtB,WAAW,CAACyB,IAAI,GAAGL,MAAK,EAAA,QAAA,CAACM,IAAI,CAC3B,CAAC,4BAA4B,EAAEN,MAAK,EAAA,QAAA,CAACO,IAAI,CAAC1E,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CACjF,CAAC;IAEF,OAAO,MAAMqC,2BAA2B,CAACiC,QAAQ,EAAE;QACjD9D,GAAG,EAAET,iBAAiB;QACtBI,IAAI,EAAE6C,OAAO;KACd,CAAC,CAAC;AACL,CAAC"}