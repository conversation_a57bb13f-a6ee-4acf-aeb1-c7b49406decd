{"version": 3, "sources": ["../../../../../src/run/ios/codeSigning/simulatorCodeSigning.ts"], "sourcesContent": ["import { IOSConfig } from '@expo/config-plugins';\nimport plist from '@expo/plist';\nimport fs from 'fs';\n\nconst debug = require('debug')('expo:run:ios:codeSigning:simulator');\n\n// NOTE(EvanBacon): These are entitlements that work in a simulator\n// but still require the project to have development code signing setup.\n// There may be more, but this is fine for now.\nconst ENTITLEMENTS_THAT_REQUIRE_CODE_SIGNING = [\n  'com.apple.developer.associated-domains',\n  'com.apple.developer.applesignin',\n];\n\nfunction getEntitlements(projectRoot: string): Record<string, any> | null {\n  try {\n    const entitlementsPath = IOSConfig.Entitlements.getEntitlementsPath(projectRoot);\n    if (!entitlementsPath || !fs.existsSync(entitlementsPath)) {\n      return null;\n    }\n\n    const entitlementsContents = fs.readFileSync(entitlementsPath, 'utf8');\n    const entitlements = plist.parse(entitlementsContents);\n    return entitlements;\n  } catch (error) {\n    debug('Failed to read entitlements', error);\n  }\n  return null;\n}\n\n/** @returns true if the simulator build should be code signed for development. */\nexport function simulatorBuildRequiresCodeSigning(projectRoot: string): boolean {\n  const entitlements = getEntitlements(projectRoot);\n  if (!entitlements) {\n    return false;\n  }\n  return ENTITLEMENTS_THAT_REQUIRE_CODE_SIGNING.some((entitlement) => entitlement in entitlements);\n}\n"], "names": ["simulatorBuildRequiresCodeSigning", "debug", "require", "ENTITLEMENTS_THAT_REQUIRE_CODE_SIGNING", "getEntitlements", "projectRoot", "entitlementsPath", "IOSConfig", "Entitlements", "getEntitlementsPath", "fs", "existsSync", "entitlementsContents", "readFileSync", "entitlements", "plist", "parse", "error", "some", "entitlement"], "mappings": "AAAA;;;;+BA+BgBA,mCAAiC;;aAAjCA,iCAAiC;;;yBA/BvB,sBAAsB;;;;;;;8DAC9B,aAAa;;;;;;;8DAChB,IAAI;;;;;;;;;;;AAEnB,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,oCAAoC,CAAC,AAAC;AAErE,mEAAmE;AACnE,wEAAwE;AACxE,+CAA+C;AAC/C,MAAMC,sCAAsC,GAAG;IAC7C,wCAAwC;IACxC,iCAAiC;CAClC,AAAC;AAEF,SAASC,eAAe,CAACC,WAAmB,EAA8B;IACxE,IAAI;QACF,MAAMC,gBAAgB,GAAGC,cAAS,EAAA,UAAA,CAACC,YAAY,CAACC,mBAAmB,CAACJ,WAAW,CAAC,AAAC;QACjF,IAAI,CAACC,gBAAgB,IAAI,CAACI,GAAE,EAAA,QAAA,CAACC,UAAU,CAACL,gBAAgB,CAAC,EAAE;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAMM,oBAAoB,GAAGF,GAAE,EAAA,QAAA,CAACG,YAAY,CAACP,gBAAgB,EAAE,MAAM,CAAC,AAAC;QACvE,MAAMQ,YAAY,GAAGC,MAAK,EAAA,QAAA,CAACC,KAAK,CAACJ,oBAAoB,CAAC,AAAC;QACvD,OAAOE,YAAY,CAAC;IACtB,EAAE,OAAOG,KAAK,EAAE;QACdhB,KAAK,CAAC,6BAA6B,EAAEgB,KAAK,CAAC,CAAC;IAC9C,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAGM,SAASjB,iCAAiC,CAACK,WAAmB,EAAW;IAC9E,MAAMS,YAAY,GAAGV,eAAe,CAACC,WAAW,CAAC,AAAC;IAClD,IAAI,CAACS,YAAY,EAAE;QACjB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAOX,sCAAsC,CAACe,IAAI,CAAC,CAACC,WAAW,GAAKA,WAAW,IAAIL,YAAY,CAAC,CAAC;AACnG,CAAC"}