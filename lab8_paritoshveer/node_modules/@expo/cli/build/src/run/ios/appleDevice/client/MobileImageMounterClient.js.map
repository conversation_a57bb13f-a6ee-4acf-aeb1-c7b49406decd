{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/client/MobileImageMounterClient.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport Debug from 'debug';\nimport * as fs from 'fs';\nimport { Socket } from 'net';\n\nimport { ResponseError, ServiceClient } from './ServiceClient';\nimport type { LockdownCommand, LockdownResponse } from '../protocol/LockdownProtocol';\nimport { isLockdownResponse, LockdownProtocolClient } from '../protocol/LockdownProtocol';\n\nconst debug = Debug('expo:apple-device:client:mobile_image_mounter');\n\nexport type MIMMountResponse = LockdownResponse;\n\nexport interface MIMMessage extends LockdownCommand {\n  ImageType: string;\n}\n\nexport interface MIMLookupResponse extends LockdownResponse {\n  ImageSignature?: string;\n}\n\nexport interface MIMUploadCompleteResponse extends LockdownResponse {\n  Status: 'Complete';\n}\n\nexport interface MIMUploadReceiveBytesResponse extends LockdownResponse {\n  Status: 'ReceiveBytesAck';\n}\n\nfunction isMIMUploadCompleteResponse(resp: any): resp is MIMUploadCompleteResponse {\n  return resp.Status === 'Complete';\n}\n\nfunction isMIMUploadReceiveBytesResponse(resp: any): resp is MIMUploadReceiveBytesResponse {\n  return resp.Status === 'ReceiveBytesAck';\n}\n\nexport class MobileImageMounterClient extends ServiceClient<LockdownProtocolClient<MIMMessage>> {\n  constructor(socket: Socket) {\n    super(socket, new LockdownProtocolClient(socket));\n  }\n\n  async mountImage(imagePath: string, imageSig: Buffer) {\n    debug(`mountImage: ${imagePath}`);\n\n    const resp = await this.protocolClient.sendMessage({\n      Command: 'MountImage',\n      ImagePath: imagePath,\n      ImageSignature: imageSig,\n      ImageType: 'Developer',\n    });\n\n    if (!isLockdownResponse(resp) || resp.Status !== 'Complete') {\n      throw new ResponseError(`There was an error mounting ${imagePath} on device`, resp);\n    }\n  }\n\n  async uploadImage(imagePath: string, imageSig: Buffer) {\n    debug(`uploadImage: ${imagePath}`);\n\n    const imageSize = fs.statSync(imagePath).size;\n    return this.protocolClient.sendMessage(\n      {\n        Command: 'ReceiveBytes',\n        ImageSize: imageSize,\n        ImageSignature: imageSig,\n        ImageType: 'Developer',\n      },\n      (resp: any, resolve, reject) => {\n        if (isMIMUploadReceiveBytesResponse(resp)) {\n          const imageStream = fs.createReadStream(imagePath);\n          imageStream.pipe(this.protocolClient.socket, { end: false });\n          imageStream.on('error', (err) => reject(err));\n        } else if (isMIMUploadCompleteResponse(resp)) {\n          resolve();\n        } else {\n          reject(\n            new ResponseError(`There was an error uploading image ${imagePath} to the device`, resp)\n          );\n        }\n      }\n    );\n  }\n\n  async lookupImage() {\n    debug('lookupImage');\n\n    return this.protocolClient.sendMessage<MIMLookupResponse>({\n      Command: 'LookupImage',\n      ImageType: 'Developer',\n    });\n  }\n}\n"], "names": ["MobileImageMounterClient", "debug", "Debug", "isMIMUploadCompleteResponse", "resp", "Status", "isMIMUploadReceiveBytesResponse", "ServiceClient", "constructor", "socket", "LockdownProtocolClient", "mountImage", "imagePath", "imageSig", "protocolClient", "sendMessage", "Command", "ImagePath", "ImageSignature", "ImageType", "isLockdownResponse", "ResponseError", "uploadImage", "imageSize", "fs", "statSync", "size", "ImageSize", "resolve", "reject", "imageStream", "createReadStream", "pipe", "end", "on", "err", "lookupImage"], "mappings": "AAAA;;;;;;CAMC,GACD;;;;+BAoCaA,0BAAwB;;aAAxBA,wBAAwB;;;8DApCnB,OAAO;;;;;;;+DACL,IAAI;;;;;;+BAGqB,iBAAiB;kCAEH,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzF,MAAMC,KAAK,GAAGC,IAAAA,MAAK,EAAA,QAAA,EAAC,+CAA+C,CAAC,AAAC;AAoBrE,SAASC,2BAA2B,CAACC,IAAS,EAAqC;IACjF,OAAOA,IAAI,CAACC,MAAM,KAAK,UAAU,CAAC;AACpC,CAAC;AAED,SAASC,+BAA+B,CAACF,IAAS,EAAyC;IACzF,OAAOA,IAAI,CAACC,MAAM,KAAK,iBAAiB,CAAC;AAC3C,CAAC;AAEM,MAAML,wBAAwB,SAASO,cAAa,cAAA;IACzDC,YAAYC,MAAc,CAAE;QAC1B,KAAK,CAACA,MAAM,EAAE,IAAIC,iBAAsB,uBAAA,CAACD,MAAM,CAAC,CAAC,CAAC;IACpD;UAEME,UAAU,CAACC,SAAiB,EAAEC,QAAgB,EAAE;QACpDZ,KAAK,CAAC,CAAC,YAAY,EAAEW,SAAS,CAAC,CAAC,CAAC,CAAC;QAElC,MAAMR,IAAI,GAAG,MAAM,IAAI,CAACU,cAAc,CAACC,WAAW,CAAC;YACjDC,OAAO,EAAE,YAAY;YACrBC,SAAS,EAAEL,SAAS;YACpBM,cAAc,EAAEL,QAAQ;YACxBM,SAAS,EAAE,WAAW;SACvB,CAAC,AAAC;QAEH,IAAI,CAACC,IAAAA,iBAAkB,mBAAA,EAAChB,IAAI,CAAC,IAAIA,IAAI,CAACC,MAAM,KAAK,UAAU,EAAE;YAC3D,MAAM,IAAIgB,cAAa,cAAA,CAAC,CAAC,4BAA4B,EAAET,SAAS,CAAC,UAAU,CAAC,EAAER,IAAI,CAAC,CAAC;QACtF,CAAC;IACH;UAEMkB,WAAW,CAACV,SAAiB,EAAEC,QAAgB,EAAE;QACrDZ,KAAK,CAAC,CAAC,aAAa,EAAEW,SAAS,CAAC,CAAC,CAAC,CAAC;QAEnC,MAAMW,SAAS,GAAGC,GAAE,EAAA,CAACC,QAAQ,CAACb,SAAS,CAAC,CAACc,IAAI,AAAC;QAC9C,OAAO,IAAI,CAACZ,cAAc,CAACC,WAAW,CACpC;YACEC,OAAO,EAAE,cAAc;YACvBW,SAAS,EAAEJ,SAAS;YACpBL,cAAc,EAAEL,QAAQ;YACxBM,SAAS,EAAE,WAAW;SACvB,EACD,CAACf,IAAS,EAAEwB,OAAO,EAAEC,MAAM,GAAK;YAC9B,IAAIvB,+BAA+B,CAACF,IAAI,CAAC,EAAE;gBACzC,MAAM0B,WAAW,GAAGN,GAAE,EAAA,CAACO,gBAAgB,CAACnB,SAAS,CAAC,AAAC;gBACnDkB,WAAW,CAACE,IAAI,CAAC,IAAI,CAAClB,cAAc,CAACL,MAAM,EAAE;oBAAEwB,GAAG,EAAE,KAAK;iBAAE,CAAC,CAAC;gBAC7DH,WAAW,CAACI,EAAE,CAAC,OAAO,EAAE,CAACC,GAAG,GAAKN,MAAM,CAACM,GAAG,CAAC,CAAC,CAAC;YAChD,OAAO,IAAIhC,2BAA2B,CAACC,IAAI,CAAC,EAAE;gBAC5CwB,OAAO,EAAE,CAAC;YACZ,OAAO;gBACLC,MAAM,CACJ,IAAIR,cAAa,cAAA,CAAC,CAAC,mCAAmC,EAAET,SAAS,CAAC,cAAc,CAAC,EAAER,IAAI,CAAC,CACzF,CAAC;YACJ,CAAC;QACH,CAAC,CACF,CAAC;IACJ;UAEMgC,WAAW,GAAG;QAClBnC,KAAK,CAAC,aAAa,CAAC,CAAC;QAErB,OAAO,IAAI,CAACa,cAAc,CAACC,WAAW,CAAoB;YACxDC,OAAO,EAAE,aAAa;YACtBG,SAAS,EAAE,WAAW;SACvB,CAAC,CAAC;IACL;CACD"}