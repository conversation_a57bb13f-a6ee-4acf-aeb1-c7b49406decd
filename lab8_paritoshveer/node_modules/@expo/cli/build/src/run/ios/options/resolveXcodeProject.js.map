{"version": 3, "sources": ["../../../../../src/run/ios/options/resolveXcodeProject.ts"], "sourcesContent": ["import { sync as globSync } from 'glob';\n\nimport { CommandError } from '../../../utils/errors';\nimport { ProjectInfo } from '../XcodeBuild.types';\n\nconst ignoredPaths = ['**/@(Carthage|Pods|vendor|node_modules)/**'];\n\nfunction findXcodeProjectPaths(\n  projectRoot: string,\n  extension: 'xcworkspace' | 'xcodeproj'\n): string[] {\n  return globSync(`ios/*.${extension}`, {\n    absolute: true,\n    cwd: projectRoot,\n    ignore: ignoredPaths,\n  });\n}\n\n/** Return the path and type of Xcode project in the given folder. */\nexport function resolveXcodeProject(projectRoot: string): ProjectInfo {\n  let paths = findXcodeProjectPaths(projectRoot, 'xcworkspace');\n  if (paths.length) {\n    return {\n      // Use full path instead of relative project root so that warnings and errors contain full paths as well, this helps with filtering.\n      // Also helps keep things consistent in monorepos.\n      name: paths[0],\n      // name: path.relative(projectRoot, paths[0]),\n      isWorkspace: true,\n    };\n  }\n  paths = findXcodeProjectPaths(projectRoot, 'xcodeproj');\n  if (paths.length) {\n    return { name: paths[0], isWorkspace: false };\n  }\n  throw new CommandError(\n    'IOS_MALFORMED',\n    `Xcode project not found in project: ${projectRoot}. You can generate a project with \\`npx expo prebuild\\``\n  );\n}\n"], "names": ["resolveXcodeProject", "ignoredPaths", "findXcodeProjectPaths", "projectRoot", "extension", "globSync", "absolute", "cwd", "ignore", "paths", "length", "name", "isWorkspace", "CommandError"], "mappings": "AAAA;;;;+BAmBgBA,qBAAmB;;aAAnBA,mBAAmB;;;yBAnBF,MAAM;;;;;;wBAEV,uBAAuB;AAGpD,MAAMC,YAAY,GAAG;IAAC,4CAA4C;CAAC,AAAC;AAEpE,SAASC,qBAAqB,CAC5BC,WAAmB,EACnBC,SAAsC,EAC5B;IACV,OAAOC,IAAAA,KAAQ,EAAA,KAAA,EAAC,CAAC,MAAM,EAAED,SAAS,CAAC,CAAC,EAAE;QACpCE,QAAQ,EAAE,IAAI;QACdC,GAAG,EAAEJ,WAAW;QAChBK,MAAM,EAAEP,YAAY;KACrB,CAAC,CAAC;AACL,CAAC;AAGM,SAASD,mBAAmB,CAACG,WAAmB,EAAe;IACpE,IAAIM,KAAK,GAAGP,qBAAqB,CAACC,WAAW,EAAE,aAAa,CAAC,AAAC;IAC9D,IAAIM,KAAK,CAACC,MAAM,EAAE;QAChB,OAAO;YACL,oIAAoI;YACpI,kDAAkD;YAClDC,IAAI,EAAEF,KAAK,CAAC,CAAC,CAAC;YACd,8CAA8C;YAC9CG,WAAW,EAAE,IAAI;SAClB,CAAC;IACJ,CAAC;IACDH,KAAK,GAAGP,qBAAqB,CAACC,WAAW,EAAE,WAAW,CAAC,CAAC;IACxD,IAAIM,KAAK,CAACC,MAAM,EAAE;QAChB,OAAO;YAAEC,IAAI,EAAEF,KAAK,CAAC,CAAC,CAAC;YAAEG,WAAW,EAAE,KAAK;SAAE,CAAC;IAChD,CAAC;IACD,MAAM,IAAIC,OAAY,aAAA,CACpB,eAAe,EACf,CAAC,oCAAoC,EAAEV,WAAW,CAAC,uDAAuD,CAAC,CAC5G,CAAC;AACJ,CAAC"}