{"version": 3, "sources": ["../../../../src/run/ios/runIosAsync.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport * as <PERSON>codeB<PERSON> from './XcodeBuild';\nimport { Options } from './XcodeBuild.types';\nimport { launchAppAsync } from './launchApp';\nimport { resolveOptionsAsync } from './options/resolveOptions';\nimport * as Log from '../../log';\nimport { maybePromptToSyncPodsAsync } from '../../utils/cocoapods';\nimport { setNodeEnv } from '../../utils/nodeEnv';\nimport { ensurePortAvailabilityAsync } from '../../utils/port';\nimport { profile } from '../../utils/profile';\nimport { getSchemesForIosAsync } from '../../utils/scheme';\nimport { ensureNativeProjectAsync } from '../ensureNativeProject';\nimport { logProjectLogsLocation } from '../hints';\nimport { startBundlerAsync } from '../startBundler';\n\nconst debug = require('debug')('expo:run:ios');\n\nexport async function runIosAsync(projectRoot: string, options: Options) {\n  setNodeEnv(options.configuration === 'Release' ? 'production' : 'development');\n  require('@expo/env').load(projectRoot);\n\n  assertPlatform();\n\n  const install = !!options.install;\n\n  if ((await ensureNativeProjectAsync(projectRoot, { platform: 'ios', install })) && install) {\n    await maybePromptToSyncPodsAsync(projectRoot);\n  }\n\n  // Resolve the CLI arguments into useable options.\n  const props = await resolveOptionsAsync(projectRoot, options);\n\n  // Spawn the `xcodebuild` process to create the app binary.\n  const buildOutput = await XcodeBuild.buildAsync(props);\n\n  // Find the path to the built app binary, this will be used to install the binary\n  // on a device.\n  const binaryPath = await profile(XcodeBuild.getAppBinaryPath)(buildOutput);\n\n  debug('Binary path:', binaryPath);\n\n  // Ensure the port hasn't become busy during the build.\n  if (props.shouldStartBundler && !(await ensurePortAvailabilityAsync(projectRoot, props))) {\n    props.shouldStartBundler = false;\n  }\n\n  // Start the dev server which creates all of the required info for\n  // launching the app on a simulator.\n  const manager = await startBundlerAsync(projectRoot, {\n    port: props.port,\n    headless: !props.shouldStartBundler,\n    // If a scheme is specified then use that instead of the package name.\n    scheme: (await getSchemesForIosAsync(projectRoot))?.[0],\n  });\n\n  // Install and launch the app binary on a device.\n  await launchAppAsync(binaryPath, manager, {\n    isSimulator: props.isSimulator,\n    device: props.device,\n    shouldStartBundler: props.shouldStartBundler,\n  });\n\n  // Log the location of the JS logs for the device.\n  if (props.shouldStartBundler) {\n    logProjectLogsLocation();\n  } else {\n    await manager.stopAsync();\n  }\n}\n\nfunction assertPlatform() {\n  if (process.platform !== 'darwin') {\n    Log.exit(\n      chalk`iOS apps can only be built on macOS devices. Use {cyan eas build -p ios} to build in the cloud.`\n    );\n  }\n}\n"], "names": ["runIosAsync", "debug", "require", "projectRoot", "options", "setNodeEnv", "configuration", "load", "assertPlatform", "install", "ensureNativeProjectAsync", "platform", "maybePromptToSyncPodsAsync", "props", "resolveOptionsAsync", "buildOutput", "XcodeBuild", "buildAsync", "binaryPath", "profile", "getAppBinaryPath", "shouldStartBundler", "ensurePortAvailabilityAsync", "manager", "startBundlerAsync", "port", "headless", "scheme", "getSchemesForIosAsync", "launchAppAsync", "isSimulator", "device", "logProjectLogsLocation", "stopAsync", "process", "Log", "exit", "chalk"], "mappings": "AAAA;;;;+BAk<PERSON><PERSON><PERSON>,aAAW;;aAAXA,WAAW;;;8DAlBf,OAAO;;;;;;kEAEG,cAAc;2BAEX,aAAa;gCACR,0BAA0B;2DACzC,WAAW;2BACW,uBAAuB;yBACvC,qBAAqB;sBACJ,kBAAkB;yBACtC,qBAAqB;wBACP,oBAAoB;qCACjB,wBAAwB;uBAC1B,UAAU;8BACf,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,AAAC;AAExC,eAAeF,WAAW,CAACG,WAAmB,EAAEC,OAAgB,EAAE;QAmC7D,GAA0C;IAlCpDC,IAAAA,QAAU,WAAA,EAACD,OAAO,CAACE,aAAa,KAAK,SAAS,GAAG,YAAY,GAAG,aAAa,CAAC,CAAC;IAC/EJ,OAAO,CAAC,WAAW,CAAC,CAACK,IAAI,CAACJ,WAAW,CAAC,CAAC;IAEvCK,cAAc,EAAE,CAAC;IAEjB,MAAMC,OAAO,GAAG,CAAC,CAACL,OAAO,CAACK,OAAO,AAAC;IAElC,IAAI,AAAC,MAAMC,IAAAA,oBAAwB,yBAAA,EAACP,WAAW,EAAE;QAAEQ,QAAQ,EAAE,KAAK;QAAEF,OAAO;KAAE,CAAC,IAAKA,OAAO,EAAE;QAC1F,MAAMG,IAAAA,UAA0B,2BAAA,EAACT,WAAW,CAAC,CAAC;IAChD,CAAC;IAED,kDAAkD;IAClD,MAAMU,KAAK,GAAG,MAAMC,IAAAA,eAAmB,oBAAA,EAACX,WAAW,EAAEC,OAAO,CAAC,AAAC;IAE9D,2DAA2D;IAC3D,MAAMW,WAAW,GAAG,MAAMC,WAAU,CAACC,UAAU,CAACJ,KAAK,CAAC,AAAC;IAEvD,iFAAiF;IACjF,eAAe;IACf,MAAMK,UAAU,GAAG,MAAMC,IAAAA,QAAO,QAAA,EAACH,WAAU,CAACI,gBAAgB,CAAC,CAACL,WAAW,CAAC,AAAC;IAE3Ed,KAAK,CAAC,cAAc,EAAEiB,UAAU,CAAC,CAAC;IAElC,uDAAuD;IACvD,IAAIL,KAAK,CAACQ,kBAAkB,IAAI,CAAE,MAAMC,IAAAA,KAA2B,4BAAA,EAACnB,WAAW,EAAEU,KAAK,CAAC,AAAC,EAAE;QACxFA,KAAK,CAACQ,kBAAkB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED,kEAAkE;IAClE,oCAAoC;IACpC,MAAME,OAAO,GAAG,MAAMC,IAAAA,aAAiB,kBAAA,EAACrB,WAAW,EAAE;QACnDsB,IAAI,EAAEZ,KAAK,CAACY,IAAI;QAChBC,QAAQ,EAAE,CAACb,KAAK,CAACQ,kBAAkB;QACnC,sEAAsE;QACtEM,MAAM,EAAE,CAAA,GAA0C,GAAzC,MAAMC,IAAAA,OAAqB,sBAAA,EAACzB,WAAW,CAAC,SAAM,GAA/C,KAAA,CAA+C,GAA/C,GAA0C,AAAE,CAAC,CAAC,CAAC;KACxD,CAAC,AAAC;IAEH,iDAAiD;IACjD,MAAM0B,IAAAA,UAAc,eAAA,EAACX,UAAU,EAAEK,OAAO,EAAE;QACxCO,WAAW,EAAEjB,KAAK,CAACiB,WAAW;QAC9BC,MAAM,EAAElB,KAAK,CAACkB,MAAM;QACpBV,kBAAkB,EAAER,KAAK,CAACQ,kBAAkB;KAC7C,CAAC,CAAC;IAEH,kDAAkD;IAClD,IAAIR,KAAK,CAACQ,kBAAkB,EAAE;QAC5BW,IAAAA,MAAsB,uBAAA,GAAE,CAAC;IAC3B,OAAO;QACL,MAAMT,OAAO,CAACU,SAAS,EAAE,CAAC;IAC5B,CAAC;AACH,CAAC;AAED,SAASzB,cAAc,GAAG;IACxB,IAAI0B,OAAO,CAACvB,QAAQ,KAAK,QAAQ,EAAE;QACjCwB,IAAG,CAACC,IAAI,CACNC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,+FAA+F,CAAC,CACvG,CAAC;IACJ,CAAC;AACH,CAAC"}