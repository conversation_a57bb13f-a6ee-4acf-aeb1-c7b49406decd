{"version": 3, "sources": ["../../../../../src/run/ios/codeSigning/xcodeCodeSigning.ts"], "sourcesContent": ["import { IOSConfig, XcodeProject } from '@expo/config-plugins';\nimport fs from 'fs';\n\nexport type CodeSigningInfo = Record<\n  string,\n  {\n    developmentTeams: string[];\n    provisioningProfiles: string[];\n  }\n>;\n\n/** Find the development team and provisioning profile that's currently in use by the Xcode project. */\nexport function getCodeSigningInfoForPbxproj(projectRoot: string): CodeSigningInfo {\n  const project = IOSConfig.XcodeUtils.getPbxproj(projectRoot);\n  const targets = IOSConfig.Target.findSignableTargets(project);\n\n  const signingInfo: CodeSigningInfo = {};\n  for (const [nativeTargetId, nativeTarget] of targets) {\n    const developmentTeams: string[] = [];\n    const provisioningProfiles: string[] = [];\n\n    IOSConfig.XcodeUtils.getBuildConfigurationsForListId(\n      project,\n      nativeTarget.buildConfigurationList\n    )\n      .filter(\n        ([, item]: IOSConfig.XcodeUtils.ConfigurationSectionEntry) =>\n          item.buildSettings.PRODUCT_NAME\n      )\n      .forEach(([, item]: IOSConfig.XcodeUtils.ConfigurationSectionEntry) => {\n        const { DEVELOPMENT_TEAM, PROVISIONING_PROFILE } = item.buildSettings;\n        if (\n          typeof DEVELOPMENT_TEAM === 'string' &&\n          // If the user selects \"Team: none\" in Xcode, it'll be an empty string.\n          !!DEVELOPMENT_TEAM &&\n          // xcode package sometimes reads an empty string as a quoted empty string.\n          DEVELOPMENT_TEAM !== '\"\"'\n        ) {\n          developmentTeams.push(DEVELOPMENT_TEAM);\n        }\n        if (typeof PROVISIONING_PROFILE === 'string' && !!PROVISIONING_PROFILE) {\n          provisioningProfiles.push(PROVISIONING_PROFILE);\n        }\n      });\n    signingInfo[nativeTargetId] = {\n      developmentTeams,\n      provisioningProfiles,\n    };\n  }\n\n  return signingInfo;\n}\n\n/**\n * Set the development team and configure the Xcode project for automatic code signing,\n * this helps us resolve the code signing on subsequent runs and emulates Xcode behavior.\n *\n * @param props.project xcode project object from `xcode` package.\n * @param props.appleTeamId Apple Team ID to use for code signing.\n */\nexport function mutateXcodeProjectWithAutoCodeSigningInfo({\n  project,\n  appleTeamId,\n}: {\n  project: XcodeProject;\n  appleTeamId: string;\n}): XcodeProject {\n  const targets = IOSConfig.Target.findSignableTargets(project);\n\n  const quotedAppleTeamId = ensureQuotes(appleTeamId);\n\n  for (const [nativeTargetId, nativeTarget] of targets) {\n    IOSConfig.XcodeUtils.getBuildConfigurationsForListId(\n      project,\n      nativeTarget.buildConfigurationList\n    )\n      .filter(\n        ([, item]: IOSConfig.XcodeUtils.ConfigurationSectionEntry) =>\n          item.buildSettings.PRODUCT_NAME\n      )\n      .forEach(([, item]: IOSConfig.XcodeUtils.ConfigurationSectionEntry) => {\n        item.buildSettings.DEVELOPMENT_TEAM = quotedAppleTeamId;\n        item.buildSettings.CODE_SIGN_IDENTITY = '\"Apple Development\"';\n        item.buildSettings.CODE_SIGN_STYLE = 'Automatic';\n      });\n\n    Object.entries(IOSConfig.XcodeUtils.getProjectSection(project))\n      .filter(IOSConfig.XcodeUtils.isNotComment)\n      .forEach(([, item]: IOSConfig.XcodeUtils.ProjectSectionEntry) => {\n        if (!item.attributes.TargetAttributes) {\n          item.attributes.TargetAttributes = {};\n        }\n\n        if (!item.attributes.TargetAttributes[nativeTargetId]) {\n          item.attributes.TargetAttributes[nativeTargetId] = {};\n        }\n\n        item.attributes.TargetAttributes[nativeTargetId].DevelopmentTeam = quotedAppleTeamId;\n        item.attributes.TargetAttributes[nativeTargetId].ProvisioningStyle = 'Automatic';\n      });\n  }\n\n  return project;\n}\n\n/**\n * Configures the Xcode project for automatic code signing and persists the results.\n */\nexport function setAutoCodeSigningInfoForPbxproj(\n  projectRoot: string,\n  { appleTeamId }: { appleTeamId: string }\n): void {\n  const project = IOSConfig.XcodeUtils.getPbxproj(projectRoot);\n  mutateXcodeProjectWithAutoCodeSigningInfo({ project, appleTeamId });\n\n  fs.writeFileSync(project.filepath, project.writeSync());\n}\n\nconst ensureQuotes = (value: string) => {\n  if (!value.match(/^['\"]/)) {\n    return `\"${value}\"`;\n  }\n  return value;\n};\n"], "names": ["getCodeSigningInfoForPbxproj", "mutateXcodeProjectWithAutoCodeSigningInfo", "setAutoCodeSigningInfoForPbxproj", "projectRoot", "project", "IOSConfig", "XcodeUtils", "getPbxproj", "targets", "Target", "findSignableTargets", "signingInfo", "nativeTargetId", "nativeTarget", "developmentTeams", "provisioningProfiles", "getBuildConfigurationsForListId", "buildConfigurationList", "filter", "item", "buildSettings", "PRODUCT_NAME", "for<PERSON>ach", "DEVELOPMENT_TEAM", "PROVISIONING_PROFILE", "push", "appleTeamId", "quotedAppleTeamId", "ensureQuotes", "CODE_SIGN_IDENTITY", "CODE_SIGN_STYLE", "Object", "entries", "getProjectSection", "isNotComment", "attributes", "TargetAttributes", "DevelopmentTeam", "ProvisioningStyle", "fs", "writeFileSync", "filepath", "writeSync", "value", "match"], "mappings": "AAAA;;;;;;;;;;;IAYgBA,4BAA4B,MAA5BA,4BAA4B;IAgD5BC,yCAAyC,MAAzCA,yCAAyC;IAgDzCC,gCAAgC,MAAhCA,gCAAgC;;;yBA5GR,sBAAsB;;;;;;;8DAC/C,IAAI;;;;;;;;;;;AAWZ,SAASF,4BAA4B,CAACG,WAAmB,EAAmB;IACjF,MAAMC,OAAO,GAAGC,cAAS,EAAA,UAAA,CAACC,UAAU,CAACC,UAAU,CAACJ,WAAW,CAAC,AAAC;IAC7D,MAAMK,OAAO,GAAGH,cAAS,EAAA,UAAA,CAACI,MAAM,CAACC,mBAAmB,CAACN,OAAO,CAAC,AAAC;IAE9D,MAAMO,WAAW,GAAoB,EAAE,AAAC;IACxC,KAAK,MAAM,CAACC,cAAc,EAAEC,YAAY,CAAC,IAAIL,OAAO,CAAE;QACpD,MAAMM,gBAAgB,GAAa,EAAE,AAAC;QACtC,MAAMC,oBAAoB,GAAa,EAAE,AAAC;QAE1CV,cAAS,EAAA,UAAA,CAACC,UAAU,CAACU,+BAA+B,CAClDZ,OAAO,EACPS,YAAY,CAACI,sBAAsB,CACpC,CACEC,MAAM,CACL,CAAC,GAAGC,IAAI,CAAiD,GACvDA,IAAI,CAACC,aAAa,CAACC,YAAY,CAClC,CACAC,OAAO,CAAC,CAAC,GAAGH,IAAI,CAAiD,GAAK;YACrE,MAAM,EAAEI,gBAAgB,CAAA,EAAEC,oBAAoB,CAAA,EAAE,GAAGL,IAAI,CAACC,aAAa,AAAC;YACtE,IACE,OAAOG,gBAAgB,KAAK,QAAQ,IACpC,uEAAuE;YACvE,CAAC,CAACA,gBAAgB,IAClB,0EAA0E;YAC1EA,gBAAgB,KAAK,IAAI,EACzB;gBACAT,gBAAgB,CAACW,IAAI,CAACF,gBAAgB,CAAC,CAAC;YAC1C,CAAC;YACD,IAAI,OAAOC,oBAAoB,KAAK,QAAQ,IAAI,CAAC,CAACA,oBAAoB,EAAE;gBACtET,oBAAoB,CAACU,IAAI,CAACD,oBAAoB,CAAC,CAAC;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;QACLb,WAAW,CAACC,cAAc,CAAC,GAAG;YAC5BE,gBAAgB;YAChBC,oBAAoB;SACrB,CAAC;IACJ,CAAC;IAED,OAAOJ,WAAW,CAAC;AACrB,CAAC;AASM,SAASV,yCAAyC,CAAC,EACxDG,OAAO,CAAA,EACPsB,WAAW,CAAA,EAIZ,EAAgB;IACf,MAAMlB,OAAO,GAAGH,cAAS,EAAA,UAAA,CAACI,MAAM,CAACC,mBAAmB,CAACN,OAAO,CAAC,AAAC;IAE9D,MAAMuB,iBAAiB,GAAGC,YAAY,CAACF,WAAW,CAAC,AAAC;IAEpD,KAAK,MAAM,CAACd,cAAc,EAAEC,YAAY,CAAC,IAAIL,OAAO,CAAE;QACpDH,cAAS,EAAA,UAAA,CAACC,UAAU,CAACU,+BAA+B,CAClDZ,OAAO,EACPS,YAAY,CAACI,sBAAsB,CACpC,CACEC,MAAM,CACL,CAAC,GAAGC,IAAI,CAAiD,GACvDA,IAAI,CAACC,aAAa,CAACC,YAAY,CAClC,CACAC,OAAO,CAAC,CAAC,GAAGH,IAAI,CAAiD,GAAK;YACrEA,IAAI,CAACC,aAAa,CAACG,gBAAgB,GAAGI,iBAAiB,CAAC;YACxDR,IAAI,CAACC,aAAa,CAACS,kBAAkB,GAAG,qBAAqB,CAAC;YAC9DV,IAAI,CAACC,aAAa,CAACU,eAAe,GAAG,WAAW,CAAC;QACnD,CAAC,CAAC,CAAC;QAELC,MAAM,CAACC,OAAO,CAAC3B,cAAS,EAAA,UAAA,CAACC,UAAU,CAAC2B,iBAAiB,CAAC7B,OAAO,CAAC,CAAC,CAC5Dc,MAAM,CAACb,cAAS,EAAA,UAAA,CAACC,UAAU,CAAC4B,YAAY,CAAC,CACzCZ,OAAO,CAAC,CAAC,GAAGH,IAAI,CAA2C,GAAK;YAC/D,IAAI,CAACA,IAAI,CAACgB,UAAU,CAACC,gBAAgB,EAAE;gBACrCjB,IAAI,CAACgB,UAAU,CAACC,gBAAgB,GAAG,EAAE,CAAC;YACxC,CAAC;YAED,IAAI,CAACjB,IAAI,CAACgB,UAAU,CAACC,gBAAgB,CAACxB,cAAc,CAAC,EAAE;gBACrDO,IAAI,CAACgB,UAAU,CAACC,gBAAgB,CAACxB,cAAc,CAAC,GAAG,EAAE,CAAC;YACxD,CAAC;YAEDO,IAAI,CAACgB,UAAU,CAACC,gBAAgB,CAACxB,cAAc,CAAC,CAACyB,eAAe,GAAGV,iBAAiB,CAAC;YACrFR,IAAI,CAACgB,UAAU,CAACC,gBAAgB,CAACxB,cAAc,CAAC,CAAC0B,iBAAiB,GAAG,WAAW,CAAC;QACnF,CAAC,CAAC,CAAC;IACP,CAAC;IAED,OAAOlC,OAAO,CAAC;AACjB,CAAC;AAKM,SAASF,gCAAgC,CAC9CC,WAAmB,EACnB,EAAEuB,WAAW,CAAA,EAA2B,EAClC;IACN,MAAMtB,OAAO,GAAGC,cAAS,EAAA,UAAA,CAACC,UAAU,CAACC,UAAU,CAACJ,WAAW,CAAC,AAAC;IAC7DF,yCAAyC,CAAC;QAAEG,OAAO;QAAEsB,WAAW;KAAE,CAAC,CAAC;IAEpEa,GAAE,EAAA,QAAA,CAACC,aAAa,CAACpC,OAAO,CAACqC,QAAQ,EAAErC,OAAO,CAACsC,SAAS,EAAE,CAAC,CAAC;AAC1D,CAAC;AAED,MAAMd,YAAY,GAAG,CAACe,KAAa,GAAK;IACtC,IAAI,CAACA,KAAK,CAACC,KAAK,SAAS,EAAE;QACzB,OAAO,CAAC,CAAC,EAAED,KAAK,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC;IACD,OAAOA,KAAK,CAAC;AACf,CAAC,AAAC"}