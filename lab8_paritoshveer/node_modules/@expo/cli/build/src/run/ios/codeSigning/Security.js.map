{"version": 3, "sources": ["../../../../../src/run/ios/codeSigning/Security.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport forge from 'node-forge';\n\nimport { SecurityBinPrerequisite } from '../../../start/doctor/SecurityBinPrerequisite';\nimport { CommandError } from '../../../utils/errors';\n\nexport type CertificateSigningInfo = {\n  /**\n   * @example 'AA00AABB0A'\n   */\n  signingCertificateId: string;\n  /**\n   * @example 'Apple Development: Evan Bacon (AA00AABB0A)'\n   */\n  codeSigningInfo?: string;\n  /**\n   * @example '650 Industries, Inc.'\n   */\n  appleTeamName?: string;\n  /**\n   * @example 'A1BCDEF234'\n   */\n  appleTeamId?: string;\n};\n\nexport async function getSecurityPemAsync(id: string) {\n  const pem = (await spawnAsync('security', ['find-certificate', '-c', id, '-p'])).stdout?.trim?.();\n  if (!pem) {\n    throw new CommandError(`Failed to get PEM certificate for ID \"${id}\" using the 'security' bin`);\n  }\n  return pem;\n}\n\nexport async function getCertificateForSigningIdAsync(id: string): Promise<forge.pki.Certificate> {\n  const pem = await getSecurityPemAsync(id);\n  return forge.pki.certificateFromPem(pem);\n}\n\n/**\n * Get the signing identities from the security bin. Return a list of parsed values with duplicates removed.\n * @returns A list like ['Apple Development: <EMAIL> (BB00AABB0A)', 'Apple Developer: Evan Bacon (AA00AABB0A)']\n */\nexport async function findIdentitiesAsync(): Promise<string[]> {\n  await SecurityBinPrerequisite.instance.assertAsync();\n\n  const results = (\n    await spawnAsync('security', ['find-identity', '-p', 'codesigning', '-v'])\n  ).stdout.trim?.();\n  // Returns a string like:\n  // 1) 12222234253761286351826735HGKDHAJGF45283 \"Apple Development: Evan Bacon (AA00AABB0A)\" (CSSMERR_TP_CERT_REVOKED)\n  // 2) 12312234253761286351826735HGKDHAJGF45283 \"Apple Development: <EMAIL> (BB00AABB0A)\"\n  // 3) 12442234253761286351826735HGKDHAJGF45283 \"iPhone Distribution: Evan Bacon (CC00AABB0B)\" (CSSMERR_TP_CERT_REVOKED)\n  // 4) 15672234253761286351826735HGKDHAJGF45283 \"Apple Development: Evan Bacon (AA00AABB0A)\"\n  //  4 valid identities found\n\n  const parsed = results\n    .split('\\n')\n    .map((line) => extractCodeSigningInfo(line))\n    .filter(Boolean) as string[];\n\n  // Remove duplicates\n  return [...new Set(parsed)];\n}\n\n/**\n * @param value '  2) 12312234253761286351826735HGKDHAJGF45283 \"Apple Development: <EMAIL> (BB00AABB0A)\"'\n * @returns 'Apple Development: Evan Bacon (PH75MDXG4H)'\n */\nexport function extractCodeSigningInfo(value: string): string | null {\n  return value.match(/^\\s*\\d+\\).+\"(.+Develop(ment|er).+)\"$/)?.[1] ?? null;\n}\n\nexport async function resolveIdentitiesAsync(\n  identities: string[]\n): Promise<CertificateSigningInfo[]> {\n  const values = identities.map(extractSigningId).filter(Boolean) as string[];\n  return Promise.all(values.map(resolveCertificateSigningInfoAsync));\n}\n\n/**\n * @param signingCertificateId 'AA00AABB0A'\n */\nexport async function resolveCertificateSigningInfoAsync(\n  signingCertificateId: string\n): Promise<CertificateSigningInfo> {\n  const certificate = await getCertificateForSigningIdAsync(signingCertificateId);\n  return {\n    signingCertificateId,\n    codeSigningInfo: certificate.subject.getField('CN')?.value,\n    appleTeamName: certificate.subject.getField('O')?.value,\n    appleTeamId: certificate.subject.getField('OU')?.value,\n  };\n}\n\n/**\n * @param codeSigningInfo 'Apple Development: Evan Bacon (AA00AABB0A)'\n * @returns 'AA00AABB0A'\n */\nexport function extractSigningId(codeSigningInfo: string): string | null {\n  return codeSigningInfo.match(/.*\\(([a-zA-Z0-9]+)\\)/)?.[1] ?? null;\n}\n"], "names": ["getSecurityPemAsync", "getCertificateForSigningIdAsync", "findIdentitiesAsync", "extractCodeSigningInfo", "resolveIdentitiesAsync", "resolveCertificateSigningInfoAsync", "extractSigningId", "id", "pem", "spawnAsync", "stdout", "trim", "CommandError", "forge", "pki", "certificateFromPem", "SecurityBinPrerequisite", "instance", "assertAsync", "results", "parsed", "split", "map", "line", "filter", "Boolean", "Set", "value", "match", "identities", "values", "Promise", "all", "signingCertificateId", "certificate", "codeSigningInfo", "subject", "getField", "appleTeamName", "appleTeamId"], "mappings": "AAAA;;;;;;;;;;;IAyBsBA,mBAAmB,MAAnBA,mBAAmB;IAQnBC,+BAA+B,MAA/BA,+BAA+B;IAS/BC,mBAAmB,MAAnBA,mBAAmB;IA0BzBC,sBAAsB,MAAtBA,sBAAsB;IAIhBC,sBAAsB,MAAtBA,sBAAsB;IAUtBC,kCAAkC,MAAlCA,kCAAkC;IAgBxCC,gBAAgB,MAAhBA,gBAAgB;;;8DAlGT,mBAAmB;;;;;;;8DACxB,YAAY;;;;;;yCAEU,+CAA+C;wBAC1D,uBAAuB;;;;;;AAqB7C,eAAeN,mBAAmB,CAACO,EAAU,EAAE;QACxC,GAA2E;IAAvF,MAAMC,GAAG,GAAG,CAAA,GAA2E,GAA3E,CAAC,MAAMC,IAAAA,WAAU,EAAA,QAAA,EAAC,UAAU,EAAE;QAAC,kBAAkB;QAAE,IAAI;QAAEF,EAAE;QAAE,IAAI;KAAC,CAAC,CAAC,CAACG,MAAM,SAAM,GAAjF,KAAA,CAAiF,GAAjF,GAA2E,CAAEC,IAAI,QAAI,GAArF,KAAA,CAAqF,GAArF,GAA2E,CAAEA,IAAI,EAAI,AAAC;IAClG,IAAI,CAACH,GAAG,EAAE;QACR,MAAM,IAAII,OAAY,aAAA,CAAC,CAAC,sCAAsC,EAAEL,EAAE,CAAC,0BAA0B,CAAC,CAAC,CAAC;IAClG,CAAC;IACD,OAAOC,GAAG,CAAC;AACb,CAAC;AAEM,eAAeP,+BAA+B,CAACM,EAAU,EAAkC;IAChG,MAAMC,GAAG,GAAG,MAAMR,mBAAmB,CAACO,EAAE,CAAC,AAAC;IAC1C,OAAOM,UAAK,EAAA,QAAA,CAACC,GAAG,CAACC,kBAAkB,CAACP,GAAG,CAAC,CAAC;AAC3C,CAAC;AAMM,eAAeN,mBAAmB,GAAsB;QAG7C,OAER,AAAK,EAFG,GAEH;IAJb,MAAMc,wBAAuB,wBAAA,CAACC,QAAQ,CAACC,WAAW,EAAE,CAAC;IAErD,MAAMC,OAAO,GAAG,CAAA,GAEH,GAFG,CAAA,OAER,GAFQ,CACd,MAAMV,IAAAA,WAAU,EAAA,QAAA,EAAC,UAAU,EAAE;QAAC,eAAe;QAAE,IAAI;QAAE,aAAa;QAAE,IAAI;KAAC,CAAC,CAC3E,CAACC,MAAM,EAACC,IAAI,SAAI,GAFD,KAAA,CAEC,GAFD,GAEH,CAFG,IAEC,CAFD,OAER,CAAS,AAAC;IAClB,yBAAyB;IACzB,qHAAqH;IACrH,8FAA8F;IAC9F,uHAAuH;IACvH,2FAA2F;IAC3F,4BAA4B;IAE5B,MAAMS,MAAM,GAAGD,OAAO,CACnBE,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAAC,CAACC,IAAI,GAAKpB,sBAAsB,CAACoB,IAAI,CAAC,CAAC,CAC3CC,MAAM,CAACC,OAAO,CAAC,AAAY,AAAC;IAE/B,oBAAoB;IACpB,OAAO;WAAI,IAAIC,GAAG,CAACN,MAAM,CAAC;KAAC,CAAC;AAC9B,CAAC;AAMM,SAASjB,sBAAsB,CAACwB,KAAa,EAAiB;QAC5DA,GAAmD;QAAnDA,IAAwD;IAA/D,OAAOA,CAAAA,IAAwD,GAAxDA,CAAAA,GAAmD,GAAnDA,KAAK,CAACC,KAAK,wCAAwC,SAAK,GAAxDD,KAAAA,CAAwD,GAAxDA,GAAmD,AAAE,CAAC,CAAC,CAAC,YAAxDA,IAAwD,GAAI,IAAI,CAAC;AAC1E,CAAC;AAEM,eAAevB,sBAAsB,CAC1CyB,UAAoB,EACe;IACnC,MAAMC,MAAM,GAAGD,UAAU,CAACP,GAAG,CAAChB,gBAAgB,CAAC,CAACkB,MAAM,CAACC,OAAO,CAAC,AAAY,AAAC;IAC5E,OAAOM,OAAO,CAACC,GAAG,CAACF,MAAM,CAACR,GAAG,CAACjB,kCAAkC,CAAC,CAAC,CAAC;AACrE,CAAC;AAKM,eAAeA,kCAAkC,CACtD4B,oBAA4B,EACK;QAIdC,GAAkC,EACpCA,IAAiC,EACnCA,IAAkC;IALjD,MAAMA,WAAW,GAAG,MAAMjC,+BAA+B,CAACgC,oBAAoB,CAAC,AAAC;IAChF,OAAO;QACLA,oBAAoB;QACpBE,eAAe,EAAED,CAAAA,GAAkC,GAAlCA,WAAW,CAACE,OAAO,CAACC,QAAQ,CAAC,IAAI,CAAC,SAAO,GAAzCH,KAAAA,CAAyC,GAAzCA,GAAkC,CAAEP,KAAK;QAC1DW,aAAa,EAAEJ,CAAAA,IAAiC,GAAjCA,WAAW,CAACE,OAAO,CAACC,QAAQ,CAAC,GAAG,CAAC,SAAO,GAAxCH,KAAAA,CAAwC,GAAxCA,IAAiC,CAAEP,KAAK;QACvDY,WAAW,EAAEL,CAAAA,IAAkC,GAAlCA,WAAW,CAACE,OAAO,CAACC,QAAQ,CAAC,IAAI,CAAC,SAAO,GAAzCH,KAAAA,CAAyC,GAAzCA,IAAkC,CAAEP,KAAK;KACvD,CAAC;AACJ,CAAC;AAMM,SAASrB,gBAAgB,CAAC6B,eAAuB,EAAiB;QAChEA,GAA6C;QAA7CA,IAAkD;IAAzD,OAAOA,CAAAA,IAAkD,GAAlDA,CAAAA,GAA6C,GAA7CA,eAAe,CAACP,KAAK,wBAAwB,SAAK,GAAlDO,KAAAA,CAAkD,GAAlDA,GAA6C,AAAE,CAAC,CAAC,CAAC,YAAlDA,IAAkD,GAAI,IAAI,CAAC;AACpE,CAAC"}