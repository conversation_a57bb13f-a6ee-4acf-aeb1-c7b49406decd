{"version": 3, "sources": ["../../../../../src/run/ios/options/promptDevice.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport * as SimControl from '../../../start/platforms/ios/simctl';\nimport prompt from '../../../utils/prompts';\nimport { ConnectedDevice } from '../appleDevice/AppleDevice';\n\ntype AnyDevice = SimControl.Device | ConnectedDevice;\n\nfunction isConnectedDevice(item: AnyDevice): item is ConnectedDevice {\n  return 'deviceType' in item;\n}\n\nfunction isSimControlDevice(item: AnyDevice): item is SimControl.Device {\n  return 'state' in item;\n}\n\n/** Format a device for the prompt list. Exposed for testing. */\nexport function formatDeviceChoice(item: AnyDevice): { title: string; value: string } {\n  const isConnected = isConnectedDevice(item) && item.deviceType === 'device';\n  const isActive = isSimControlDevice(item) && item.state === 'Booted';\n  const symbol =\n    item.osType === 'macOS'\n      ? '🖥️  '\n      : isConnected\n        ? item.connectionType === 'Network'\n          ? '🌐 '\n          : '🔌 '\n        : '';\n  const format = isActive ? chalk.bold : (text: string) => text;\n  return {\n    title: `${symbol}${format(item.name)}${\n      item.osVersion ? chalk.dim(` (${item.osVersion})`) : ''\n    }`,\n    value: item.udid,\n  };\n}\n\n/** Prompt to select a device from a searchable list of devices. */\nexport async function promptDeviceAsync(devices: AnyDevice[]): Promise<AnyDevice> {\n  // --device with no props after\n  const { value } = await prompt({\n    type: 'autocomplete',\n    name: 'value',\n    limit: 11,\n    message: 'Select a device',\n    choices: devices.map((item) => formatDeviceChoice(item)),\n    suggest: (input: any, choices: any) => {\n      const regex = new RegExp(input, 'i');\n      return choices.filter((choice: any) => regex.test(choice.title));\n    },\n  });\n  return devices.find((device) => device.udid === value)!;\n}\n"], "names": ["formatDeviceChoice", "promptDeviceAsync", "isConnectedDevice", "item", "isSimControlDevice", "isConnected", "deviceType", "isActive", "state", "symbol", "osType", "connectionType", "format", "chalk", "bold", "text", "title", "name", "osVersion", "dim", "value", "udid", "devices", "prompt", "type", "limit", "message", "choices", "map", "suggest", "input", "regex", "RegExp", "filter", "choice", "test", "find", "device"], "mappings": "AAAA;;;;;;;;;;;IAiBgBA,kBAAkB,MAAlBA,kBAAkB;IAqBZC,iBAAiB,MAAjBA,iBAAiB;;;8DAtCrB,OAAO;;;;;;8DAGN,wBAAwB;;;;;;AAK3C,SAASC,iBAAiB,CAACC,IAAe,EAA2B;IACnE,OAAO,YAAY,IAAIA,IAAI,CAAC;AAC9B,CAAC;AAED,SAASC,kBAAkB,CAACD,IAAe,EAA6B;IACtE,OAAO,OAAO,IAAIA,IAAI,CAAC;AACzB,CAAC;AAGM,SAASH,kBAAkB,CAACG,IAAe,EAAoC;IACpF,MAAME,WAAW,GAAGH,iBAAiB,CAACC,IAAI,CAAC,IAAIA,IAAI,CAACG,UAAU,KAAK,QAAQ,AAAC;IAC5E,MAAMC,QAAQ,GAAGH,kBAAkB,CAACD,IAAI,CAAC,IAAIA,IAAI,CAACK,KAAK,KAAK,QAAQ,AAAC;IACrE,MAAMC,MAAM,GACVN,IAAI,CAACO,MAAM,KAAK,OAAO,GACnB,iBAAM,GACNL,WAAW,GACTF,IAAI,CAACQ,cAAc,KAAK,SAAS,GAC/B,eAAI,GACJ,eAAI,GACN,EAAE,AAAC;IACX,MAAMC,MAAM,GAAGL,QAAQ,GAAGM,MAAK,EAAA,QAAA,CAACC,IAAI,GAAG,CAACC,IAAY,GAAKA,IAAI,AAAC;IAC9D,OAAO;QACLC,KAAK,EAAE,CAAC,EAAEP,MAAM,CAAC,EAAEG,MAAM,CAACT,IAAI,CAACc,IAAI,CAAC,CAAC,EACnCd,IAAI,CAACe,SAAS,GAAGL,MAAK,EAAA,QAAA,CAACM,GAAG,CAAC,CAAC,EAAE,EAAEhB,IAAI,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CACxD,CAAC;QACFE,KAAK,EAAEjB,IAAI,CAACkB,IAAI;KACjB,CAAC;AACJ,CAAC;AAGM,eAAepB,iBAAiB,CAACqB,OAAoB,EAAsB;IAChF,+BAA+B;IAC/B,MAAM,EAAEF,KAAK,CAAA,EAAE,GAAG,MAAMG,IAAAA,QAAM,QAAA,EAAC;QAC7BC,IAAI,EAAE,cAAc;QACpBP,IAAI,EAAE,OAAO;QACbQ,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,iBAAiB;QAC1BC,OAAO,EAAEL,OAAO,CAACM,GAAG,CAAC,CAACzB,IAAI,GAAKH,kBAAkB,CAACG,IAAI,CAAC,CAAC;QACxD0B,OAAO,EAAE,CAACC,KAAU,EAAEH,OAAY,GAAK;YACrC,MAAMI,KAAK,GAAG,IAAIC,MAAM,CAACF,KAAK,EAAE,GAAG,CAAC,AAAC;YACrC,OAAOH,OAAO,CAACM,MAAM,CAAC,CAACC,MAAW,GAAKH,KAAK,CAACI,IAAI,CAACD,MAAM,CAAClB,KAAK,CAAC,CAAC,CAAC;QACnE,CAAC;KACF,CAAC,AAAC;IACH,OAAOM,OAAO,CAACc,IAAI,CAAC,CAACC,MAAM,GAAKA,MAAM,CAAChB,IAAI,KAAKD,KAAK,CAAC,CAAE;AAC1D,CAAC"}