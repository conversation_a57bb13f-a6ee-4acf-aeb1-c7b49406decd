{"version": 3, "sources": ["../../../../../src/run/ios/options/resolveOptions.ts"], "sourcesContent": ["import { isSimulatorDevice, resolveDeviceAsync } from './resolveDevice';\nimport { resolveNativeSchemePropsAsync } from './resolveNativeScheme';\nimport { resolveXcodeProject } from './resolveXcodeProject';\nimport { isOSType } from '../../../start/platforms/ios/simctl';\nimport { resolveBundlerPropsAsync } from '../../resolveBundlerProps';\nimport { BuildProps, Options } from '../XcodeBuild.types';\n\n/** Resolve arguments for the `run:ios` command. */\nexport async function resolveOptionsAsync(\n  projectRoot: string,\n  options: Options\n): Promise<BuildProps> {\n  const xcodeProject = resolveXcodeProject(projectRoot);\n\n  const bundlerProps = await resolveBundlerPropsAsync(projectRoot, options);\n\n  // Resolve the scheme before the device so we can filter devices based on\n  // whichever scheme is selected (i.e. don't present TV devices if the scheme cannot be run on a TV).\n  const { osType, name: scheme } = await resolveNativeSchemePropsAsync(\n    projectRoot,\n    options,\n    xcodeProject\n  );\n\n  // Use the configuration or `Debug` if none is provided.\n  const configuration = options.configuration || 'Debug';\n\n  // Resolve the device based on the provided device id or prompt\n  // from a list of devices (connected or simulated) that are filtered by the scheme.\n  const device = await resolveDeviceAsync(options.device, {\n    // It's unclear if there's any value to asserting that we haven't hardcoded the os type in the CLI.\n    osType: isOSType(osType) ? osType : undefined,\n    xcodeProject,\n    scheme,\n    configuration,\n  });\n\n  const isSimulator = isSimulatorDevice(device);\n\n  // This optimization skips resetting the Metro cache needlessly.\n  // The cache is reset in `../node_modules/react-native/scripts/react-native-xcode.sh` when the\n  // project is running in Debug and built onto a physical device. It seems that this is done because\n  // the script is run from Xcode and unaware of the CLI instance.\n  const shouldSkipInitialBundling = configuration === 'Debug' && !isSimulator;\n\n  return {\n    ...bundlerProps,\n    projectRoot,\n    isSimulator,\n    xcodeProject,\n    device,\n    configuration,\n    shouldSkipInitialBundling,\n    buildCache: options.buildCache !== false,\n    scheme,\n  };\n}\n"], "names": ["resolveOptionsAsync", "projectRoot", "options", "xcodeProject", "resolveXcodeProject", "bundlerProps", "resolveBundlerPropsAsync", "osType", "name", "scheme", "resolveNativeSchemePropsAsync", "configuration", "device", "resolveDeviceAsync", "isOSType", "undefined", "isSimulator", "isSimulatorDevice", "shouldSkipInitialBundling", "buildCache"], "mappings": "AAAA;;;;+BAQs<PERSON>,qBAAmB;;aAAnBA,mBAAmB;;+BARa,iBAAiB;qCACzB,uBAAuB;qCACjC,uBAAuB;wBAClC,qCAAqC;qCACrB,2BAA2B;AAI7D,eAAeA,mBAAmB,CACvCC,WAAmB,EACnBC,OAAgB,EACK;IACrB,MAAMC,YAAY,GAAGC,IAAAA,oBAAmB,oBAAA,EAACH,WAAW,CAAC,AAAC;IAEtD,MAAMI,YAAY,GAAG,MAAMC,IAAAA,oBAAwB,yBAAA,EAACL,WAAW,EAAEC,OAAO,CAAC,AAAC;IAE1E,yEAAyE;IACzE,oGAAoG;IACpG,MAAM,EAAEK,MAAM,CAAA,EAAEC,IAAI,EAAEC,MAAM,CAAA,EAAE,GAAG,MAAMC,IAAAA,oBAA6B,8BAAA,EAClET,WAAW,EACXC,OAAO,EACPC,YAAY,CACb,AAAC;IAEF,wDAAwD;IACxD,MAAMQ,aAAa,GAAGT,OAAO,CAACS,aAAa,IAAI,OAAO,AAAC;IAEvD,+DAA+D;IAC/D,mFAAmF;IACnF,MAAMC,MAAM,GAAG,MAAMC,IAAAA,cAAkB,mBAAA,EAACX,OAAO,CAACU,MAAM,EAAE;QACtD,mGAAmG;QACnGL,MAAM,EAAEO,IAAAA,OAAQ,SAAA,EAACP,MAAM,CAAC,GAAGA,MAAM,GAAGQ,SAAS;QAC7CZ,YAAY;QACZM,MAAM;QACNE,aAAa;KACd,CAAC,AAAC;IAEH,MAAMK,WAAW,GAAGC,IAAAA,cAAiB,kBAAA,EAACL,MAAM,CAAC,AAAC;IAE9C,gEAAgE;IAChE,8FAA8F;IAC9F,mGAAmG;IACnG,gEAAgE;IAChE,MAAMM,yBAAyB,GAAGP,aAAa,KAAK,OAAO,IAAI,CAACK,WAAW,AAAC;IAE5E,OAAO;QACL,GAAGX,YAAY;QACfJ,WAAW;QACXe,WAAW;QACXb,YAAY;QACZS,MAAM;QACND,aAAa;QACbO,yBAAyB;QACzBC,UAAU,EAAEjB,OAAO,CAACiB,UAAU,KAAK,KAAK;QACxCV,MAAM;KACP,CAAC;AACJ,CAAC"}