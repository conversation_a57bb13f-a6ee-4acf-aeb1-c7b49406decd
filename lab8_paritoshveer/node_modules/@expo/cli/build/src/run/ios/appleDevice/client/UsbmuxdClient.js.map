{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/client/UsbmuxdClient.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport plist from '@expo/plist';\nimport Debug from 'debug';\nimport { Socket, connect } from 'net';\n\nimport { ResponseError, ServiceClient } from './ServiceClient';\nimport { CommandError } from '../../../../utils/errors';\nimport { parsePlistBuffer } from '../../../../utils/plist';\nimport { UsbmuxProtocolClient } from '../protocol/UsbmuxProtocol';\n\nconst debug = Debug('expo:apple-device:client:usbmuxd');\n\nexport interface UsbmuxdDeviceProperties {\n  /** @example 'USB' */\n  ConnectionType: 'USB' | 'Network';\n  /** @example 7 */\n  DeviceID: number;\n  /** @example 339738624 */\n  LocationID?: number;\n  /** @example '00008101-001964A22629003A' */\n  SerialNumber: string;\n  /**\n   * Only available for USB connection.\n   * @example 480000000\n   */\n  ConnectionSpeed?: number;\n  /**\n   * Only available for USB connection.\n   * @example 4776\n   */\n  ProductID?: number;\n  /**\n   * Only available for USB connection.\n   * @example '00008101-001964A22629003A'\n   */\n  UDID?: string;\n  /**\n   * Only available for USB connection.\n   * @example '00008101001964A22629003A'\n   */\n  USBSerialNumber?: string;\n  /**\n   * Only available for Network connection.\n   * @example '08:c7:29:05:f2:30@fe80::ac7:29ff:fe05:f230-supportsRP._apple-mobdev2._tcp.local.'\n   */\n  EscapedFullServiceName?: string;\n  /**\n   * Only available for Network connection.\n   * @example 5\n   */\n  InterfaceIndex?: number;\n  /**\n   * Only available for Network connection.\n   */\n  NetworkAddress?: Buffer;\n}\n\nexport interface UsbmuxdDevice {\n  /** @example 7 */\n  DeviceID: number;\n  MessageType: 'Attached'; // TODO: what else?\n  Properties: UsbmuxdDeviceProperties;\n}\n\nexport interface UsbmuxdConnectResponse {\n  MessageType: 'Result';\n  Number: number;\n}\n\nexport interface UsbmuxdDeviceResponse {\n  DeviceList: UsbmuxdDevice[];\n}\n\nexport interface UsbmuxdPairRecordResponse {\n  PairRecordData: Buffer;\n}\n\nexport interface UsbmuxdPairRecord {\n  DeviceCertificate: Buffer;\n  EscrowBag: Buffer;\n  HostCertificate: Buffer;\n  HostID: string;\n  HostPrivateKey: Buffer;\n  RootCertificate: Buffer;\n  RootPrivateKey: Buffer;\n  SystemBUID: string;\n  WiFiMACAddress: string;\n}\n\nfunction isUsbmuxdConnectResponse(resp: any): resp is UsbmuxdConnectResponse {\n  return resp.MessageType === 'Result' && resp.Number !== undefined;\n}\n\nfunction isUsbmuxdDeviceResponse(resp: any): resp is UsbmuxdDeviceResponse {\n  return resp.DeviceList !== undefined;\n}\n\nfunction isUsbmuxdPairRecordResponse(resp: any): resp is UsbmuxdPairRecordResponse {\n  return resp.PairRecordData !== undefined;\n}\n\nexport class UsbmuxdClient extends ServiceClient<UsbmuxProtocolClient> {\n  constructor(public socket: Socket) {\n    super(socket, new UsbmuxProtocolClient(socket));\n  }\n\n  static connectUsbmuxdSocket(): Socket {\n    debug('connectUsbmuxdSocket');\n    if (process.platform === 'win32') {\n      return connect({ port: 27015, host: 'localhost' });\n    } else {\n      return connect({ path: '/var/run/usbmuxd' });\n    }\n  }\n\n  async connect(device: Pick<UsbmuxdDevice, 'DeviceID'>, port: number): Promise<Socket> {\n    debug(`connect: ${device.DeviceID} on port ${port}`);\n    debug(`connect:device: %O`, device);\n\n    const response = await this.protocolClient.sendMessage({\n      messageType: 'Connect',\n      extraFields: {\n        DeviceID: device.DeviceID,\n        PortNumber: htons(port),\n      },\n    });\n    debug(`connect:device:response: %O`, response);\n\n    if (isUsbmuxdConnectResponse(response) && response.Number === 0) {\n      return this.protocolClient.socket;\n    } else {\n      throw new ResponseError(\n        `There was an error connecting to the USB connected device (id: ${device.DeviceID}, port: ${port})`,\n        response\n      );\n    }\n  }\n\n  async getDevices(): Promise<UsbmuxdDevice[]> {\n    debug('getDevices');\n\n    const resp = await this.protocolClient.sendMessage({\n      messageType: 'ListDevices',\n    });\n\n    if (isUsbmuxdDeviceResponse(resp)) {\n      return resp.DeviceList;\n    } else {\n      throw new ResponseError('Invalid response from getDevices', resp);\n    }\n  }\n\n  async getDevice(udid?: string): Promise<UsbmuxdDevice> {\n    debug(`getDevice ${udid ? 'udid: ' + udid : ''}`);\n    const devices = await this.getDevices();\n\n    if (!devices.length) {\n      throw new CommandError('APPLE_DEVICE_USBMUXD', 'No devices found');\n    }\n\n    if (!udid) {\n      return devices[0];\n    }\n\n    for (const device of devices) {\n      if (device.Properties && device.Properties.SerialNumber === udid) {\n        return device;\n      }\n    }\n\n    throw new CommandError('APPLE_DEVICE_USBMUXD', `No device found (udid: ${udid})`);\n  }\n\n  async readPairRecord(udid: string): Promise<UsbmuxdPairRecord> {\n    debug(`readPairRecord: ${udid}`);\n\n    const resp = await this.protocolClient.sendMessage({\n      messageType: 'ReadPairRecord',\n      extraFields: { PairRecordID: udid },\n    });\n\n    if (isUsbmuxdPairRecordResponse(resp)) {\n      // the pair record can be created as a binary plist\n      const BPLIST_MAGIC = Buffer.from('bplist00');\n      if (BPLIST_MAGIC.compare(resp.PairRecordData, 0, 8) === 0) {\n        debug('Binary plist pair record detected.');\n        return parsePlistBuffer(resp.PairRecordData)[0];\n      } else {\n        // TODO: use parsePlistBuffer\n        return plist.parse(resp.PairRecordData.toString()) as any; // TODO: type guard\n      }\n    } else {\n      throw new ResponseError(\n        `There was an error reading pair record for device (udid: ${udid})`,\n        resp\n      );\n    }\n  }\n}\n\nfunction htons(n: number): number {\n  return ((n & 0xff) << 8) | ((n >> 8) & 0xff);\n}\n"], "names": ["UsbmuxdClient", "debug", "Debug", "isUsbmuxdConnectResponse", "resp", "MessageType", "Number", "undefined", "isUsbmuxdDeviceResponse", "DeviceList", "isUsbmuxdPairRecordResponse", "PairRecordData", "ServiceClient", "constructor", "socket", "UsbmuxProtocolClient", "connectUsbmuxdSocket", "process", "platform", "connect", "port", "host", "path", "device", "DeviceID", "response", "protocolClient", "sendMessage", "messageType", "extraFields", "PortNumber", "htons", "ResponseError", "getDevices", "getDevice", "udid", "devices", "length", "CommandError", "Properties", "SerialNumber", "readPairRecord", "PairRecordID", "BPLIST_MAGIC", "<PERSON><PERSON><PERSON>", "from", "compare", "parsePlist<PERSON><PERSON><PERSON>", "plist", "parse", "toString", "n"], "mappings": "AAAA;;;;;;CAMC,GACD;;;;+BAoGaA,eAAa;;aAAbA,aAAa;;;8DApGR,aAAa;;;;;;;8DACb,OAAO;;;;;;;yBACO,KAAK;;;;;;+BAEQ,iBAAiB;wBACjC,0BAA0B;wBACtB,yBAAyB;gCACrB,4BAA4B;;;;;;AAEjE,MAAMC,KAAK,GAAGC,IAAAA,MAAK,EAAA,QAAA,EAAC,kCAAkC,CAAC,AAAC;AA+ExD,SAASC,wBAAwB,CAACC,IAAS,EAAkC;IAC3E,OAAOA,IAAI,CAACC,WAAW,KAAK,QAAQ,IAAID,IAAI,CAACE,MAAM,KAAKC,SAAS,CAAC;AACpE,CAAC;AAED,SAASC,uBAAuB,CAACJ,IAAS,EAAiC;IACzE,OAAOA,IAAI,CAACK,UAAU,KAAKF,SAAS,CAAC;AACvC,CAAC;AAED,SAASG,2BAA2B,CAACN,IAAS,EAAqC;IACjF,OAAOA,IAAI,CAACO,cAAc,KAAKJ,SAAS,CAAC;AAC3C,CAAC;AAEM,MAAMP,aAAa,SAASY,cAAa,cAAA;IAC9CC,YAAmBC,MAAc,CAAE;QACjC,KAAK,CAACA,MAAM,EAAE,IAAIC,eAAoB,qBAAA,CAACD,MAAM,CAAC,CAAC,CAAC;QAD/BA,cAAAA,MAAc,CAAA;IAEjC;WAEOE,oBAAoB,GAAW;QACpCf,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC9B,IAAIgB,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;YAChC,OAAOC,IAAAA,IAAO,EAAA,QAAA,EAAC;gBAAEC,IAAI,EAAE,KAAK;gBAAEC,IAAI,EAAE,WAAW;aAAE,CAAC,CAAC;QACrD,OAAO;YACL,OAAOF,IAAAA,IAAO,EAAA,QAAA,EAAC;gBAAEG,IAAI,EAAE,kBAAkB;aAAE,CAAC,CAAC;QAC/C,CAAC;IACH;UAEMH,OAAO,CAACI,MAAuC,EAAEH,IAAY,EAAmB;QACpFnB,KAAK,CAAC,CAAC,SAAS,EAAEsB,MAAM,CAACC,QAAQ,CAAC,SAAS,EAAEJ,IAAI,CAAC,CAAC,CAAC,CAAC;QACrDnB,KAAK,CAAC,CAAC,kBAAkB,CAAC,EAAEsB,MAAM,CAAC,CAAC;QAEpC,MAAME,QAAQ,GAAG,MAAM,IAAI,CAACC,cAAc,CAACC,WAAW,CAAC;YACrDC,WAAW,EAAE,SAAS;YACtBC,WAAW,EAAE;gBACXL,QAAQ,EAAED,MAAM,CAACC,QAAQ;gBACzBM,UAAU,EAAEC,KAAK,CAACX,IAAI,CAAC;aACxB;SACF,CAAC,AAAC;QACHnB,KAAK,CAAC,CAAC,2BAA2B,CAAC,EAAEwB,QAAQ,CAAC,CAAC;QAE/C,IAAItB,wBAAwB,CAACsB,QAAQ,CAAC,IAAIA,QAAQ,CAACnB,MAAM,KAAK,CAAC,EAAE;YAC/D,OAAO,IAAI,CAACoB,cAAc,CAACZ,MAAM,CAAC;QACpC,OAAO;YACL,MAAM,IAAIkB,cAAa,cAAA,CACrB,CAAC,+DAA+D,EAAET,MAAM,CAACC,QAAQ,CAAC,QAAQ,EAAEJ,IAAI,CAAC,CAAC,CAAC,EACnGK,QAAQ,CACT,CAAC;QACJ,CAAC;IACH;UAEMQ,UAAU,GAA6B;QAC3ChC,KAAK,CAAC,YAAY,CAAC,CAAC;QAEpB,MAAMG,IAAI,GAAG,MAAM,IAAI,CAACsB,cAAc,CAACC,WAAW,CAAC;YACjDC,WAAW,EAAE,aAAa;SAC3B,CAAC,AAAC;QAEH,IAAIpB,uBAAuB,CAACJ,IAAI,CAAC,EAAE;YACjC,OAAOA,IAAI,CAACK,UAAU,CAAC;QACzB,OAAO;YACL,MAAM,IAAIuB,cAAa,cAAA,CAAC,kCAAkC,EAAE5B,IAAI,CAAC,CAAC;QACpE,CAAC;IACH;UAEM8B,SAAS,CAACC,IAAa,EAA0B;QACrDlC,KAAK,CAAC,CAAC,UAAU,EAAEkC,IAAI,GAAG,QAAQ,GAAGA,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAClD,MAAMC,OAAO,GAAG,MAAM,IAAI,CAACH,UAAU,EAAE,AAAC;QAExC,IAAI,CAACG,OAAO,CAACC,MAAM,EAAE;YACnB,MAAM,IAAIC,OAAY,aAAA,CAAC,sBAAsB,EAAE,kBAAkB,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAACH,IAAI,EAAE;YACT,OAAOC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QAED,KAAK,MAAMb,MAAM,IAAIa,OAAO,CAAE;YAC5B,IAAIb,MAAM,CAACgB,UAAU,IAAIhB,MAAM,CAACgB,UAAU,CAACC,YAAY,KAAKL,IAAI,EAAE;gBAChE,OAAOZ,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAED,MAAM,IAAIe,OAAY,aAAA,CAAC,sBAAsB,EAAE,CAAC,uBAAuB,EAAEH,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACpF;UAEMM,cAAc,CAACN,IAAY,EAA8B;QAC7DlC,KAAK,CAAC,CAAC,gBAAgB,EAAEkC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEjC,MAAM/B,IAAI,GAAG,MAAM,IAAI,CAACsB,cAAc,CAACC,WAAW,CAAC;YACjDC,WAAW,EAAE,gBAAgB;YAC7BC,WAAW,EAAE;gBAAEa,YAAY,EAAEP,IAAI;aAAE;SACpC,CAAC,AAAC;QAEH,IAAIzB,2BAA2B,CAACN,IAAI,CAAC,EAAE;YACrC,mDAAmD;YACnD,MAAMuC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAAC,UAAU,CAAC,AAAC;YAC7C,IAAIF,YAAY,CAACG,OAAO,CAAC1C,IAAI,CAACO,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE;gBACzDV,KAAK,CAAC,oCAAoC,CAAC,CAAC;gBAC5C,OAAO8C,IAAAA,OAAgB,iBAAA,EAAC3C,IAAI,CAACO,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,OAAO;gBACL,6BAA6B;gBAC7B,OAAOqC,MAAK,EAAA,QAAA,CAACC,KAAK,CAAC7C,IAAI,CAACO,cAAc,CAACuC,QAAQ,EAAE,CAAC,CAAQ,CAAC,mBAAmB;YAChF,CAAC;QACH,OAAO;YACL,MAAM,IAAIlB,cAAa,cAAA,CACrB,CAAC,yDAAyD,EAAEG,IAAI,CAAC,CAAC,CAAC,EACnE/B,IAAI,CACL,CAAC;QACJ,CAAC;IACH;CACD;AAED,SAAS2B,KAAK,CAACoB,CAAS,EAAU;IAChC,OAAO,AAAC,CAACA,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAK,AAACA,CAAC,IAAI,CAAC,GAAI,IAAI,AAAC,CAAC;AAC/C,CAAC"}