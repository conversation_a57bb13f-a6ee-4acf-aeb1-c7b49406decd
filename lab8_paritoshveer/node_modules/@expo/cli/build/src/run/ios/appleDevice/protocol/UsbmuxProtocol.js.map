{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/protocol/UsbmuxProtocol.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport plist from '@expo/plist';\nimport Debug from 'debug';\nimport { Socket } from 'net';\n\nimport type { ProtocolWriter } from './AbstractProtocol';\nimport { PlistProtocolReader, ProtocolClient, ProtocolReaderFactory } from './AbstractProtocol';\n\nconst debug = Debug('expo:apple-device:protocol:usbmux');\n\nexport const USBMUXD_HEADER_SIZE = 16;\n\nexport interface UsbmuxMessage {\n  messageType: string;\n  extraFields?: { [key: string]: any };\n}\n\nexport class UsbmuxProtocolClient extends ProtocolClient<UsbmuxMessage> {\n  constructor(socket: Socket) {\n    super(socket, new ProtocolReaderFactory(UsbmuxProtocolReader), new UsbmuxProtocolWriter());\n  }\n}\n\nexport class UsbmuxProtocolReader extends PlistProtocolReader {\n  constructor(callback: (data: any) => any) {\n    super(USBMUXD_HEADER_SIZE, callback);\n  }\n\n  parseHeader(data: Buffer) {\n    return data.readUInt32LE(0) - USBMUXD_HEADER_SIZE;\n  }\n\n  parseBody(data: Buffer) {\n    const resp = super.parseBody(data);\n    debug(`Response: ${JSON.stringify(resp)}`);\n    return resp;\n  }\n}\n\nexport class UsbmuxProtocolWriter implements ProtocolWriter {\n  private useTag = 0;\n\n  write(socket: Socket, msg: UsbmuxMessage) {\n    // TODO Usbmux message type\n    debug(`socket write: ${JSON.stringify(msg)}`);\n    const { messageType, extraFields } = msg;\n    const plistMessage = plist.build({\n      BundleID: 'dev.expo.native-run', // TODO\n      ClientVersionString: 'usbmux.js', // TODO\n      MessageType: messageType,\n      ProgName: 'native-run', // TODO\n      kLibUSBMuxVersion: 3,\n      ...extraFields,\n    });\n\n    const dataSize = plistMessage ? plistMessage.length : 0;\n    const protocolVersion = 1;\n    const messageCode = 8;\n\n    const header = Buffer.alloc(USBMUXD_HEADER_SIZE);\n    header.writeUInt32LE(USBMUXD_HEADER_SIZE + dataSize, 0);\n    header.writeUInt32LE(protocolVersion, 4);\n    header.writeUInt32LE(messageCode, 8);\n    header.writeUInt32LE(this.useTag++, 12); // TODO\n    socket.write(header);\n    socket.write(plistMessage);\n  }\n}\n"], "names": ["USBMUXD_HEADER_SIZE", "UsbmuxProtocolClient", "UsbmuxProtocolReader", "UsbmuxProtocolWriter", "debug", "Debug", "ProtocolClient", "constructor", "socket", "ProtocolReaderFactory", "PlistProtocolReader", "callback", "parse<PERSON><PERSON><PERSON>", "data", "readUInt32LE", "parseBody", "resp", "JSON", "stringify", "useTag", "write", "msg", "messageType", "extraFields", "plistMessage", "plist", "build", "BundleID", "ClientVersionString", "MessageType", "ProgName", "kLibUSBMuxVersion", "dataSize", "length", "protocolVersion", "messageCode", "header", "<PERSON><PERSON><PERSON>", "alloc", "writeUInt32LE"], "mappings": "AAAA;;;;;;CAMC,GAED;;;;;;;;;;;IASaA,mBAAmB,MAAnBA,mBAAmB;IAOnBC,oBAAoB,MAApBA,oBAAoB;IAMpBC,oBAAoB,MAApBA,oBAAoB;IAgBpBC,oBAAoB,MAApBA,oBAAoB;;;8DAtCf,aAAa;;;;;;;8DACb,OAAO;;;;;;kCAIkD,oBAAoB;;;;;;AAE/F,MAAMC,KAAK,GAAGC,IAAAA,MAAK,EAAA,QAAA,EAAC,mCAAmC,CAAC,AAAC;AAElD,MAAML,mBAAmB,GAAG,EAAE,AAAC;AAO/B,MAAMC,oBAAoB,SAASK,iBAAc,eAAA;IACtDC,YAAYC,MAAc,CAAE;QAC1B,KAAK,CAACA,MAAM,EAAE,IAAIC,iBAAqB,sBAAA,CAACP,oBAAoB,CAAC,EAAE,IAAIC,oBAAoB,EAAE,CAAC,CAAC;IAC7F;CACD;AAEM,MAAMD,oBAAoB,SAASQ,iBAAmB,oBAAA;IAC3DH,YAAYI,QAA4B,CAAE;QACxC,KAAK,CAACX,mBAAmB,EAAEW,QAAQ,CAAC,CAAC;IACvC;IAEAC,WAAW,CAACC,IAAY,EAAE;QACxB,OAAOA,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC,GAAGd,mBAAmB,CAAC;IACpD;IAEAe,SAAS,CAACF,IAAY,EAAE;QACtB,MAAMG,IAAI,GAAG,KAAK,CAACD,SAAS,CAACF,IAAI,CAAC,AAAC;QACnCT,KAAK,CAAC,CAAC,UAAU,EAAEa,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,OAAOA,IAAI,CAAC;IACd;CACD;AAEM,MAAMb,oBAAoB;IAC/B,AAAQgB,MAAM,GAAG,CAAC,CAAC;IAEnBC,KAAK,CAACZ,MAAc,EAAEa,GAAkB,EAAE;QACxC,2BAA2B;QAC3BjB,KAAK,CAAC,CAAC,cAAc,EAAEa,IAAI,CAACC,SAAS,CAACG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,MAAM,EAAEC,WAAW,CAAA,EAAEC,WAAW,CAAA,EAAE,GAAGF,GAAG,AAAC;QACzC,MAAMG,YAAY,GAAGC,MAAK,EAAA,QAAA,CAACC,KAAK,CAAC;YAC/BC,QAAQ,EAAE,qBAAqB;YAC/BC,mBAAmB,EAAE,WAAW;YAChCC,WAAW,EAAEP,WAAW;YACxBQ,QAAQ,EAAE,YAAY;YACtBC,iBAAiB,EAAE,CAAC;YACpB,GAAGR,WAAW;SACf,CAAC,AAAC;QAEH,MAAMS,QAAQ,GAAGR,YAAY,GAAGA,YAAY,CAACS,MAAM,GAAG,CAAC,AAAC;QACxD,MAAMC,eAAe,GAAG,CAAC,AAAC;QAC1B,MAAMC,WAAW,GAAG,CAAC,AAAC;QAEtB,MAAMC,MAAM,GAAGC,MAAM,CAACC,KAAK,CAACtC,mBAAmB,CAAC,AAAC;QACjDoC,MAAM,CAACG,aAAa,CAACvC,mBAAmB,GAAGgC,QAAQ,EAAE,CAAC,CAAC,CAAC;QACxDI,MAAM,CAACG,aAAa,CAACL,eAAe,EAAE,CAAC,CAAC,CAAC;QACzCE,MAAM,CAACG,aAAa,CAACJ,WAAW,EAAE,CAAC,CAAC,CAAC;QACrCC,MAAM,CAACG,aAAa,CAAC,IAAI,CAACpB,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO;QAChDX,MAAM,CAACY,KAAK,CAACgB,MAAM,CAAC,CAAC;QACrB5B,MAAM,CAACY,KAAK,CAACI,YAAY,CAAC,CAAC;IAC7B;CACD"}