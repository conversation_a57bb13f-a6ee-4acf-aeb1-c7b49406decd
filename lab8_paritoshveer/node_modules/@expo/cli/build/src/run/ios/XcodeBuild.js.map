{"version": 3, "sources": ["../../../../src/run/ios/XcodeBuild.ts"], "sourcesContent": ["import { ExpoRunFormatter } from '@expo/xcpretty';\nimport chalk from 'chalk';\nimport { spawn, SpawnOptionsWithoutStdio } from 'child_process';\nimport fs from 'fs';\nimport os from 'os';\nimport path from 'path';\n\nimport { BuildProps, ProjectInfo } from './XcodeBuild.types';\nimport { ensureDeviceIsCodeSignedForDeploymentAsync } from './codeSigning/configureCodeSigning';\nimport { simulatorBuildRequiresCodeSigning } from './codeSigning/simulatorCodeSigning';\nimport * as Log from '../../log';\nimport { ensureDirectory } from '../../utils/dir';\nimport { env } from '../../utils/env';\nimport { AbortCommandError, CommandError } from '../../utils/errors';\nimport { getUserTerminal } from '../../utils/terminal';\nexport function logPrettyItem(message: string) {\n  Log.log(chalk`{whiteBright \\u203A} ${message}`);\n}\n\n/**\n *\n * @returns '/Users/<USER>/Library/Developer/Xcode/DerivedData/myapp-gpgjqjodrxtervaufttwnsgimhrx/Build/Products/Debug-iphonesimulator/myapp.app'\n */\nexport function getAppBinaryPath(buildOutput: string) {\n  // Matches what's used in \"Bundle React Native code and images\" script.\n  // Requires that `-hideShellScriptEnvironment` is not included in the build command (extra logs).\n\n  // Like `\\=/Users/<USER>/Library/Developer/Xcode/DerivedData/Exponent-anpuosnglkxokahjhfszejloqfvo/Build/Products/Debug-iphonesimulator`\n  const CONFIGURATION_BUILD_DIR = extractEnvVariableFromBuild(\n    buildOutput,\n    'CONFIGURATION_BUILD_DIR'\n  ).sort(\n    // Longer name means more suffixes, we want the shortest possible one to be first.\n    // Massive projects (like Expo Go) can sometimes print multiple different sets of environment variables.\n    // This can become an issue with some\n    (a, b) => a.length - b.length\n  );\n  // Like `Exponent.app`\n  const UNLOCALIZED_RESOURCES_FOLDER_PATH = extractEnvVariableFromBuild(\n    buildOutput,\n    'UNLOCALIZED_RESOURCES_FOLDER_PATH'\n  );\n\n  const binaryPath = path.join(\n    // Use the shortest defined env variable (usually there's just one).\n    CONFIGURATION_BUILD_DIR[0],\n    // Use the last defined env variable.\n    UNLOCALIZED_RESOURCES_FOLDER_PATH[UNLOCALIZED_RESOURCES_FOLDER_PATH.length - 1]\n  );\n\n  // If the app has a space in the name it'll fail because it isn't escaped properly by Xcode.\n  return getEscapedPath(binaryPath);\n}\n\nexport function getEscapedPath(filePath: string): string {\n  if (fs.existsSync(filePath)) {\n    return filePath;\n  }\n  const unescapedPath = filePath.split(/\\\\ /).join(' ');\n  if (fs.existsSync(unescapedPath)) {\n    return unescapedPath;\n  }\n  throw new CommandError(\n    'XCODE_BUILD',\n    `Unexpected: Generated app at path \"${filePath}\" cannot be read, the app cannot be installed. Please report this and build onto a simulator.`\n  );\n}\n\nexport function extractEnvVariableFromBuild(buildOutput: string, variableName: string) {\n  // Xcode can sometimes escape `=` with a backslash or put the value in quotes\n  const reg = new RegExp(`export ${variableName}\\\\\\\\?=(.*)$`, 'mg');\n  const matched = [...buildOutput.matchAll(reg)];\n\n  if (!matched || !matched.length) {\n    throw new CommandError(\n      'XCODE_BUILD',\n      `Malformed xcodebuild results: \"${variableName}\" variable was not generated in build output. Please report this issue and run your project with Xcode instead.`\n    );\n  }\n  return matched.map((value) => value[1]).filter(Boolean) as string[];\n}\n\nexport function getProcessOptions({\n  packager,\n  shouldSkipInitialBundling,\n  terminal,\n  port,\n}: {\n  packager: boolean;\n  shouldSkipInitialBundling?: boolean;\n  terminal: string | undefined;\n  port: number;\n}): SpawnOptionsWithoutStdio {\n  const SKIP_BUNDLING = shouldSkipInitialBundling ? '1' : undefined;\n  if (packager) {\n    return {\n      env: {\n        ...process.env,\n        RCT_TERMINAL: terminal,\n        SKIP_BUNDLING,\n        RCT_METRO_PORT: port.toString(),\n      },\n    };\n  }\n\n  return {\n    env: {\n      ...process.env,\n      RCT_TERMINAL: terminal,\n      SKIP_BUNDLING,\n      // Always skip launching the packager from a build script.\n      // The script is used for people building their project directly from Xcode.\n      // This essentially means \"› Running script 'Start Packager'\" does nothing.\n      RCT_NO_LAUNCH_PACKAGER: 'true',\n      // FORCE_BUNDLING: '0'\n    },\n  };\n}\n\nexport async function getXcodeBuildArgsAsync(\n  props: Pick<\n    BuildProps,\n    | 'buildCache'\n    | 'projectRoot'\n    | 'xcodeProject'\n    | 'configuration'\n    | 'scheme'\n    | 'device'\n    | 'isSimulator'\n  >\n): Promise<string[]> {\n  const args = [\n    props.xcodeProject.isWorkspace ? '-workspace' : '-project',\n    props.xcodeProject.name,\n    '-configuration',\n    props.configuration,\n    '-scheme',\n    props.scheme,\n    '-destination',\n    `id=${props.device.udid}`,\n  ];\n\n  if (!props.isSimulator || simulatorBuildRequiresCodeSigning(props.projectRoot)) {\n    const developmentTeamId = await ensureDeviceIsCodeSignedForDeploymentAsync(props.projectRoot);\n    if (developmentTeamId) {\n      args.push(\n        `DEVELOPMENT_TEAM=${developmentTeamId}`,\n        '-allowProvisioningUpdates',\n        '-allowProvisioningDeviceRegistration'\n      );\n    }\n  }\n\n  // Add last\n  if (props.buildCache === false) {\n    args.push(\n      // Will first clean the derived data folder.\n      'clean',\n      // Then build step must be added otherwise the process will simply clean and exit.\n      'build'\n    );\n  }\n  return args;\n}\n\nfunction spawnXcodeBuild(\n  args: string[],\n  options: SpawnOptionsWithoutStdio,\n  { onData }: { onData: (data: string) => void }\n): Promise<{ code: number | null; results: string; error: string }> {\n  const buildProcess = spawn('xcodebuild', args, options);\n\n  let results = '';\n  let error = '';\n\n  buildProcess.stdout.on('data', (data: Buffer) => {\n    const stringData = data.toString();\n    results += stringData;\n    onData(stringData);\n  });\n\n  buildProcess.stderr.on('data', (data: Buffer) => {\n    const stringData = data instanceof Buffer ? data.toString() : data;\n    error += stringData;\n  });\n\n  return new Promise(async (resolve, reject) => {\n    buildProcess.on('close', (code: number) => {\n      resolve({ code, results, error });\n    });\n  });\n}\n\nasync function spawnXcodeBuildWithFlush(\n  args: string[],\n  options: SpawnOptionsWithoutStdio,\n  { onFlush }: { onFlush: (data: string) => void }\n): Promise<{ code: number | null; results: string; error: string }> {\n  let currentBuffer = '';\n\n  // Data can be sent in chunks that would have no relevance to our regex\n  // this can cause massive slowdowns, so we need to ensure the data is complete before attempting to parse it.\n  function flushBuffer() {\n    if (!currentBuffer) {\n      return;\n    }\n\n    const data = currentBuffer;\n    // Reset buffer.\n    currentBuffer = '';\n    // Process data.\n    onFlush(data);\n  }\n\n  const data = await spawnXcodeBuild(args, options, {\n    onData(stringData) {\n      currentBuffer += stringData;\n      // Only flush the data if we have a full line.\n      if (currentBuffer.endsWith(os.EOL)) {\n        flushBuffer();\n      }\n    },\n  });\n\n  // Flush log data at the end just in case we missed something.\n  flushBuffer();\n  return data;\n}\n\nasync function spawnXcodeBuildWithFormat(\n  args: string[],\n  options: SpawnOptionsWithoutStdio,\n  { projectRoot, xcodeProject }: { projectRoot: string; xcodeProject: ProjectInfo }\n): Promise<{ code: number | null; results: string; error: string; formatter: ExpoRunFormatter }> {\n  Log.debug(`  xcodebuild ${args.join(' ')}`);\n\n  logPrettyItem(chalk.bold`Planning build`);\n\n  const formatter = ExpoRunFormatter.create(projectRoot, {\n    xcodeProject,\n    isDebug: env.EXPO_DEBUG,\n  });\n\n  const results = await spawnXcodeBuildWithFlush(args, options, {\n    onFlush(data) {\n      // Process data.\n      for (const line of formatter.pipe(data)) {\n        // Log parsed results.\n        Log.log(line);\n      }\n    },\n  });\n\n  Log.debug(`Exited with code: ${results.code}`);\n\n  if (\n    // User cancelled with ctrl-c\n    results.code === null ||\n    // Build interrupted\n    results.code === 75\n  ) {\n    throw new AbortCommandError();\n  }\n\n  Log.log(formatter.getBuildSummary());\n\n  return { ...results, formatter };\n}\n\nexport async function buildAsync(props: BuildProps): Promise<string> {\n  const args = await getXcodeBuildArgsAsync(props);\n\n  const { projectRoot, xcodeProject, shouldSkipInitialBundling, port } = props;\n\n  const { code, results, formatter, error } = await spawnXcodeBuildWithFormat(\n    args,\n    getProcessOptions({\n      packager: false,\n      terminal: getUserTerminal(),\n      shouldSkipInitialBundling,\n      port,\n    }),\n    {\n      projectRoot,\n      xcodeProject,\n    }\n  );\n\n  const logFilePath = writeBuildLogs(projectRoot, results, error);\n\n  if (code !== 0) {\n    // Determine if the logger found any errors;\n    const wasErrorPresented = !!formatter.errors.length;\n\n    if (wasErrorPresented) {\n      // This has a flaw, if the user is missing a file, and there is a script error, only the missing file error will be shown.\n      // They will only see the script error if they fix the missing file and rerun.\n      // The flaw can be fixed by catching script errors in the custom logger.\n      throw new CommandError(\n        `Failed to build iOS project. \"xcodebuild\" exited with error code ${code}.`\n      );\n    }\n\n    _assertXcodeBuildResults(code, results, error, xcodeProject, logFilePath);\n  }\n  return results;\n}\n\n// Exposed for testing.\nexport function _assertXcodeBuildResults(\n  code: number | null,\n  results: string,\n  error: string,\n  xcodeProject: { name: string },\n  logFilePath: string\n): void {\n  const errorTitle = `Failed to build iOS project. \"xcodebuild\" exited with error code ${code}.`;\n\n  const throwWithMessage = (message: string): never => {\n    throw new CommandError(\n      `${errorTitle}\\nTo view more error logs, try building the app with Xcode directly, by opening ${xcodeProject.name}.\\n\\n` +\n        message +\n        `Build logs written to ${chalk.underline(logFilePath)}`\n    );\n  };\n\n  const localizedError = error.match(/NSLocalizedFailure = \"(.*)\"/)?.[1];\n\n  if (localizedError) {\n    throwWithMessage(chalk.bold(localizedError) + '\\n\\n');\n  }\n  // Show all the log info because often times the error is coming from a shell script,\n  // that invoked a node script, that started metro, which threw an error.\n\n  throwWithMessage(results + '\\n\\n' + error);\n}\n\nfunction writeBuildLogs(projectRoot: string, buildOutput: string, errorOutput: string) {\n  const [logFilePath, errorFilePath] = getErrorLogFilePath(projectRoot);\n\n  fs.writeFileSync(logFilePath, buildOutput);\n  fs.writeFileSync(errorFilePath, errorOutput);\n  return logFilePath;\n}\n\nfunction getErrorLogFilePath(projectRoot: string): [string, string] {\n  const folder = path.join(projectRoot, '.expo');\n  ensureDirectory(folder);\n  return [path.join(folder, 'xcodebuild.log'), path.join(folder, 'xcodebuild-error.log')];\n}\n"], "names": ["logPrettyItem", "getAppBinaryPath", "getEscapedPath", "extractEnvVariableFromBuild", "getProcessOptions", "getXcodeBuildArgsAsync", "buildAsync", "_assertXcodeBuildResults", "message", "Log", "log", "chalk", "buildOutput", "CONFIGURATION_BUILD_DIR", "sort", "a", "b", "length", "UNLOCALIZED_RESOURCES_FOLDER_PATH", "binaryPath", "path", "join", "filePath", "fs", "existsSync", "unescapedPath", "split", "CommandError", "variableName", "reg", "RegExp", "matched", "matchAll", "map", "value", "filter", "Boolean", "packager", "shouldSkipInitialBundling", "terminal", "port", "SKIP_BUNDLING", "undefined", "env", "process", "RCT_TERMINAL", "RCT_METRO_PORT", "toString", "RCT_NO_LAUNCH_PACKAGER", "props", "args", "xcodeProject", "isWorkspace", "name", "configuration", "scheme", "device", "udid", "isSimulator", "simulatorBuildRequiresCodeSigning", "projectRoot", "developmentTeamId", "ensureDeviceIsCodeSignedForDeploymentAsync", "push", "buildCache", "spawnXcodeBuild", "options", "onData", "buildProcess", "spawn", "results", "error", "stdout", "on", "data", "stringData", "stderr", "<PERSON><PERSON><PERSON>", "Promise", "resolve", "reject", "code", "spawnXcodeBuildWithFlush", "onFlush", "current<PERSON><PERSON><PERSON>", "flushBuffer", "endsWith", "os", "EOL", "spawnXcodeBuildWithFormat", "debug", "bold", "formatter", "ExpoRunFormatter", "create", "isDebug", "EXPO_DEBUG", "line", "pipe", "AbortCommandError", "getBuildSummary", "getUserTerminal", "logFilePath", "writeBuildLogs", "wasErrorPresented", "errors", "errorTitle", "throwWithMessage", "underline", "localizedError", "match", "errorOutput", "errorFilePath", "getErrorLogFilePath", "writeFileSync", "folder", "ensureDirectory"], "mappings": "AAAA;;;;;;;;;;;IAegBA,aAAa,MAAbA,aAAa;IAQbC,gBAAgB,MAAhBA,gBAAgB;IA+BhBC,cAAc,MAAdA,cAAc;IAcdC,2BAA2B,MAA3BA,2BAA2B;IAc3BC,iBAAiB,MAAjBA,iBAAiB;IAqCXC,sBAAsB,MAAtBA,sBAAsB;IAsJtBC,UAAU,MAAVA,UAAU;IAwChBC,0BAAwB,MAAxBA,wBAAwB;;;yBArTP,gBAAgB;;;;;;;8DAC/B,OAAO;;;;;;;yBACuB,eAAe;;;;;;;8DAChD,IAAI;;;;;;;8DACJ,IAAI;;;;;;;8DACF,MAAM;;;;;;sCAGoC,oCAAoC;sCAC7C,oCAAoC;2DACjE,WAAW;qBACA,iBAAiB;qBAC7B,iBAAiB;wBACW,oBAAoB;0BACpC,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAC/C,SAASP,aAAa,CAACQ,OAAe,EAAE;IAC7CC,IAAG,CAACC,GAAG,CAACC,IAAAA,MAAK,EAAA,QAAA,CAAA,CAAC,qBAAqB,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;AAClD,CAAC;AAMM,SAASP,gBAAgB,CAACW,WAAmB,EAAE;IACpD,uEAAuE;IACvE,iGAAiG;IAEjG,2IAA2I;IAC3I,MAAMC,uBAAuB,GAAGV,2BAA2B,CACzDS,WAAW,EACX,yBAAyB,CAC1B,CAACE,IAAI,CACJ,kFAAkF;IAClF,wGAAwG;IACxG,qCAAqC;IACrC,CAACC,CAAC,EAAEC,CAAC,GAAKD,CAAC,CAACE,MAAM,GAAGD,CAAC,CAACC,MAAM,CAC9B,AAAC;IACF,sBAAsB;IACtB,MAAMC,iCAAiC,GAAGf,2BAA2B,CACnES,WAAW,EACX,mCAAmC,CACpC,AAAC;IAEF,MAAMO,UAAU,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAC1B,oEAAoE;IACpER,uBAAuB,CAAC,CAAC,CAAC,EAC1B,qCAAqC;IACrCK,iCAAiC,CAACA,iCAAiC,CAACD,MAAM,GAAG,CAAC,CAAC,CAChF,AAAC;IAEF,4FAA4F;IAC5F,OAAOf,cAAc,CAACiB,UAAU,CAAC,CAAC;AACpC,CAAC;AAEM,SAASjB,cAAc,CAACoB,QAAgB,EAAU;IACvD,IAAIC,GAAE,EAAA,QAAA,CAACC,UAAU,CAACF,QAAQ,CAAC,EAAE;QAC3B,OAAOA,QAAQ,CAAC;IAClB,CAAC;IACD,MAAMG,aAAa,GAAGH,QAAQ,CAACI,KAAK,OAAO,CAACL,IAAI,CAAC,GAAG,CAAC,AAAC;IACtD,IAAIE,GAAE,EAAA,QAAA,CAACC,UAAU,CAACC,aAAa,CAAC,EAAE;QAChC,OAAOA,aAAa,CAAC;IACvB,CAAC;IACD,MAAM,IAAIE,OAAY,aAAA,CACpB,aAAa,EACb,CAAC,mCAAmC,EAAEL,QAAQ,CAAC,6FAA6F,CAAC,CAC9I,CAAC;AACJ,CAAC;AAEM,SAASnB,2BAA2B,CAACS,WAAmB,EAAEgB,YAAoB,EAAE;IACrF,6EAA6E;IAC7E,MAAMC,GAAG,GAAG,IAAIC,MAAM,CAAC,CAAC,OAAO,EAAEF,YAAY,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,AAAC;IAClE,MAAMG,OAAO,GAAG;WAAInB,WAAW,CAACoB,QAAQ,CAACH,GAAG,CAAC;KAAC,AAAC;IAE/C,IAAI,CAACE,OAAO,IAAI,CAACA,OAAO,CAACd,MAAM,EAAE;QAC/B,MAAM,IAAIU,OAAY,aAAA,CACpB,aAAa,EACb,CAAC,+BAA+B,EAAEC,YAAY,CAAC,+GAA+G,CAAC,CAChK,CAAC;IACJ,CAAC;IACD,OAAOG,OAAO,CAACE,GAAG,CAAC,CAACC,KAAK,GAAKA,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAAa;AACtE,CAAC;AAEM,SAAShC,iBAAiB,CAAC,EAChCiC,QAAQ,CAAA,EACRC,yBAAyB,CAAA,EACzBC,QAAQ,CAAA,EACRC,IAAI,CAAA,EAML,EAA4B;IAC3B,MAAMC,aAAa,GAAGH,yBAAyB,GAAG,GAAG,GAAGI,SAAS,AAAC;IAClE,IAAIL,QAAQ,EAAE;QACZ,OAAO;YACLM,GAAG,EAAE;gBACH,GAAGC,OAAO,CAACD,GAAG;gBACdE,YAAY,EAAEN,QAAQ;gBACtBE,aAAa;gBACbK,cAAc,EAAEN,IAAI,CAACO,QAAQ,EAAE;aAChC;SACF,CAAC;IACJ,CAAC;IAED,OAAO;QACLJ,GAAG,EAAE;YACH,GAAGC,OAAO,CAACD,GAAG;YACdE,YAAY,EAAEN,QAAQ;YACtBE,aAAa;YACb,0DAA0D;YAC1D,4EAA4E;YAC5E,2EAA2E;YAC3EO,sBAAsB,EAAE,MAAM;SAE/B;KACF,CAAC;AACJ,CAAC;AAEM,eAAe3C,sBAAsB,CAC1C4C,KASC,EACkB;IACnB,MAAMC,IAAI,GAAG;QACXD,KAAK,CAACE,YAAY,CAACC,WAAW,GAAG,YAAY,GAAG,UAAU;QAC1DH,KAAK,CAACE,YAAY,CAACE,IAAI;QACvB,gBAAgB;QAChBJ,KAAK,CAACK,aAAa;QACnB,SAAS;QACTL,KAAK,CAACM,MAAM;QACZ,cAAc;QACd,CAAC,GAAG,EAAEN,KAAK,CAACO,MAAM,CAACC,IAAI,CAAC,CAAC;KAC1B,AAAC;IAEF,IAAI,CAACR,KAAK,CAACS,WAAW,IAAIC,IAAAA,qBAAiC,kCAAA,EAACV,KAAK,CAACW,WAAW,CAAC,EAAE;QAC9E,MAAMC,iBAAiB,GAAG,MAAMC,IAAAA,qBAA0C,2CAAA,EAACb,KAAK,CAACW,WAAW,CAAC,AAAC;QAC9F,IAAIC,iBAAiB,EAAE;YACrBX,IAAI,CAACa,IAAI,CACP,CAAC,iBAAiB,EAAEF,iBAAiB,CAAC,CAAC,EACvC,2BAA2B,EAC3B,sCAAsC,CACvC,CAAC;QACJ,CAAC;IACH,CAAC;IAED,WAAW;IACX,IAAIZ,KAAK,CAACe,UAAU,KAAK,KAAK,EAAE;QAC9Bd,IAAI,CAACa,IAAI,CACP,4CAA4C;QAC5C,OAAO,EACP,kFAAkF;QAClF,OAAO,CACR,CAAC;IACJ,CAAC;IACD,OAAOb,IAAI,CAAC;AACd,CAAC;AAED,SAASe,eAAe,CACtBf,IAAc,EACdgB,OAAiC,EACjC,EAAEC,MAAM,CAAA,EAAsC,EACoB;IAClE,MAAMC,YAAY,GAAGC,IAAAA,aAAK,EAAA,MAAA,EAAC,YAAY,EAAEnB,IAAI,EAAEgB,OAAO,CAAC,AAAC;IAExD,IAAII,OAAO,GAAG,EAAE,AAAC;IACjB,IAAIC,KAAK,GAAG,EAAE,AAAC;IAEfH,YAAY,CAACI,MAAM,CAACC,EAAE,CAAC,MAAM,EAAE,CAACC,IAAY,GAAK;QAC/C,MAAMC,UAAU,GAAGD,IAAI,CAAC3B,QAAQ,EAAE,AAAC;QACnCuB,OAAO,IAAIK,UAAU,CAAC;QACtBR,MAAM,CAACQ,UAAU,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC;IAEHP,YAAY,CAACQ,MAAM,CAACH,EAAE,CAAC,MAAM,EAAE,CAACC,IAAY,GAAK;QAC/C,MAAMC,UAAU,GAAGD,IAAI,YAAYG,MAAM,GAAGH,IAAI,CAAC3B,QAAQ,EAAE,GAAG2B,IAAI,AAAC;QACnEH,KAAK,IAAII,UAAU,CAAC;IACtB,CAAC,CAAC,CAAC;IAEH,OAAO,IAAIG,OAAO,CAAC,OAAOC,OAAO,EAAEC,MAAM,GAAK;QAC5CZ,YAAY,CAACK,EAAE,CAAC,OAAO,EAAE,CAACQ,IAAY,GAAK;YACzCF,OAAO,CAAC;gBAAEE,IAAI;gBAAEX,OAAO;gBAAEC,KAAK;aAAE,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAeW,wBAAwB,CACrChC,IAAc,EACdgB,OAAiC,EACjC,EAAEiB,OAAO,CAAA,EAAuC,EACkB;IAClE,IAAIC,aAAa,GAAG,EAAE,AAAC;IAEvB,uEAAuE;IACvE,6GAA6G;IAC7G,SAASC,WAAW,GAAG;QACrB,IAAI,CAACD,aAAa,EAAE;YAClB,OAAO;QACT,CAAC;QAED,MAAMV,IAAI,GAAGU,aAAa,AAAC;QAC3B,gBAAgB;QAChBA,aAAa,GAAG,EAAE,CAAC;QACnB,gBAAgB;QAChBD,OAAO,CAACT,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,MAAMA,IAAI,GAAG,MAAMT,eAAe,CAACf,IAAI,EAAEgB,OAAO,EAAE;QAChDC,MAAM,EAACQ,UAAU,EAAE;YACjBS,aAAa,IAAIT,UAAU,CAAC;YAC5B,8CAA8C;YAC9C,IAAIS,aAAa,CAACE,QAAQ,CAACC,GAAE,EAAA,QAAA,CAACC,GAAG,CAAC,EAAE;gBAClCH,WAAW,EAAE,CAAC;YAChB,CAAC;QACH,CAAC;KACF,CAAC,AAAC;IAEH,8DAA8D;IAC9DA,WAAW,EAAE,CAAC;IACd,OAAOX,IAAI,CAAC;AACd,CAAC;AAED,eAAee,yBAAyB,CACtCvC,IAAc,EACdgB,OAAiC,EACjC,EAAEN,WAAW,CAAA,EAAET,YAAY,CAAA,EAAsD,EACc;IAC/F1C,IAAG,CAACiF,KAAK,CAAC,CAAC,aAAa,EAAExC,IAAI,CAAC7B,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE5CrB,aAAa,CAACW,MAAK,EAAA,QAAA,CAACgF,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;IAE1C,MAAMC,SAAS,GAAGC,SAAgB,EAAA,iBAAA,CAACC,MAAM,CAAClC,WAAW,EAAE;QACrDT,YAAY;QACZ4C,OAAO,EAAEpD,IAAG,IAAA,CAACqD,UAAU;KACxB,CAAC,AAAC;IAEH,MAAM1B,OAAO,GAAG,MAAMY,wBAAwB,CAAChC,IAAI,EAAEgB,OAAO,EAAE;QAC5DiB,OAAO,EAACT,IAAI,EAAE;YACZ,gBAAgB;YAChB,KAAK,MAAMuB,IAAI,IAAIL,SAAS,CAACM,IAAI,CAACxB,IAAI,CAAC,CAAE;gBACvC,sBAAsB;gBACtBjE,IAAG,CAACC,GAAG,CAACuF,IAAI,CAAC,CAAC;YAChB,CAAC;QACH,CAAC;KACF,CAAC,AAAC;IAEHxF,IAAG,CAACiF,KAAK,CAAC,CAAC,kBAAkB,EAAEpB,OAAO,CAACW,IAAI,CAAC,CAAC,CAAC,CAAC;IAE/C,IACE,6BAA6B;IAC7BX,OAAO,CAACW,IAAI,KAAK,IAAI,IACrB,oBAAoB;IACpBX,OAAO,CAACW,IAAI,KAAK,EAAE,EACnB;QACA,MAAM,IAAIkB,OAAiB,kBAAA,EAAE,CAAC;IAChC,CAAC;IAED1F,IAAG,CAACC,GAAG,CAACkF,SAAS,CAACQ,eAAe,EAAE,CAAC,CAAC;IAErC,OAAO;QAAE,GAAG9B,OAAO;QAAEsB,SAAS;KAAE,CAAC;AACnC,CAAC;AAEM,eAAetF,UAAU,CAAC2C,KAAiB,EAAmB;IACnE,MAAMC,IAAI,GAAG,MAAM7C,sBAAsB,CAAC4C,KAAK,CAAC,AAAC;IAEjD,MAAM,EAAEW,WAAW,CAAA,EAAET,YAAY,CAAA,EAAEb,yBAAyB,CAAA,EAAEE,IAAI,CAAA,EAAE,GAAGS,KAAK,AAAC;IAE7E,MAAM,EAAEgC,IAAI,CAAA,EAAEX,OAAO,CAAA,EAAEsB,SAAS,CAAA,EAAErB,KAAK,CAAA,EAAE,GAAG,MAAMkB,yBAAyB,CACzEvC,IAAI,EACJ9C,iBAAiB,CAAC;QAChBiC,QAAQ,EAAE,KAAK;QACfE,QAAQ,EAAE8D,IAAAA,SAAe,gBAAA,GAAE;QAC3B/D,yBAAyB;QACzBE,IAAI;KACL,CAAC,EACF;QACEoB,WAAW;QACXT,YAAY;KACb,CACF,AAAC;IAEF,MAAMmD,WAAW,GAAGC,cAAc,CAAC3C,WAAW,EAAEU,OAAO,EAAEC,KAAK,CAAC,AAAC;IAEhE,IAAIU,IAAI,KAAK,CAAC,EAAE;QACd,4CAA4C;QAC5C,MAAMuB,iBAAiB,GAAG,CAAC,CAACZ,SAAS,CAACa,MAAM,CAACxF,MAAM,AAAC;QAEpD,IAAIuF,iBAAiB,EAAE;YACrB,0HAA0H;YAC1H,8EAA8E;YAC9E,wEAAwE;YACxE,MAAM,IAAI7E,OAAY,aAAA,CACpB,CAAC,iEAAiE,EAAEsD,IAAI,CAAC,CAAC,CAAC,CAC5E,CAAC;QACJ,CAAC;QAED1E,wBAAwB,CAAC0E,IAAI,EAAEX,OAAO,EAAEC,KAAK,EAAEpB,YAAY,EAAEmD,WAAW,CAAC,CAAC;IAC5E,CAAC;IACD,OAAOhC,OAAO,CAAC;AACjB,CAAC;AAGM,SAAS/D,wBAAwB,CACtC0E,IAAmB,EACnBX,OAAe,EACfC,KAAa,EACbpB,YAA8B,EAC9BmD,WAAmB,EACb;QAWiB/B,GAA0C;IAVjE,MAAMmC,UAAU,GAAG,CAAC,iEAAiE,EAAEzB,IAAI,CAAC,CAAC,CAAC,AAAC;IAE/F,MAAM0B,gBAAgB,GAAG,CAACnG,OAAe,GAAY;QACnD,MAAM,IAAImB,OAAY,aAAA,CACpB,CAAC,EAAE+E,UAAU,CAAC,gFAAgF,EAAEvD,YAAY,CAACE,IAAI,CAAC,KAAK,CAAC,GACtH7C,OAAO,GACP,CAAC,sBAAsB,EAAEG,MAAK,EAAA,QAAA,CAACiG,SAAS,CAACN,WAAW,CAAC,CAAC,CAAC,CAC1D,CAAC;IACJ,CAAC,AAAC;IAEF,MAAMO,cAAc,GAAGtC,CAAAA,GAA0C,GAA1CA,KAAK,CAACuC,KAAK,+BAA+B,SAAK,GAA/CvC,KAAAA,CAA+C,GAA/CA,GAA0C,AAAE,CAAC,CAAC,CAAC,AAAC;IAEvE,IAAIsC,cAAc,EAAE;QAClBF,gBAAgB,CAAChG,MAAK,EAAA,QAAA,CAACgF,IAAI,CAACkB,cAAc,CAAC,GAAG,MAAM,CAAC,CAAC;IACxD,CAAC;IACD,qFAAqF;IACrF,wEAAwE;IAExEF,gBAAgB,CAACrC,OAAO,GAAG,MAAM,GAAGC,KAAK,CAAC,CAAC;AAC7C,CAAC;AAED,SAASgC,cAAc,CAAC3C,WAAmB,EAAEhD,WAAmB,EAAEmG,WAAmB,EAAE;IACrF,MAAM,CAACT,WAAW,EAAEU,aAAa,CAAC,GAAGC,mBAAmB,CAACrD,WAAW,CAAC,AAAC;IAEtErC,GAAE,EAAA,QAAA,CAAC2F,aAAa,CAACZ,WAAW,EAAE1F,WAAW,CAAC,CAAC;IAC3CW,GAAE,EAAA,QAAA,CAAC2F,aAAa,CAACF,aAAa,EAAED,WAAW,CAAC,CAAC;IAC7C,OAAOT,WAAW,CAAC;AACrB,CAAC;AAED,SAASW,mBAAmB,CAACrD,WAAmB,EAAoB;IAClE,MAAMuD,MAAM,GAAG/F,KAAI,EAAA,QAAA,CAACC,IAAI,CAACuC,WAAW,EAAE,OAAO,CAAC,AAAC;IAC/CwD,IAAAA,IAAe,gBAAA,EAACD,MAAM,CAAC,CAAC;IACxB,OAAO;QAAC/F,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC8F,MAAM,EAAE,gBAAgB,CAAC;QAAE/F,KAAI,EAAA,QAAA,CAACC,IAAI,CAAC8F,MAAM,EAAE,sBAAsB,CAAC;KAAC,CAAC;AAC1F,CAAC"}