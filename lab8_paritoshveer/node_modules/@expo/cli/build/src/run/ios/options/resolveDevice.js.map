{"version": 3, "sources": ["../../../../../src/run/ios/options/resolveDevice.ts"], "sourcesContent": ["// import { resolveDestinationsAsync } from './appleDestinations';\nimport { promptDeviceAsync } from './promptDevice';\nimport * as Log from '../../../log';\nimport {\n  AppleDeviceManager,\n  ensureSimulatorOpenAsync,\n} from '../../../start/platforms/ios/AppleDeviceManager';\nimport { sortDefaultDeviceToBeginningAsync } from '../../../start/platforms/ios/promptAppleDevice';\nimport { OSType } from '../../../start/platforms/ios/simctl';\nimport * as SimControl from '../../../start/platforms/ios/simctl';\nimport { uniqBy } from '../../../utils/array';\nimport { CommandError } from '../../../utils/errors';\nimport { profile } from '../../../utils/profile';\nimport { logDeviceArgument } from '../../hints';\nimport { BuildProps } from '../XcodeBuild.types';\nimport * as AppleDevice from '../appleDevice/AppleDevice';\n\ntype AnyDevice = {\n  name: string;\n  osType: OSType;\n  osVersion: string;\n  udid: string;\n  deviceType?: string;\n};\n// type AnyDevice = SimControl.Device | AppleDevice.ConnectedDevice;\n\n/** Get a list of devices (called destinations) that are connected to the host machine. Filter by `osType` if defined. */\nasync function getDevicesAsync({\n  osType,\n  // ...buildProps\n}: { osType?: OSType } & Pick<BuildProps, 'xcodeProject' | 'scheme' | 'configuration'>): Promise<\n  AnyDevice[]\n> {\n  const devices = await sortDefaultDeviceToBeginningAsync(\n    uniqBy(\n      (\n        await Promise.all([\n          AppleDevice.getConnectedDevicesAsync(),\n          await profile(SimControl.getDevicesAsync)(),\n          // resolveDestinationsAsync(buildProps),\n        ])\n      ).flat(),\n      (item) => item.udid\n    ),\n    osType\n  );\n\n  // Sort devices to top of front of the list\n\n  const physical: AnyDevice[] = [];\n\n  const simulators = devices.filter((device) => {\n    if ('isAvailable' in device) {\n      return true;\n    } else {\n      physical.push(device);\n      return false;\n    }\n  });\n\n  const isPhone = (a: any) => a.osType === 'iOS';\n  const sorted = [\n    ...physical.sort((a, b) => {\n      const aPhone = isPhone(a);\n      const bPhone = isPhone(b);\n      if (aPhone && !bPhone) return -1;\n      if (!aPhone && bPhone) return 1;\n\n      return 0;\n    }),\n    ...simulators,\n  ];\n\n  // If osType is defined, then filter out ineligible simulators.\n  // Only do this inside of the device selection so users who pass the entire device udid can attempt to select any simulator (even if it's invalid).\n  return osType ? filterDevicesForOsType(sorted, osType) : sorted;\n}\n\n/** @returns a list of devices, filtered by the provided `osType`. */\nfunction filterDevicesForOsType<TDevice extends { osType: OSType }>(\n  devices: TDevice[],\n  osType: OSType\n): TDevice[] {\n  return devices.filter((device) => {\n    if (osType === 'iOS') {\n      // Compatible devices for iOS builds\n      return ['iOS', 'macOS', 'xrOS'].includes(device.osType);\n    }\n    return device.osType === osType;\n  });\n}\n\n/** Given a `device` argument from the CLI, parse and prompt our way to a usable device for building. */\nexport async function resolveDeviceAsync(\n  device: string | boolean | undefined,\n  buildProps: { osType?: OSType } & Pick<BuildProps, 'xcodeProject' | 'scheme' | 'configuration'>\n): Promise<AnyDevice> {\n  await AppleDeviceManager.assertSystemRequirementsAsync();\n\n  if (!device) {\n    /** Finds the first possible device and returns in a booted state. */\n    const manager = await AppleDeviceManager.resolveAsync({\n      device: {\n        osType: buildProps.osType,\n      },\n    });\n    Log.debug(\n      `Resolved default device (name: ${manager.device.name}, udid: ${manager.device.udid}, osType: ${buildProps.osType})`\n    );\n    return manager.device;\n  }\n\n  const devices = await getDevicesAsync(buildProps);\n\n  const resolved =\n    device === true\n      ? // `--device` (no props after)\n        // @ts-expect-error\n        await promptDeviceAsync(devices)\n      : // `--device <name|udid>`\n        findDeviceFromSearchValue(devices, device.toLowerCase());\n\n  return ensureBootedAsync(resolved);\n}\n\n/** @returns `true` if the given device is a simulator. */\nexport function isSimulatorDevice(device: AnyDevice): boolean {\n  return (\n    !('deviceType' in device) ||\n    !!device.deviceType?.startsWith?.('com.apple.CoreSimulator.SimDeviceType.')\n  );\n}\n\n/** @returns device matching the `searchValue` against name or UDID. */\nfunction findDeviceFromSearchValue(devices: AnyDevice[], searchValue: string): AnyDevice {\n  const device = devices.find(\n    (device) =>\n      device.udid.toLowerCase() === searchValue || device.name.toLowerCase() === searchValue\n  );\n  if (!device) {\n    throw new CommandError('BAD_ARGS', `No device UDID or name matching \"${searchValue}\"`);\n  }\n  return device;\n}\n\n/** Ensures the device is booted if it's a simulator. */\nasync function ensureBootedAsync(device: AnyDevice): Promise<AnyDevice> {\n  // --device with no props after\n  logDeviceArgument(device.udid);\n  if (isSimulatorDevice(device)) {\n    return ensureSimulatorOpenAsync({ udid: device.udid });\n  }\n  return device;\n}\n"], "names": ["resolveDeviceAsync", "isSimulatorDevice", "getDevicesAsync", "osType", "devices", "sortDefaultDeviceToBeginningAsync", "uniqBy", "Promise", "all", "AppleDevice", "getConnectedDevicesAsync", "profile", "SimControl", "flat", "item", "udid", "physical", "simulators", "filter", "device", "push", "isPhone", "a", "sorted", "sort", "b", "aPhone", "bPhone", "filterDevicesForOsType", "includes", "buildProps", "AppleDeviceManager", "assertSystemRequirementsAsync", "manager", "resolveAsync", "Log", "debug", "name", "resolved", "promptDeviceAsync", "findDeviceFromSearchValue", "toLowerCase", "ensureBootedAsync", "deviceType", "startsWith", "searchValue", "find", "CommandError", "logDeviceArgument", "ensureSimulatorOpenAsync"], "mappings": "AAAA,kEAAkE;AAClE;;;;;;;;;;;IA4FsBA,kBAAkB,MAAlBA,kBAAkB;IAiCxBC,iBAAiB,MAAjBA,iBAAiB;;8BA7HC,gBAAgB;2DAC7B,cAAc;oCAI5B,iDAAiD;mCACN,gDAAgD;8DAEtE,qCAAqC;uBAC1C,sBAAsB;wBAChB,uBAAuB;yBAC5B,wBAAwB;uBACd,aAAa;mEAElB,4BAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD,oEAAoE;AAEpE,uHAAuH,GACvH,eAAeC,eAAe,CAAC,EAC7BC,MAAM,CAAA,EAE8E,EAEpF;IACA,MAAMC,OAAO,GAAG,MAAMC,IAAAA,kBAAiC,kCAAA,EACrDC,IAAAA,MAAM,OAAA,EACJ,CACE,MAAMC,OAAO,CAACC,GAAG,CAAC;QAChBC,YAAW,CAACC,wBAAwB,EAAE;QACtC,MAAMC,IAAAA,QAAO,QAAA,EAACC,OAAU,CAACV,eAAe,CAAC,EAAE;KAE5C,CAAC,CACH,CAACW,IAAI,EAAE,EACR,CAACC,IAAI,GAAKA,IAAI,CAACC,IAAI,CACpB,EACDZ,MAAM,CACP,AAAC;IAEF,2CAA2C;IAE3C,MAAMa,QAAQ,GAAgB,EAAE,AAAC;IAEjC,MAAMC,UAAU,GAAGb,OAAO,CAACc,MAAM,CAAC,CAACC,MAAM,GAAK;QAC5C,IAAI,aAAa,IAAIA,MAAM,EAAE;YAC3B,OAAO,IAAI,CAAC;QACd,OAAO;YACLH,QAAQ,CAACI,IAAI,CAACD,MAAM,CAAC,CAAC;YACtB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,CAAC,AAAC;IAEH,MAAME,OAAO,GAAG,CAACC,CAAM,GAAKA,CAAC,CAACnB,MAAM,KAAK,KAAK,AAAC;IAC/C,MAAMoB,MAAM,GAAG;WACVP,QAAQ,CAACQ,IAAI,CAAC,CAACF,CAAC,EAAEG,CAAC,GAAK;YACzB,MAAMC,MAAM,GAAGL,OAAO,CAACC,CAAC,CAAC,AAAC;YAC1B,MAAMK,MAAM,GAAGN,OAAO,CAACI,CAAC,CAAC,AAAC;YAC1B,IAAIC,MAAM,IAAI,CAACC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;YACjC,IAAI,CAACD,MAAM,IAAIC,MAAM,EAAE,OAAO,CAAC,CAAC;YAEhC,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;WACCV,UAAU;KACd,AAAC;IAEF,+DAA+D;IAC/D,mJAAmJ;IACnJ,OAAOd,MAAM,GAAGyB,sBAAsB,CAACL,MAAM,EAAEpB,MAAM,CAAC,GAAGoB,MAAM,CAAC;AAClE,CAAC;AAED,mEAAmE,GACnE,SAASK,sBAAsB,CAC7BxB,OAAkB,EAClBD,MAAc,EACH;IACX,OAAOC,OAAO,CAACc,MAAM,CAAC,CAACC,MAAM,GAAK;QAChC,IAAIhB,MAAM,KAAK,KAAK,EAAE;YACpB,oCAAoC;YACpC,OAAO;gBAAC,KAAK;gBAAE,OAAO;gBAAE,MAAM;aAAC,CAAC0B,QAAQ,CAACV,MAAM,CAAChB,MAAM,CAAC,CAAC;QAC1D,CAAC;QACD,OAAOgB,MAAM,CAAChB,MAAM,KAAKA,MAAM,CAAC;IAClC,CAAC,CAAC,CAAC;AACL,CAAC;AAGM,eAAeH,kBAAkB,CACtCmB,MAAoC,EACpCW,UAA+F,EAC3E;IACpB,MAAMC,mBAAkB,mBAAA,CAACC,6BAA6B,EAAE,CAAC;IAEzD,IAAI,CAACb,MAAM,EAAE;QACX,mEAAmE,GACnE,MAAMc,OAAO,GAAG,MAAMF,mBAAkB,mBAAA,CAACG,YAAY,CAAC;YACpDf,MAAM,EAAE;gBACNhB,MAAM,EAAE2B,UAAU,CAAC3B,MAAM;aAC1B;SACF,CAAC,AAAC;QACHgC,IAAG,CAACC,KAAK,CACP,CAAC,+BAA+B,EAAEH,OAAO,CAACd,MAAM,CAACkB,IAAI,CAAC,QAAQ,EAAEJ,OAAO,CAACd,MAAM,CAACJ,IAAI,CAAC,UAAU,EAAEe,UAAU,CAAC3B,MAAM,CAAC,CAAC,CAAC,CACrH,CAAC;QACF,OAAO8B,OAAO,CAACd,MAAM,CAAC;IACxB,CAAC;IAED,MAAMf,OAAO,GAAG,MAAMF,eAAe,CAAC4B,UAAU,CAAC,AAAC;IAElD,MAAMQ,QAAQ,GACZnB,MAAM,KAAK,IAAI,GAEX,mBAAmB;IACnB,MAAMoB,IAAAA,aAAiB,kBAAA,EAACnC,OAAO,CAAC,GAEhCoC,yBAAyB,CAACpC,OAAO,EAAEe,MAAM,CAACsB,WAAW,EAAE,CAAC,AAAC;IAE/D,OAAOC,iBAAiB,CAACJ,QAAQ,CAAC,CAAC;AACrC,CAAC;AAGM,SAASrC,iBAAiB,CAACkB,MAAiB,EAAW;QAGxDA,GAAiB;IAFrB,OACE,CAAC,CAAC,YAAY,IAAIA,MAAM,CAAC,IACzB,CAAC,EAACA,CAAAA,GAAiB,GAAjBA,MAAM,CAACwB,UAAU,SAAY,GAA7BxB,KAAAA,CAA6B,GAA7BA,GAAiB,CAAEyB,UAAU,QAA4C,GAAzEzB,KAAAA,CAAyE,GAAzEA,GAAiB,CAAEyB,UAAU,CAAG,wCAAwC,CAAC,CAAA,CAC3E;AACJ,CAAC;AAED,qEAAqE,GACrE,SAASJ,yBAAyB,CAACpC,OAAoB,EAAEyC,WAAmB,EAAa;IACvF,MAAM1B,MAAM,GAAGf,OAAO,CAAC0C,IAAI,CACzB,CAAC3B,MAAM,GACLA,MAAM,CAACJ,IAAI,CAAC0B,WAAW,EAAE,KAAKI,WAAW,IAAI1B,MAAM,CAACkB,IAAI,CAACI,WAAW,EAAE,KAAKI,WAAW,CACzF,AAAC;IACF,IAAI,CAAC1B,MAAM,EAAE;QACX,MAAM,IAAI4B,OAAY,aAAA,CAAC,UAAU,EAAE,CAAC,iCAAiC,EAAEF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACzF,CAAC;IACD,OAAO1B,MAAM,CAAC;AAChB,CAAC;AAED,sDAAsD,GACtD,eAAeuB,iBAAiB,CAACvB,MAAiB,EAAsB;IACtE,+BAA+B;IAC/B6B,IAAAA,MAAiB,kBAAA,EAAC7B,MAAM,CAACJ,IAAI,CAAC,CAAC;IAC/B,IAAId,iBAAiB,CAACkB,MAAM,CAAC,EAAE;QAC7B,OAAO8B,IAAAA,mBAAwB,yBAAA,EAAC;YAAElC,IAAI,EAAEI,MAAM,CAACJ,IAAI;SAAE,CAAC,CAAC;IACzD,CAAC;IACD,OAAOI,MAAM,CAAC;AAChB,CAAC"}