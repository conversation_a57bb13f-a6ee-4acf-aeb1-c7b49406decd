{"version": 3, "sources": ["../../../../../src/run/ios/options/resolveNativeScheme.ts"], "sourcesContent": ["import { IOSConfig } from '@expo/config-plugins';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { profile } from '../../../utils/profile';\nimport { selectAsync } from '../../../utils/prompts';\nimport { Options, ProjectInfo, XcodeConfiguration } from '../XcodeBuild.types';\n\nconst debug = require('debug')('expo:run:ios:options:resolveNativeScheme') as typeof console.log;\n\ntype NativeSchemeProps = {\n  name: string;\n  osType?: string;\n};\n\nexport async function resolveNativeSchemePropsAsync(\n  projectRoot: string,\n  options: Pick<Options, 'scheme' | 'configuration'>,\n  xcodeProject: ProjectInfo\n): Promise<NativeSchemeProps> {\n  return (\n    (await promptOrQueryNativeSchemeAsync(projectRoot, options)) ??\n    getDefaultNativeScheme(projectRoot, options, xcodeProject)\n  );\n}\n\n/** Resolve the native iOS build `scheme` for a given `configuration`. If the `scheme` isn't provided then the user will be prompted to select one. */\nexport async function promptOrQueryNativeSchemeAsync(\n  projectRoot: string,\n  { scheme, configuration }: { scheme?: string | boolean; configuration?: XcodeConfiguration }\n): Promise<NativeSchemeProps | null> {\n  const schemes = IOSConfig.BuildScheme.getRunnableSchemesFromXcodeproj(projectRoot, {\n    configuration,\n  });\n\n  if (!schemes.length) {\n    throw new CommandError('IOS_MALFORMED', 'No native iOS build schemes found');\n  }\n\n  if (scheme === true) {\n    if (schemes.length === 1) {\n      Log.log(`Auto selecting only available scheme: ${schemes[0].name}`);\n      return schemes[0];\n    }\n    const resolvedSchemeName = await selectAsync(\n      'Select a scheme',\n      schemes.map((value) => {\n        const isApp =\n          value.type === IOSConfig.Target.TargetType.APPLICATION && value.osType === 'iOS';\n        return {\n          value: value.name,\n          title: isApp ? chalk.bold(value.name) + chalk.gray(' (app)') : value.name,\n        };\n      }),\n      {\n        nonInteractiveHelp: `--scheme: argument must be provided with a string in non-interactive mode. Valid choices are: ${schemes.join(\n          ', '\n        )}`,\n      }\n    );\n    return schemes.find(({ name }) => resolvedSchemeName === name) ?? null;\n  }\n  // Attempt to match the schemes up so we can open the correct simulator\n  return scheme ? schemes.find(({ name }) => name === scheme) || { name: scheme } : null;\n}\n\nexport function getDefaultNativeScheme(\n  projectRoot: string,\n  options: Pick<Options, 'configuration'>,\n  xcodeProject: Pick<ProjectInfo, 'name'>\n): NativeSchemeProps {\n  // If the resolution failed then we should just use the first runnable scheme that\n  // matches the provided configuration.\n  const resolvedSchemes = profile(IOSConfig.BuildScheme.getRunnableSchemesFromXcodeproj)(\n    projectRoot,\n    {\n      configuration: options.configuration,\n    }\n  );\n\n  // If there are multiple schemes, then the default should be the application.\n  if (resolvedSchemes.length > 1) {\n    const scheme =\n      resolvedSchemes.find(({ type }) => type === IOSConfig.Target.TargetType.APPLICATION) ??\n      resolvedSchemes[0];\n    debug(`Using default scheme: ${scheme.name}`);\n    return scheme;\n  }\n\n  // If we couldn't find the scheme, then we'll guess at it,\n  // this is needed for cases where the native code hasn't been generated yet.\n  if (resolvedSchemes[0]) {\n    return resolvedSchemes[0];\n  }\n  return {\n    name: path.basename(xcodeProject.name, path.extname(xcodeProject.name)),\n  };\n}\n"], "names": ["resolveNativeSchemePropsAsync", "promptOrQueryNativeSchemeAsync", "getDefaultNativeScheme", "debug", "require", "projectRoot", "options", "xcodeProject", "scheme", "configuration", "schemes", "IOSConfig", "BuildScheme", "getRunnableSchemesFromXcodeproj", "length", "CommandError", "Log", "log", "name", "resolvedSchemeName", "selectAsync", "map", "value", "isApp", "type", "Target", "TargetType", "APPLICATION", "osType", "title", "chalk", "bold", "gray", "nonInteractiveHelp", "join", "find", "resolvedSchemes", "profile", "path", "basename", "extname"], "mappings": "AAAA;;;;;;;;;;;IAiBsBA,6BAA6B,MAA7BA,6BAA6B;IAY7BC,8BAA8B,MAA9BA,8BAA8B;IAuCpCC,sBAAsB,MAAtBA,sBAAsB;;;yBApEZ,sBAAsB;;;;;;;8DAC9B,OAAO;;;;;;;8DACR,MAAM;;;;;;2DAEF,cAAc;wBACN,uBAAuB;yBAC5B,wBAAwB;yBACpB,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGpD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,0CAA0C,CAAC,AAAsB,AAAC;AAO1F,eAAeJ,6BAA6B,CACjDK,WAAmB,EACnBC,OAAkD,EAClDC,YAAyB,EACG;QAE1B,GAA4D;IAD9D,OACE,CAAA,GAA4D,GAA3D,MAAMN,8BAA8B,CAACI,WAAW,EAAEC,OAAO,CAAC,YAA3D,GAA4D,GAC5DJ,sBAAsB,CAACG,WAAW,EAAEC,OAAO,EAAEC,YAAY,CAAC,CAC1D;AACJ,CAAC;AAGM,eAAeN,8BAA8B,CAClDI,WAAmB,EACnB,EAAEG,MAAM,CAAA,EAAEC,aAAa,CAAA,EAAqE,EACzD;IACnC,MAAMC,OAAO,GAAGC,cAAS,EAAA,UAAA,CAACC,WAAW,CAACC,+BAA+B,CAACR,WAAW,EAAE;QACjFI,aAAa;KACd,CAAC,AAAC;IAEH,IAAI,CAACC,OAAO,CAACI,MAAM,EAAE;QACnB,MAAM,IAAIC,OAAY,aAAA,CAAC,eAAe,EAAE,mCAAmC,CAAC,CAAC;IAC/E,CAAC;IAED,IAAIP,MAAM,KAAK,IAAI,EAAE;QACnB,IAAIE,OAAO,CAACI,MAAM,KAAK,CAAC,EAAE;YACxBE,IAAG,CAACC,GAAG,CAAC,CAAC,sCAAsC,EAAEP,OAAO,CAAC,CAAC,CAAC,CAACQ,IAAI,CAAC,CAAC,CAAC,CAAC;YACpE,OAAOR,OAAO,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QACD,MAAMS,kBAAkB,GAAG,MAAMC,IAAAA,QAAW,YAAA,EAC1C,iBAAiB,EACjBV,OAAO,CAACW,GAAG,CAAC,CAACC,KAAK,GAAK;YACrB,MAAMC,KAAK,GACTD,KAAK,CAACE,IAAI,KAAKb,cAAS,EAAA,UAAA,CAACc,MAAM,CAACC,UAAU,CAACC,WAAW,IAAIL,KAAK,CAACM,MAAM,KAAK,KAAK,AAAC;YACnF,OAAO;gBACLN,KAAK,EAAEA,KAAK,CAACJ,IAAI;gBACjBW,KAAK,EAAEN,KAAK,GAAGO,MAAK,EAAA,QAAA,CAACC,IAAI,CAACT,KAAK,CAACJ,IAAI,CAAC,GAAGY,MAAK,EAAA,QAAA,CAACE,IAAI,CAAC,QAAQ,CAAC,GAAGV,KAAK,CAACJ,IAAI;aAC1E,CAAC;QACJ,CAAC,CAAC,EACF;YACEe,kBAAkB,EAAE,CAAC,8FAA8F,EAAEvB,OAAO,CAACwB,IAAI,CAC/H,IAAI,CACL,CAAC,CAAC;SACJ,CACF,AAAC;YACKxB,GAAuD;QAA9D,OAAOA,CAAAA,GAAuD,GAAvDA,OAAO,CAACyB,IAAI,CAAC,CAAC,EAAEjB,IAAI,CAAA,EAAE,GAAKC,kBAAkB,KAAKD,IAAI,CAAC,YAAvDR,GAAuD,GAAI,IAAI,CAAC;IACzE,CAAC;IACD,uEAAuE;IACvE,OAAOF,MAAM,GAAGE,OAAO,CAACyB,IAAI,CAAC,CAAC,EAAEjB,IAAI,CAAA,EAAE,GAAKA,IAAI,KAAKV,MAAM,CAAC,IAAI;QAAEU,IAAI,EAAEV,MAAM;KAAE,GAAG,IAAI,CAAC;AACzF,CAAC;AAEM,SAASN,sBAAsB,CACpCG,WAAmB,EACnBC,OAAuC,EACvCC,YAAuC,EACpB;IACnB,kFAAkF;IAClF,sCAAsC;IACtC,MAAM6B,eAAe,GAAGC,IAAAA,QAAO,QAAA,EAAC1B,cAAS,EAAA,UAAA,CAACC,WAAW,CAACC,+BAA+B,CAAC,CACpFR,WAAW,EACX;QACEI,aAAa,EAAEH,OAAO,CAACG,aAAa;KACrC,CACF,AAAC;IAEF,6EAA6E;IAC7E,IAAI2B,eAAe,CAACtB,MAAM,GAAG,CAAC,EAAE;YAE5BsB,GAAoF;QADtF,MAAM5B,MAAM,GACV4B,CAAAA,GAAoF,GAApFA,eAAe,CAACD,IAAI,CAAC,CAAC,EAAEX,IAAI,CAAA,EAAE,GAAKA,IAAI,KAAKb,cAAS,EAAA,UAAA,CAACc,MAAM,CAACC,UAAU,CAACC,WAAW,CAAC,YAApFS,GAAoF,GACpFA,eAAe,CAAC,CAAC,CAAC,AAAC;QACrBjC,KAAK,CAAC,CAAC,sBAAsB,EAAEK,MAAM,CAACU,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9C,OAAOV,MAAM,CAAC;IAChB,CAAC;IAED,0DAA0D;IAC1D,4EAA4E;IAC5E,IAAI4B,eAAe,CAAC,CAAC,CAAC,EAAE;QACtB,OAAOA,eAAe,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IACD,OAAO;QACLlB,IAAI,EAAEoB,KAAI,EAAA,QAAA,CAACC,QAAQ,CAAChC,YAAY,CAACW,IAAI,EAAEoB,KAAI,EAAA,QAAA,CAACE,OAAO,CAACjC,YAAY,CAACW,IAAI,CAAC,CAAC;KACxE,CAAC;AACJ,CAAC"}