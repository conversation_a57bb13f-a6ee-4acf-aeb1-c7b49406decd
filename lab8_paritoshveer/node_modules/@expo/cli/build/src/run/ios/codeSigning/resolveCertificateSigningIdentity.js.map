{"version": 3, "sources": ["../../../../../src/run/ios/codeSigning/resolveCertificateSigningIdentity.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport * as Security from './Security';\nimport { getLastDeveloperCodeSigningIdAsync, setLastDeveloperCodeSigningIdAsync } from './settings';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\nimport { isInteractive } from '../../../utils/interactive';\nimport { learnMore } from '../../../utils/link';\nimport { selectAsync } from '../../../utils/prompts';\n\n/**\n * Sort the code signing items so the last selected item (user's default) is the first suggested.\n */\nexport async function sortDefaultIdToBeginningAsync(\n  identities: Security.CertificateSigningInfo[]\n): Promise<[Security.CertificateSigningInfo[], string | null]> {\n  const lastSelected = await getLastDeveloperCodeSigningIdAsync();\n\n  if (lastSelected) {\n    let iterations = 0;\n    while (identities[0].signingCertificateId !== lastSelected && iterations < identities.length) {\n      identities.push(identities.shift()!);\n      iterations++;\n    }\n  }\n  return [identities, lastSelected];\n}\n\n/**\n * Assert that the computer needs code signing setup.\n * This links to an FYI page that was user tested internally.\n */\nfunction assertCodeSigningSetup(): never {\n  // TODO: We can probably do this too automatically.\n  Log.log(\n    `\\u203A Your computer requires some additional setup before you can build onto physical iOS devices.\\n  ${chalk.bold(\n      learnMore('https://expo.fyi/setup-xcode-signing')\n    )}`\n  );\n\n  throw new CommandError('No code signing certificates are available to use.');\n}\n\n/**\n * Resolve the best certificate signing identity from a given list of IDs.\n * - If no IDs: Assert that the user has to setup code signing.\n * - If one ID: Return the first ID.\n * - If multiple IDs: Ask the user to select one, then store the value to be suggested first next time (since users generally use the same ID).\n */\nexport async function resolveCertificateSigningIdentityAsync(\n  ids: string[]\n): Promise<Security.CertificateSigningInfo> {\n  // The user has no valid code signing identities.\n  if (!ids.length) {\n    assertCodeSigningSetup();\n  }\n\n  //  One ID available 🤝 Program is not interactive\n  //\n  //     using the the first available option\n  if (ids.length === 1 || !isInteractive()) {\n    // This method is cheaper than `resolveIdentitiesAsync` and checking the\n    // cached user preference so we should use this as early as possible.\n    return Security.resolveCertificateSigningInfoAsync(ids[0]);\n  }\n\n  // Get identities and sort by the one that the user is most likely to choose.\n  const [identities, preferred] = await sortDefaultIdToBeginningAsync(\n    await Security.resolveIdentitiesAsync(ids)\n  );\n\n  const selected = await selectDevelopmentTeamAsync(identities, preferred);\n\n  // Store the last used value and suggest it as the first value\n  // next time the user has to select a code signing identity.\n  await setLastDeveloperCodeSigningIdAsync(selected.signingCertificateId);\n\n  return selected;\n}\n\n/** Prompt the user to select a development team, highlighting the preferred value based on the user history. */\nexport async function selectDevelopmentTeamAsync(\n  identities: Security.CertificateSigningInfo[],\n  preferredId: string | null\n): Promise<Security.CertificateSigningInfo> {\n  const index = await selectAsync(\n    'Development team for signing the app',\n    identities.map((value, i) => {\n      const format =\n        value.signingCertificateId === preferredId ? chalk.bold : (message: string) => message;\n      return {\n        // Formatted like: `650 Industries, Inc. (A1BCDEF234) - Apple Development: Evan Bacon (AA00AABB0A)`\n        title: format(\n          [value.appleTeamName, `(${value.appleTeamId}) -`, value.codeSigningInfo].join(' ')\n        ),\n        value: i,\n      };\n    })\n  );\n\n  return identities[index];\n}\n"], "names": ["sortDefaultIdToBeginningAsync", "resolveCertificateSigningIdentityAsync", "selectDevelopmentTeamAsync", "identities", "lastSelected", "getLastDeveloperCodeSigningIdAsync", "iterations", "signingCertificateId", "length", "push", "shift", "assertCodeSigningSetup", "Log", "log", "chalk", "bold", "learnMore", "CommandError", "ids", "isInteractive", "Security", "resolveCertificateSigningInfoAsync", "preferred", "resolveIdentitiesAsync", "selected", "setLastDeveloperCodeSigningIdAsync", "preferredId", "index", "selectAsync", "map", "value", "i", "format", "message", "title", "appleTeamName", "appleTeamId", "codeSigningInfo", "join"], "mappings": "AAAA;;;;;;;;;;;IAasBA,6BAA6B,MAA7BA,6BAA6B;IAoC7BC,sCAAsC,MAAtCA,sCAAsC;IAgCtCC,0BAA0B,MAA1BA,0BAA0B;;;8DAjF9B,OAAO;;;;;;gEAEC,YAAY;0BACiD,YAAY;2DAC9E,cAAc;wBACN,uBAAuB;6BACtB,4BAA4B;sBAChC,qBAAqB;yBACnB,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAK7C,eAAeF,6BAA6B,CACjDG,UAA6C,EACgB;IAC7D,MAAMC,YAAY,GAAG,MAAMC,IAAAA,SAAkC,mCAAA,GAAE,AAAC;IAEhE,IAAID,YAAY,EAAE;QAChB,IAAIE,UAAU,GAAG,CAAC,AAAC;QACnB,MAAOH,UAAU,CAAC,CAAC,CAAC,CAACI,oBAAoB,KAAKH,YAAY,IAAIE,UAAU,GAAGH,UAAU,CAACK,MAAM,CAAE;YAC5FL,UAAU,CAACM,IAAI,CAACN,UAAU,CAACO,KAAK,EAAE,CAAE,CAAC;YACrCJ,UAAU,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IACD,OAAO;QAACH,UAAU;QAAEC,YAAY;KAAC,CAAC;AACpC,CAAC;AAED;;;CAGC,GACD,SAASO,sBAAsB,GAAU;IACvC,mDAAmD;IACnDC,IAAG,CAACC,GAAG,CACL,CAAC,uGAAuG,EAAEC,MAAK,EAAA,QAAA,CAACC,IAAI,CAClHC,IAAAA,KAAS,UAAA,EAAC,sCAAsC,CAAC,CAClD,CAAC,CAAC,CACJ,CAAC;IAEF,MAAM,IAAIC,OAAY,aAAA,CAAC,oDAAoD,CAAC,CAAC;AAC/E,CAAC;AAQM,eAAehB,sCAAsC,CAC1DiB,GAAa,EAC6B;IAC1C,iDAAiD;IACjD,IAAI,CAACA,GAAG,CAACV,MAAM,EAAE;QACfG,sBAAsB,EAAE,CAAC;IAC3B,CAAC;IAED,iDAAiD;IACjD,EAAE;IACF,2CAA2C;IAC3C,IAAIO,GAAG,CAACV,MAAM,KAAK,CAAC,IAAI,CAACW,IAAAA,YAAa,cAAA,GAAE,EAAE;QACxC,wEAAwE;QACxE,qEAAqE;QACrE,OAAOC,SAAQ,CAACC,kCAAkC,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED,6EAA6E;IAC7E,MAAM,CAACf,UAAU,EAAEmB,SAAS,CAAC,GAAG,MAAMtB,6BAA6B,CACjE,MAAMoB,SAAQ,CAACG,sBAAsB,CAACL,GAAG,CAAC,CAC3C,AAAC;IAEF,MAAMM,QAAQ,GAAG,MAAMtB,0BAA0B,CAACC,UAAU,EAAEmB,SAAS,CAAC,AAAC;IAEzE,8DAA8D;IAC9D,4DAA4D;IAC5D,MAAMG,IAAAA,SAAkC,mCAAA,EAACD,QAAQ,CAACjB,oBAAoB,CAAC,CAAC;IAExE,OAAOiB,QAAQ,CAAC;AAClB,CAAC;AAGM,eAAetB,0BAA0B,CAC9CC,UAA6C,EAC7CuB,WAA0B,EACgB;IAC1C,MAAMC,KAAK,GAAG,MAAMC,IAAAA,QAAW,YAAA,EAC7B,sCAAsC,EACtCzB,UAAU,CAAC0B,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,GAAK;QAC3B,MAAMC,MAAM,GACVF,KAAK,CAACvB,oBAAoB,KAAKmB,WAAW,GAAGZ,MAAK,EAAA,QAAA,CAACC,IAAI,GAAG,CAACkB,OAAe,GAAKA,OAAO,AAAC;QACzF,OAAO;YACL,mGAAmG;YACnGC,KAAK,EAAEF,MAAM,CACX;gBAACF,KAAK,CAACK,aAAa;gBAAE,CAAC,CAAC,EAAEL,KAAK,CAACM,WAAW,CAAC,GAAG,CAAC;gBAAEN,KAAK,CAACO,eAAe;aAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CACnF;YACDR,KAAK,EAAEC,CAAC;SACT,CAAC;IACJ,CAAC,CAAC,CACH,AAAC;IAEF,OAAO5B,UAAU,CAACwB,KAAK,CAAC,CAAC;AAC3B,CAAC"}