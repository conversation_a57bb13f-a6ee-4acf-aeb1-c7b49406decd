{"version": 3, "sources": ["../../../../../src/run/ios/codeSigning/settings.ts"], "sourcesContent": ["import UserSettings from '../../../api/user/UserSettings';\n\n/** Get the cached code signing ID from the last time a user configured code signing via the CLI. */\nexport async function getLastDeveloperCodeSigningIdAsync(): Promise<string | null> {\n  const { developmentCodeSigningId } = await UserSettings.readAsync();\n  return developmentCodeSigningId ?? null;\n}\n\n/** Cache the code signing ID that the user chose for their project, we'll recommend this value for the next project they code sign. */\nexport async function setLastDeveloperCodeSigningIdAsync(id: string): Promise<void> {\n  await UserSettings.setAsync('developmentCodeSigningId', id).catch(() => {});\n}\n"], "names": ["getLastDeveloperCodeSigningIdAsync", "setLastDeveloperCodeSigningIdAsync", "developmentCodeSigningId", "UserSettings", "readAsync", "id", "setAsync", "catch"], "mappings": "AAAA;;;;;;;;;;;IAGsBA,kCAAkC,MAAlCA,kCAAkC;IAMlCC,kCAAkC,MAAlCA,kCAAkC;;mEAT/B,gCAAgC;;;;;;AAGlD,eAAeD,kCAAkC,GAA2B;IACjF,MAAM,EAAEE,wBAAwB,CAAA,EAAE,GAAG,MAAMC,aAAY,QAAA,CAACC,SAAS,EAAE,AAAC;IACpE,OAAOF,wBAAwB,WAAxBA,wBAAwB,GAAI,IAAI,CAAC;AAC1C,CAAC;AAGM,eAAeD,kCAAkC,CAACI,EAAU,EAAiB;IAClF,MAAMF,aAAY,QAAA,CAACG,QAAQ,CAAC,0BAA0B,EAAED,EAAE,CAAC,CAACE,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC,CAAC;AAC9E,CAAC"}