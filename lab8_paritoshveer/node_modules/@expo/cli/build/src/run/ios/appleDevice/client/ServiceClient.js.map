{"version": 3, "sources": ["../../../../../../src/run/ios/appleDevice/client/ServiceClient.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2018 Drifty Co.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { Socket } from 'net';\n\nimport { CommandError } from '../../../../utils/errors';\nimport { ProtocolClient } from '../protocol/AbstractProtocol';\n\nexport abstract class ServiceClient<T extends ProtocolClient> {\n  constructor(\n    public socket: Socket,\n    protected protocolClient: T\n  ) {}\n}\n\nexport class ResponseError extends CommandError {\n  constructor(\n    msg: string,\n    public response: any\n  ) {\n    super(msg);\n  }\n}\n"], "names": ["ServiceClient", "ResponseError", "constructor", "socket", "protocolClient", "CommandError", "msg", "response"], "mappings": "AAAA;;;;;;CAMC,GACD;;;;;;;;;;;IAKsBA,aAAa,MAAbA,aAAa;IAOtBC,aAAa,MAAbA,aAAa;;wBAVG,0BAA0B;AAGhD,MAAeD,aAAa;IACjCE,YACSC,MAAc,EACXC,cAAiB,CAC3B;QAFOD,cAAAA,MAAc,CAAA;QACXC,sBAAAA,cAAiB,CAAA;IAC1B;CACJ;AAEM,MAAMH,aAAa,SAASI,OAAY,aAAA;IAC7CH,YACEI,GAAW,EACJC,QAAa,CACpB;QACA,KAAK,CAACD,GAAG,CAAC,CAAC;QAFJC,gBAAAA,QAAa,CAAA;IAGtB;CACD"}