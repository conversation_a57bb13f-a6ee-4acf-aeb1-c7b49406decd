{"version": 3, "sources": ["../../../../src/run/ios/XcodeBuild.types.ts"], "sourcesContent": ["import { OSType } from '../../start/platforms/ios/simctl';\nimport { BundlerProps } from '../resolveBundlerProps';\n\nexport type XcodeConfiguration = 'Debug' | 'Release';\n\nexport type Options = {\n  /** iOS device to target. */\n  device?: string | boolean;\n  /** Dev server port to use, ignored if `bundler` is `false`. */\n  port?: number;\n  /** Xcode scheme to build. */\n  scheme?: string | boolean;\n  /** Xcode configuration to build. Default `Debug` */\n  configuration?: XcodeConfiguration;\n  /** Should start the bundler dev server. */\n  bundler?: boolean;\n  /** Should install missing dependencies before building. */\n  install?: boolean;\n  /** Should use derived data for builds. */\n  buildCache?: boolean;\n};\n\nexport type ProjectInfo = {\n  isWorkspace: boolean;\n  name: string;\n};\n\nexport type BuildProps = {\n  /** Root to the iOS native project. */\n  projectRoot: string;\n  /** Is the target a simulator. */\n  isSimulator: boolean;\n  xcodeProject: ProjectInfo;\n  device: { name: string; udid: string; osType: OSType };\n  configuration: XcodeConfiguration;\n  /** Disable the initial bundling from the native script. */\n  shouldSkipInitialBundling: boolean;\n  /** Should use derived data for builds. */\n  buildCache: boolean;\n  scheme: string;\n} & BundlerProps;\n"], "names": [], "mappings": "AAAA"}