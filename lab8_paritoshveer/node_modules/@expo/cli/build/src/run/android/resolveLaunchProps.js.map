{"version": 3, "sources": ["../../../../src/run/android/resolveLaunchProps.ts"], "sourcesContent": ["import { AndroidConfig } from '@expo/config-plugins';\n\nimport { AndroidAppIdResolver } from '../../start/platforms/android/AndroidAppIdResolver';\nimport { CommandError } from '../../utils/errors';\n\nexport interface LaunchProps {\n  packageName: string;\n  mainActivity: string;\n  launchActivity: string;\n}\n\nasync function getMainActivityAsync(projectRoot: string): Promise<string> {\n  const filePath = await AndroidConfig.Paths.getAndroidManifestAsync(projectRoot);\n  const androidManifest = await AndroidConfig.Manifest.readAndroidManifestAsync(filePath);\n\n  // Assert MainActivity defined.\n  const activity = await AndroidConfig.Manifest.getRunnableActivity(androidManifest);\n  if (!activity) {\n    throw new CommandError(\n      'ANDROID_MALFORMED',\n      `${filePath} is missing a runnable activity element.`\n    );\n  }\n  // Often this is \".MainActivity\"\n  return activity.$['android:name'];\n}\n\nexport async function resolveLaunchPropsAsync(projectRoot: string): Promise<LaunchProps> {\n  // Often this is \".MainActivity\"\n  const mainActivity = await getMainActivityAsync(projectRoot);\n\n  const packageName = await new AndroidAppIdResolver(projectRoot).getAppIdFromNativeAsync();\n  const launchActivity = `${packageName}/${mainActivity}`;\n\n  return {\n    mainActivity,\n    launchActivity,\n    packageName,\n  };\n}\n"], "names": ["resolveLaunchPropsAsync", "getMainActivityAsync", "projectRoot", "filePath", "AndroidConfig", "Paths", "getAndroidManifestAsync", "androidManifest", "Manifest", "readAndroidManifestAsync", "activity", "getRunnableActivity", "CommandError", "$", "mainActivity", "packageName", "AndroidAppIdResolver", "getAppIdFromNativeAsync", "launchActivity"], "mappings": "AAAA;;;;+BA2Bs<PERSON>,yBAAuB;;aAAvBA,uBAAuB;;;yBA3Bf,sBAAsB;;;;;;sCAEf,oDAAoD;wBAC5D,oBAAoB;AAQjD,eAAeC,oBAAoB,CAACC,WAAmB,EAAmB;IACxE,MAAMC,QAAQ,GAAG,MAAMC,cAAa,EAAA,cAAA,CAACC,KAAK,CAACC,uBAAuB,CAACJ,WAAW,CAAC,AAAC;IAChF,MAAMK,eAAe,GAAG,MAAMH,cAAa,EAAA,cAAA,CAACI,QAAQ,CAACC,wBAAwB,CAACN,QAAQ,CAAC,AAAC;IAExF,+BAA+B;IAC/B,MAAMO,QAAQ,GAAG,MAAMN,cAAa,EAAA,cAAA,CAACI,QAAQ,CAACG,mBAAmB,CAACJ,eAAe,CAAC,AAAC;IACnF,IAAI,CAACG,QAAQ,EAAE;QACb,MAAM,IAAIE,OAAY,aAAA,CACpB,mBAAmB,EACnB,CAAC,EAAET,QAAQ,CAAC,wCAAwC,CAAC,CACtD,CAAC;IACJ,CAAC;IACD,gCAAgC;IAChC,OAAOO,QAAQ,CAACG,CAAC,CAAC,cAAc,CAAC,CAAC;AACpC,CAAC;AAEM,eAAeb,uBAAuB,CAACE,WAAmB,EAAwB;IACvF,gCAAgC;IAChC,MAAMY,YAAY,GAAG,MAAMb,oBAAoB,CAACC,WAAW,CAAC,AAAC;IAE7D,MAAMa,WAAW,GAAG,MAAM,IAAIC,qBAAoB,qBAAA,CAACd,WAAW,CAAC,CAACe,uBAAuB,EAAE,AAAC;IAC1F,MAAMC,cAAc,GAAG,CAAC,EAAEH,WAAW,CAAC,CAAC,EAAED,YAAY,CAAC,CAAC,AAAC;IAExD,OAAO;QACLA,YAAY;QACZI,cAAc;QACdH,WAAW;KACZ,CAAC;AACJ,CAAC"}