{"version": 3, "sources": ["../../../../src/run/android/runAndroidAsync.ts"], "sourcesContent": ["import path from 'path';\n\nimport { resolveInstallApkNameAsync } from './resolveInstallApkName';\nimport { Options, ResolvedOptions, resolveOptionsAsync } from './resolveOptions';\nimport { Log } from '../../log';\nimport { assembleAsync, installAsync } from '../../start/platforms/android/gradle';\nimport { setNodeEnv } from '../../utils/nodeEnv';\nimport { ensurePortAvailabilityAsync } from '../../utils/port';\nimport { getSchemesForAndroidAsync } from '../../utils/scheme';\nimport { ensureNativeProjectAsync } from '../ensureNativeProject';\nimport { logProjectLogsLocation } from '../hints';\nimport { startBundlerAsync } from '../startBundler';\n\nconst debug = require('debug')('expo:run:android');\n\nexport async function runAndroidAsync(projectRoot: string, { install, ...options }: Options) {\n  // NOTE: This is a guess, the developer can overwrite with `NODE_ENV`.\n  setNodeEnv(options.variant === 'release' ? 'production' : 'development');\n  require('@expo/env').load(projectRoot);\n\n  await ensureNativeProjectAsync(projectRoot, { platform: 'android', install });\n\n  const props = await resolveOptionsAsync(projectRoot, options);\n\n  debug('Package name: ' + props.packageName);\n  Log.log('› Building app...');\n\n  const androidProjectRoot = path.join(projectRoot, 'android');\n\n  await assembleAsync(androidProjectRoot, {\n    variant: props.variant,\n    port: props.port,\n    appName: props.appName,\n    buildCache: props.buildCache,\n    architectures: props.architectures,\n  });\n\n  // Ensure the port hasn't become busy during the build.\n  if (props.shouldStartBundler && !(await ensurePortAvailabilityAsync(projectRoot, props))) {\n    props.shouldStartBundler = false;\n  }\n\n  const manager = await startBundlerAsync(projectRoot, {\n    port: props.port,\n    // If a scheme is specified then use that instead of the package name.\n    scheme: (await getSchemesForAndroidAsync(projectRoot))?.[0],\n    headless: !props.shouldStartBundler,\n  });\n\n  await installAppAsync(androidProjectRoot, props);\n\n  await manager.getDefaultDevServer().openCustomRuntimeAsync(\n    'emulator',\n    {\n      applicationId: props.packageName,\n    },\n    { device: props.device.device }\n  );\n\n  if (props.shouldStartBundler) {\n    logProjectLogsLocation();\n  } else {\n    await manager.stopAsync();\n  }\n}\n\nasync function installAppAsync(androidProjectRoot: string, props: ResolvedOptions) {\n  // Find the APK file path\n  const apkFile = await resolveInstallApkNameAsync(props.device.device, props);\n\n  if (apkFile) {\n    // Attempt to install the APK from the file path\n    const binaryPath = path.join(props.apkVariantDirectory, apkFile);\n    debug('Installing:', binaryPath);\n    await props.device.installAppAsync(binaryPath);\n  } else {\n    // If we cannot resolve the APK file path then we can attempt to install using Gradle.\n    // This offers more advanced resolution that we may not have first class support for.\n    Log.log('› Failed to locate binary file, installing with Gradle...');\n    await installAsync(androidProjectRoot, {\n      variant: props.variant ?? 'debug',\n      appName: props.appName ?? 'app',\n      port: props.port,\n    });\n  }\n}\n"], "names": ["runAndroidAsync", "debug", "require", "projectRoot", "install", "options", "setNodeEnv", "variant", "load", "ensureNativeProjectAsync", "platform", "props", "resolveOptionsAsync", "packageName", "Log", "log", "androidProjectRoot", "path", "join", "assembleAsync", "port", "appName", "buildCache", "architectures", "shouldStartBundler", "ensurePortAvailabilityAsync", "manager", "startBundlerAsync", "scheme", "getSchemesForAndroidAsync", "headless", "installAppAsync", "getDefaultDevServer", "openCustomRuntimeAsync", "applicationId", "device", "logProjectLogsLocation", "stopAsync", "apkFile", "resolveInstallApkNameAsync", "binaryPath", "apkVariantDirectory", "installAsync"], "mappings": "AAAA;;;;+BAes<PERSON>,iBAAe;;aAAfA,eAAe;;;8DAfpB,MAAM;;;;;;uCAEoB,yBAAyB;gCACN,kBAAkB;qBAC5D,WAAW;wBACa,sCAAsC;yBACvD,qBAAqB;sBACJ,kBAAkB;wBACpB,oBAAoB;qCACrB,wBAAwB;uBAC1B,UAAU;8BACf,iBAAiB;;;;;;AAEnD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC,AAAC;AAE5C,eAAeF,eAAe,CAACG,WAAmB,EAAE,EAAEC,OAAO,CAAA,EAAE,GAAGC,OAAO,EAAW,EAAE;QA8BjF,GAA8C;IA7BxD,sEAAsE;IACtEC,IAAAA,QAAU,WAAA,EAACD,OAAO,CAACE,OAAO,KAAK,SAAS,GAAG,YAAY,GAAG,aAAa,CAAC,CAAC;IACzEL,OAAO,CAAC,WAAW,CAAC,CAACM,IAAI,CAACL,WAAW,CAAC,CAAC;IAEvC,MAAMM,IAAAA,oBAAwB,yBAAA,EAACN,WAAW,EAAE;QAAEO,QAAQ,EAAE,SAAS;QAAEN,OAAO;KAAE,CAAC,CAAC;IAE9E,MAAMO,KAAK,GAAG,MAAMC,IAAAA,eAAmB,oBAAA,EAACT,WAAW,EAAEE,OAAO,CAAC,AAAC;IAE9DJ,KAAK,CAAC,gBAAgB,GAAGU,KAAK,CAACE,WAAW,CAAC,CAAC;IAC5CC,IAAG,IAAA,CAACC,GAAG,CAAC,mBAAmB,CAAC,CAAC;IAE7B,MAAMC,kBAAkB,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACf,WAAW,EAAE,SAAS,CAAC,AAAC;IAE7D,MAAMgB,IAAAA,OAAa,cAAA,EAACH,kBAAkB,EAAE;QACtCT,OAAO,EAAEI,KAAK,CAACJ,OAAO;QACtBa,IAAI,EAAET,KAAK,CAACS,IAAI;QAChBC,OAAO,EAAEV,KAAK,CAACU,OAAO;QACtBC,UAAU,EAAEX,KAAK,CAACW,UAAU;QAC5BC,aAAa,EAAEZ,KAAK,CAACY,aAAa;KACnC,CAAC,CAAC;IAEH,uDAAuD;IACvD,IAAIZ,KAAK,CAACa,kBAAkB,IAAI,CAAE,MAAMC,IAAAA,KAA2B,4BAAA,EAACtB,WAAW,EAAEQ,KAAK,CAAC,AAAC,EAAE;QACxFA,KAAK,CAACa,kBAAkB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED,MAAME,OAAO,GAAG,MAAMC,IAAAA,aAAiB,kBAAA,EAACxB,WAAW,EAAE;QACnDiB,IAAI,EAAET,KAAK,CAACS,IAAI;QAChB,sEAAsE;QACtEQ,MAAM,EAAE,CAAA,GAA8C,GAA7C,MAAMC,IAAAA,OAAyB,0BAAA,EAAC1B,WAAW,CAAC,SAAM,GAAnD,KAAA,CAAmD,GAAnD,GAA8C,AAAE,CAAC,CAAC,CAAC;QAC3D2B,QAAQ,EAAE,CAACnB,KAAK,CAACa,kBAAkB;KACpC,CAAC,AAAC;IAEH,MAAMO,eAAe,CAACf,kBAAkB,EAAEL,KAAK,CAAC,CAAC;IAEjD,MAAMe,OAAO,CAACM,mBAAmB,EAAE,CAACC,sBAAsB,CACxD,UAAU,EACV;QACEC,aAAa,EAAEvB,KAAK,CAACE,WAAW;KACjC,EACD;QAAEsB,MAAM,EAAExB,KAAK,CAACwB,MAAM,CAACA,MAAM;KAAE,CAChC,CAAC;IAEF,IAAIxB,KAAK,CAACa,kBAAkB,EAAE;QAC5BY,IAAAA,MAAsB,uBAAA,GAAE,CAAC;IAC3B,OAAO;QACL,MAAMV,OAAO,CAACW,SAAS,EAAE,CAAC;IAC5B,CAAC;AACH,CAAC;AAED,eAAeN,eAAe,CAACf,kBAA0B,EAAEL,KAAsB,EAAE;IACjF,yBAAyB;IACzB,MAAM2B,OAAO,GAAG,MAAMC,IAAAA,sBAA0B,2BAAA,EAAC5B,KAAK,CAACwB,MAAM,CAACA,MAAM,EAAExB,KAAK,CAAC,AAAC;IAE7E,IAAI2B,OAAO,EAAE;QACX,gDAAgD;QAChD,MAAME,UAAU,GAAGvB,KAAI,EAAA,QAAA,CAACC,IAAI,CAACP,KAAK,CAAC8B,mBAAmB,EAAEH,OAAO,CAAC,AAAC;QACjErC,KAAK,CAAC,aAAa,EAAEuC,UAAU,CAAC,CAAC;QACjC,MAAM7B,KAAK,CAACwB,MAAM,CAACJ,eAAe,CAACS,UAAU,CAAC,CAAC;IACjD,OAAO;QACL,sFAAsF;QACtF,qFAAqF;QACrF1B,IAAG,IAAA,CAACC,GAAG,CAAC,2DAA2D,CAAC,CAAC;YAE1DJ,QAAa,EACbA,QAAa;QAFxB,MAAM+B,IAAAA,OAAY,aAAA,EAAC1B,kBAAkB,EAAE;YACrCT,OAAO,EAAEI,CAAAA,QAAa,GAAbA,KAAK,CAACJ,OAAO,YAAbI,QAAa,GAAI,OAAO;YACjCU,OAAO,EAAEV,CAAAA,QAAa,GAAbA,KAAK,CAACU,OAAO,YAAbV,QAAa,GAAI,KAAK;YAC/BS,IAAI,EAAET,KAAK,CAACS,IAAI;SACjB,CAAC,CAAC;IACL,CAAC;AACH,CAAC"}