{"version": 3, "sources": ["../../../../src/run/android/resolveGradlePropsAsync.ts"], "sourcesContent": ["import path from 'path';\n\nimport { Device, getDeviceABIsAsync } from '../../start/platforms/android/adb';\nimport { CommandError } from '../../utils/errors';\n\n// Supported ABIs for Android. see https://developer.android.com/ndk/guides/abis\nconst VALID_ARCHITECTURES = ['armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'];\n\nexport type GradleProps = {\n  /** Directory for the APK based on the `variant`. */\n  apkVariantDirectory: string;\n  /** Name of the app, used in the `apkVariantDirectory`. */\n  appName: string;\n  /** First section of the provided `variant`, indicates the last part of the file name for the output APK. */\n  buildType: string;\n  /** Used to assemble the APK, also included in the output APK filename. */\n  flavors?: string[];\n  /** Architectures to build for. */\n  architectures?: string;\n};\n\nfunction assertVariant(variant?: string) {\n  if (variant && typeof variant !== 'string') {\n    throw new CommandError('BAD_ARGS', '--variant must be a string');\n  }\n  return variant ?? 'debug';\n}\n\nexport async function resolveGradlePropsAsync(\n  projectRoot: string,\n  options: { variant?: string; allArch?: boolean },\n  device: Device\n): Promise<GradleProps> {\n  const variant = assertVariant(options.variant);\n  // NOTE(EvanBacon): Why would this be different? Can we get the different name?\n  const appName = 'app';\n\n  const apkDirectory = path.join(projectRoot, 'android', appName, 'build', 'outputs', 'apk');\n\n  // buildDeveloperTrust -> build, developer, trust (where developer, and trust are flavors).\n  // This won't work for non-standard flavor names like \"myFlavor\" would be treated as \"my\", \"flavor\".\n  const [buildType, ...flavors] = variant.split(/(?=[A-Z])/).map((v) => v.toLowerCase());\n  const apkVariantDirectory = path.join(apkDirectory, ...flavors, buildType);\n  const architectures = await getConnectedDeviceABIS(buildType, device, options.allArch);\n\n  return {\n    appName,\n    buildType,\n    flavors,\n    apkVariantDirectory,\n    architectures,\n  };\n}\n\nasync function getConnectedDeviceABIS(\n  buildType: string,\n  device: Device,\n  allArch?: boolean\n): Promise<string> {\n  // Follow the same behavior as iOS, only enable this for debug builds\n  if (allArch || buildType !== 'debug') {\n    return '';\n  }\n\n  const abis = await getDeviceABIsAsync(device);\n\n  const validAbis = abis.filter((abi) => VALID_ARCHITECTURES.includes(abi));\n  return validAbis.filter((abi, i, arr) => arr.indexOf(abi) === i).join(',');\n}\n"], "names": ["resolveGradlePropsAsync", "VALID_ARCHITECTURES", "assert<PERSON><PERSON>t", "variant", "CommandError", "projectRoot", "options", "device", "appName", "apkDirectory", "path", "join", "buildType", "flavors", "split", "map", "v", "toLowerCase", "apkVariantDirectory", "architectures", "getConnectedDeviceABIS", "allArch", "abis", "getDeviceABIsAsync", "validAbis", "filter", "abi", "includes", "i", "arr", "indexOf"], "mappings": "AAAA;;;;+BA4Bs<PERSON>,yBAAuB;;aAAvBA,uBAAuB;;;8DA5B5B,MAAM;;;;;;qBAEoB,mCAAmC;wBACjD,oBAAoB;;;;;;AAEjD,gFAAgF;AAChF,MAAMC,mBAAmB,GAAG;IAAC,aAAa;IAAE,WAAW;IAAE,KAAK;IAAE,QAAQ;CAAC,AAAC;AAe1E,SAASC,aAAa,CAACC,OAAgB,EAAE;IACvC,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC1C,MAAM,IAAIC,OAAY,aAAA,CAAC,UAAU,EAAE,4BAA4B,CAAC,CAAC;IACnE,CAAC;IACD,OAAOD,OAAO,WAAPA,OAAO,GAAI,OAAO,CAAC;AAC5B,CAAC;AAEM,eAAeH,uBAAuB,CAC3CK,WAAmB,EACnBC,OAAgD,EAChDC,MAAc,EACQ;IACtB,MAAMJ,OAAO,GAAGD,aAAa,CAACI,OAAO,CAACH,OAAO,CAAC,AAAC;IAC/C,+EAA+E;IAC/E,MAAMK,OAAO,GAAG,KAAK,AAAC;IAEtB,MAAMC,YAAY,GAAGC,KAAI,EAAA,QAAA,CAACC,IAAI,CAACN,WAAW,EAAE,SAAS,EAAEG,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC,AAAC;IAE3F,2FAA2F;IAC3F,oGAAoG;IACpG,MAAM,CAACI,SAAS,EAAE,GAAGC,OAAO,CAAC,GAAGV,OAAO,CAACW,KAAK,aAAa,CAACC,GAAG,CAAC,CAACC,CAAC,GAAKA,CAAC,CAACC,WAAW,EAAE,CAAC,AAAC;IACvF,MAAMC,mBAAmB,GAAGR,KAAI,EAAA,QAAA,CAACC,IAAI,CAACF,YAAY,KAAKI,OAAO,EAAED,SAAS,CAAC,AAAC;IAC3E,MAAMO,aAAa,GAAG,MAAMC,sBAAsB,CAACR,SAAS,EAAEL,MAAM,EAAED,OAAO,CAACe,OAAO,CAAC,AAAC;IAEvF,OAAO;QACLb,OAAO;QACPI,SAAS;QACTC,OAAO;QACPK,mBAAmB;QACnBC,aAAa;KACd,CAAC;AACJ,CAAC;AAED,eAAeC,sBAAsB,CACnCR,SAAiB,EACjBL,MAAc,EACdc,OAAiB,EACA;IACjB,qEAAqE;IACrE,IAAIA,OAAO,IAAIT,SAAS,KAAK,OAAO,EAAE;QACpC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,MAAMU,IAAI,GAAG,MAAMC,IAAAA,IAAkB,mBAAA,EAAChB,MAAM,CAAC,AAAC;IAE9C,MAAMiB,SAAS,GAAGF,IAAI,CAACG,MAAM,CAAC,CAACC,GAAG,GAAKzB,mBAAmB,CAAC0B,QAAQ,CAACD,GAAG,CAAC,CAAC,AAAC;IAC1E,OAAOF,SAAS,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEE,CAAC,EAAEC,GAAG,GAAKA,GAAG,CAACC,OAAO,CAACJ,GAAG,CAAC,KAAKE,CAAC,CAAC,CAACjB,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7E,CAAC"}