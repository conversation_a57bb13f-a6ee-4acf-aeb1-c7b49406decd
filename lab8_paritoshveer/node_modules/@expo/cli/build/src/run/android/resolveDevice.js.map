{"version": 3, "sources": ["../../../../src/run/android/resolveDevice.ts"], "sourcesContent": ["import { AndroidDeviceManager } from '../../start/platforms/android/AndroidDeviceManager';\nimport { logDeviceArgument } from '../hints';\n\nconst debug = require('debug')('expo:android:resolveDevice');\n\n/** Given a `device` argument from the CLI, parse and prompt our way to a usable device for building. */\nexport async function resolveDeviceAsync(device?: string | boolean) {\n  if (!device) {\n    const manager = await AndroidDeviceManager.resolveAsync();\n    debug(`Resolved default device (name: ${manager.device.name}, pid: ${manager.device.pid})`);\n    return manager;\n  }\n\n  debug(`Resolving device from argument: ${device}`);\n  const manager =\n    device === true\n      ? // `--device` (no props after)\n        await AndroidDeviceManager.resolveAsync({ shouldPrompt: true })\n      : // `--device <name>`\n        await AndroidDeviceManager.resolveFromNameAsync(device);\n  logDeviceArgument(manager.device.name);\n  return manager;\n}\n"], "names": ["resolveDeviceAsync", "debug", "require", "device", "manager", "AndroidDeviceManager", "resolveAsync", "name", "pid", "should<PERSON>rompt", "resolveFromNameAsync", "logDeviceArgument"], "mappings": "AAAA;;;;+BA<PERSON><PERSON>,oBAAkB;;aAAlBA,kBAAkB;;sCANH,oDAAoD;uBACvD,UAAU;AAE5C,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC,AAAC;AAGtD,eAAeF,kBAAkB,CAACG,MAAyB,EAAE;IAClE,IAAI,CAACA,MAAM,EAAE;QACX,MAAMC,OAAO,GAAG,MAAMC,qBAAoB,qBAAA,CAACC,YAAY,EAAE,AAAC;QAC1DL,KAAK,CAAC,CAAC,+BAA+B,EAAEG,OAAO,CAACD,MAAM,CAACI,IAAI,CAAC,OAAO,EAAEH,OAAO,CAACD,MAAM,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,OAAOJ,OAAO,CAAC;IACjB,CAAC;IAEDH,KAAK,CAAC,CAAC,gCAAgC,EAAEE,MAAM,CAAC,CAAC,CAAC,CAAC;IACnD,MAAMC,QAAO,GACXD,MAAM,KAAK,IAAI,GAEX,MAAME,qBAAoB,qBAAA,CAACC,YAAY,CAAC;QAAEG,YAAY,EAAE,IAAI;KAAE,CAAC,GAE/D,MAAMJ,qBAAoB,qBAAA,CAACK,oBAAoB,CAACP,MAAM,CAAC,AAAC;IAC9DQ,IAAAA,MAAiB,kBAAA,EAACP,QAAO,CAACD,MAAM,CAACI,IAAI,CAAC,CAAC;IACvC,OAAOH,QAAO,CAAC;AACjB,CAAC"}