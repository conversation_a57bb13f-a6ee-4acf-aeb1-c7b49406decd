{"version": 3, "sources": ["../../../../src/run/android/resolveInstallApkName.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\nimport { GradleProps } from './resolveGradlePropsAsync';\nimport { Device, DeviceABI, getDeviceABIsAsync } from '../../start/platforms/android/adb';\n\nconst debug = require('debug')('expo:run:android:resolveInstallApkName') as typeof console.log;\n\nexport async function resolveInstallApkNameAsync(\n  device: Pick<Device, 'name' | 'pid'>,\n  { appName, buildType, flavors, apkVariantDirectory }: GradleProps\n) {\n  const availableCPUs = await getDeviceABIsAsync(device);\n  availableCPUs.push(DeviceABI.universal);\n\n  debug('Supported ABIs: ' + availableCPUs.join(', '));\n  debug('Searching for APK: ' + apkVariantDirectory);\n\n  // Check for cpu specific builds first\n  for (const availableCPU of availableCPUs) {\n    const apkName = getApkFileName(appName, buildType, flavors, availableCPU);\n    const apkPath = path.join(apkVariantDirectory, apkName);\n    debug('Checking for APK at:', apkPath);\n    if (fs.existsSync(apkPath)) {\n      return apkName;\n    }\n  }\n\n  // Otherwise use the default apk named after the variant: app-debug.apk\n  const apkName = getApkFileName(appName, buildType, flavors);\n  const apkPath = path.join(apkVariantDirectory, apkName);\n  debug('Checking for fallback APK at:', apkPath);\n  if (fs.existsSync(apkPath)) {\n    return apkName;\n  }\n\n  return null;\n}\n\nfunction getApkFileName(\n  appName: string,\n  buildType: string,\n  flavors?: string[] | null,\n  cpuArch?: string | null\n) {\n  let apkName = `${appName}-`;\n  if (flavors) {\n    apkName += flavors.reduce((rest, flavor) => `${rest}${flavor}-`, '');\n  }\n  if (cpuArch) {\n    apkName += `${cpuArch}-`;\n  }\n  apkName += `${buildType}.apk`;\n\n  return apkName;\n}\n"], "names": ["resolveInstallApkNameAsync", "debug", "require", "device", "appName", "buildType", "flavors", "apkVariantDirectory", "availableCPUs", "getDeviceABIsAsync", "push", "DeviceABI", "universal", "join", "availableCPU", "apkName", "getApkFileName", "apkPath", "path", "fs", "existsSync", "cpuArch", "reduce", "rest", "flavor"], "mappings": "AAAA;;;;+BAQsBA,4BAA0B;;aAA1BA,0BAA0B;;;8DARjC,IAAI;;;;;;;8DACF,MAAM;;;;;;qBAG+B,mCAAmC;;;;;;AAEzF,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,wCAAwC,CAAC,AAAsB,AAAC;AAExF,eAAeF,0BAA0B,CAC9CG,MAAoC,EACpC,EAAEC,OAAO,CAAA,EAAEC,SAAS,CAAA,EAAEC,OAAO,CAAA,EAAEC,mBAAmB,CAAA,EAAe,EACjE;IACA,MAAMC,aAAa,GAAG,MAAMC,IAAAA,IAAkB,mBAAA,EAACN,MAAM,CAAC,AAAC;IACvDK,aAAa,CAACE,IAAI,CAACC,IAAS,UAAA,CAACC,SAAS,CAAC,CAAC;IAExCX,KAAK,CAAC,kBAAkB,GAAGO,aAAa,CAACK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACrDZ,KAAK,CAAC,qBAAqB,GAAGM,mBAAmB,CAAC,CAAC;IAEnD,sCAAsC;IACtC,KAAK,MAAMO,YAAY,IAAIN,aAAa,CAAE;QACxC,MAAMO,OAAO,GAAGC,cAAc,CAACZ,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEQ,YAAY,CAAC,AAAC;QAC1E,MAAMG,OAAO,GAAGC,KAAI,EAAA,QAAA,CAACL,IAAI,CAACN,mBAAmB,EAAEQ,OAAO,CAAC,AAAC;QACxDd,KAAK,CAAC,sBAAsB,EAAEgB,OAAO,CAAC,CAAC;QACvC,IAAIE,GAAE,EAAA,QAAA,CAACC,UAAU,CAACH,OAAO,CAAC,EAAE;YAC1B,OAAOF,OAAO,CAAC;QACjB,CAAC;IACH,CAAC;IAED,uEAAuE;IACvE,MAAMA,QAAO,GAAGC,cAAc,CAACZ,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC,AAAC;IAC5D,MAAMW,QAAO,GAAGC,KAAI,EAAA,QAAA,CAACL,IAAI,CAACN,mBAAmB,EAAEQ,QAAO,CAAC,AAAC;IACxDd,KAAK,CAAC,+BAA+B,EAAEgB,QAAO,CAAC,CAAC;IAChD,IAAIE,GAAE,EAAA,QAAA,CAACC,UAAU,CAACH,QAAO,CAAC,EAAE;QAC1B,OAAOF,QAAO,CAAC;IACjB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAASC,cAAc,CACrBZ,OAAe,EACfC,SAAiB,EACjBC,OAAyB,EACzBe,OAAuB,EACvB;IACA,IAAIN,OAAO,GAAG,CAAC,EAAEX,OAAO,CAAC,CAAC,CAAC,AAAC;IAC5B,IAAIE,OAAO,EAAE;QACXS,OAAO,IAAIT,OAAO,CAACgB,MAAM,CAAC,CAACC,IAAI,EAAEC,MAAM,GAAK,CAAC,EAAED,IAAI,CAAC,EAAEC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACvE,CAAC;IACD,IAAIH,OAAO,EAAE;QACXN,OAAO,IAAI,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACDN,OAAO,IAAI,CAAC,EAAEV,SAAS,CAAC,IAAI,CAAC,CAAC;IAE9B,OAAOU,OAAO,CAAC;AACjB,CAAC"}