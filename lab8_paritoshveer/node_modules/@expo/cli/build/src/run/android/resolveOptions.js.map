{"version": 3, "sources": ["../../../../src/run/android/resolveOptions.ts"], "sourcesContent": ["import { resolveDeviceAsync } from './resolveDevice';\nimport { GradleProps, resolveGradlePropsAsync } from './resolveGradlePropsAsync';\nimport { LaunchProps, resolveLaunchPropsAsync } from './resolveLaunchProps';\nimport { AndroidDeviceManager } from '../../start/platforms/android/AndroidDeviceManager';\nimport { BundlerProps, resolveBundlerPropsAsync } from '../resolveBundlerProps';\n\nexport type Options = {\n  variant?: string;\n  device?: boolean | string;\n  port?: number;\n  bundler?: boolean;\n  install?: boolean;\n  buildCache?: boolean;\n  allArch?: boolean;\n};\n\nexport type ResolvedOptions = GradleProps &\n  BundlerProps &\n  LaunchProps & {\n    variant: string;\n    buildCache: boolean;\n    device: AndroidDeviceManager;\n    install: boolean;\n    architectures?: string;\n  };\n\nexport async function resolveOptionsAsync(\n  projectRoot: string,\n  options: Options\n): Promise<ResolvedOptions> {\n  // Resolve the device before the gradle props because we need the device to be running to get the ABI.\n  const device = await resolveDeviceAsync(options.device);\n\n  return {\n    ...(await resolveBundlerPropsAsync(projectRoot, options)),\n    ...(await resolveGradlePropsAsync(projectRoot, options, device.device)),\n    ...(await resolveLaunchPropsAsync(projectRoot)),\n    variant: options.variant ?? 'debug',\n    // Resolve the device based on the provided device id or prompt\n    // from a list of devices (connected or simulated) that are filtered by the scheme.\n    device,\n    buildCache: !!options.buildCache,\n    install: !!options.install,\n  };\n}\n"], "names": ["resolveOptionsAsync", "projectRoot", "options", "device", "resolveDeviceAsync", "resolveBundlerPropsAsync", "resolveGradlePropsAsync", "resolveLaunchPropsAsync", "variant", "buildCache", "install"], "mappings": "AAAA;;;;+BA0Bs<PERSON>,qBAAmB;;aAAnBA,mBAAmB;;+BA1B<PERSON>,iBAAiB;yCACC,2BAA2B;oCAC3B,sBAAsB;qCAEpB,wBAAwB;AAsBxE,eAAeA,mBAAmB,CACvCC,WAAmB,EACnBC,OAAgB,EACU;IAC1B,sGAAsG;IACtG,MAAMC,MAAM,GAAG,MAAMC,IAAAA,cAAkB,mBAAA,EAACF,OAAO,CAACC,MAAM,CAAC,AAAC;QAM7CD,QAAe;IAJ1B,OAAO;QACL,GAAI,MAAMG,IAAAA,oBAAwB,yBAAA,EAACJ,WAAW,EAAEC,OAAO,CAAC;QACxD,GAAI,MAAMI,IAAAA,wBAAuB,wBAAA,EAACL,WAAW,EAAEC,OAAO,EAAEC,MAAM,CAACA,MAAM,CAAC;QACtE,GAAI,MAAMI,IAAAA,mBAAuB,wBAAA,EAACN,WAAW,CAAC;QAC9CO,OAAO,EAAEN,CAAAA,QAAe,GAAfA,OAAO,CAACM,OAAO,YAAfN,QAAe,GAAI,OAAO;QACnC,+DAA+D;QAC/D,mFAAmF;QACnFC,MAAM;QACNM,UAAU,EAAE,CAAC,CAACP,OAAO,CAACO,UAAU;QAChCC,OAAO,EAAE,CAAC,CAACR,OAAO,CAACQ,OAAO;KAC3B,CAAC;AACJ,CAAC"}