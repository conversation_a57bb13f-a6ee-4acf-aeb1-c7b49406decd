{"version": 3, "sources": ["../../../src/customize/typescript.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\n\nimport { Log } from '../log';\n\nexport async function typescript(projectRoot: string) {\n  const { TypeScriptProjectPrerequisite } = await import(\n    '../start/doctor/typescript/TypeScriptProjectPrerequisite.js'\n  );\n  const { MetroBundlerDevServer } = await import('../start/server/metro/MetroBundlerDevServer.js');\n  const { getPlatformBundlers } = await import('../start/server/platformBundlers.js');\n\n  try {\n    await new TypeScriptProjectPrerequisite(projectRoot).bootstrapAsync();\n  } catch (error: any) {\n    // Ensure the process doesn't fail if the TypeScript check fails.\n    // This could happen during the install.\n    Log.log();\n    Log.exception(error);\n    return;\n  }\n\n  const { exp } = getConfig(projectRoot, { skipSDKVersionRequirement: true });\n  await new MetroBundlerDevServer(projectRoot, getPlatformBundlers(projectRoot, exp), {\n    isDevClient: true,\n  }).startTypeScriptServices();\n}\n"], "names": ["typescript", "projectRoot", "TypeScriptProjectPrerequisite", "MetroBundlerDevServer", "getPlatformBundlers", "bootstrapAsync", "error", "Log", "log", "exception", "exp", "getConfig", "skipSDKVersionRequirement", "isDevClient", "startTypeScriptServices"], "mappings": "AAAA;;;;+BAIsBA,YAAU;;aAAVA,UAAU;;;yBAJN,cAAc;;;;;;qBAEpB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,eAAeA,UAAU,CAACC,WAAmB,EAAE;IACpD,MAAM,EAAEC,6BAA6B,CAAA,EAAE,GAAG,MAAM,iEAAA,OAAM,CACpD,6DAA6D,GAC9D,AAAC;IACF,MAAM,EAAEC,qBAAqB,CAAA,EAAE,GAAG,MAAM,iEAAA,OAAM,CAAC,gDAAgD,GAAC,AAAC;IACjG,MAAM,EAAEC,mBAAmB,CAAA,EAAE,GAAG,MAAM,iEAAA,OAAM,CAAC,qCAAqC,GAAC,AAAC;IAEpF,IAAI;QACF,MAAM,IAAIF,6BAA6B,CAACD,WAAW,CAAC,CAACI,cAAc,EAAE,CAAC;IACxE,EAAE,OAAOC,KAAK,EAAO;QACnB,iEAAiE;QACjE,wCAAwC;QACxCC,IAAG,IAAA,CAACC,GAAG,EAAE,CAAC;QACVD,IAAG,IAAA,CAACE,SAAS,CAACH,KAAK,CAAC,CAAC;QACrB,OAAO;IACT,CAAC;IAED,MAAM,EAAEI,GAAG,CAAA,EAAE,GAAGC,IAAAA,OAAS,EAAA,UAAA,EAACV,WAAW,EAAE;QAAEW,yBAAyB,EAAE,IAAI;KAAE,CAAC,AAAC;IAC5E,MAAM,IAAIT,qBAAqB,CAACF,WAAW,EAAEG,mBAAmB,CAACH,WAAW,EAAES,GAAG,CAAC,EAAE;QAClFG,WAAW,EAAE,IAAI;KAClB,CAAC,CAACC,uBAAuB,EAAE,CAAC;AAC/B,CAAC"}