{"version": 3, "sources": ["../../../src/customize/templates.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport prompt, { ExpoChoice } from '../utils/prompts';\n\nconst debug = require('debug')('expo:customize:templates');\n\nexport type DestinationResolutionProps = {\n  /** Web 'public' folder path (defaults to `/web`). This technically can be changed but shouldn't be. */\n  webStaticPath: string;\n};\n\nfunction importFromExpoWebpackConfig(projectRoot: string, folder: string, moduleId: string) {\n  try {\n    const filePath = resolveFrom(projectRoot, `@expo/webpack-config/${folder}/${moduleId}`);\n    debug(`Using @expo/webpack-config template for \"${moduleId}\": ${filePath}`);\n    return filePath;\n  } catch {\n    debug(`@expo/webpack-config template for \"${moduleId}\" not found, falling back on @expo/cli`);\n  }\n  return importFromVendor(projectRoot, moduleId);\n}\n\nfunction importFromVendor(projectRoot: string, moduleId: string) {\n  try {\n    const filePath = resolveFrom(projectRoot, '@expo/cli/static/template/' + moduleId);\n    debug(`Using @expo/cli template for \"${moduleId}\": ${filePath}`);\n    return filePath;\n  } catch {\n    // For dev mode, testing and other cases where @expo/cli is not installed\n    const filePath = require.resolve(`@expo/cli/static/template/${moduleId}`);\n    debug(\n      `Local @expo/cli template for \"${moduleId}\" not found, falling back on template relative to @expo/cli: ${filePath}`\n    );\n\n    return filePath;\n  }\n}\n\nexport const TEMPLATES: {\n  /** Unique ID for easily indexing. */\n  id: string;\n  /** Template file path to copy into the project. */\n  file: (projectRoot: string) => string;\n  /** Output location for the file in the user project. */\n  destination: (props: DestinationResolutionProps) => string;\n  /** List of dependencies to install in the project. These are used inside of the template file. */\n  dependencies: string[];\n\n  /** Custom step for configuring the file. Return true to exit early. */\n  configureAsync?: (projectRoot: string) => Promise<boolean>;\n}[] = [\n  {\n    id: 'babel.config.js',\n    file: (projectRoot) => importFromVendor(projectRoot, 'babel.config.js'),\n    destination: () => 'babel.config.js',\n    dependencies: [\n      // Even though this is installed in `expo`, we should add it for now.\n      'babel-preset-expo',\n    ],\n  },\n  {\n    id: 'metro.config.js',\n    dependencies: ['@expo/metro-config'],\n    destination: () => 'metro.config.js',\n    file: (projectRoot) => importFromVendor(projectRoot, 'metro.config.js'),\n  },\n  {\n    // `tsconfig.json` is special-cased and doesn't follow the template.\n    id: 'tsconfig.json',\n    dependencies: [],\n    destination: () => 'tsconfig.json',\n    file: () => '',\n    configureAsync: async (projectRoot) => {\n      const { typescript } = require('./typescript') as typeof import('./typescript');\n      await typescript(projectRoot);\n      return true;\n    },\n  },\n  {\n    id: '.eslintrc.js',\n    dependencies: [],\n    destination: () => '.eslintrc.js',\n    file: (projectRoot) => importFromVendor(projectRoot, '.eslintrc.js'),\n    configureAsync: async (projectRoot) => {\n      const { ESLintProjectPrerequisite } =\n        require('../lint/ESlintPrerequisite') as typeof import('../lint/ESlintPrerequisite.js');\n      const prerequisite = new ESLintProjectPrerequisite(projectRoot);\n      if (!(await prerequisite.assertAsync())) {\n        await prerequisite.bootstrapAsync();\n      }\n      return false;\n    },\n  },\n  {\n    id: 'serve.json',\n    file: (projectRoot) => importFromExpoWebpackConfig(projectRoot, 'web-default', 'serve.json'),\n    // web/serve.json\n    destination: ({ webStaticPath }) => webStaticPath + '/serve.json',\n    dependencies: [],\n  },\n  {\n    id: 'index.html',\n    file: (projectRoot) => importFromExpoWebpackConfig(projectRoot, 'web-default', 'index.html'),\n    // web/index.html\n    destination: ({ webStaticPath }) => webStaticPath + '/index.html',\n    dependencies: [],\n  },\n  {\n    id: 'webpack.config.js',\n    file: (projectRoot) =>\n      importFromExpoWebpackConfig(projectRoot, 'template', 'webpack.config.js'),\n    destination: () => 'webpack.config.js',\n    dependencies: ['@expo/webpack-config'],\n  },\n];\n\n/** Generate the prompt choices. */\nfunction createChoices(\n  projectRoot: string,\n  props: DestinationResolutionProps\n): ExpoChoice<number>[] {\n  return TEMPLATES.map((template, index) => {\n    const destination = template.destination(props);\n    const localProjectFile = path.resolve(projectRoot, destination);\n    const exists = fs.existsSync(localProjectFile);\n\n    return {\n      title: destination,\n      value: index,\n      description: exists ? chalk.red('This will overwrite the existing file') : undefined,\n    };\n  });\n}\n\n/** Prompt to select templates to add. */\nexport async function selectTemplatesAsync(projectRoot: string, props: DestinationResolutionProps) {\n  const options = createChoices(projectRoot, props);\n\n  const { answer } = await prompt({\n    type: 'multiselect',\n    name: 'answer',\n    message: 'Which files would you like to generate?',\n    hint: '- Space to select. Return to submit',\n    warn: 'File already exists.',\n    limit: options.length,\n    instructions: '',\n    choices: options,\n  });\n  return answer;\n}\n"], "names": ["TEMPLATES", "selectTemplatesAsync", "debug", "require", "importFromExpoWebpackConfig", "projectRoot", "folder", "moduleId", "filePath", "resolveFrom", "importFromVendor", "resolve", "id", "file", "destination", "dependencies", "configure<PERSON><PERSON>", "typescript", "ESLintProjectPrerequisite", "prerequisite", "assertAsync", "bootstrapAsync", "webStaticPath", "createChoices", "props", "map", "template", "index", "localProjectFile", "path", "exists", "fs", "existsSync", "title", "value", "description", "chalk", "red", "undefined", "options", "answer", "prompt", "type", "name", "message", "hint", "warn", "limit", "length", "instructions", "choices"], "mappings": "AAAA;;;;;;;;;;;IAyCaA,SAAS,MAATA,SAAS;IAiGAC,oBAAoB,MAApBA,oBAAoB;;;8DA1IxB,OAAO;;;;;;;8DACV,IAAI;;;;;;;8DACF,MAAM;;;;;;;8DACC,cAAc;;;;;;8DAEH,kBAAkB;;;;;;AAErD,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,0BAA0B,CAAC,AAAC;AAO3D,SAASC,2BAA2B,CAACC,WAAmB,EAAEC,MAAc,EAAEC,QAAgB,EAAE;IAC1F,IAAI;QACF,MAAMC,QAAQ,GAAGC,IAAAA,YAAW,EAAA,QAAA,EAACJ,WAAW,EAAE,CAAC,qBAAqB,EAAEC,MAAM,CAAC,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC,AAAC;QACxFL,KAAK,CAAC,CAAC,yCAAyC,EAAEK,QAAQ,CAAC,GAAG,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5E,OAAOA,QAAQ,CAAC;IAClB,EAAE,OAAM;QACNN,KAAK,CAAC,CAAC,mCAAmC,EAAEK,QAAQ,CAAC,sCAAsC,CAAC,CAAC,CAAC;IAChG,CAAC;IACD,OAAOG,gBAAgB,CAACL,WAAW,EAAEE,QAAQ,CAAC,CAAC;AACjD,CAAC;AAED,SAASG,gBAAgB,CAACL,WAAmB,EAAEE,QAAgB,EAAE;IAC/D,IAAI;QACF,MAAMC,QAAQ,GAAGC,IAAAA,YAAW,EAAA,QAAA,EAACJ,WAAW,EAAE,4BAA4B,GAAGE,QAAQ,CAAC,AAAC;QACnFL,KAAK,CAAC,CAAC,8BAA8B,EAAEK,QAAQ,CAAC,GAAG,EAAEC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjE,OAAOA,QAAQ,CAAC;IAClB,EAAE,OAAM;QACN,yEAAyE;QACzE,MAAMA,SAAQ,GAAGL,OAAO,CAACQ,OAAO,CAAC,CAAC,0BAA0B,EAAEJ,QAAQ,CAAC,CAAC,CAAC,AAAC;QAC1EL,KAAK,CACH,CAAC,8BAA8B,EAAEK,QAAQ,CAAC,6DAA6D,EAAEC,SAAQ,CAAC,CAAC,CACpH,CAAC;QAEF,OAAOA,SAAQ,CAAC;IAClB,CAAC;AACH,CAAC;AAEM,MAAMR,SAAS,GAYhB;IACJ;QACEY,EAAE,EAAE,iBAAiB;QACrBC,IAAI,EAAE,CAACR,WAAW,GAAKK,gBAAgB,CAACL,WAAW,EAAE,iBAAiB,CAAC;QACvES,WAAW,EAAE,IAAM,iBAAiB;QACpCC,YAAY,EAAE;YACZ,qEAAqE;YACrE,mBAAmB;SACpB;KACF;IACD;QACEH,EAAE,EAAE,iBAAiB;QACrBG,YAAY,EAAE;YAAC,oBAAoB;SAAC;QACpCD,WAAW,EAAE,IAAM,iBAAiB;QACpCD,IAAI,EAAE,CAACR,WAAW,GAAKK,gBAAgB,CAACL,WAAW,EAAE,iBAAiB,CAAC;KACxE;IACD;QACE,oEAAoE;QACpEO,EAAE,EAAE,eAAe;QACnBG,YAAY,EAAE,EAAE;QAChBD,WAAW,EAAE,IAAM,eAAe;QAClCD,IAAI,EAAE,IAAM,EAAE;QACdG,cAAc,EAAE,OAAOX,WAAW,GAAK;YACrC,MAAM,EAAEY,UAAU,CAAA,EAAE,GAAGd,OAAO,CAAC,cAAc,CAAC,AAAiC,AAAC;YAChF,MAAMc,UAAU,CAACZ,WAAW,CAAC,CAAC;YAC9B,OAAO,IAAI,CAAC;QACd,CAAC;KACF;IACD;QACEO,EAAE,EAAE,cAAc;QAClBG,YAAY,EAAE,EAAE;QAChBD,WAAW,EAAE,IAAM,cAAc;QACjCD,IAAI,EAAE,CAACR,WAAW,GAAKK,gBAAgB,CAACL,WAAW,EAAE,cAAc,CAAC;QACpEW,cAAc,EAAE,OAAOX,WAAW,GAAK;YACrC,MAAM,EAAEa,yBAAyB,CAAA,EAAE,GACjCf,OAAO,CAAC,4BAA4B,CAAC,AAAkD,AAAC;YAC1F,MAAMgB,YAAY,GAAG,IAAID,yBAAyB,CAACb,WAAW,CAAC,AAAC;YAChE,IAAI,CAAE,MAAMc,YAAY,CAACC,WAAW,EAAE,AAAC,EAAE;gBACvC,MAAMD,YAAY,CAACE,cAAc,EAAE,CAAC;YACtC,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;KACF;IACD;QACET,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,CAACR,WAAW,GAAKD,2BAA2B,CAACC,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC;QAC5F,iBAAiB;QACjBS,WAAW,EAAE,CAAC,EAAEQ,aAAa,CAAA,EAAE,GAAKA,aAAa,GAAG,aAAa;QACjEP,YAAY,EAAE,EAAE;KACjB;IACD;QACEH,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,CAACR,WAAW,GAAKD,2BAA2B,CAACC,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC;QAC5F,iBAAiB;QACjBS,WAAW,EAAE,CAAC,EAAEQ,aAAa,CAAA,EAAE,GAAKA,aAAa,GAAG,aAAa;QACjEP,YAAY,EAAE,EAAE;KACjB;IACD;QACEH,EAAE,EAAE,mBAAmB;QACvBC,IAAI,EAAE,CAACR,WAAW,GAChBD,2BAA2B,CAACC,WAAW,EAAE,UAAU,EAAE,mBAAmB,CAAC;QAC3ES,WAAW,EAAE,IAAM,mBAAmB;QACtCC,YAAY,EAAE;YAAC,sBAAsB;SAAC;KACvC;CACF,AAAC;AAEF,iCAAiC,GACjC,SAASQ,aAAa,CACpBlB,WAAmB,EACnBmB,KAAiC,EACX;IACtB,OAAOxB,SAAS,CAACyB,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,GAAK;QACxC,MAAMb,WAAW,GAAGY,QAAQ,CAACZ,WAAW,CAACU,KAAK,CAAC,AAAC;QAChD,MAAMI,gBAAgB,GAAGC,KAAI,EAAA,QAAA,CAAClB,OAAO,CAACN,WAAW,EAAES,WAAW,CAAC,AAAC;QAChE,MAAMgB,MAAM,GAAGC,GAAE,EAAA,QAAA,CAACC,UAAU,CAACJ,gBAAgB,CAAC,AAAC;QAE/C,OAAO;YACLK,KAAK,EAAEnB,WAAW;YAClBoB,KAAK,EAAEP,KAAK;YACZQ,WAAW,EAAEL,MAAM,GAAGM,MAAK,EAAA,QAAA,CAACC,GAAG,CAAC,uCAAuC,CAAC,GAAGC,SAAS;SACrF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAGM,eAAerC,oBAAoB,CAACI,WAAmB,EAAEmB,KAAiC,EAAE;IACjG,MAAMe,OAAO,GAAGhB,aAAa,CAAClB,WAAW,EAAEmB,KAAK,CAAC,AAAC;IAElD,MAAM,EAAEgB,MAAM,CAAA,EAAE,GAAG,MAAMC,IAAAA,QAAM,QAAA,EAAC;QAC9BC,IAAI,EAAE,aAAa;QACnBC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,yCAAyC;QAClDC,IAAI,EAAE,qCAAqC;QAC3CC,IAAI,EAAE,sBAAsB;QAC5BC,KAAK,EAAER,OAAO,CAACS,MAAM;QACrBC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAEX,OAAO;KACjB,CAAC,AAAC;IACH,OAAOC,MAAM,CAAC;AAChB,CAAC"}