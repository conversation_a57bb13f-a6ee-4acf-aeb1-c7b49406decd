{"version": 3, "sources": ["../../../../../src/api/graphql/queries/UserQuery.ts"], "sourcesContent": ["import gql from 'graphql-tag';\n\nimport { CurrentUserQuery } from '../../../graphql/generated';\nimport { graphqlClient, withErrorHandlingAsync } from '../client';\n\nexport const UserQuery = {\n  async currentUserAsync(): Promise<CurrentUserQuery['meActor']> {\n    const data = await withErrorHandlingAsync(\n      graphqlClient\n        .query<CurrentUserQuery>(\n          gql`\n            query CurrentUser {\n              meActor {\n                __typename\n                id\n                ... on UserActor {\n                  primaryAccount {\n                    id\n                  }\n                  username\n                }\n                ... on Robot {\n                  firstName\n                }\n                accounts {\n                  id\n                  users {\n                    actor {\n                      id\n                    }\n                    permissions\n                  }\n                }\n              }\n            }\n          `,\n          /* variables */ undefined,\n          {\n            additionalTypenames: ['User', 'SSOUser'],\n          }\n        )\n        .toPromise()\n    );\n\n    return data.meActor;\n  },\n};\n"], "names": ["UserQuery", "currentUserAsync", "data", "withErrorHandlingAsync", "graphqlClient", "query", "gql", "undefined", "additionalTypenames", "to<PERSON>romise", "meActor"], "mappings": "AAAA;;;;+BAKa<PERSON>,WAAS;;aAATA,SAAS;;;8DALN,aAAa;;;;;;wBAGyB,WAAW;;;;;;AAE1D,MAAMA,SAAS,GAAG;IACvB,MAAMC,gBAAgB,IAAyC;QAC7D,MAAMC,IAAI,GAAG,MAAMC,IAAAA,OAAsB,uBAAA,EACvCC,OAAa,cAAA,CACVC,KAAK,CACJC,IAAAA,WAAG,EAAA,QAAA,CAAA,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;UAyBJ,CAAC,EACD,aAAa,GAAGC,SAAS,EACzB;YACEC,mBAAmB,EAAE;gBAAC,MAAM;gBAAE,SAAS;aAAC;SACzC,CACF,CACAC,SAAS,EAAE,CACf,AAAC;QAEF,OAAOP,IAAI,CAACQ,OAAO,CAAC;IACtB,CAAC;CACF,AAAC"}