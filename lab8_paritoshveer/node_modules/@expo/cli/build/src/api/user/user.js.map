{"version": 3, "sources": ["../../../../src/api/user/user.ts"], "sourcesContent": ["import { promises as fs } from 'fs';\nimport gql from 'graphql-tag';\n\nimport UserSettings from './UserSettings';\nimport { getSessionUsingBrowserAuthFlowAsync } from './expoSsoLauncher';\nimport { CurrentUserQuery } from '../../graphql/generated';\nimport * as Log from '../../log';\nimport { getDevelopmentCodeSigningDirectory } from '../../utils/codesigning';\nimport { env } from '../../utils/env';\nimport { getTelemetry } from '../../utils/telemetry';\nimport { getExpoWebsiteBaseUrl } from '../endpoint';\nimport { graphqlClient } from '../graphql/client';\nimport { UserQuery } from '../graphql/queries/UserQuery';\nimport { fetchAsync } from '../rest/client';\n\nexport type Actor = NonNullable<CurrentUserQuery['meActor']>;\n\nlet currentUser: Actor | undefined;\n\nexport const ANONYMOUS_USERNAME = 'anonymous';\n\n/**\n * Resolve the name of the actor, either normal user or robot user.\n * This should be used whenever the \"current user\" needs to be displayed.\n * The display name CANNOT be used as project owner.\n */\nexport function getActorDisplayName(user?: Actor): string {\n  switch (user?.__typename) {\n    case 'User':\n      return user.username;\n    case 'SSOUser':\n      return user.username;\n    case 'Robot':\n      return user.firstName ? `${user.firstName} (robot)` : 'robot';\n    default:\n      return ANONYMOUS_USERNAME;\n  }\n}\n\nexport async function getUserAsync(): Promise<Actor | undefined> {\n  const hasCredentials = UserSettings.getAccessToken() || UserSettings.getSession()?.sessionSecret;\n  if (!env.EXPO_OFFLINE && !currentUser && hasCredentials) {\n    const user = await UserQuery.currentUserAsync();\n    currentUser = user ?? undefined;\n    getTelemetry()?.identify(currentUser);\n  }\n  return currentUser;\n}\n\nexport async function loginAsync(json: {\n  username: string;\n  password: string;\n  otp?: string;\n}): Promise<void> {\n  const res = await fetchAsync('auth/loginAsync', {\n    method: 'POST',\n    body: JSON.stringify(json),\n  });\n  const {\n    data: { sessionSecret },\n  } = await res.json();\n\n  const userData = await fetchUserAsync({ sessionSecret });\n\n  await UserSettings.setSessionAsync({\n    sessionSecret,\n    userId: userData.id,\n    username: userData.username,\n    currentConnection: 'Username-Password-Authentication',\n  });\n}\n\nexport async function ssoLoginAsync(): Promise<void> {\n  const sessionSecret = await getSessionUsingBrowserAuthFlowAsync({\n    expoWebsiteUrl: getExpoWebsiteBaseUrl(),\n  });\n  const userData = await fetchUserAsync({ sessionSecret });\n\n  await UserSettings.setSessionAsync({\n    sessionSecret,\n    userId: userData.id,\n    username: userData.username,\n    currentConnection: 'Browser-Flow-Authentication',\n  });\n}\n\nexport async function logoutAsync(): Promise<void> {\n  currentUser = undefined;\n  await Promise.all([\n    fs.rm(getDevelopmentCodeSigningDirectory(), { recursive: true, force: true }),\n    UserSettings.setSessionAsync(undefined),\n  ]);\n  Log.log('Logged out');\n}\n\nasync function fetchUserAsync({\n  sessionSecret,\n}: {\n  sessionSecret: string;\n}): Promise<{ id: string; username: string }> {\n  const result = await graphqlClient\n    .query(\n      gql`\n        query UserQuery {\n          meUserActor {\n            id\n            username\n          }\n        }\n      `,\n      {},\n      {\n        fetchOptions: {\n          headers: {\n            'expo-session': sessionSecret,\n          },\n        },\n        additionalTypenames: [] /* UserQuery has immutable fields */,\n      }\n    )\n    .toPromise();\n  const { data } = result;\n  return {\n    id: data.meUserActor.id,\n    username: data.meUserActor.username,\n  };\n}\n"], "names": ["ANONYMOUS_USERNAME", "getActorDisplayName", "getUserAsync", "loginAsync", "ssoLoginAsync", "logoutAsync", "currentUser", "user", "__typename", "username", "firstName", "UserSettings", "hasCredentials", "getAccessToken", "getSession", "sessionSecret", "env", "EXPO_OFFLINE", "getTelemetry", "UserQuery", "currentUserAsync", "undefined", "identify", "json", "res", "fetchAsync", "method", "body", "JSON", "stringify", "data", "userData", "fetchUserAsync", "setSessionAsync", "userId", "id", "currentConnection", "getSessionUsingBrowserAuthFlowAsync", "expoWebsiteUrl", "getExpoWebsiteBaseUrl", "Promise", "all", "fs", "rm", "getDevelopmentCodeSigningDirectory", "recursive", "force", "Log", "log", "result", "graphqlClient", "query", "gql", "fetchOptions", "headers", "additionalTypenames", "to<PERSON>romise", "meUserActor"], "mappings": "AAAA;;;;;;;;;;;IAmBaA,kBAAkB,MAAlBA,kBAAkB;IAOfC,mBAAmB,MAAnBA,mBAAmB;IAabC,YAAY,MAAZA,YAAY;IAUZC,UAAU,MAAVA,UAAU;IAuBVC,aAAa,MAAbA,aAAa;IAcbC,WAAW,MAAXA,WAAW;;;yBAtFF,IAAI;;;;;;;8DACnB,aAAa;;;;;;mEAEJ,gBAAgB;iCACW,mBAAmB;2DAElD,WAAW;6BACmB,yBAAyB;qBACxD,iBAAiB;2BACR,uBAAuB;0BACd,aAAa;wBACrB,mBAAmB;2BACvB,8BAA8B;yBAC7B,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI3C,IAAIC,WAAW,AAAmB,AAAC;AAE5B,MAAMN,kBAAkB,GAAG,WAAW,AAAC;AAOvC,SAASC,mBAAmB,CAACM,IAAY,EAAU;IACxD,OAAQA,IAAI,QAAY,GAAhBA,KAAAA,CAAgB,GAAhBA,IAAI,CAAEC,UAAU;QACtB,KAAK,MAAM;YACT,OAAOD,IAAI,CAACE,QAAQ,CAAC;QACvB,KAAK,SAAS;YACZ,OAAOF,IAAI,CAACE,QAAQ,CAAC;QACvB,KAAK,OAAO;YACV,OAAOF,IAAI,CAACG,SAAS,GAAG,CAAC,EAAEH,IAAI,CAACG,SAAS,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC;QAChE;YACE,OAAOV,kBAAkB,CAAC;KAC7B;AACH,CAAC;AAEM,eAAeE,YAAY,GAA+B;QACPS,GAAyB;IAAjF,MAAMC,cAAc,GAAGD,aAAY,QAAA,CAACE,cAAc,EAAE,IAAIF,CAAAA,CAAAA,GAAyB,GAAzBA,aAAY,QAAA,CAACG,UAAU,EAAE,SAAe,GAAxCH,KAAAA,CAAwC,GAAxCA,GAAyB,CAAEI,aAAa,CAAA,AAAC;IACjG,IAAI,CAACC,IAAG,IAAA,CAACC,YAAY,IAAI,CAACX,WAAW,IAAIM,cAAc,EAAE;YAGvDM,IAAc;QAFd,MAAMX,IAAI,GAAG,MAAMY,UAAS,UAAA,CAACC,gBAAgB,EAAE,AAAC;QAChDd,WAAW,GAAGC,IAAI,WAAJA,IAAI,GAAIc,SAAS,CAAC;QAChCH,CAAAA,IAAc,GAAdA,IAAAA,UAAY,aAAA,GAAE,SAAU,GAAxBA,KAAAA,CAAwB,GAAxBA,IAAc,CAAEI,QAAQ,CAAChB,WAAW,CAAC,CAAC;IACxC,CAAC;IACD,OAAOA,WAAW,CAAC;AACrB,CAAC;AAEM,eAAeH,UAAU,CAACoB,IAIhC,EAAiB;IAChB,MAAMC,GAAG,GAAG,MAAMC,IAAAA,QAAU,WAAA,EAAC,iBAAiB,EAAE;QAC9CC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACN,IAAI,CAAC;KAC3B,CAAC,AAAC;IACH,MAAM,EACJO,IAAI,EAAE,EAAEf,aAAa,CAAA,EAAE,CAAA,IACxB,GAAG,MAAMS,GAAG,CAACD,IAAI,EAAE,AAAC;IAErB,MAAMQ,QAAQ,GAAG,MAAMC,cAAc,CAAC;QAAEjB,aAAa;KAAE,CAAC,AAAC;IAEzD,MAAMJ,aAAY,QAAA,CAACsB,eAAe,CAAC;QACjClB,aAAa;QACbmB,MAAM,EAAEH,QAAQ,CAACI,EAAE;QACnB1B,QAAQ,EAAEsB,QAAQ,CAACtB,QAAQ;QAC3B2B,iBAAiB,EAAE,kCAAkC;KACtD,CAAC,CAAC;AACL,CAAC;AAEM,eAAehC,aAAa,GAAkB;IACnD,MAAMW,aAAa,GAAG,MAAMsB,IAAAA,gBAAmC,oCAAA,EAAC;QAC9DC,cAAc,EAAEC,IAAAA,SAAqB,sBAAA,GAAE;KACxC,CAAC,AAAC;IACH,MAAMR,QAAQ,GAAG,MAAMC,cAAc,CAAC;QAAEjB,aAAa;KAAE,CAAC,AAAC;IAEzD,MAAMJ,aAAY,QAAA,CAACsB,eAAe,CAAC;QACjClB,aAAa;QACbmB,MAAM,EAAEH,QAAQ,CAACI,EAAE;QACnB1B,QAAQ,EAAEsB,QAAQ,CAACtB,QAAQ;QAC3B2B,iBAAiB,EAAE,6BAA6B;KACjD,CAAC,CAAC;AACL,CAAC;AAEM,eAAe/B,WAAW,GAAkB;IACjDC,WAAW,GAAGe,SAAS,CAAC;IACxB,MAAMmB,OAAO,CAACC,GAAG,CAAC;QAChBC,GAAE,EAAA,SAAA,CAACC,EAAE,CAACC,IAAAA,YAAkC,mCAAA,GAAE,EAAE;YAAEC,SAAS,EAAE,IAAI;YAAEC,KAAK,EAAE,IAAI;SAAE,CAAC;QAC7EnC,aAAY,QAAA,CAACsB,eAAe,CAACZ,SAAS,CAAC;KACxC,CAAC,CAAC;IACH0B,IAAG,CAACC,GAAG,CAAC,YAAY,CAAC,CAAC;AACxB,CAAC;AAED,eAAehB,cAAc,CAAC,EAC5BjB,aAAa,CAAA,EAGd,EAA6C;IAC5C,MAAMkC,MAAM,GAAG,MAAMC,OAAa,cAAA,CAC/BC,KAAK,CACJC,IAAAA,WAAG,EAAA,QAAA,CAAA,CAAC;;;;;;;MAOJ,CAAC,EACD,EAAE,EACF;QACEC,YAAY,EAAE;YACZC,OAAO,EAAE;gBACP,cAAc,EAAEvC,aAAa;aAC9B;SACF;QACDwC,mBAAmB,EAAE,EAAE;KACxB,CACF,CACAC,SAAS,EAAE,AAAC;IACf,MAAM,EAAE1B,IAAI,CAAA,EAAE,GAAGmB,MAAM,AAAC;IACxB,OAAO;QACLd,EAAE,EAAEL,IAAI,CAAC2B,WAAW,CAACtB,EAAE;QACvB1B,QAAQ,EAAEqB,IAAI,CAAC2B,WAAW,CAAChD,QAAQ;KACpC,CAAC;AACJ,CAAC"}