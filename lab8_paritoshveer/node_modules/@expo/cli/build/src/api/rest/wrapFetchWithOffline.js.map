{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithOffline.ts"], "sourcesContent": ["import { FetchLike } from './client.types';\nimport { env } from '../../utils/env';\n\nconst debug = require('debug')('expo:api:fetch:offline') as typeof console.log;\n\n/** Wrap fetch with support for `EXPO_OFFLINE` to disable network requests. */\nexport function wrapFetchWithOffline(fetchFunction: FetchLike): FetchLike {\n  // NOTE(EvanBacon): DO NOT RETURN AN ASYNC WRAPPER. THIS BREAKS LOADING INDICATORS.\n  return function fetchWithOffline(url, options = {}) {\n    if (env.EXPO_OFFLINE) {\n      debug('Skipping network request: ' + url);\n      options.timeout = 1;\n    }\n    return fetchFunction(url, options);\n  };\n}\n"], "names": ["wrapFetchWithOffline", "debug", "require", "fetchFunction", "fetchWithOffline", "url", "options", "env", "EXPO_OFFLINE", "timeout"], "mappings": "AAAA;;;;+BAMgBA,sBAAoB;;aAApBA,oBAAoB;;qBALhB,iBAAiB;AAErC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,wBAAwB,CAAC,AAAsB,AAAC;AAGxE,SAASF,oBAAoB,CAACG,aAAwB,EAAa;IACxE,mFAAmF;IACnF,OAAO,SAASC,gBAAgB,CAACC,GAAG,EAAEC,OAAO,GAAG,EAAE,EAAE;QAClD,IAAIC,IAAG,IAAA,CAACC,YAAY,EAAE;YACpBP,KAAK,CAAC,4BAA4B,GAAGI,GAAG,CAAC,CAAC;YAC1CC,OAAO,CAACG,OAAO,GAAG,CAAC,CAAC;QACtB,CAAC;QACD,OAAON,aAAa,CAACE,GAAG,EAAEC,OAAO,CAAC,CAAC;IACrC,CAAC,CAAC;AACJ,CAAC"}