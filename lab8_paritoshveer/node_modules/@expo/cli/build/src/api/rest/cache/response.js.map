{"version": 3, "sources": ["../../../../../src/api/rest/cache/response.ts"], "sourcesContent": ["import type { CacheObject } from 'cacache';\nimport { BodyInit, Response, ResponseInit } from 'node-fetch';\n\nconst responseInternalSymbol = Object.getOwnPropertySymbols(new Response())[1];\n\nexport class NFCResponse extends Response {\n  constructor(\n    bodyStream?: BodyInit,\n    metaData?: ResponseInit,\n    public ejectFromCache: () => Promise<[CacheObject, CacheObject]> = function ejectFromCache(\n      this: any\n    ) {\n      return this.ejectSelfFromCache();\n    },\n    public fromCache: boolean = false\n  ) {\n    super(bodyStream, metaData);\n  }\n\n  static serializeMetaFromNodeFetchResponse(res: Response) {\n    const metaData = {\n      url: res.url,\n      status: res.status,\n      statusText: res.statusText,\n      headers: res.headers.raw(),\n      size: res.size,\n      timeout: res.timeout,\n      // @ts-ignore\n      counter: res[responseInternalSymbol].counter,\n    };\n\n    return metaData;\n  }\n}\n"], "names": ["NFCResponse", "responseInternalSymbol", "Object", "getOwnPropertySymbols", "Response", "constructor", "bodyStream", "metaData", "ejectFromCache", "ejectSelf<PERSON>rom<PERSON>ache", "fromCache", "serializeMetaFromNodeFetchResponse", "res", "url", "status", "statusText", "headers", "raw", "size", "timeout", "counter"], "mappings": "AAAA;;;;+BA<PERSON>a<PERSON>,aAAW;;aAAXA,WAAW;;;yBAJyB,YAAY;;;;;;AAE7D,MAAMC,sBAAsB,GAAGC,MAAM,CAACC,qBAAqB,CAAC,IAAIC,CAAAA,UAAQ,EAAA,CAAA,SAAA,EAAE,CAAC,CAAC,CAAC,CAAC,AAAC;AAExE,MAAMJ,WAAW,SAASI,UAAQ,EAAA,SAAA;IACvCC,YACEC,UAAqB,EACrBC,QAAuB,EAChBC,cAAyD,GAAG,SAASA,cAAc,GAExF;QACA,OAAO,IAAI,CAACC,kBAAkB,EAAE,CAAC;IACnC,CAAC,EACMC,SAAkB,GAAG,KAAK,CACjC;QACA,KAAK,CAACJ,UAAU,EAAEC,QAAQ,CAAC,CAAC;QAPrBC,sBAAAA,cAAyD,CAAA;QAKzDE,iBAAAA,SAAkB,CAAA;IAG3B;WAEOC,kCAAkC,CAACC,GAAa,EAAE;QACvD,MAAML,QAAQ,GAAG;YACfM,GAAG,EAAED,GAAG,CAACC,GAAG;YACZC,MAAM,EAAEF,GAAG,CAACE,MAAM;YAClBC,UAAU,EAAEH,GAAG,CAACG,UAAU;YAC1BC,OAAO,EAAEJ,GAAG,CAACI,OAAO,CAACC,GAAG,EAAE;YAC1BC,IAAI,EAAEN,GAAG,CAACM,IAAI;YACdC,OAAO,EAAEP,GAAG,CAACO,OAAO;YACpB,aAAa;YACbC,OAAO,EAAER,GAAG,CAACX,sBAAsB,CAAC,CAACmB,OAAO;SAC7C,AAAC;QAEF,OAAOb,QAAQ,CAAC;IAClB;CACD"}