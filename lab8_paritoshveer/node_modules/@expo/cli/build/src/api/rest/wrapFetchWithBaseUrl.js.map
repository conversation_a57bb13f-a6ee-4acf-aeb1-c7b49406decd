{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithBaseUrl.ts"], "sourcesContent": ["import { URL } from 'url';\n\nimport { FetchLike } from './client.types';\n\n// const debug = require('debug')('expo:api:fetch:base') as typeof console.log;\n\n/**\n * Wrap a fetch function with support for a predefined base URL.\n * This implementation works like the browser fetch, applying the input to a prefix base URL.\n */\nexport function wrapFetchWithBaseUrl(fetch: FetchLike, baseUrl: string): FetchLike {\n  // NOTE(EvanBacon): DO NOT RETURN AN ASYNC WRAPPER. THIS BREAKS LOADING INDICATORS.\n  return (url, init) => {\n    if (typeof url !== 'string') {\n      throw new TypeError('Custom fetch function only accepts a string URL as the first parameter');\n    }\n    const parsed = new URL(url, baseUrl);\n    if (init?.searchParams) {\n      parsed.search = init.searchParams.toString();\n    }\n    // debug('fetch:', parsed.toString().trim());\n    return fetch(parsed.toString(), init);\n  };\n}\n"], "names": ["wrapFetchWithBaseUrl", "fetch", "baseUrl", "url", "init", "TypeError", "parsed", "URL", "searchParams", "search", "toString"], "mappings": "AAAA;;;;+BAUgBA,sBAAoB;;aAAp<PERSON>,oBAAoB;;;yBAVhB,KAAK;;;;;;AAUlB,SAASA,oBAAoB,CAACC,KAAgB,EAAEC,OAAe,EAAa;IACjF,mFAAmF;IACnF,OAAO,CAACC,GAAG,EAAEC,IAAI,GAAK;QACpB,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;YAC3B,MAAM,IAAIE,SAAS,CAAC,wEAAwE,CAAC,CAAC;QAChG,CAAC;QACD,MAAMC,MAAM,GAAG,IAAIC,CAAAA,IAAG,EAAA,CAAA,IAAA,CAACJ,GAAG,EAAED,OAAO,CAAC,AAAC;QACrC,IAAIE,IAAI,QAAc,GAAlBA,KAAAA,CAAkB,GAAlBA,IAAI,CAAEI,YAAY,EAAE;YACtBF,MAAM,CAACG,MAAM,GAAGL,IAAI,CAACI,YAAY,CAACE,QAAQ,EAAE,CAAC;QAC/C,CAAC;QACD,6CAA6C;QAC7C,OAAOT,KAAK,CAACK,MAAM,CAACI,QAAQ,EAAE,EAAEN,IAAI,CAAC,CAAC;IACxC,CAAC,CAAC;AACJ,CAAC"}