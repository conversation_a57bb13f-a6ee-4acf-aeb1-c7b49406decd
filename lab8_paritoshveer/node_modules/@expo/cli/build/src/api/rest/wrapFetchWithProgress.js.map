{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithProgress.ts"], "sourcesContent": ["import { FetchLike } from './client.types';\nimport * as Log from '../../log';\nconst debug = require('debug')('expo:api:fetch:progress') as typeof console.log;\n\nexport function wrapFetchWithProgress(fetch: FetchLike): FetchLike {\n  return (url, init) => {\n    return fetch(url, init).then((res) => {\n      if (res.ok && init?.onProgress) {\n        const totalDownloadSize = res.headers.get('Content-Length');\n        const total = Number(totalDownloadSize);\n\n        debug(`Download size: ${totalDownloadSize}`);\n        if (!totalDownloadSize || isNaN(total) || total < 0) {\n          Log.warn(\n            'Progress callback not supported for network request because \"Content-Length\" header missing or invalid in response from URL:',\n            url.toString()\n          );\n          return res;\n        }\n\n        let length = 0;\n\n        debug(`Starting progress animation for ${url}`);\n        res.body.on('data', (chunk) => {\n          length += Buffer.byteLength(chunk);\n          onProgress();\n        });\n\n        res.body.on('end', () => {\n          debug(`Finished progress animation for ${url}`);\n          onProgress();\n        });\n\n        const onProgress = () => {\n          const progress = length / total || 0;\n          init.onProgress?.({\n            progress,\n            total,\n            loaded: length,\n          });\n        };\n      }\n      return res;\n    });\n  };\n}\n"], "names": ["wrapFetchWithProgress", "debug", "require", "fetch", "url", "init", "then", "res", "ok", "onProgress", "totalDownloadSize", "headers", "get", "total", "Number", "isNaN", "Log", "warn", "toString", "length", "body", "on", "chunk", "<PERSON><PERSON><PERSON>", "byteLength", "progress", "loaded"], "mappings": "AAAA;;;;+BAIg<PERSON>,uBAAqB;;aAArBA,qBAAqB;;2DAHhB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAChC,MAAMC,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,AAAsB,AAAC;AAEzE,SAASF,qBAAqB,CAACG,KAAgB,EAAa;IACjE,OAAO,CAACC,GAAG,EAAEC,IAAI,GAAK;QACpB,OAAOF,KAAK,CAACC,GAAG,EAAEC,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,GAAG,GAAK;YACpC,IAAIA,GAAG,CAACC,EAAE,IAAIH,CAAAA,IAAI,QAAY,GAAhBA,KAAAA,CAAgB,GAAhBA,IAAI,CAAEI,UAAU,CAAA,EAAE;gBAC9B,MAAMC,iBAAiB,GAAGH,GAAG,CAACI,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,AAAC;gBAC5D,MAAMC,KAAK,GAAGC,MAAM,CAACJ,iBAAiB,CAAC,AAAC;gBAExCT,KAAK,CAAC,CAAC,eAAe,EAAES,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBAC7C,IAAI,CAACA,iBAAiB,IAAIK,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;oBACnDG,IAAG,CAACC,IAAI,CACN,8HAA8H,EAC9Hb,GAAG,CAACc,QAAQ,EAAE,CACf,CAAC;oBACF,OAAOX,GAAG,CAAC;gBACb,CAAC;gBAED,IAAIY,MAAM,GAAG,CAAC,AAAC;gBAEflB,KAAK,CAAC,CAAC,gCAAgC,EAAEG,GAAG,CAAC,CAAC,CAAC,CAAC;gBAChDG,GAAG,CAACa,IAAI,CAACC,EAAE,CAAC,MAAM,EAAE,CAACC,KAAK,GAAK;oBAC7BH,MAAM,IAAII,MAAM,CAACC,UAAU,CAACF,KAAK,CAAC,CAAC;oBACnCb,UAAU,EAAE,CAAC;gBACf,CAAC,CAAC,CAAC;gBAEHF,GAAG,CAACa,IAAI,CAACC,EAAE,CAAC,KAAK,EAAE,IAAM;oBACvBpB,KAAK,CAAC,CAAC,gCAAgC,EAAEG,GAAG,CAAC,CAAC,CAAC,CAAC;oBAChDK,UAAU,EAAE,CAAC;gBACf,CAAC,CAAC,CAAC;gBAEH,MAAMA,UAAU,GAAG,IAAM;oBACvB,MAAMgB,QAAQ,GAAGN,MAAM,GAAGN,KAAK,IAAI,CAAC,AAAC;oBACrCR,IAAI,CAACI,UAAU,QAIb,GAJFJ,KAAAA,CAIE,GAJFA,IAAI,CAACI,UAAU,CAAG;wBAChBgB,QAAQ;wBACRZ,KAAK;wBACLa,MAAM,EAAEP,MAAM;qBACf,CAAC,CAAC;gBACL,CAAC,AAAC;YACJ,CAAC;YACD,OAAOZ,GAAG,CAAC;QACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC"}