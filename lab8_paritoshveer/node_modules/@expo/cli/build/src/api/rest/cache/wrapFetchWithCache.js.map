{"version": 3, "sources": ["../../../../../src/api/rest/cache/wrapFetchWithCache.ts"], "sourcesContent": ["/**\n * Copyright (c) 2021 Expo, Inc.\n * Copyright (c) 2020 mistval.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/mistval/node-fetch-cache/blob/9c40ddf786b0de22ce521d8bdaa6347bc44dd629/src/index.js#L1\n * But with TypeScript support to fix Jest tests, and removed unused code.\n */\nimport crypto from 'crypto';\nimport FormData from 'form-data';\nimport fs from 'fs';\nimport { Request, RequestInfo, RequestInit, Response } from 'node-fetch';\nimport { URLSearchParams } from 'url';\n\nimport { FileSystemCache } from './FileSystemCache';\nimport { NFCResponse } from './response';\nimport { FetchLike } from '../client.types';\n\nconst CACHE_VERSION = 3;\n\nconst lockPromiseForKey: Record<string, Promise<any>> = {};\nconst unlockFunctionForKey: Record<string, any> = {};\n\n/**\n * Take out a lock. When this function returns (asynchronously),\n * you have the lock.\n * @param {string} key - The key to lock on. Anyone else who\n *   tries to lock on the same key will need to wait for it to\n *   be unlocked.\n */\nasync function lock(key: string) {\n  if (!lockPromiseForKey[key]) {\n    lockPromiseForKey[key] = Promise.resolve();\n  }\n\n  const takeLockPromise = lockPromiseForKey[key];\n  lockPromiseForKey[key] = takeLockPromise.then(\n    () =>\n      new Promise((fulfill) => {\n        unlockFunctionForKey[key] = fulfill;\n      })\n  );\n\n  return takeLockPromise;\n}\n\n/**\n * Release a lock.\n * @param {string} key - The key to release the lock for.\n *   The next person in line will now be able to take out\n *   the lock for that key.\n */\nfunction unlock(key: string) {\n  if (unlockFunctionForKey[key]) {\n    unlockFunctionForKey[key]();\n    delete unlockFunctionForKey[key];\n  }\n}\n\nfunction md5(str: string) {\n  return crypto.createHash('md5').update(str).digest('hex');\n}\n\n// Since the boundary in FormData is random,\n// we ignore it for purposes of calculating\n// the cache key.\nfunction getFormDataCacheKey(formData: FormData) {\n  const cacheKey = { ...formData };\n  const boundary = formData.getBoundary();\n\n  // @ts-expect-error\n  delete cacheKey._boundary;\n\n  const boundaryReplaceRegex = new RegExp(boundary, 'g');\n\n  // @ts-expect-error\n  cacheKey._streams = cacheKey._streams.map((s) => {\n    if (typeof s === 'string') {\n      return s.replace(boundaryReplaceRegex, '');\n    }\n\n    return s;\n  });\n\n  return cacheKey;\n}\n\nfunction getBodyCacheKeyJson(body: any) {\n  if (!body) {\n    return body;\n  }\n  if (typeof body === 'string') {\n    return body;\n  }\n  if (body instanceof URLSearchParams) {\n    return body.toString();\n  }\n  if (body instanceof fs.ReadStream) {\n    return body.path;\n  }\n  if (body.toString && body.toString() === '[object FormData]') {\n    return getFormDataCacheKey(body);\n  }\n  if (body instanceof Buffer) {\n    return body.toString();\n  }\n\n  throw new Error(\n    'Unsupported body type. Supported body types are: string, number, undefined, null, url.URLSearchParams, fs.ReadStream, FormData'\n  );\n}\n\nfunction getRequestCacheKey(req: any) {\n  return {\n    cache: req.cache,\n    credentials: req.credentials,\n    destination: req.destination,\n    headers: req.headers,\n    integrity: req.integrity,\n    method: req.method,\n    redirect: req.redirect,\n    referrer: req.referrer,\n    referrerPolicy: req.referrerPolicy,\n    url: req.url,\n    body: getBodyCacheKeyJson(req.body),\n  };\n}\n\nfunction getCacheKey(requestArguments: any[]) {\n  const resource = requestArguments[0];\n  const init = requestArguments[1] || {};\n\n  const resourceCacheKeyJson =\n    resource instanceof Request ? getRequestCacheKey(resource) : { url: resource };\n\n  const initCacheKeyJson = { ...init };\n\n  // @ts-ignore\n  resourceCacheKeyJson.body = getBodyCacheKeyJson(resourceCacheKeyJson.body);\n  initCacheKeyJson.body = getBodyCacheKeyJson(initCacheKeyJson.body);\n\n  delete initCacheKeyJson.agent;\n\n  return md5(JSON.stringify([resourceCacheKeyJson, initCacheKeyJson, CACHE_VERSION]));\n}\n\nexport function wrapFetchWithCache(\n  fetch: FetchLike,\n  cache: FileSystemCache\n): (url: RequestInfo, init?: RequestInit | undefined) => Promise<Response> {\n  async function getResponse(\n    cache: FileSystemCache,\n    url: RequestInfo,\n    init?: RequestInit | undefined\n  ) {\n    const cacheKey = getCacheKey([url, init]);\n    let cachedValue = await cache.get(cacheKey);\n\n    const ejectSelfFromCache = () => cache.remove(cacheKey);\n\n    if (cachedValue) {\n      return new NFCResponse(\n        cachedValue.bodyStream,\n        cachedValue.metaData,\n        ejectSelfFromCache,\n        true\n      );\n    }\n\n    await lock(cacheKey);\n    try {\n      cachedValue = await cache.get(cacheKey);\n      if (cachedValue) {\n        return new NFCResponse(\n          cachedValue.bodyStream,\n          cachedValue.metaData,\n          ejectSelfFromCache,\n          true\n        );\n      }\n\n      const fetchResponse = await fetch(url, init);\n      const serializedMeta = NFCResponse.serializeMetaFromNodeFetchResponse(fetchResponse);\n\n      const newlyCachedData = await cache.set(\n        cacheKey,\n        // @ts-expect-error\n        fetchResponse.body,\n        serializedMeta\n      );\n\n      return new NFCResponse(\n        newlyCachedData!.bodyStream,\n        newlyCachedData!.metaData,\n        ejectSelfFromCache,\n        false\n      );\n    } finally {\n      unlock(cacheKey);\n    }\n  }\n  return (url: RequestInfo, init?: RequestInit | undefined) => getResponse(cache, url, init);\n}\n"], "names": ["wrapFetchWithCache", "CACHE_VERSION", "lockPromiseFor<PERSON>ey", "unlockFunctionForKey", "lock", "key", "Promise", "resolve", "takeLockPromise", "then", "fulfill", "unlock", "md5", "str", "crypto", "createHash", "update", "digest", "getFormDataCacheKey", "formData", "cache<PERSON>ey", "boundary", "getBoundary", "_boundary", "boundaryReplaceRegex", "RegExp", "_streams", "map", "s", "replace", "getBodyCache<PERSON><PERSON>son", "body", "URLSearchParams", "toString", "fs", "ReadStream", "path", "<PERSON><PERSON><PERSON>", "Error", "getRequestCacheKey", "req", "cache", "credentials", "destination", "headers", "integrity", "method", "redirect", "referrer", "referrerPolicy", "url", "get<PERSON><PERSON><PERSON><PERSON>", "requestArguments", "resource", "init", "resourceCache<PERSON>ey<PERSON>son", "Request", "initCache<PERSON>ey<PERSON>son", "agent", "JSON", "stringify", "fetch", "getResponse", "cachedValue", "get", "ejectSelf<PERSON>rom<PERSON>ache", "remove", "NFCResponse", "bodyStream", "metaData", "fetchResponse", "serializedMeta", "serializeMetaFromNodeFetchResponse", "newlyCachedData", "set"], "mappings": "AAAA;;;;;;;;;CASC,GACD;;;;+BA0IgBA,oBAAkB;;aAAlBA,kBAAkB;;;8DA1If,QAAQ;;;;;;;8DAEZ,IAAI;;;;;;;yBACyC,YAAY;;;;;;;yBACxC,KAAK;;;;;;0BAGT,YAAY;;;;;;AAGxC,MAAMC,aAAa,GAAG,CAAC,AAAC;AAExB,MAAMC,iBAAiB,GAAiC,EAAE,AAAC;AAC3D,MAAMC,oBAAoB,GAAwB,EAAE,AAAC;AAErD;;;;;;CAMC,GACD,eAAeC,IAAI,CAACC,GAAW,EAAE;IAC/B,IAAI,CAACH,iBAAiB,CAACG,GAAG,CAAC,EAAE;QAC3BH,iBAAiB,CAACG,GAAG,CAAC,GAAGC,OAAO,CAACC,OAAO,EAAE,CAAC;IAC7C,CAAC;IAED,MAAMC,eAAe,GAAGN,iBAAiB,CAACG,GAAG,CAAC,AAAC;IAC/CH,iBAAiB,CAACG,GAAG,CAAC,GAAGG,eAAe,CAACC,IAAI,CAC3C,IACE,IAAIH,OAAO,CAAC,CAACI,OAAO,GAAK;YACvBP,oBAAoB,CAACE,GAAG,CAAC,GAAGK,OAAO,CAAC;QACtC,CAAC,CAAC,CACL,CAAC;IAEF,OAAOF,eAAe,CAAC;AACzB,CAAC;AAED;;;;;CAKC,GACD,SAASG,MAAM,CAACN,GAAW,EAAE;IAC3B,IAAIF,oBAAoB,CAACE,GAAG,CAAC,EAAE;QAC7BF,oBAAoB,CAACE,GAAG,CAAC,EAAE,CAAC;QAC5B,OAAOF,oBAAoB,CAACE,GAAG,CAAC,CAAC;IACnC,CAAC;AACH,CAAC;AAED,SAASO,GAAG,CAACC,GAAW,EAAE;IACxB,OAAOC,OAAM,EAAA,QAAA,CAACC,UAAU,CAAC,KAAK,CAAC,CAACC,MAAM,CAACH,GAAG,CAAC,CAACI,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAED,4CAA4C;AAC5C,2CAA2C;AAC3C,iBAAiB;AACjB,SAASC,mBAAmB,CAACC,QAAkB,EAAE;IAC/C,MAAMC,QAAQ,GAAG;QAAE,GAAGD,QAAQ;KAAE,AAAC;IACjC,MAAME,QAAQ,GAAGF,QAAQ,CAACG,WAAW,EAAE,AAAC;IAExC,mBAAmB;IACnB,OAAOF,QAAQ,CAACG,SAAS,CAAC;IAE1B,MAAMC,oBAAoB,GAAG,IAAIC,MAAM,CAACJ,QAAQ,EAAE,GAAG,CAAC,AAAC;IAEvD,mBAAmB;IACnBD,QAAQ,CAACM,QAAQ,GAAGN,QAAQ,CAACM,QAAQ,CAACC,GAAG,CAAC,CAACC,CAAC,GAAK;QAC/C,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;YACzB,OAAOA,CAAC,CAACC,OAAO,CAACL,oBAAoB,EAAE,EAAE,CAAC,CAAC;QAC7C,CAAC;QAED,OAAOI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,OAAOR,QAAQ,CAAC;AAClB,CAAC;AAED,SAASU,mBAAmB,CAACC,IAAS,EAAE;IACtC,IAAI,CAACA,IAAI,EAAE;QACT,OAAOA,IAAI,CAAC;IACd,CAAC;IACD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAOA,IAAI,CAAC;IACd,CAAC;IACD,IAAIA,IAAI,YAAYC,IAAe,EAAA,gBAAA,EAAE;QACnC,OAAOD,IAAI,CAACE,QAAQ,EAAE,CAAC;IACzB,CAAC;IACD,IAAIF,IAAI,YAAYG,GAAE,EAAA,QAAA,CAACC,UAAU,EAAE;QACjC,OAAOJ,IAAI,CAACK,IAAI,CAAC;IACnB,CAAC;IACD,IAAIL,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACE,QAAQ,EAAE,KAAK,mBAAmB,EAAE;QAC5D,OAAOf,mBAAmB,CAACa,IAAI,CAAC,CAAC;IACnC,CAAC;IACD,IAAIA,IAAI,YAAYM,MAAM,EAAE;QAC1B,OAAON,IAAI,CAACE,QAAQ,EAAE,CAAC;IACzB,CAAC;IAED,MAAM,IAAIK,KAAK,CACb,gIAAgI,CACjI,CAAC;AACJ,CAAC;AAED,SAASC,kBAAkB,CAACC,GAAQ,EAAE;IACpC,OAAO;QACLC,KAAK,EAAED,GAAG,CAACC,KAAK;QAChBC,WAAW,EAAEF,GAAG,CAACE,WAAW;QAC5BC,WAAW,EAAEH,GAAG,CAACG,WAAW;QAC5BC,OAAO,EAAEJ,GAAG,CAACI,OAAO;QACpBC,SAAS,EAAEL,GAAG,CAACK,SAAS;QACxBC,MAAM,EAAEN,GAAG,CAACM,MAAM;QAClBC,QAAQ,EAAEP,GAAG,CAACO,QAAQ;QACtBC,QAAQ,EAAER,GAAG,CAACQ,QAAQ;QACtBC,cAAc,EAAET,GAAG,CAACS,cAAc;QAClCC,GAAG,EAAEV,GAAG,CAACU,GAAG;QACZnB,IAAI,EAAED,mBAAmB,CAACU,GAAG,CAACT,IAAI,CAAC;KACpC,CAAC;AACJ,CAAC;AAED,SAASoB,WAAW,CAACC,gBAAuB,EAAE;IAC5C,MAAMC,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC,AAAC;IACrC,MAAME,IAAI,GAAGF,gBAAgB,CAAC,CAAC,CAAC,IAAI,EAAE,AAAC;IAEvC,MAAMG,oBAAoB,GACxBF,QAAQ,YAAYG,UAAO,EAAA,QAAA,GAAGjB,kBAAkB,CAACc,QAAQ,CAAC,GAAG;QAAEH,GAAG,EAAEG,QAAQ;KAAE,AAAC;IAEjF,MAAMI,gBAAgB,GAAG;QAAE,GAAGH,IAAI;KAAE,AAAC;IAErC,aAAa;IACbC,oBAAoB,CAACxB,IAAI,GAAGD,mBAAmB,CAACyB,oBAAoB,CAACxB,IAAI,CAAC,CAAC;IAC3E0B,gBAAgB,CAAC1B,IAAI,GAAGD,mBAAmB,CAAC2B,gBAAgB,CAAC1B,IAAI,CAAC,CAAC;IAEnE,OAAO0B,gBAAgB,CAACC,KAAK,CAAC;IAE9B,OAAO9C,GAAG,CAAC+C,IAAI,CAACC,SAAS,CAAC;QAACL,oBAAoB;QAAEE,gBAAgB;QAAExD,aAAa;KAAC,CAAC,CAAC,CAAC;AACtF,CAAC;AAEM,SAASD,kBAAkB,CAChC6D,KAAgB,EAChBpB,KAAsB,EACmD;IACzE,eAAeqB,WAAW,CACxBrB,KAAsB,EACtBS,GAAgB,EAChBI,IAA8B,EAC9B;QACA,MAAMlC,QAAQ,GAAG+B,WAAW,CAAC;YAACD,GAAG;YAAEI,IAAI;SAAC,CAAC,AAAC;QAC1C,IAAIS,WAAW,GAAG,MAAMtB,KAAK,CAACuB,GAAG,CAAC5C,QAAQ,CAAC,AAAC;QAE5C,MAAM6C,kBAAkB,GAAG,IAAMxB,KAAK,CAACyB,MAAM,CAAC9C,QAAQ,CAAC,AAAC;QAExD,IAAI2C,WAAW,EAAE;YACf,OAAO,IAAII,SAAW,YAAA,CACpBJ,WAAW,CAACK,UAAU,EACtBL,WAAW,CAACM,QAAQ,EACpBJ,kBAAkB,EAClB,IAAI,CACL,CAAC;QACJ,CAAC;QAED,MAAM7D,IAAI,CAACgB,QAAQ,CAAC,CAAC;QACrB,IAAI;YACF2C,WAAW,GAAG,MAAMtB,KAAK,CAACuB,GAAG,CAAC5C,QAAQ,CAAC,CAAC;YACxC,IAAI2C,WAAW,EAAE;gBACf,OAAO,IAAII,SAAW,YAAA,CACpBJ,WAAW,CAACK,UAAU,EACtBL,WAAW,CAACM,QAAQ,EACpBJ,kBAAkB,EAClB,IAAI,CACL,CAAC;YACJ,CAAC;YAED,MAAMK,aAAa,GAAG,MAAMT,KAAK,CAACX,GAAG,EAAEI,IAAI,CAAC,AAAC;YAC7C,MAAMiB,cAAc,GAAGJ,SAAW,YAAA,CAACK,kCAAkC,CAACF,aAAa,CAAC,AAAC;YAErF,MAAMG,eAAe,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CACrCtD,QAAQ,EACR,mBAAmB;YACnBkD,aAAa,CAACvC,IAAI,EAClBwC,cAAc,CACf,AAAC;YAEF,OAAO,IAAIJ,SAAW,YAAA,CACpBM,eAAe,CAAEL,UAAU,EAC3BK,eAAe,CAAEJ,QAAQ,EACzBJ,kBAAkB,EAClB,KAAK,CACN,CAAC;QACJ,SAAU;YACRtD,MAAM,CAACS,QAAQ,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IACD,OAAO,CAAC8B,GAAgB,EAAEI,IAA8B,GAAKQ,WAAW,CAACrB,KAAK,EAAES,GAAG,EAAEI,IAAI,CAAC,CAAC;AAC7F,CAAC"}