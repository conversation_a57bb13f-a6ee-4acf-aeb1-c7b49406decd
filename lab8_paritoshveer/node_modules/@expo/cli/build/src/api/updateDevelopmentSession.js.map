{"version": 3, "sources": ["../../../src/api/updateDevelopmentSession.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config';\nimport os from 'os';\nimport { URLSearchParams } from 'url';\n\nimport { fetchAsync } from './rest/client';\nimport { CommandError } from '../utils/errors';\n\n/** Create the expected session info. */\nexport function createSessionInfo({\n  exp,\n  runtime,\n  url,\n}: {\n  exp: Pick<ExpoConfig, 'name' | 'description' | 'slug' | 'primaryColor'>;\n  runtime: 'native' | 'web';\n  url: string;\n}) {\n  return {\n    session: {\n      description: `${exp.name} on ${os.hostname()}`,\n      hostname: os.hostname(),\n      platform: runtime,\n      config: {\n        // TODO: if icons are specified, upload a url for them too so people can distinguish\n        description: exp.description,\n        name: exp.name,\n        slug: exp.slug,\n        primaryColor: exp.primaryColor,\n      },\n      url,\n      source: 'desktop',\n    },\n  };\n}\n\n/** Send a request to Expo API to keep the 'development session' alive for the provided devices. */\nexport async function updateDevelopmentSessionAsync({\n  deviceIds,\n  exp,\n  runtime,\n  url,\n}: {\n  deviceIds: string[];\n  exp: Pick<ExpoConfig, 'name' | 'description' | 'slug' | 'primaryColor'>;\n  runtime: 'native' | 'web';\n  url: string;\n}) {\n  const searchParams = new URLSearchParams();\n  deviceIds.forEach((id) => {\n    searchParams.append('deviceId', id);\n  });\n\n  const results = await fetchAsync('development-sessions/notify-alive', {\n    searchParams,\n    method: 'POST',\n    body: JSON.stringify({\n      data: createSessionInfo({ exp, runtime, url }),\n    }),\n  });\n\n  if (!results.ok) {\n    throw new CommandError(\n      'API',\n      `Unexpected response when updating the development session on Expo servers: ${results.statusText}.`\n    );\n  }\n}\n\n/** Send a request to Expo API to close the 'development session' for the provided devices. */\nexport async function closeDevelopmentSessionAsync({\n  deviceIds,\n  url,\n}: {\n  deviceIds: string[];\n  url: string;\n}) {\n  const searchParams = new URLSearchParams();\n  deviceIds.forEach((id) => {\n    searchParams.append('deviceId', id);\n  });\n\n  const results = await fetchAsync('development-sessions/notify-close', {\n    searchParams,\n    method: 'POST',\n    body: JSON.stringify({\n      session: { url },\n    }),\n  });\n\n  if (!results.ok) {\n    throw new CommandError(\n      'API',\n      `Unexpected response when closing the development session on Expo servers: ${results.statusText}.`\n    );\n  }\n}\n"], "names": ["createSessionInfo", "updateDevelopmentSessionAsync", "closeDevelopmentSessionAsync", "exp", "runtime", "url", "session", "description", "name", "os", "hostname", "platform", "config", "slug", "primaryColor", "source", "deviceIds", "searchParams", "URLSearchParams", "for<PERSON>ach", "id", "append", "results", "fetchAsync", "method", "body", "JSON", "stringify", "data", "ok", "CommandError", "statusText"], "mappings": "AAAA;;;;;;;;;;;IAQgBA,iBAAiB,MAAjBA,iBAAiB;IA4BXC,6BAA6B,MAA7BA,6BAA6B;IAiC7BC,4BAA4B,MAA5BA,4BAA4B;;;8DApEnC,IAAI;;;;;;;yBACa,KAAK;;;;;;wBAEV,eAAe;wBACb,iBAAiB;;;;;;AAGvC,SAASF,iBAAiB,CAAC,EAChCG,GAAG,CAAA,EACHC,OAAO,CAAA,EACPC,GAAG,CAAA,EAKJ,EAAE;IACD,OAAO;QACLC,OAAO,EAAE;YACPC,WAAW,EAAE,CAAC,EAAEJ,GAAG,CAACK,IAAI,CAAC,IAAI,EAAEC,GAAE,EAAA,QAAA,CAACC,QAAQ,EAAE,CAAC,CAAC;YAC9CA,QAAQ,EAAED,GAAE,EAAA,QAAA,CAACC,QAAQ,EAAE;YACvBC,QAAQ,EAAEP,OAAO;YACjBQ,MAAM,EAAE;gBACN,oFAAoF;gBACpFL,WAAW,EAAEJ,GAAG,CAACI,WAAW;gBAC5BC,IAAI,EAAEL,GAAG,CAACK,IAAI;gBACdK,IAAI,EAAEV,GAAG,CAACU,IAAI;gBACdC,YAAY,EAAEX,GAAG,CAACW,YAAY;aAC/B;YACDT,GAAG;YACHU,MAAM,EAAE,SAAS;SAClB;KACF,CAAC;AACJ,CAAC;AAGM,eAAed,6BAA6B,CAAC,EAClDe,SAAS,CAAA,EACTb,GAAG,CAAA,EACHC,OAAO,CAAA,EACPC,GAAG,CAAA,EAMJ,EAAE;IACD,MAAMY,YAAY,GAAG,IAAIC,CAAAA,IAAe,EAAA,CAAA,gBAAA,EAAE,AAAC;IAC3CF,SAAS,CAACG,OAAO,CAAC,CAACC,EAAE,GAAK;QACxBH,YAAY,CAACI,MAAM,CAAC,UAAU,EAAED,EAAE,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,MAAME,OAAO,GAAG,MAAMC,IAAAA,OAAU,WAAA,EAAC,mCAAmC,EAAE;QACpEN,YAAY;QACZO,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,IAAI,EAAE5B,iBAAiB,CAAC;gBAAEG,GAAG;gBAAEC,OAAO;gBAAEC,GAAG;aAAE,CAAC;SAC/C,CAAC;KACH,CAAC,AAAC;IAEH,IAAI,CAACiB,OAAO,CAACO,EAAE,EAAE;QACf,MAAM,IAAIC,OAAY,aAAA,CACpB,KAAK,EACL,CAAC,2EAA2E,EAAER,OAAO,CAACS,UAAU,CAAC,CAAC,CAAC,CACpG,CAAC;IACJ,CAAC;AACH,CAAC;AAGM,eAAe7B,4BAA4B,CAAC,EACjDc,SAAS,CAAA,EACTX,GAAG,CAAA,EAIJ,EAAE;IACD,MAAMY,YAAY,GAAG,IAAIC,CAAAA,IAAe,EAAA,CAAA,gBAAA,EAAE,AAAC;IAC3CF,SAAS,CAACG,OAAO,CAAC,CAACC,EAAE,GAAK;QACxBH,YAAY,CAACI,MAAM,CAAC,UAAU,EAAED,EAAE,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,MAAME,OAAO,GAAG,MAAMC,IAAAA,OAAU,WAAA,EAAC,mCAAmC,EAAE;QACpEN,YAAY;QACZO,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBrB,OAAO,EAAE;gBAAED,GAAG;aAAE;SACjB,CAAC;KACH,CAAC,AAAC;IAEH,IAAI,CAACiB,OAAO,CAACO,EAAE,EAAE;QACf,MAAM,IAAIC,OAAY,aAAA,CACpB,KAAK,EACL,CAAC,0EAA0E,EAAER,OAAO,CAACS,UAAU,CAAC,CAAC,CAAC,CACnG,CAAC;IACJ,CAAC;AACH,CAAC"}