{"version": 3, "sources": ["../../../src/api/settings.ts"], "sourcesContent": ["// This file represents temporary globals for the CLI when using the API.\n// Settings should be as minimal as possible since they are globals.\nimport chalk from 'chalk';\n\nimport { Log } from '../log';\nimport { env } from '../utils/env';\n\nexport function disableNetwork() {\n  if (env.EXPO_OFFLINE) return;\n  process.env.EXPO_OFFLINE = '1';\n  Log.log(chalk.gray('Networking has been disabled'));\n}\n"], "names": ["disableNetwork", "env", "EXPO_OFFLINE", "process", "Log", "log", "chalk", "gray"], "mappings": "AAAA,yEAAyE;AACzE,oEAAoE;AACpE;;;;+BAKgBA,gBAAc;;aAAdA,cAAc;;;8DALZ,OAAO;;;;;;qBAEL,QAAQ;qBACR,cAAc;;;;;;AAE3B,SAASA,cAAc,GAAG;IAC/B,IAAIC,IAAG,IAAA,CAACC,YAAY,EAAE,OAAO;IAC7BC,OAAO,CAACF,GAAG,CAACC,YAAY,GAAG,GAAG,CAAC;IAC/BE,IAAG,IAAA,CAACC,GAAG,CAACC,MAAK,EAAA,QAAA,CAACC,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC;AACtD,CAAC"}