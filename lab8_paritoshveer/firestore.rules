rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Notes collection rules
    match /notes/{noteId} {
      // Allow read/write only if user is authenticated and owns the note
      allow read, write: if request.auth != null 
        && request.auth.uid == resource.data.userId;
      
      // Allow create only if user is authenticated and sets themselves as owner
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.userId
        && validateNoteData(request.resource.data);
      
      // Allow update only if user owns the note and data is valid
      allow update: if request.auth != null 
        && request.auth.uid == resource.data.userId
        && request.auth.uid == request.resource.data.userId
        && validateNoteData(request.resource.data);
      
      // Allow delete only if user owns the note
      allow delete: if request.auth != null 
        && request.auth.uid == resource.data.userId;
    }
    
    // Search index collection rules (managed by Cloud Functions)
    match /searchIndex/{indexId} {
      // Allow read only if user is authenticated and owns the indexed note
      allow read: if request.auth != null 
        && request.auth.uid == resource.data.userId;
      
      // Only Cloud Functions can write to search index
      allow write: if false;
    }
    
    // User profiles collection (if needed for future features)
    match /users/{userId} {
      // Allow read/write only for the user's own profile
      allow read, write: if request.auth != null 
        && request.auth.uid == userId
        && validateUserData(request.resource.data);
    }
    
    // Helper functions for data validation
    function validateNoteData(data) {
      return data.keys().hasAll(['userId', 'createdAt', 'updatedAt']) &&
             data.userId is string &&
             data.userId.size() > 0 &&
             (data.title is string || !('title' in data)) &&
             (data.content is string || !('content' in data)) &&
             (data.category is string || !('category' in data)) &&
             (data.tags is list || !('tags' in data)) &&
             (data.isArchived is bool || !('isArchived' in data)) &&
             // Validate tags are strings
             (!('tags' in data) || data.tags.size() <= 20) &&
             (!('tags' in data) || validateTags(data.tags)) &&
             // Validate string lengths
             (!('title' in data) || data.title.size() <= 200) &&
             (!('content' in data) || data.content.size() <= 50000) &&
             (!('category' in data) || data.category.size() <= 50);
    }
    
    function validateTags(tags) {
      return tags.size() <= 20 &&
             tags.hasOnly(string) &&
             tags.hasAll(function(tag) { return tag.size() <= 30; });
    }
    
    function validateUserData(data) {
      return data.keys().hasAll(['email']) &&
             data.email is string &&
             data.email.size() > 0 &&
             (data.displayName is string || !('displayName' in data)) &&
             (data.preferences is map || !('preferences' in data));
    }
  }
}
