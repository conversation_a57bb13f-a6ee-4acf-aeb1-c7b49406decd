import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { View, ActivityIndicator, StyleSheet, Alert } from 'react-native';
import NetInfo from '@react-native-community/netinfo';

// Context Providers
import { AuthProvider, useAuth } from './src/hooks/useAuth';
import { NotesProvider } from './src/hooks/useNotes';
import { OfflineSyncProvider } from './src/hooks/useOfflineSync';

// Screens
import Login from './src/screens/Login';
import NotesList from './src/screens/NotesList';
import NoteEditor from './src/screens/NoteEditor';

// Firebase
import { setNetworkState } from './src/services/firebase';

const Stack = createStackNavigator();

// Main App Navigation
const AppNavigator = () => {
  const { user, loading } = useAuth();
  const [networkState, setNetworkStateLocal] = useState(true);

  // Monitor network connectivity for offline support
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const isConnected = state.isConnected && state.isInternetReachable;
      setNetworkStateLocal(isConnected);
      setNetworkState(isConnected);
      
      if (!isConnected) {
        Alert.alert(
          'Offline Mode',
          'You are currently offline. Your changes will sync when you reconnect.',
          [{ text: 'OK' }]
        );
      }
    });

    return unsubscribe;
  }, []);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
      </View>
    );
  }

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          cardStyle: { backgroundColor: '#fff' }
        }}
      >
        {user ? (
          // Authenticated user screens
          <>
            <Stack.Screen 
              name="NotesList" 
              component={NotesList}
              options={{ title: 'My Notes' }}
            />
            <Stack.Screen 
              name="NoteEditor" 
              component={NoteEditor}
              options={{ 
                title: 'Edit Note',
                presentation: 'modal'
              }}
            />
          </>
        ) : (
          // Authentication screen
          <Stack.Screen 
            name="Login" 
            component={Login}
            options={{ title: 'Sign In' }}
          />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

// Root App Component
export default function App() {
  return (
    <AuthProvider>
      <OfflineSyncProvider>
        <NotesProvider>
          <StatusBar style="auto" />
          <AppNavigator />
        </NotesProvider>
      </OfflineSyncProvider>
    </AuthProvider>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
});
