{"name": "lab8-paritoshveer-firebase-notes", "version": "1.0.0", "description": "Personal Notes App with Firebase Integration - Lab 8", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest"}, "dependencies": {"expo": "~51.0.0", "react": "18.2.0", "react-native": "0.74.5", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "react-native-screens": "~3.31.1", "react-native-safe-area-context": "4.10.5", "react-native-gesture-handler": "~2.16.1", "firebase": "^10.7.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "11.3.1", "react-native-vector-icons": "^10.0.3", "expo-status-bar": "~1.12.1"}, "devDependencies": {"@babel/core": "^7.20.0", "jest": "^29.2.1"}, "keywords": ["react-native", "firebase", "notes-app", "real-time", "authentication"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT"}