# Firebase Setup Guide

This guide will walk you through setting up Firebase for the Personal Notes App.

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: `personal-notes-app-[your-name]`
4. Enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Configure Authentication

1. In Firebase Console, go to **Authentication**
2. Click **Get started**
3. Go to **Sign-in method** tab
4. Click **Email/Password**
5. Enable **Email/Password** provider
6. Click **Save**

## Step 3: Create Firestore Database

1. Go to **Firestore Database**
2. Click **Create database**
3. Choose **Start in test mode** (we'll add security rules later)
4. Select a location (choose closest to your users)
5. Click **Done**

## Step 4: Get Firebase Configuration

1. Go to **Project Settings** (gear icon)
2. Scroll down to **Your apps** section
3. Click **Add app** > **Web app** (</>) icon
4. Enter app nickname: `personal-notes-web`
5. Click **Register app**
6. Copy the configuration object that looks like:

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyC...",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
};
```

## Step 5: Update App Configuration

1. Open `src/services/firebase.js`
2. Replace the placeholder config with your actual config:

```javascript
// Replace this section
const firebaseConfig = {
  apiKey: "your-api-key-here",           // Replace with your apiKey
  authDomain: "your-project-id.firebaseapp.com",  // Replace with your authDomain
  projectId: "your-project-id",          // Replace with your projectId
  storageBucket: "your-project-id.appspot.com",   // Replace with your storageBucket
  messagingSenderId: "your-sender-id",   // Replace with your messagingSenderId
  appId: "your-app-id"                   // Replace with your appId
};
```

## Step 6: Install Firebase CLI

```bash
npm install -g firebase-tools
```

## Step 7: Login and Initialize Firebase

```bash
# Login to Firebase
firebase login

# Initialize Firebase in your project directory
cd lab8_paritoshveer
firebase init
```

When prompted, select:
- **Firestore**: Configure security rules and indexes files
- **Functions**: Configure a Cloud Functions directory and files
- Use existing project and select your project
- Accept default Firestore rules file (firestore.rules)
- Accept default Firestore indexes file (firestore.indexes.json)
- Select JavaScript for Cloud Functions
- Install dependencies with npm: Yes

## Step 8: Deploy Security Rules

```bash
firebase deploy --only firestore:rules
```

## Step 9: Set up Cloud Functions

```bash
# Navigate to functions directory
cd functions

# Install dependencies
npm install

# Deploy functions
firebase deploy --only functions
```

## Step 10: Test the Setup

1. Start your React Native app:
```bash
cd ..
npm start
```

2. Try creating an account and signing in
3. Create a test note
4. Verify data appears in Firestore Console

## Verification Checklist

- [ ] Firebase project created
- [ ] Authentication enabled with Email/Password
- [ ] Firestore database created
- [ ] App configuration updated with your Firebase config
- [ ] Firebase CLI installed and logged in
- [ ] Security rules deployed
- [ ] Cloud Functions deployed
- [ ] App successfully connects to Firebase
- [ ] Can create account and sign in
- [ ] Can create and sync notes

## Troubleshooting

### Common Issues

**1. "Firebase config not found" error**
- Make sure you've replaced the placeholder config in `firebase.js`
- Verify all config values are correct (no quotes around values)

**2. "Permission denied" errors**
- Ensure security rules are deployed: `firebase deploy --only firestore:rules`
- Check that user is authenticated before accessing Firestore

**3. Cloud Functions not working**
- Verify functions are deployed: `firebase deploy --only functions`
- Check function logs: `firebase functions:log`

**4. Authentication not working**
- Ensure Email/Password provider is enabled in Firebase Console
- Check network connectivity

### Getting Help

1. Check Firebase Console for error messages
2. Review browser/app console for detailed errors
3. Check Firebase documentation: https://firebase.google.com/docs
4. Review the app's error handling in `src/utils/errorHandler.js`

## Security Notes

- Never commit your Firebase config to public repositories
- The provided security rules ensure users can only access their own data
- Consider enabling App Check for additional security in production

## Next Steps

After setup is complete:
1. Test all app features thoroughly
2. Review security rules for your specific needs
3. Monitor usage in Firebase Console
4. Consider setting up Firebase Analytics for insights
