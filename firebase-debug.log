[debug] [2025-07-25T23:24:07.074Z] ----------------------------------------------------------------------
[debug] [2025-07-25T23:24:07.081Z] Command:       /opt/homebrew/Cellar/node/24.3.0/bin/node /opt/homebrew/bin/firebase init
[debug] [2025-07-25T23:24:07.081Z] CLI Version:   14.11.1
[debug] [2025-07-25T23:24:07.081Z] Platform:      darwin
[debug] [2025-07-25T23:24:07.081Z] Node Version:  v24.3.0
[debug] [2025-07-25T23:24:07.081Z] Time:          Fri Jul 25 2025 19:24:07 GMT-0400 (Eastern Daylight Time)
[debug] [2025-07-25T23:24:07.081Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-25T23:24:07.084Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-25T23:24:07.084Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  /Users/<USER>/Desktop/Humber College/cpan213/labs

